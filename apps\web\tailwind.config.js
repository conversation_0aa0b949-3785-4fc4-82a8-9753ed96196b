import { fontFamily } from 'tailwindcss/defaultTheme';
import forms from '@tailwindcss/forms';
import typography from '@tailwindcss/typography';

/** @type {import('tailwindcss').Config} */
export default {
	content: ['./src/**/*.{html,js,svelte,ts}'],
	darkMode: 'class',
	theme: {
		extend: {
			// Custom color palette for Life Note
			colors: {
				// Primary brand colors
				primary: {
					50: '#f0f9ff',
					100: '#e0f2fe',
					200: '#bae6fd',
					300: '#7dd3fc',
					400: '#38bdf8',
					500: '#0ea5e9',
					600: '#0284c7',
					700: '#0369a1',
					800: '#075985',
					900: '#0c4a6e',
					950: '#082f49'
				},
				// Secondary accent colors
				secondary: {
					50: '#fdf4ff',
					100: '#fae8ff',
					200: '#f5d0fe',
					300: '#f0abfc',
					400: '#e879f9',
					500: '#d946ef',
					600: '#c026d3',
					700: '#a21caf',
					800: '#86198f',
					900: '#701a75',
					950: '#4a044e'
				},
				// Neutral grays
				gray: {
					50: '#f8fafc',
					100: '#f1f5f9',
					200: '#e2e8f0',
					300: '#cbd5e1',
					400: '#94a3b8',
					500: '#64748b',
					600: '#475569',
					700: '#334155',
					800: '#1e293b',
					900: '#0f172a',
					950: '#020617'
				},
				// Status colors
				success: {
					50: '#f0fdf4',
					500: '#22c55e',
					600: '#16a34a',
					700: '#15803d'
				},
				warning: {
					50: '#fffbeb',
					500: '#f59e0b',
					600: '#d97706',
					700: '#b45309'
				},
				error: {
					50: '#fef2f2',
					500: '#ef4444',
					600: '#dc2626',
					700: '#b91c1c'
				}
			},

			// Custom font families
			fontFamily: {
				sans: ['Inter', 'system-ui', ...fontFamily.sans],
				mono: ['JetBrains Mono', 'Fira Code', ...fontFamily.mono],
				serif: ['Merriweather', 'Georgia', ...fontFamily.serif]
			},

			// Custom spacing
			spacing: {
				18: '4.5rem',
				88: '22rem',
				128: '32rem'
			},

			// Custom border radius
			borderRadius: {
				'4xl': '2rem'
			},

			// Custom shadows
			boxShadow: {
				soft: '0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04)',
				medium: '0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
				large: '0 10px 50px -12px rgba(0, 0, 0, 0.25)'
			},

			// Custom animations
			animation: {
				'fade-in': 'fadeIn 0.5s ease-in-out',
				'slide-up': 'slideUp 0.3s ease-out',
				'slide-down': 'slideDown 0.3s ease-out',
				'scale-in': 'scaleIn 0.2s ease-out',
				'bounce-subtle': 'bounceSubtle 0.6s ease-in-out'
			},

			// Custom keyframes
			keyframes: {
				fadeIn: {
					'0%': { opacity: '0' },
					'100%': { opacity: '1' }
				},
				slideUp: {
					'0%': { transform: 'translateY(10px)', opacity: '0' },
					'100%': { transform: 'translateY(0)', opacity: '1' }
				},
				slideDown: {
					'0%': { transform: 'translateY(-10px)', opacity: '0' },
					'100%': { transform: 'translateY(0)', opacity: '1' }
				},
				scaleIn: {
					'0%': { transform: 'scale(0.95)', opacity: '0' },
					'100%': { transform: 'scale(1)', opacity: '1' }
				},
				bounceSubtle: {
					'0%, 100%': { transform: 'translateY(0)' },
					'50%': { transform: 'translateY(-5px)' }
				}
			},

			// Custom typography
			typography: {
				DEFAULT: {
					css: {
						maxWidth: 'none',
						color: 'rgb(var(--color-text))',
						'[class~="lead"]': {
							color: 'rgb(var(--color-text-muted))'
						},
						a: {
							color: 'rgb(var(--color-primary))',
							textDecoration: 'none',
							'&:hover': {
								textDecoration: 'underline'
							}
						},
						strong: {
							color: 'rgb(var(--color-text))',
							fontWeight: '600'
						},
						'ol[type="A"]': {
							'--list-counter-style': 'upper-alpha'
						},
						'ol[type="a"]': {
							'--list-counter-style': 'lower-alpha'
						},
						'ol[type="A" s]': {
							'--list-counter-style': 'upper-alpha'
						},
						'ol[type="a" s]': {
							'--list-counter-style': 'lower-alpha'
						},
						'ol[type="I"]': {
							'--list-counter-style': 'upper-roman'
						},
						'ol[type="i"]': {
							'--list-counter-style': 'lower-roman'
						},
						'ol[type="I" s]': {
							'--list-counter-style': 'upper-roman'
						},
						'ol[type="i" s]': {
							'--list-counter-style': 'lower-roman'
						},
						'ol[type="1"]': {
							'--list-counter-style': 'decimal'
						}
					}
				}
			}
		}
	},
	plugins: [
		forms({
			strategy: 'class'
		}),
		typography
	]
};
