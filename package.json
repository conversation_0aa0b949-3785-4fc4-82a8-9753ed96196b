{"name": "life-note-client-vibe", "version": "0.1.0", "description": "Life Note - AI-Enhanced Note Management System", "private": true, "type": "module", "scripts": {"build": "turbo build", "dev": "turbo dev", "clean": "turbo clean", "test": "turbo test", "test:watch": "turbo test:watch", "test:coverage": "turbo test:coverage", "lint": "turbo lint", "lint:fix": "turbo lint:fix", "format": "prettier --write .", "format:check": "prettier --check .", "typecheck": "turbo typecheck", "db:generate": "turbo db:generate", "db:push": "turbo db:push", "db:migrate": "turbo db:migrate", "db:reset": "turbo db:reset", "db:seed": "turbo db:seed", "db:studio": "pnpm --filter @life-note/storage db:studio", "prepare": "husky"}, "devDependencies": {"@commitlint/cli": "^19.6.0", "@commitlint/config-conventional": "^19.6.0", "@types/node": "^20.19.2", "@typescript-eslint/eslint-plugin": "^8.18.1", "@typescript-eslint/parser": "^8.18.1", "eslint": "^9.17.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.1", "husky": "^9.1.7", "lint-staged": "^15.2.10", "prettier": "^3.4.2", "turbo": "^2.3.3", "typescript": "^5.6.3"}, "packageManager": "pnpm@9.15.0", "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "workspaces": ["apps/*", "packages/*", "services/*"], "keywords": ["note-taking", "markdown", "ai", "knowledge-management", "dependency-tracking", "mcp", "tauri", "svelte"], "author": "Life Note Team", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/life-note/life-note-client-vibe.git"}, "bugs": {"url": "https://github.com/life-note/life-note-client-vibe/issues"}, "homepage": "https://github.com/life-note/life-note-client-vibe#readme"}