<script lang="ts">
	import { onMount, createEventDispatcher } from 'svelte';
	import { EditorView, basicSetup } from 'codemirror';
	import { EditorState } from '@codemirror/state';
	import { markdown } from '@codemirror/lang-markdown';
	import { oneDark } from '@codemirror/theme-one-dark';
	import { keymap } from '@codemirror/view';
	import { defaultKeymap, indentWithTab } from '@codemirror/commands';
	import { searchKeymap } from '@codemirror/search';
	import { autocompletion } from '@codemirror/autocomplete';

	import { themeStore } from '$stores/theme';
	import EditorToolbar from './EditorToolbar.svelte';
	import MarkdownPreview from './MarkdownPreview.svelte';

	// Props
	let {
		value = $bindable(''),
		placeholder = '開始寫作...',
		readonly = false,
		showPreview = false,
		showToolbar = true,
		autofocus = false,
		lineNumbers = true,
		lineWrapping = true,
		spellcheck = true
	}: {
		value?: string;
		placeholder?: string;
		readonly?: boolean;
		showPreview?: boolean;
		showToolbar?: boolean;
		autofocus?: boolean;
		lineNumbers?: boolean;
		lineWrapping?: boolean;
		spellcheck?: boolean;
	} = $props();

	// Event dispatcher
	const dispatch = createEventDispatcher<{
		change: { value: string };
		save: { value: string };
		focus: void;
		blur: void;
	}>();

	// Editor state
	let editorContainer = $state<HTMLDivElement>();
	let editor = $state<EditorView | null>(null);
	let previewMode = $state<'split' | 'preview' | 'edit'>('edit');

	// Reactive values
	const isDark = $derived($themeStore.resolvedTheme === 'dark');

	// Editor configuration
	const createEditorState = (content: string) => {
		const extensions = [
			basicSetup,
			markdown(),
			keymap.of([
				...defaultKeymap,
				...searchKeymap,
				indentWithTab,
				{
					key: 'Mod-s',
					run: () => {
						dispatch('save', { value: editor?.state.doc.toString() || '' });
						return true;
					}
				},
				{
					key: 'Mod-b',
					run: () => {
						insertMarkdown('**', '**');
						return true;
					}
				},
				{
					key: 'Mod-i',
					run: () => {
						insertMarkdown('*', '*');
						return true;
					}
				},
				{
					key: 'Mod-k',
					run: () => {
						insertMarkdown('[', '](url)');
						return true;
					}
				}
			]),
			autocompletion(),
			EditorView.updateListener.of(update => {
				if (update.docChanged) {
					const newValue = update.state.doc.toString();
					value = newValue;
					dispatch('change', { value: newValue });
				}
			}),
			EditorView.theme({
				'&': {
					height: '100%',
					fontSize: '14px',
					fontFamily: '"JetBrains Mono", "Fira Code", "Consolas", monospace'
				},
				'.cm-content': {
					padding: '16px',
					minHeight: '200px',
					lineHeight: '1.6'
				},
				'.cm-focused': {
					outline: 'none'
				},
				'.cm-editor': {
					borderRadius: '8px'
				},
				'.cm-scroller': {
					fontFamily: 'inherit'
				}
			}),
			EditorView.lineWrapping
		];

		// Add dark theme if needed
		if (isDark) {
			extensions.push(oneDark);
		}

		return EditorState.create({
			doc: content,
			extensions
		});
	};

	// Initialize editor
	onMount(() => {
		if (editorContainer) {
			const state = createEditorState(value);
			editor = new EditorView({
				state,
				parent: editorContainer
			});

			if (autofocus) {
				editor.focus();
			}

			// Add event listeners
			editor.dom.addEventListener('focus', () => dispatch('focus'));
			editor.dom.addEventListener('blur', () => dispatch('blur'));
		}
	});

	// 使用 $effect 來管理清理邏輯
	$effect(() => {
		return () => {
			if (editor) {
				editor.destroy();
			}
		};
	});

	// Update editor when value changes externally
	$: if (editor && value !== editor.state.doc.toString()) {
		const transaction = editor.state.update({
			changes: {
				from: 0,
				to: editor.state.doc.length,
				insert: value
			}
		});
		editor.dispatch(transaction);
	}

	// Update theme
	$: if (editor) {
		const state = createEditorState(editor.state.doc.toString());
		editor.setState(state);
	}

	// Editor actions
	const insertMarkdown = (before: string, after: string = '') => {
		if (!editor) return;

		const { from, to } = editor.state.selection.main;
		const selectedText = editor.state.doc.sliceString(from, to);

		const replacement = before + selectedText + after;

		editor.dispatch({
			changes: { from, to, insert: replacement },
			selection: {
				anchor: from + before.length,
				head: from + before.length + selectedText.length
			}
		});

		editor.focus();
	};

	const insertText = (text: string) => {
		if (!editor) return;

		const { from } = editor.state.selection.main;

		editor.dispatch({
			changes: { from, insert: text },
			selection: { anchor: from + text.length }
		});

		editor.focus();
	};

	// Toolbar actions
	const handleToolbarAction = (action: string, data?: any) => {
		switch (action) {
			case 'bold':
				insertMarkdown('**', '**');
				break;
			case 'italic':
				insertMarkdown('*', '*');
				break;
			case 'strikethrough':
				insertMarkdown('~~', '~~');
				break;
			case 'code':
				insertMarkdown('`', '`');
				break;
			case 'codeblock':
				insertMarkdown('\n```\n', '\n```\n');
				break;
			case 'link':
				insertMarkdown('[', '](url)');
				break;
			case 'image':
				insertMarkdown('![', '](url)');
				break;
			case 'quote':
				insertMarkdown('\n> ', '');
				break;
			case 'list':
				insertMarkdown('\n- ', '');
				break;
			case 'orderedlist':
				insertMarkdown('\n1. ', '');
				break;
			case 'heading':
				const level = data?.level || 1;
				const hashes = '#'.repeat(level);
				insertMarkdown(`\n${hashes} `, '');
				break;
			case 'hr':
				insertText('\n---\n');
				break;
			case 'table':
				insertText('\n| Column 1 | Column 2 |\n|----------|----------|\n| Cell 1   | Cell 2   |\n');
				break;
			case 'preview':
				previewMode = previewMode === 'edit' ? 'split' : 'edit';
				break;
		}
	};

	// Export functions for parent component
	export const focus = () => editor?.focus();
	export const blur = () => editor?.dom.blur();
	export const getValue = () => editor?.state.doc.toString() || '';
	export const setValue = (newValue: string) => {
		if (editor) {
			const transaction = editor.state.update({
				changes: {
					from: 0,
					to: editor.state.doc.length,
					insert: newValue
				}
			});
			editor.dispatch(transaction);
		}
	};
</script>

<div class="markdown-editor flex flex-col h-full">
	{#if showToolbar}
		<EditorToolbar
			on:action={e => handleToolbarAction(e.detail.action, e.detail.data)}
			{previewMode}
			on:preview-mode={e => (previewMode = e.detail)}
		/>
	{/if}

	<div class="editor-content flex-1 flex overflow-hidden">
		{#if previewMode === 'edit' || previewMode === 'split'}
			<div
				class="editor-pane {previewMode === 'split' ? 'w-1/2' : 'w-full'} border-r border-border"
			>
				<div bind:this={editorContainer} class="h-full"></div>
			</div>
		{/if}

		{#if previewMode === 'preview' || previewMode === 'split'}
			<div class="preview-pane {previewMode === 'split' ? 'w-1/2' : 'w-full'}">
				<MarkdownPreview content={value} />
			</div>
		{/if}
	</div>
</div>

<style>
	.markdown-editor {
		border: 1px solid hsl(var(--border));
		border-radius: 8px;
		overflow: hidden;
		background: hsl(var(--background));
	}

	.editor-pane {
		position: relative;
	}

	.preview-pane {
		background: hsl(var(--background));
	}

	:global(.cm-editor) {
		height: 100% !important;
	}

	:global(.cm-scroller) {
		height: 100% !important;
	}
</style>
