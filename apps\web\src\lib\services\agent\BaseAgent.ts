import type {
	<PERSON><PERSON><PERSON>,
	AgentConfig,
	AgentStatus,
	AgentMetrics,
	AgentTaskRequest,
	AgentTaskResult,
	AgentTaskType,
	TaskStatus,
	AgentEvent,
	AgentEventListener
} from '$lib/types/agent';

/**
 * Agent 基礎實現類
 * 提供所有 Agent 的通用功能和生命週期管理
 */
export abstract class BaseAgent implements IAgent {
	protected _status: AgentStatus = 'offline';
	protected _metrics: AgentMetrics;
	protected runningTasks = new Map<string, AgentTaskRequest>();
	protected eventListeners = new Map<string, AgentEventListener[]>();
	protected startTime = Date.now();

	constructor(protected _config: AgentConfig) {
		this._metrics = {
			agentId: _config.id,
			totalTasks: 0,
			completedTasks: 0,
			failedTasks: 0,
			averageExecutionTime: 0,
			successRate: 0,
			lastActiveAt: new Date(),
			uptime: 0
		};
	}

	get id(): string {
		return this._config.id;
	}

	get config(): AgentConfig {
		return { ...this._config };
	}

	get status(): AgentStatus {
		return this._status;
	}

	get metrics(): AgentMetrics {
		return {
			...this._metrics,
			uptime: Date.now() - this.startTime
		};
	}

	/**
	 * 初始化 Agent
	 */
	async initialize(): Promise<void> {
		try {
			await this.onInitialize();
			this._status = 'idle';
			this.emitEvent('agent_status_changed', { status: this._status });
			console.log(`Agent ${this.id} initialized successfully`);
		} catch (error) {
			this._status = 'error';
			this.emitEvent('agent_status_changed', { status: this._status, error });
			throw error;
		}
	}

	/**
	 * 執行任務
	 */
	async executeTask(request: AgentTaskRequest): Promise<AgentTaskResult> {
		const startTime = Date.now();

		// 檢查是否支援任務類型
		if (!this.canHandle(request.type)) {
			throw new Error(`Agent ${this.id} cannot handle task type: ${request.type}`);
		}

		// 檢查當前負載
		if (this.getCurrentLoad() >= 1.0) {
			throw new Error(`Agent ${this.id} is at maximum capacity`);
		}

		// 更新狀態和指標
		this._status = 'busy';
		this._metrics.totalTasks++;
		this._metrics.lastActiveAt = new Date();
		this.runningTasks.set(request.id, request);

		this.emitEvent('task_started', { taskId: request.id, type: request.type });

		try {
			// 設置超時
			const timeout = request.timeout || this._config.defaultTimeout;
			const timeoutPromise = new Promise<never>((_, reject) => {
				setTimeout(() => reject(new Error('Task timeout')), timeout);
			});

			// 執行任務
			const resultPromise = this.onExecuteTask(request);
			const output = await Promise.race([resultPromise, timeoutPromise]);

			// 計算執行時間
			const executionTime = Date.now() - startTime;

			// 創建結果
			const result: AgentTaskResult = {
				id: this.generateResultId(),
				taskId: request.id,
				status: 'completed',
				output,
				executionTime,
				completedAt: new Date(),
				agentId: this.id
			};

			// 更新指標
			this.updateMetrics(executionTime, true);
			this.runningTasks.delete(request.id);

			// 更新狀態
			if (this.runningTasks.size === 0) {
				this._status = 'idle';
			}

			this.emitEvent('task_completed', { taskId: request.id, result });
			return result;
		} catch (error) {
			const executionTime = Date.now() - startTime;

			// 創建錯誤結果
			const result: AgentTaskResult = {
				id: this.generateResultId(),
				taskId: request.id,
				status: 'failed',
				error: error instanceof Error ? error.message : String(error),
				executionTime,
				completedAt: new Date(),
				agentId: this.id
			};

			// 更新指標
			this.updateMetrics(executionTime, false);
			this.runningTasks.delete(request.id);

			// 更新狀態
			if (this.runningTasks.size === 0) {
				this._status = 'idle';
			}

			this.emitEvent('task_failed', { taskId: request.id, error: result.error });
			return result;
		}
	}

	/**
	 * 檢查是否支援任務類型
	 */
	canHandle(taskType: AgentTaskType): boolean {
		return this._config.capabilities.includes(taskType);
	}

	/**
	 * 獲取當前負載
	 */
	getCurrentLoad(): number {
		return this.runningTasks.size / this._config.maxConcurrentTasks;
	}

	/**
	 * 停止 Agent
	 */
	async stop(): Promise<void> {
		try {
			// 等待所有運行中的任務完成或取消
			const runningTaskIds = Array.from(this.runningTasks.keys());
			await Promise.allSettled(runningTaskIds.map(taskId => this.cancelTask(taskId)));

			await this.onStop();
			this._status = 'offline';
			this.emitEvent('agent_status_changed', { status: this._status });
			console.log(`Agent ${this.id} stopped successfully`);
		} catch (error) {
			this._status = 'error';
			this.emitEvent('agent_status_changed', { status: this._status, error });
			throw error;
		}
	}

	/**
	 * 更新配置
	 */
	async updateConfig(config: Partial<AgentConfig>): Promise<void> {
		const oldConfig = { ...this._config };
		this._config = { ...this._config, ...config };

		try {
			await this.onConfigUpdate(oldConfig, this._config);
			console.log(`Agent ${this.id} configuration updated`);
		} catch (error) {
			// 回滾配置
			this._config = oldConfig;
			throw error;
		}
	}

	/**
	 * 添加事件監聽器
	 */
	addEventListener(eventType: string, listener: AgentEventListener): void {
		if (!this.eventListeners.has(eventType)) {
			this.eventListeners.set(eventType, []);
		}
		this.eventListeners.get(eventType)!.push(listener);
	}

	/**
	 * 移除事件監聽器
	 */
	removeEventListener(eventType: string, listener: AgentEventListener): void {
		const listeners = this.eventListeners.get(eventType);
		if (listeners) {
			const index = listeners.indexOf(listener);
			if (index > -1) {
				listeners.splice(index, 1);
			}
		}
	}

	/**
	 * 發送事件
	 */
	protected emitEvent(type: string, data?: any): void {
		const event: AgentEvent = {
			type: type as any,
			agentId: this.id,
			data,
			timestamp: new Date()
		};

		const listeners = this.eventListeners.get(type);
		if (listeners) {
			listeners.forEach(listener => {
				try {
					listener(event);
				} catch (error) {
					console.error(`Error in event listener for ${type}:`, error);
				}
			});
		}
	}

	/**
	 * 更新性能指標
	 */
	protected updateMetrics(executionTime: number, success: boolean): void {
		if (success) {
			this._metrics.completedTasks++;
		} else {
			this._metrics.failedTasks++;
		}

		// 更新平均執行時間
		const totalCompletedTasks = this._metrics.completedTasks + this._metrics.failedTasks;
		this._metrics.averageExecutionTime =
			(this._metrics.averageExecutionTime * (totalCompletedTasks - 1) + executionTime) /
			totalCompletedTasks;

		// 更新成功率
		this._metrics.successRate = this._metrics.completedTasks / this._metrics.totalTasks;
	}

	/**
	 * 取消任務
	 */
	protected async cancelTask(taskId: string): Promise<void> {
		const task = this.runningTasks.get(taskId);
		if (task) {
			try {
				await this.onCancelTask(taskId);
				this.runningTasks.delete(taskId);
			} catch (error) {
				console.error(`Error cancelling task ${taskId}:`, error);
			}
		}
	}

	/**
	 * 生成結果 ID
	 */
	protected generateResultId(): string {
		return `result-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
	}

	// 抽象方法，由子類實現

	/**
	 * 初始化實現
	 */
	protected abstract onInitialize(): Promise<void>;

	/**
	 * 執行任務實現
	 */
	protected abstract onExecuteTask(request: AgentTaskRequest): Promise<any>;

	/**
	 * 停止實現
	 */
	protected abstract onStop(): Promise<void>;

	/**
	 * 配置更新實現
	 */
	protected abstract onConfigUpdate(oldConfig: AgentConfig, newConfig: AgentConfig): Promise<void>;

	/**
	 * 取消任務實現
	 */
	protected abstract onCancelTask(taskId: string): Promise<void>;
}
