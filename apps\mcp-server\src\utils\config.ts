import { existsSync, statSync } from "fs";
import { resolve } from "path";
import {
  ServerConfig,
  defaultConfig,
  envMapping,
  configValidationRules,
  ValidationResult,
  ValidationError,
  ValidationWarning,
} from "../types/config.js";
import { logger } from "./logger.js";

/**
 * 驗證配置
 */
export async function validateConfig(options: any): Promise<ServerConfig> {
  try {
    // 合併配置：默認配置 -> 環境變數 -> 命令行參數 -> 配置文件
    let config = { ...defaultConfig };

    // 從環境變數載入配置
    config = mergeEnvConfig(config);

    // 從命令行參數載入配置
    config = mergeCliConfig(config, options);

    // 從配置文件載入配置（如果指定）
    if (options.config) {
      const fileConfig = await loadConfigFile(options.config);
      config = { ...config, ...fileConfig };
    }

    // 驗證配置
    const validation = validateConfigObject(config);

    if (!validation.isValid) {
      const errorMessages = validation.errors.map(
        (e) => `${e.field}: ${e.message}`,
      );
      throw new Error(
        `Configuration validation failed:\n${errorMessages.join("\n")}`,
      );
    }

    // 顯示警告
    if (validation.warnings.length > 0) {
      validation.warnings.forEach((warning) => {
        logger.warn(
          `Configuration warning - ${warning.field}: ${warning.message}`,
        );
      });
    }

    // 驗證筆記目錄
    await validateNotesDirectory(config.notesDir);

    return config;
  } catch (error) {
    logger.error("Configuration validation failed:", error);
    throw error;
  }
}

/**
 * 從環境變數合併配置
 */
function mergeEnvConfig(config: ServerConfig): ServerConfig {
  const envConfig: Partial<ServerConfig> = {};

  for (const [envKey, configKey] of Object.entries(envMapping)) {
    const envValue = process.env[envKey];
    if (envValue !== undefined) {
      // 類型轉換
      let value: any = envValue;

      if (configKey === "port") {
        value = parseInt(envValue, 10);
      } else if (
        [
          "enableCors",
          "enableAuth",
          "enableHttpServer",
          "enableMCPServer",
          "enableFileWatcher",
        ].includes(configKey)
      ) {
        value = envValue.toLowerCase() === "true";
      } else if (configKey === "corsOrigins" && envValue.includes(",")) {
        value = envValue.split(",").map((s) => s.trim());
      }

      (envConfig as any)[configKey] = value;
    }
  }

  return { ...config, ...envConfig };
}

/**
 * 從命令行參數合併配置
 */
function mergeCliConfig(config: ServerConfig, options: any): ServerConfig {
  const cliConfig: Partial<ServerConfig> = {};

  if (options.port) cliConfig.port = parseInt(options.port, 10);
  if (options.host) cliConfig.host = options.host;
  if (options.notesDir) cliConfig.notesDir = options.notesDir;
  if (options.logLevel) cliConfig.logLevel = options.logLevel;
  if (options.enableCors !== undefined)
    cliConfig.enableCors = options.enableCors;
  if (options.enableAuth !== undefined)
    cliConfig.enableAuth = options.enableAuth;

  return { ...config, ...cliConfig };
}

/**
 * 載入配置文件
 */
async function loadConfigFile(
  configPath: string,
): Promise<Partial<ServerConfig>> {
  try {
    const fullPath = resolve(configPath);

    if (!existsSync(fullPath)) {
      throw new Error(`Configuration file not found: ${fullPath}`);
    }

    const { default: config } = await import(fullPath);
    return config;
  } catch (error) {
    logger.error(`Failed to load configuration file: ${configPath}`, error);
    throw error;
  }
}

/**
 * 驗證配置對象
 */
function validateConfigObject(config: ServerConfig): ValidationResult {
  const errors: ValidationError[] = [];
  const warnings: ValidationWarning[] = [];

  for (const rule of configValidationRules) {
    const value = (config as any)[rule.field];

    // 檢查必填字段
    if (rule.required && (value === undefined || value === null)) {
      errors.push({
        field: rule.field,
        message: "This field is required",
        value,
      });
      continue;
    }

    // 跳過未定義的可選字段
    if (value === undefined || value === null) {
      continue;
    }

    // 類型檢查
    if (rule.type) {
      const actualType = Array.isArray(value) ? "array" : typeof value;
      if (actualType !== rule.type) {
        errors.push({
          field: rule.field,
          message: `Expected type ${rule.type}, got ${actualType}`,
          value,
        });
        continue;
      }
    }

    // 數值範圍檢查
    if (rule.type === "number") {
      if (rule.min !== undefined && value < rule.min) {
        errors.push({
          field: rule.field,
          message: `Value must be at least ${rule.min}`,
          value,
        });
      }
      if (rule.max !== undefined && value > rule.max) {
        errors.push({
          field: rule.field,
          message: `Value must be at most ${rule.max}`,
          value,
        });
      }
    }

    // 正則表達式檢查
    if (rule.pattern && typeof value === "string") {
      if (!rule.pattern.test(value)) {
        errors.push({
          field: rule.field,
          message: `Value does not match required pattern`,
          value,
        });
      }
    }

    // 自定義驗證器
    if (rule.validator) {
      const result = rule.validator(value);
      if (result !== true) {
        errors.push({
          field: rule.field,
          message: typeof result === "string" ? result : "Validation failed",
          value,
        });
      }
    }
  }

  // 額外的邏輯驗證
  performLogicalValidation(config, errors, warnings);

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * 執行邏輯驗證
 */
function performLogicalValidation(
  config: ServerConfig,
  errors: ValidationError[],
  warnings: ValidationWarning[],
): void {
  // 檢查端口衝突
  if (config.port < 1024) {
    warnings.push({
      field: "port",
      message: "Using a port below 1024 may require elevated privileges",
      value: config.port,
    });
  }

  // 檢查認證配置
  if (config.enableAuth && !config.authSecret) {
    errors.push({
      field: "authSecret",
      message: "Auth secret is required when authentication is enabled",
    });
  }

  // 檢查 CORS 配置
  if (config.enableCors && config.corsOrigins === "*") {
    warnings.push({
      field: "corsOrigins",
      message:
        "Using wildcard CORS origin may pose security risks in production",
      value: config.corsOrigins,
    });
  }

  // 檢查服務啟用狀態
  if (!config.enableHttpServer && !config.enableMCPServer) {
    errors.push({
      field: "enableHttpServer",
      message: "At least one server type (HTTP or MCP) must be enabled",
    });
  }

  // 檢查日誌級別
  if (config.logLevel === "debug") {
    warnings.push({
      field: "logLevel",
      message: "Debug log level may impact performance in production",
      value: config.logLevel,
    });
  }
}

/**
 * 驗證筆記目錄
 */
async function validateNotesDirectory(notesDir: string): Promise<void> {
  try {
    const fullPath = resolve(notesDir);

    if (!existsSync(fullPath)) {
      throw new Error(`Notes directory does not exist: ${fullPath}`);
    }

    const stats = statSync(fullPath);
    if (!stats.isDirectory()) {
      throw new Error(`Notes path is not a directory: ${fullPath}`);
    }

    // 檢查讀取權限
    try {
      const { readdir } = await import("fs/promises");
      await readdir(fullPath);
    } catch (error) {
      throw new Error(`Cannot read notes directory: ${fullPath}`);
    }

    logger.debug(`Notes directory validated: ${fullPath}`);
  } catch (error) {
    logger.error("Notes directory validation failed:", error);
    throw error;
  }
}

/**
 * 創建運行時配置
 */
export function createRuntimeConfig(config: ServerConfig) {
  return {
    ...config,
    startTime: new Date(),
    processId: process.pid,
    nodeVersion: process.version,
    platform: process.platform,
    architecture: process.arch,
  };
}

/**
 * 配置差異比較
 */
export function compareConfigs(
  oldConfig: ServerConfig,
  newConfig: ServerConfig,
): { changed: string[]; added: string[]; removed: string[] } {
  const changed: string[] = [];
  const added: string[] = [];
  const removed: string[] = [];

  const allKeys = new Set([
    ...Object.keys(oldConfig),
    ...Object.keys(newConfig),
  ]);

  for (const key of allKeys) {
    const oldValue = (oldConfig as any)[key];
    const newValue = (newConfig as any)[key];

    if (oldValue === undefined && newValue !== undefined) {
      added.push(key);
    } else if (oldValue !== undefined && newValue === undefined) {
      removed.push(key);
    } else if (JSON.stringify(oldValue) !== JSON.stringify(newValue)) {
      changed.push(key);
    }
  }

  return { changed, added, removed };
}
