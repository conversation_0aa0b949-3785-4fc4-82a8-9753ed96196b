import {
  ValueObject,
  BusinessRuleViolationError,
} from "../../shared/Entity.js";

/**
 * 標籤值對象
 * 代表筆記的分類標籤
 */
export class Tag extends ValueObject {
  private readonly _name: string;
  private readonly _color?: string;

  constructor(name: string, color?: string) {
    super();
    this.validateName(name);
    if (color) {
      this.validateColor(color);
    }

    this._name = name.toLowerCase().trim();
    this._color = color;
  }

  /**
   * 創建標籤
   */
  static create(name: string, color?: string): Tag {
    return new Tag(name, color);
  }

  /**
   * 驗證標籤名稱
   */
  private validateName(name: string): void {
    if (!name || name.trim().length === 0) {
      throw new BusinessRuleViolationError("Tag name cannot be empty");
    }

    if (name.length > 50) {
      throw new BusinessRuleViolationError(
        "Tag name cannot exceed 50 characters",
      );
    }

    // 檢查是否只包含字母數字字符、連字符和下劃線
    if (!/^[a-zA-Z0-9\-_\u4e00-\u9fff]+$/.test(name)) {
      throw new BusinessRuleViolationError(
        "Tag name can only contain alphanumeric characters, hyphens, underscores, and Chinese characters",
      );
    }
  }

  /**
   * 驗證顏色格式
   */
  private validateColor(color: string): void {
    // 檢查是否為有效的十六進制顏色代碼
    if (!/^#[0-9A-Fa-f]{6}$/.test(color)) {
      throw new BusinessRuleViolationError(
        "Color must be a valid hex color code (e.g., #FF0000)",
      );
    }
  }

  get name(): string {
    return this._name;
  }

  get color(): string | undefined {
    return this._color;
  }

  /**
   * 檢查標籤是否匹配搜索詞
   */
  matches(searchTerm: string): boolean {
    return this._name.includes(searchTerm.toLowerCase().trim());
  }

  equals(other: ValueObject): boolean {
    if (!(other instanceof Tag)) {
      return false;
    }
    return this._name === other._name && this._color === other._color;
  }

  hashCode(): string {
    return `${this._name}:${this._color || ""}`;
  }

  toString(): string {
    return this._name;
  }

  toPlainObject(): Record<string, unknown> {
    return {
      name: this._name,
      color: this._color,
    };
  }

  static fromPlainObject(data: Record<string, unknown>): Tag {
    if (typeof data.name !== "string") {
      throw new BusinessRuleViolationError(
        "Invalid Tag data: name is required",
      );
    }

    return new Tag(data.name, data.color as string | undefined);
  }
}

/**
 * 預定義的系統標籤
 */
export class SystemTags {
  static readonly IMPORTANT = Tag.create("important", "#FF0000");
  static readonly TODO = Tag.create("todo", "#FFA500");
  static readonly COMPLETED = Tag.create("completed", "#00FF00");
  static readonly ARCHIVED = Tag.create("archived", "#808080");
  static readonly DRAFT = Tag.create("draft", "#0000FF");
  static readonly PUBLISHED = Tag.create("published", "#008000");

  /**
   * 獲取所有系統標籤
   */
  static getAll(): Tag[] {
    return [
      this.IMPORTANT,
      this.TODO,
      this.COMPLETED,
      this.ARCHIVED,
      this.DRAFT,
      this.PUBLISHED,
    ];
  }

  /**
   * 檢查是否為系統標籤
   */
  static isSystemTag(tag: Tag): boolean {
    return this.getAll().some((systemTag) => systemTag.equals(tag));
  }
}
