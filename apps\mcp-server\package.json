{"name": "@life-note/mcp-server", "version": "1.0.0", "description": "Life Note MCP Server - Model Context Protocol server for Life Note application", "type": "module", "main": "index.js", "bin": {"life-note-mcp-server": "./index.js"}, "scripts": {"start": "node index.js", "dev": "node --inspect index.js", "test": "node test.js", "build": "echo 'No build step required for this server'", "lint": "echo '<PERSON><PERSON> not configured yet'", "clean": "echo 'No clean step required'"}, "keywords": ["mcp", "model-context-protocol", "life-note", "notes", "ai", "assistant", "server"], "author": "Life Note Team", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0"}, "devDependencies": {"@types/node": "^20.0.0"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/life-note/life-note-client-vibe.git", "directory": "apps/mcp-server"}, "bugs": {"url": "https://github.com/life-note/life-note-client-vibe/issues"}, "homepage": "https://github.com/life-note/life-note-client-vibe#readme", "files": ["index.js", "README.md", "LICENSE"], "publishConfig": {"access": "public"}}