/* eslint-disable @typescript-eslint/no-unused-vars */
import { z } from "zod";

import { Entity, BusinessRuleViolationError } from "../../shared/Entity.js";
import { DependencyId } from "../value-objects/DependencyId.js";
import { NoteId } from "../value-objects/NoteId.js";

/**
 * 依賴關係類型枚舉
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export enum DependencyType {
  REFERENCE = "reference", // 引用關係
  INCLUDE = "include", // 包含關係
  EXTENDS = "extends", // 繼承關係
  IMPLEMENTS = "implements", // 實現關係
  USES = "uses", // 使用關係
  DEPENDS_ON = "depends_on", // 依賴關係
}

/**
 * 依賴關係強度枚舉
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export enum DependencyStrength {
  WEAK = "weak", // 弱依賴
  MEDIUM = "medium", // 中等依賴
  STRONG = "strong", // 強依賴
  CRITICAL = "critical", // 關鍵依賴
}

/**
 * 依賴關係創建參數
 */
export interface CreateDependencyParams {
  sourceNoteId: NoteId;
  targetNoteId: NoteId;
  type: DependencyType;
  strength?: DependencyStrength;
  description?: string;
  metadata?: Record<string, unknown>;
}

/**
 * 依賴關係實體
 * 代表兩個筆記之間的依賴關係
 */
export class Dependency extends Entity<DependencyId> {
  private _sourceNoteId: NoteId;
  private _targetNoteId: NoteId;
  private _type: DependencyType;
  private _strength: DependencyStrength;
  private _description?: string;
  private _metadata: Record<string, unknown>;
  private _isActive: boolean;

  constructor(
    id: DependencyId,
    sourceNoteId: NoteId,
    targetNoteId: NoteId,
    type: DependencyType,
    options: {
      strength?: DependencyStrength;
      description?: string;
      metadata?: Record<string, unknown>;
      isActive?: boolean;
      createdAt?: Date;
    } = {},
  ) {
    super(id, options.createdAt);

    this.validateDependency(sourceNoteId, targetNoteId);

    this._sourceNoteId = sourceNoteId;
    this._targetNoteId = targetNoteId;
    this._type = type;
    this._strength = options.strength || DependencyStrength.MEDIUM;
    this._description = options.description;
    this._metadata = options.metadata || {};
    this._isActive = options.isActive !== false;
  }

  /**
   * 創建新的依賴關係
   */
  static create(params: CreateDependencyParams): Dependency {
    const dependencyId = DependencyId.generate();

    return new Dependency(
      dependencyId,
      params.sourceNoteId,
      params.targetNoteId,
      params.type,
      {
        strength: params.strength,
        description: params.description,
        metadata: params.metadata,
      },
    );
  }

  /**
   * 驗證依賴關係
   */
  private validateDependency(sourceNoteId: NoteId, targetNoteId: NoteId): void {
    if (sourceNoteId.equals(targetNoteId)) {
      throw new BusinessRuleViolationError("A note cannot depend on itself");
    }
  }

  /**
   * 更新依賴關係描述
   */
  updateDescription(description: string): void {
    this._description = description;
    this.touch();
  }

  /**
   * 更新依賴關係強度
   */
  updateStrength(strength: DependencyStrength): void {
    this._strength = strength;
    this.touch();
  }

  /**
   * 更新元數據
   */
  updateMetadata(metadata: Record<string, unknown>): void {
    this._metadata = { ...this._metadata, ...metadata };
    this.touch();
  }

  /**
   * 激活依賴關係
   */
  activate(): void {
    this._isActive = true;
    this.touch();
  }

  /**
   * 停用依賴關係
   */
  deactivate(): void {
    this._isActive = false;
    this.touch();
  }

  /**
   * 檢查是否為循環依賴
   */
  wouldCreateCycle(existingDependencies: Dependency[]): boolean {
    // 簡單的循環檢測：檢查目標筆記是否已經依賴於源筆記
    return existingDependencies.some(
      (dep) =>
        dep._sourceNoteId.equals(this._targetNoteId) &&
        dep._targetNoteId.equals(this._sourceNoteId) &&
        dep._isActive,
    );
  }

  /**
   * 檢查是否為雙向依賴
   */
  isBidirectional(other: Dependency): boolean {
    return (
      this._sourceNoteId.equals(other._targetNoteId) &&
      this._targetNoteId.equals(other._sourceNoteId) &&
      this._isActive &&
      other._isActive
    );
  }

  /**
   * 檢查是否涉及指定筆記
   */
  involves(noteId: NoteId): boolean {
    return (
      this._sourceNoteId.equals(noteId) || this._targetNoteId.equals(noteId)
    );
  }

  /**
   * 檢查是否為強依賴
   */
  get isStrong(): boolean {
    return (
      this._strength === DependencyStrength.STRONG ||
      this._strength === DependencyStrength.CRITICAL
    );
  }

  /**
   * 檢查是否為關鍵依賴
   */
  get isCritical(): boolean {
    return this._strength === DependencyStrength.CRITICAL;
  }

  // Getters
  get sourceNoteId(): NoteId {
    return this._sourceNoteId;
  }

  get targetNoteId(): NoteId {
    return this._targetNoteId;
  }

  get type(): DependencyType {
    return this._type;
  }

  get strength(): DependencyStrength {
    return this._strength;
  }

  get description(): string | undefined {
    return this._description;
  }

  get metadata(): Record<string, unknown> {
    return { ...this._metadata };
  }

  get isActive(): boolean {
    return this._isActive;
  }

  /**
   * 轉換為普通對象
   */
  toPlainObject(): Record<string, unknown> {
    return {
      id: this._id.value,
      sourceNoteId: this._sourceNoteId.value,
      targetNoteId: this._targetNoteId.value,
      type: this._type,
      strength: this._strength,
      description: this._description,
      metadata: this._metadata,
      isActive: this._isActive,
      createdAt: this._createdAt.toISOString(),
      updatedAt: this._updatedAt.toISOString(),
    };
  }

  /**
   * 從普通對象創建依賴關係實體
   */
  static fromPlainObject(data: Record<string, unknown>): Dependency {
    const schema = z.object({
      id: z.string(),
      sourceNoteId: z.string(),
      targetNoteId: z.string(),
      type: z.nativeEnum(DependencyType),
      strength: z
        .nativeEnum(DependencyStrength)
        .default(DependencyStrength.MEDIUM),
      description: z.string().optional(),
      metadata: z.record(z.unknown()).default({}),
      isActive: z.boolean().default(true),
      createdAt: z.string().optional(),
      updatedAt: z.string().optional(),
    });

    const parsed = schema.parse(data);

    return new Dependency(
      DependencyId.fromString(parsed.id),
      NoteId.fromString(parsed.sourceNoteId),
      NoteId.fromString(parsed.targetNoteId),
      parsed.type,
      {
        strength: parsed.strength,
        description: parsed.description,
        metadata: parsed.metadata,
        isActive: parsed.isActive,
        createdAt: parsed.createdAt ? new Date(parsed.createdAt) : undefined,
      },
    );
  }
}
