import { PrismaClient } from "./generated/index.js";
import type { IDatabaseConnection } from "../interfaces/IRepository.js";

/**
 * Prisma 客戶端包裝器
 * 提供數據庫連接管理和事務支援
 */
export class PrismaClientWrapper implements IDatabaseConnection {
  private _client: PrismaClient;
  private _isConnected: boolean = false;

  constructor() {
    this._client = new PrismaClient({
      log:
        process.env.NODE_ENV === "development"
          ? ["query", "info", "warn", "error"]
          : ["error"],
      errorFormat: "pretty",
    });
  }

  /**
   * 獲取 Prisma 客戶端實例
   */
  get client(): PrismaClient {
    return this._client;
  }

  /**
   * 連接到數據庫
   */
  async connect(): Promise<void> {
    try {
      await this._client.$connect();
      this._isConnected = true;
      console.log("✅ Database connected successfully");
    } catch (error) {
      console.error("❌ Failed to connect to database:", error);
      throw error;
    }
  }

  /**
   * 斷開數據庫連接
   */
  async disconnect(): Promise<void> {
    try {
      await this._client.$disconnect();
      this._isConnected = false;
      console.log("✅ Database disconnected successfully");
    } catch (error) {
      console.error("❌ Failed to disconnect from database:", error);
      throw error;
    }
  }

  /**
   * 檢查連接狀態
   */
  isConnected(): boolean {
    return this._isConnected;
  }

  /**
   * 執行原始 SQL 查詢
   */
  async executeRaw(sql: string, params?: unknown[]): Promise<unknown> {
    try {
      if (params && params.length > 0) {
        return await this._client.$executeRawUnsafe(sql, ...params);
      } else {
        return await this._client.$executeRawUnsafe(sql);
      }
    } catch (error) {
      console.error("❌ Failed to execute raw SQL:", error);
      throw error;
    }
  }

  /**
   * 執行原始 SQL 查詢並返回結果
   */
  async queryRaw<T = unknown>(sql: string, params?: unknown[]): Promise<T[]> {
    try {
      if (params && params.length > 0) {
        return await this._client.$queryRawUnsafe<T[]>(sql, ...params);
      } else {
        return await this._client.$queryRawUnsafe<T[]>(sql);
      }
    } catch (error) {
      console.error("❌ Failed to query raw SQL:", error);
      throw error;
    }
  }

  /**
   * 在事務中執行操作
   */
  async transaction<T>(
    operation: (client: PrismaClient) => Promise<T>,
  ): Promise<T> {
    try {
      return await this._client.$transaction(async (tx) => {
        return await operation(tx);
      });
    } catch (error) {
      console.error("❌ Transaction failed:", error);
      throw error;
    }
  }

  /**
   * 檢查數據庫健康狀態
   */
  async healthCheck(): Promise<boolean> {
    try {
      await this._client.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      console.error("❌ Database health check failed:", error);
      return false;
    }
  }

  /**
   * 獲取數據庫統計信息
   */
  async getDatabaseStats(): Promise<{
    users: number;
    notes: number;
    workspaces: number;
    dependencies: number;
  }> {
    try {
      const [users, notes, workspaces, dependencies] = await Promise.all([
        this._client.user.count(),
        this._client.note.count(),
        this._client.workspace.count(),
        this._client.dependency.count(),
      ]);

      return {
        users,
        notes,
        workspaces,
        dependencies,
      };
    } catch (error) {
      console.error("❌ Failed to get database stats:", error);
      throw error;
    }
  }

  /**
   * 清理數據庫（僅用於測試環境）
   */
  async cleanup(): Promise<void> {
    if (process.env.NODE_ENV === "production") {
      throw new Error("Database cleanup is not allowed in production");
    }

    try {
      // 按照外鍵依賴順序刪除
      await this._client.userSession.deleteMany();
      await this._client.userStatistics.deleteMany();
      await this._client.userPreference.deleteMany();
      await this._client.searchIndex.deleteMany();
      await this._client.noteVersion.deleteMany();
      await this._client.dependency.deleteMany();
      await this._client.note.deleteMany();
      await this._client.workspaceMember.deleteMany();
      await this._client.workspace.deleteMany();
      await this._client.user.deleteMany();
      await this._client.tag.deleteMany();
      await this._client.category.deleteMany();

      console.log("✅ Database cleaned up successfully");
    } catch (error) {
      console.error("❌ Failed to cleanup database:", error);
      throw error;
    }
  }

  /**
   * 重置數據庫（僅用於測試環境）
   */
  async reset(): Promise<void> {
    if (process.env.NODE_ENV === "production") {
      throw new Error("Database reset is not allowed in production");
    }

    try {
      await this.cleanup();
      console.log("✅ Database reset successfully");
    } catch (error) {
      console.error("❌ Failed to reset database:", error);
      throw error;
    }
  }
}

// 創建全局 Prisma 客戶端實例
export const prismaClient = new PrismaClientWrapper();

// 導出 Prisma 類型
export type { PrismaClient } from "./generated/index.js";
export * from "./generated/index.js";
