{"name": "@life-note/storage", "version": "0.1.0", "description": "Life Note Storage Layer with Prisma ORM", "type": "module", "main": "src/index.ts", "types": "src/index.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:reset": "prisma migrate reset", "db:seed": "tsx src/seed.ts", "db:studio": "prisma studio"}, "dependencies": {"@prisma/client": "^5.22.0", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20.19.2", "prisma": "^5.22.0", "tsx": "^4.19.2", "typescript": "^5.6.3", "vitest": "^2.1.8"}, "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}, "./repositories": {"import": "./dist/repositories/index.js", "types": "./dist/repositories/index.d.ts"}, "./interfaces": {"import": "./dist/interfaces/index.js", "types": "./dist/interfaces/index.d.ts"}, "./prisma": {"import": "./dist/prisma/index.js", "types": "./dist/prisma/index.d.ts"}}, "files": ["dist", "prisma/schema.prisma"], "keywords": ["storage", "prisma", "orm", "sqlite", "repository"], "author": "Life Note Team", "license": "MIT"}