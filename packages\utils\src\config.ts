import { z } from "zod";

// 環境變數驗證 Schema
export const ConfigSchema = z.object({
  // 應用配置
  app: z.object({
    name: z.string().default("Life Note Client Vibe"),
    version: z.string().default("1.0.0"),
    port: z.number().default(3000),
    nodeEnv: z
      .enum(["development", "production", "test"])
      .default("development"),
  }),

  // 資料庫配置
  database: z.object({
    url: z.string().default("file:./data/life-note.db"),
    poolSize: z.number().default(10),
    timeout: z.number().default(30000),
  }),

  // AI 服務配置
  ai: z.object({
    gemini: z.object({
      apiKey: z.string().min(1, "Gemini API key is required"),
      model: z.string().default("gemini-2.5-flash"),
      maxTokens: z.number().default(2048),
      temperature: z.number().min(0).max(1).default(0.7),
      topP: z.number().min(0).max(1).default(0.8),
      topK: z.number().default(40),
    }),
    openai: z.object({
      apiKey: z.string().optional(),
      model: z.string().default("gpt-4"),
      maxTokens: z.number().default(2048),
      temperature: z.number().min(0).max(1).default(0.7),
    }),
    anthropic: z.object({
      apiKey: z.string().optional(),
      model: z.string().default("claude-3-sonnet-********"),
      maxTokens: z.number().default(2048),
      temperature: z.number().min(0).max(1).default(0.7),
    }),
    routing: z.object({
      defaultProvider: z
        .enum(["gemini", "openai", "anthropic"])
        .default("gemini"),
      fallbackProvider: z
        .enum(["gemini", "openai", "anthropic"])
        .default("openai"),
      complexityThreshold: z.number().min(0).max(1).default(0.7),
      sensitivityThreshold: z.number().min(0).max(1).default(0.8),
    }),
  }),

  // MCP 服務配置
  mcp: z.object({
    port: z.number().default(3001),
    host: z.string().default("localhost"),
    enableAuth: z.boolean().default(false),
    sessionTimeout: z.number().default(3600000),
    maxConnections: z.number().default(100),
  }),

  // 本地 Agent 配置
  localAgent: z.object({
    port: z.number().default(3002),
    maxWorkers: z.number().default(4),
    timeout: z.number().default(30000),
  }),

  // 安全配置
  security: z.object({
    jwtSecret: z.string().min(32, "JWT secret must be at least 32 characters"),
    encryptionKey: z
      .string()
      .min(32, "Encryption key must be at least 32 characters"),
    corsOrigin: z.array(z.string()).default(["http://localhost:3000"]),
  }),

  // 存儲配置
  storage: z.object({
    path: z.string().default("./data/notes"),
    maxFileSize: z.number().default(10485760), // 10MB
    allowedExtensions: z
      .array(z.string())
      .default([".md", ".txt", ".json", ".yaml", ".yml"]),
  }),

  // 日誌配置
  logging: z.object({
    level: z.enum(["error", "warn", "info", "debug"]).default("info"),
    file: z.string().default("./logs/app.log"),
    maxSize: z.number().default(10485760),
    maxFiles: z.number().default(5),
  }),

  // 功能開關
  features: z.object({
    aiAssistant: z.boolean().default(true),
    dependencyAnalysis: z.boolean().default(true),
    versionControl: z.boolean().default(true),
    collaboration: z.boolean().default(false),
    cloudSync: z.boolean().default(false),
  }),
});

export type Config = z.infer<typeof ConfigSchema>;

// 載入和驗證配置
export function loadConfig(): Config {
  const config = {
    app: {
      name: process.env.APP_NAME || "Life Note Client Vibe",
      version: process.env.APP_VERSION || "1.0.0",
      port: parseInt(process.env.APP_PORT || "3000"),
      nodeEnv:
        (process.env.NODE_ENV as "development" | "production" | "test") ||
        "development",
    },
    database: {
      url: process.env.DATABASE_URL || "file:./data/life-note.db",
      poolSize: parseInt(process.env.DATABASE_POOL_SIZE || "10"),
      timeout: parseInt(process.env.DATABASE_TIMEOUT || "30000"),
    },
    ai: {
      gemini: {
        apiKey: process.env.VITE_GEMINI_API_KEY || "",
        model: process.env.VITE_GEMINI_MODEL || "gemini-2.5-flash",
        maxTokens: parseInt(process.env.VITE_GEMINI_MAX_TOKENS || "2048"),
        temperature: parseFloat(process.env.VITE_GEMINI_TEMPERATURE || "0.7"),
        topP: parseFloat(process.env.VITE_GEMINI_TOP_P || "0.8"),
        topK: parseInt(process.env.VITE_GEMINI_TOP_K || "40"),
      },
      openai: {
        apiKey: process.env.VITE_OPENAI_API_KEY,
        model: process.env.VITE_OPENAI_MODEL || "gpt-4",
        maxTokens: parseInt(process.env.VITE_OPENAI_MAX_TOKENS || "2048"),
        temperature: parseFloat(process.env.VITE_OPENAI_TEMPERATURE || "0.7"),
      },
      anthropic: {
        apiKey: process.env.VITE_ANTHROPIC_API_KEY,
        model: process.env.VITE_ANTHROPIC_MODEL || "claude-3-sonnet-********",
        maxTokens: parseInt(process.env.VITE_ANTHROPIC_MAX_TOKENS || "2048"),
        temperature: parseFloat(
          process.env.VITE_ANTHROPIC_TEMPERATURE || "0.7",
        ),
      },
      routing: {
        defaultProvider:
          (process.env.VITE_AI_DEFAULT_PROVIDER as
            | "gemini"
            | "openai"
            | "anthropic") || "gemini",
        fallbackProvider:
          (process.env.VITE_AI_FALLBACK_PROVIDER as
            | "gemini"
            | "openai"
            | "anthropic") || "openai",
        complexityThreshold: parseFloat(
          process.env.VITE_AI_COMPLEXITY_THRESHOLD || "0.7",
        ),
        sensitivityThreshold: parseFloat(
          process.env.VITE_AI_SENSITIVITY_THRESHOLD || "0.8",
        ),
      },
    },
    mcp: {
      port: parseInt(process.env.MCP_SERVER_PORT || "3001"),
      host: process.env.MCP_SERVER_HOST || "localhost",
      enableAuth: process.env.MCP_ENABLE_AUTH === "true",
      sessionTimeout: parseInt(process.env.MCP_SESSION_TIMEOUT || "3600000"),
      maxConnections: parseInt(process.env.MCP_MAX_CONNECTIONS || "100"),
    },
    localAgent: {
      port: parseInt(process.env.LOCAL_AGENT_PORT || "3002"),
      maxWorkers: parseInt(process.env.LOCAL_AGENT_MAX_WORKERS || "4"),
      timeout: parseInt(process.env.LOCAL_AGENT_TIMEOUT || "30000"),
    },
    security: {
      jwtSecret:
        process.env.JWT_SECRET || "default-jwt-secret-change-in-production",
      encryptionKey:
        process.env.ENCRYPTION_KEY ||
        "default-encryption-key-change-in-production",
      corsOrigin: process.env.CORS_ORIGIN?.split(",") || [
        "http://localhost:3000",
      ],
    },
    storage: {
      path: process.env.STORAGE_PATH || "./data/notes",
      maxFileSize: parseInt(process.env.STORAGE_MAX_FILE_SIZE || "10485760"),
      allowedExtensions: process.env.STORAGE_ALLOWED_EXTENSIONS?.split(",") || [
        ".md",
        ".txt",
        ".json",
        ".yaml",
        ".yml",
      ],
    },
    logging: {
      level:
        (process.env.LOG_LEVEL as "error" | "warn" | "info" | "debug") ||
        "info",
      file: process.env.LOG_FILE || "./logs/app.log",
      maxSize: parseInt(process.env.LOG_MAX_SIZE || "10485760"),
      maxFiles: parseInt(process.env.LOG_MAX_FILES || "5"),
    },
    features: {
      aiAssistant: process.env.FEATURE_AI_ASSISTANT !== "false",
      dependencyAnalysis: process.env.FEATURE_DEPENDENCY_ANALYSIS !== "false",
      versionControl: process.env.FEATURE_VERSION_CONTROL !== "false",
      collaboration: process.env.FEATURE_COLLABORATION === "true",
      cloudSync: process.env.FEATURE_CLOUD_SYNC === "true",
    },
  };

  return ConfigSchema.parse(config);
}

// 配置驗證函數
export function validateConfig(config: unknown): config is Config {
  try {
    ConfigSchema.parse(config);
    return true;
  } catch {
    return false;
  }
}

// 獲取特定配置段
export function getAIConfig() {
  return loadConfig().ai;
}

export function getDatabaseConfig() {
  return loadConfig().database;
}

export function getMCPConfig() {
  return loadConfig().mcp;
}

export function getSecurityConfig() {
  return loadConfig().security;
}
