import { prismaClient } from "./prisma/client.js";

/**
 * 數據庫種子數據
 * 用於初始化開發和測試環境
 */
async function seed() {
  console.log("🌱 開始播種數據庫...");

  try {
    // 連接數據庫
    await prismaClient.connect();

    // 創建測試用戶
    const testUser = await prismaClient.client.user.create({
      data: {
        id: "user-1",
        username: "testuser",
        email: "<EMAIL>",
        displayName: "測試用戶",
        role: "user",
        status: "active",
        metadata: JSON.stringify({
          preferences: {
            theme: "light",
            language: "zh-TW",
          },
        }),
      },
    });

    console.log("✅ 創建測試用戶:", testUser.username);

    // 創建測試工作空間
    const testWorkspace = await prismaClient.client.workspace.create({
      data: {
        id: "workspace-1",
        name: "我的工作空間",
        description: "這是一個測試工作空間",
        isPublic: false,
        ownerId: testUser.id,
        settings: JSON.stringify({
          allowGuestAccess: false,
          defaultNoteVisibility: "private",
        }),
        statistics: JSON.stringify({
          noteCount: 0,
          memberCount: 1,
        }),
      },
    });

    console.log("✅ 創建測試工作空間:", testWorkspace.name);

    // 添加工作空間成員
    await prismaClient.client.workspaceMember.create({
      data: {
        workspaceId: testWorkspace.id,
        userId: testUser.id,
        role: "owner",
        permissions: JSON.stringify(["read", "write", "admin"]),
      },
    });

    console.log("✅ 添加工作空間成員");

    // 創建測試筆記
    const testNotes = [
      {
        id: "note-1",
        title: "歡迎使用 Life Note",
        content: `# 歡迎使用 Life Note

這是您的第一篇筆記！Life Note 是一個智能筆記管理系統，具有以下特色：

## 主要功能

- 📝 Markdown 編輯器
- 🔗 依賴關係追蹤
- 🤖 AI 助手整合
- 📊 知識圖譜可視化
- 🏷️ 智能標籤系統

## 開始使用

1. 創建新筆記
2. 使用 Markdown 語法編寫內容
3. 添加標籤和分類
4. 建立筆記間的依賴關係

祝您使用愉快！`,
        category: "指南",
        tags: JSON.stringify(["歡迎", "指南", "入門"]),
        status: "published",
        priority: "medium",
        version: "1.0.0",
        authorId: testUser.id,
        workspaceId: testWorkspace.id,
        metadata: JSON.stringify({
          wordCount: 150,
          readingTime: 1,
        }),
      },
      {
        id: "note-2",
        title: "Markdown 語法指南",
        content: `# Markdown 語法指南

## 標題
使用 # 來創建標題，# 的數量表示標題級別。

## 文字格式
- **粗體文字**
- *斜體文字*
- ~~刪除線~~
- \`行內代碼\`

## 列表
### 無序列表
- 項目 1
- 項目 2
- 項目 3

### 有序列表
1. 第一項
2. 第二項
3. 第三項

## 連結和圖片
[連結文字](https://example.com)
![圖片描述](image-url)

## 代碼塊
\`\`\`javascript
function hello() {
  console.log("Hello, World!");
}
\`\`\`

## 表格
| 欄位1 | 欄位2 | 欄位3 |
|-------|-------|-------|
| 內容1 | 內容2 | 內容3 |`,
        category: "指南",
        tags: JSON.stringify(["Markdown", "語法", "教學"]),
        status: "published",
        priority: "high",
        version: "1.0.0",
        authorId: testUser.id,
        workspaceId: testWorkspace.id,
        metadata: JSON.stringify({
          wordCount: 200,
          readingTime: 2,
        }),
      },
      {
        id: "note-3",
        title: "我的待辦事項",
        content: `# 今日待辦

## 工作
- [ ] 完成專案報告
- [ ] 參加團隊會議
- [x] 回覆客戶郵件

## 個人
- [ ] 運動 30 分鐘
- [ ] 閱讀技術文章
- [ ] 準備晚餐

## 學習
- [ ] 學習新的程式語言
- [ ] 觀看線上課程
- [x] 整理筆記`,
        category: "待辦",
        tags: JSON.stringify(["待辦", "任務", "計劃"]),
        status: "draft",
        priority: "high",
        version: "1.0.0",
        authorId: testUser.id,
        workspaceId: testWorkspace.id,
        metadata: JSON.stringify({
          wordCount: 80,
          readingTime: 1,
        }),
      },
    ];

    for (const noteData of testNotes) {
      const note = await prismaClient.client.note.create({
        data: noteData,
      });
      console.log("✅ 創建測試筆記:", note.title);

      // 創建搜索索引
      const tags = JSON.parse(noteData.tags) as string[];
      await prismaClient.client.searchIndex.create({
        data: {
          noteId: note.id,
          title: note.title,
          content: note.content,
          tags: tags.join(" "),
          category: note.category,
        },
      });
    }

    // 創建依賴關係
    await prismaClient.client.dependency.create({
      data: {
        id: "dep-1",
        sourceNoteId: "note-1",
        targetNoteId: "note-2",
        type: "reference",
        strength: "medium",
        description: "歡迎筆記引用了 Markdown 指南",
        metadata: JSON.stringify({
          createdBy: testUser.id,
          reason: "reference",
        }),
      },
    });

    console.log("✅ 創建依賴關係");

    // 創建分類
    const categories = [
      { name: "指南", description: "使用指南和教學", color: "#3B82F6" },
      { name: "待辦", description: "待辦事項和任務", color: "#EF4444" },
      { name: "筆記", description: "一般筆記", color: "#10B981" },
      { name: "想法", description: "靈感和想法", color: "#F59E0B" },
    ];

    for (const categoryData of categories) {
      const category = await prismaClient.client.category.create({
        data: {
          id: `cat-${categoryData.name}`,
          name: categoryData.name,
          description: categoryData.description,
          color: categoryData.color,
          order: 0,
        },
      });
      console.log("✅ 創建分類:", category.name);
    }

    // 創建標籤
    const tags = [
      { name: "歡迎", color: "#3B82F6" },
      { name: "指南", color: "#10B981" },
      { name: "入門", color: "#F59E0B" },
      { name: "Markdown", color: "#8B5CF6" },
      { name: "語法", color: "#EC4899" },
      { name: "教學", color: "#06B6D4" },
      { name: "待辦", color: "#EF4444" },
      { name: "任務", color: "#F97316" },
      { name: "計劃", color: "#84CC16" },
    ];

    for (const tagData of tags) {
      const tag = await prismaClient.client.tag.create({
        data: {
          id: `tag-${tagData.name}`,
          name: tagData.name,
          color: tagData.color,
          usage_count: 1,
        },
      });
      console.log("✅ 創建標籤:", tag.name);
    }

    // 創建用戶偏好設置
    await prismaClient.client.userPreference.create({
      data: {
        userId: testUser.id,
        theme: "auto",
        language: "zh-TW",
        timezone: "Asia/Taipei",
        editorSettings: JSON.stringify({
          fontSize: 14,
          lineHeight: 1.6,
          wordWrap: true,
          showLineNumbers: true,
        }),
        notificationSettings: JSON.stringify({
          emailNotifications: true,
          pushNotifications: false,
          weeklyDigest: true,
        }),
        privacySettings: JSON.stringify({
          profileVisibility: "private",
          activityVisibility: "friends",
        }),
      },
    });

    console.log("✅ 創建用戶偏好設置");

    // 創建用戶統計
    await prismaClient.client.userStatistics.create({
      data: {
        userId: testUser.id,
        totalNotes: 3,
        publishedNotes: 2,
        draftNotes: 1,
        archivedNotes: 0,
        totalWords: 430,
        averageWordsPerNote: 143.33,
        notesCreatedThisWeek: 3,
        notesCreatedThisMonth: 3,
        longestStreak: 1,
        currentStreak: 1,
        favoriteCategories: JSON.stringify(["指南", "待辦"]),
        mostUsedTags: JSON.stringify(["指南", "教學", "待辦"]),
      },
    });

    console.log("✅ 創建用戶統計");

    console.log("🎉 數據庫播種完成！");

    // 顯示統計信息
    const stats = await prismaClient.getDatabaseStats();
    console.log("📊 數據庫統計:", stats);
  } catch (error) {
    console.error("❌ 播種失敗:", error);
    throw error;
  } finally {
    await prismaClient.disconnect();
  }
}

// 執行播種
if (import.meta.url === `file://${process.argv[1]}`) {
  seed()
    .then(() => {
      console.log("✅ 播種腳本執行完成");
      process.exit(0);
    })
    .catch((error) => {
      console.error("❌ 播種腳本執行失敗:", error);
      process.exit(1);
    });
}

export { seed };
