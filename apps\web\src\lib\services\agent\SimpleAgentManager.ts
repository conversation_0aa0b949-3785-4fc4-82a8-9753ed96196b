import { GeminiAgent } from './GeminiAgent';
import type {
	AgentTaskRequest,
	AgentTaskResult,
	AgentTaskType,
	AgentSystemStatus,
	AgentEvent,
	AgentEventListener
} from '$lib/types/agent';

/**
 * 簡化的 Agent 管理器
 * 專門管理 Gemini Agent 和 A2A 通信
 */
export class SimpleAgentManager {
	private primaryAgent: GeminiAgent | null = null;
	private remoteAgents = new Map<string, any>(); // 遠程 Agent 連接
	private taskQueue: AgentTaskRequest[] = [];
	private runningTasks = new Map<string, AgentTaskRequest>();
	private eventListeners = new Map<string, AgentEventListener[]>();
	private mcpConnections = new Map<string, any>(); // MCP 連接
	private systemStartTime = Date.now();

	constructor() {
		this.startQueueProcessor();
	}

	/**
	 * 初始化主要的 Gemini Agent
	 */
	async initializePrimaryAgent(apiKey: string): Promise<void> {
		if (this.primaryAgent) {
			await this.primaryAgent.stop();
		}

		const config = {
			id: 'primary-gemini-agent',
			name: 'Primary Gemini Agent',
			description: 'Main Gemini 2.5 Flash agent for all AI tasks',
			capabilities: [
				'note_analysis',
				'content_generation',
				'summarization',
				'translation',
				'question_answering',
				'classification',
				'extraction',
				'dependency_check',
				'search_query',
				'recommendation'
			] as AgentTaskType[],
			capabilityLevel: 'expert' as const,
			executionMode: 'local' as const,
			maxConcurrentTasks: 5,
			defaultTimeout: 30000,
			enabled: true
		};

		this.primaryAgent = new GeminiAgent(config, apiKey);
		await this.primaryAgent.initialize();

		this.emitEvent('agent_registered', { agentId: config.id });
		console.log('Primary Gemini Agent initialized successfully');
	}

	/**
	 * 設置 MCP 連接
	 */
	async setupMcpConnection(name: string, mcpClient: any): Promise<void> {
		this.mcpConnections.set(name, mcpClient);

		// 將 MCP 客戶端設置到主要 Agent
		if (this.primaryAgent) {
			this.primaryAgent.setMcpClient(mcpClient);
		}

		console.log(`MCP connection '${name}' established`);
	}

	/**
	 * 註冊遠程 Agent（用於 A2A 通信）
	 */
	async registerRemoteAgent(agentId: string, connection: any): Promise<void> {
		this.remoteAgents.set(agentId, connection);
		this.emitEvent('agent_registered', { agentId });
		console.log(`Remote agent '${agentId}' registered for A2A communication`);
	}

	/**
	 * 取消註冊遠程 Agent
	 */
	async unregisterRemoteAgent(agentId: string): Promise<void> {
		const connection = this.remoteAgents.get(agentId);
		if (connection) {
			// 清理連接
			if (typeof connection.close === 'function') {
				await connection.close();
			}
			this.remoteAgents.delete(agentId);
			this.emitEvent('agent_unregistered', { agentId });
			console.log(`Remote agent '${agentId}' unregistered`);
		}
	}

	/**
	 * 執行任務
	 */
	async executeTask(request: AgentTaskRequest): Promise<AgentTaskResult> {
		if (!this.primaryAgent) {
			throw new Error('Primary agent not initialized. Please set API key first.');
		}

		// 檢查是否需要 A2A 通信
		const remoteResult = await this.tryRemoteExecution(request);
		if (remoteResult) {
			return remoteResult;
		}

		// 使用主要 Agent 執行
		if (this.primaryAgent.getCurrentLoad() >= 1.0) {
			// 如果 Agent 忙碌，加入隊列
			this.taskQueue.push(request);
			this.sortTaskQueue();

			return new Promise((resolve, reject) => {
				const timeout = request.timeout || 30000;
				const timeoutId = setTimeout(() => {
					const queueIndex = this.taskQueue.findIndex(task => task.id === request.id);
					if (queueIndex > -1) {
						this.taskQueue.splice(queueIndex, 1);
					}
					reject(new Error('Task execution timeout'));
				}, timeout);

				// 監聽任務完成
				const checkCompletion = () => {
					if (!this.runningTasks.has(request.id)) {
						setTimeout(checkCompletion, 100);
						return;
					}

					clearTimeout(timeoutId);
					// 這裡需要實現任務完成監聽機制
				};

				checkCompletion();
			});
		}

		// 直接執行任務
		this.runningTasks.set(request.id, request);
		this.emitEvent('task_started', { taskId: request.id, type: request.type });

		try {
			const result = await this.primaryAgent.executeTask(request);
			this.runningTasks.delete(request.id);
			this.emitEvent('task_completed', { taskId: request.id, result });
			return result;
		} catch (error) {
			this.runningTasks.delete(request.id);
			this.emitEvent('task_failed', { taskId: request.id, error });
			throw error;
		}
	}

	/**
	 * 嘗試遠程執行（A2A 通信）
	 */
	private async tryRemoteExecution(request: AgentTaskRequest): Promise<AgentTaskResult | null> {
		// 檢查是否有適合的遠程 Agent
		const suitableAgent = this.findSuitableRemoteAgent(request);
		if (!suitableAgent) {
			return null;
		}

		try {
			console.log(`Delegating task ${request.id} to remote agent ${suitableAgent.id}`);

			// 通過 A2A 協議發送任務
			const result = await this.sendTaskToRemoteAgent(suitableAgent, request);

			return {
				id: this.generateResultId(),
				taskId: request.id,
				status: 'completed',
				output: result,
				executionTime: 0, // 遠程執行時間由遠程 Agent 計算
				completedAt: new Date(),
				agentId: suitableAgent.id,
				metadata: { executionMode: 'remote', remoteAgent: suitableAgent.id }
			};
		} catch (error) {
			console.warn(`Remote execution failed for task ${request.id}:`, error);
			return null;
		}
	}

	/**
	 * 尋找適合的遠程 Agent
	 */
	private findSuitableRemoteAgent(request: AgentTaskRequest): any {
		// 根據任務類型和 Agent 能力匹配
		for (const [agentId, connection] of this.remoteAgents) {
			if (connection.capabilities?.includes(request.type)) {
				return { id: agentId, connection };
			}
		}
		return null;
	}

	/**
	 * 發送任務到遠程 Agent
	 */
	private async sendTaskToRemoteAgent(agent: any, request: AgentTaskRequest): Promise<any> {
		// 實現 A2A 通信協議
		const message = {
			type: 'task_request',
			taskId: request.id,
			taskType: request.type,
			input: request.input,
			options: request.options,
			timestamp: new Date().toISOString()
		};

		// 發送消息並等待回應
		const response = await agent.connection.sendMessage(message);

		if (response.type === 'task_result') {
			return response.output;
		} else if (response.type === 'task_error') {
			throw new Error(response.error);
		} else {
			throw new Error('Invalid response from remote agent');
		}
	}

	/**
	 * 獲取系統狀態
	 */
	getSystemStatus(): AgentSystemStatus {
		const totalAgents = 1 + this.remoteAgents.size; // 主要 Agent + 遠程 Agent
		const activeAgents = (this.primaryAgent?.status !== 'offline' ? 1 : 0) + this.remoteAgents.size;
		const totalTasks = this.primaryAgent?.metrics.totalTasks || 0;
		const runningTasks = this.runningTasks.size;
		const queuedTasks = this.taskQueue.length;
		const systemLoad = this.primaryAgent?.getCurrentLoad() || 0;

		return {
			totalAgents,
			activeAgents,
			totalTasks,
			runningTasks,
			queuedTasks,
			systemLoad,
			uptime: Date.now() - this.systemStartTime
		};
	}

	/**
	 * 更新 API Key
	 */
	async updateApiKey(apiKey: string): Promise<void> {
		if (this.primaryAgent) {
			this.primaryAgent.setApiKey(apiKey);
		} else {
			await this.initializePrimaryAgent(apiKey);
		}
	}

	/**
	 * 檢查 Agent 是否就緒
	 */
	isReady(): boolean {
		return this.primaryAgent !== null && this.primaryAgent.status === 'idle';
	}

	/**
	 * 獲取可用的任務類型
	 */
	getAvailableTaskTypes(): AgentTaskType[] {
		if (!this.primaryAgent) {
			return [];
		}
		return this.primaryAgent.config.capabilities;
	}

	/**
	 * 添加事件監聽器
	 */
	addEventListener(eventType: string, listener: AgentEventListener): void {
		if (!this.eventListeners.has(eventType)) {
			this.eventListeners.set(eventType, []);
		}
		this.eventListeners.get(eventType)!.push(listener);
	}

	/**
	 * 移除事件監聽器
	 */
	removeEventListener(eventType: string, listener: AgentEventListener): void {
		const listeners = this.eventListeners.get(eventType);
		if (listeners) {
			const index = listeners.indexOf(listener);
			if (index > -1) {
				listeners.splice(index, 1);
			}
		}
	}

	/**
	 * 停止所有 Agent
	 */
	async shutdown(): Promise<void> {
		if (this.primaryAgent) {
			await this.primaryAgent.stop();
			this.primaryAgent = null;
		}

		// 關閉所有遠程連接
		for (const [agentId, connection] of this.remoteAgents) {
			await this.unregisterRemoteAgent(agentId);
		}

		// 關閉 MCP 連接
		for (const [name, mcpClient] of this.mcpConnections) {
			if (typeof mcpClient.close === 'function') {
				await mcpClient.close();
			}
		}
		this.mcpConnections.clear();

		this.taskQueue = [];
		this.runningTasks.clear();
		this.eventListeners.clear();

		console.log('Agent manager shutdown completed');
	}

	// 私有方法

	/**
	 * 啟動任務隊列處理器
	 */
	private startQueueProcessor(): void {
		setInterval(async () => {
			if (
				this.taskQueue.length > 0 &&
				this.primaryAgent &&
				this.primaryAgent.getCurrentLoad() < 1.0
			) {
				const task = this.taskQueue.shift();
				if (task) {
					try {
						await this.executeTask(task);
					} catch (error) {
						console.error(`Queued task ${task.id} failed:`, error);
					}
				}
			}
		}, 1000);
	}

	/**
	 * 排序任務隊列
	 */
	private sortTaskQueue(): void {
		const priorityOrder = { urgent: 4, high: 3, normal: 2, low: 1 };

		this.taskQueue.sort((a, b) => {
			const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
			if (priorityDiff !== 0) {
				return priorityDiff;
			}
			return a.createdAt.getTime() - b.createdAt.getTime();
		});
	}

	/**
	 * 生成結果 ID
	 */
	private generateResultId(): string {
		return `result-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
	}

	/**
	 * 發送事件
	 */
	private emitEvent(type: string, data?: any): void {
		const event: AgentEvent = {
			type: type as any,
			data,
			timestamp: new Date()
		};

		const listeners = this.eventListeners.get(type);
		if (listeners) {
			listeners.forEach(listener => {
				try {
					listener(event);
				} catch (error) {
					console.error(`Error in event listener for ${type}:`, error);
				}
			});
		}
	}
}

// 導出單例實例
export const simpleAgentManager = new SimpleAgentManager();
