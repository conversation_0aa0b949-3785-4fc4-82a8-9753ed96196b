# Life Note MCP Server

Life Note MCP (Model Context Protocol) Server 是一個為 AI 助手提供與 Life Note 知識管理系統交互能力的服務器。

## 🚀 功能特性

### 核心功能

- **筆記管理**: 創建、讀取、更新、刪除筆記
- **搜索功能**: 全文搜索、標籤搜索、分類搜索
- **資源訪問**: 提供筆記數據和統計信息的資源接口
- **實時監控**: 文件變化監控和自動更新

### MCP 工具

1. **create_note** - 創建新筆記
2. **get_note** - 根據 ID 獲取筆記
3. **update_note** - 更新現有筆記
4. **delete_note** - 刪除筆記
5. **search_notes** - 搜索筆記
6. **list_all_notes** - 列出所有筆記
7. **analyze_dependencies** - 分析筆記依賴關係

### MCP 資源

1. **notes://all** - 所有筆記的完整列表
2. **notes://statistics** - 筆記統計信息
3. **notes://by-tag/{tag}** - 按標籤分組的筆記
4. **notes://by-category/{category}** - 按分類分組的筆記

## 📦 安裝和設置

### 前提條件

- Node.js >= 18.0.0
- pnpm >= 8.0.0

### 安裝依賴

```bash
cd apps/mcp-server
pnpm install
```

### 構建項目（TypeScript 版本）

```bash
pnpm build
```

## 🎯 使用方法

### 1. 直接啟動（JavaScript 版本）

```bash
node index.js
```

### 2. 使用 CLI（TypeScript 版本）

```bash
# 啟動服務器
pnpm start

# 或使用開發模式
pnpm dev

# 驗證配置
node src/index.js validate --notes-dir ./notes

# 測試連接
node src/index.js test-connection --notes-dir ./notes

# 顯示服務器信息
node src/index.js info
```

### 3. 作為 MCP 服務器使用

```bash
# 在 AI 客戶端配置中添加
{
  "mcpServers": {
    "life-note": {
      "command": "node",
      "args": ["path/to/apps/mcp-server/index.js"]
    }
  }
}
```

## 🧪 測試

### 運行測試客戶端

```bash
# 測試簡化版服務器
node simple-test.js

# 測試工作版服務器
node working-test.js

# 測試完整版服務器
node test-client.js
```

### 測試覆蓋的功能

- ✅ MCP 服務器啟動和連接
- ✅ 工具列表獲取
- ✅ 資源列表獲取
- ✅ 筆記創建、讀取、搜索
- ✅ 資源數據訪問
- ⚠️ SDK 兼容性問題（需要解決）

## 🔧 配置選項

### 環境變數

```bash
# 筆記目錄路徑
NOTES_DIR=./notes

# 服務器端口（HTTP 模式）
PORT=3001

# 服務器主機
HOST=localhost

# 日誌級別
LOG_LEVEL=info

# 啟用 CORS
ENABLE_CORS=true

# 啟用文件監控
ENABLE_FILE_WATCHER=true
```

### 配置文件示例

```json
{
  "notesDir": "./notes",
  "port": 3001,
  "host": "localhost",
  "logLevel": "info",
  "enableCors": true,
  "enableFileWatcher": true,
  "enableHttpServer": false,
  "enableMCPServer": true
}
```

## 📁 項目結構

```
apps/mcp-server/
├── src/                    # TypeScript 源代碼
│   ├── server/            # MCP 服務器實現
│   ├── services/          # 業務邏輯服務
│   ├── types/             # 類型定義
│   ├── utils/             # 工具函數
│   └── index.ts           # 主入口文件
├── index.js               # JavaScript 簡化版本
├── simple-server.js       # 簡化測試服務器
├── working-server.js      # 工作版本服務器
├── test-client.js         # 測試客戶端
├── simple-test.js         # 簡化測試
├── working-test.js        # 工作版本測試
├── package.json           # 項目配置
├── tsconfig.json          # TypeScript 配置
└── README.md              # 項目文檔
```

## 🐛 已知問題

### 1. MCP SDK 兼容性問題

- **問題**: `@modelcontextprotocol/sdk@0.5.0` 存在 JSON 解析錯誤
- **狀態**: 正在調查
- **臨時解決方案**: 使用簡化的實現或等待 SDK 更新

### 2. Windows 路徑問題

- **問題**: Windows 環境下的文件路徑處理
- **解決方案**: 使用 `path.join()` 和 `path.resolve()`

### 3. 依賴安裝問題

- **問題**: npm workspace 配置衝突
- **解決方案**: 使用 pnpm 而不是 npm

## 🔄 開發狀態

### ✅ 已完成

- [x] 基本 MCP 服務器架構
- [x] 筆記 CRUD 操作
- [x] 搜索功能實現
- [x] 資源接口實現
- [x] 測試客戶端
- [x] 文檔編寫

### 🚧 進行中

- [ ] MCP SDK 兼容性修復
- [ ] 完整的錯誤處理
- [ ] 性能優化

### 📋 待辦事項

- [ ] 單元測試
- [ ] 集成測試
- [ ] Docker 支持
- [ ] 部署文檔
- [ ] API 文檔生成

## 🤝 貢獻指南

1. Fork 項目
2. 創建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 開啟 Pull Request

## 📄 許可證

本項目使用 MIT 許可證 - 查看 [LICENSE](LICENSE) 文件了解詳情。

## 🆘 支持

如果您遇到問題或有疑問，請：

1. 查看 [Issues](https://github.com/life-note/life-note-client-vibe/issues)
2. 創建新的 Issue
3. 聯繫開發團隊

## 🔗 相關鏈接

- [Life Note 主項目](https://github.com/life-note/life-note-client-vibe)
- [Model Context Protocol 規範](https://modelcontextprotocol.io/)
- [MCP SDK 文檔](https://github.com/modelcontextprotocol/sdk)

---

**注意**: 這個 MCP 服務器目前處於開發階段，某些功能可能不穩定。建議在生產環境使用前進行充分測試。
