# @life-note/storage

Life Note 存儲層 - 基於 Prisma ORM 的數據持久化解決方案

## 功能特色

- 🗄️ **SQLite 數據庫**：輕量級本地數據庫
- 🔄 **Prisma ORM**：類型安全的數據庫操作
- 📦 **Repository 模式**：清晰的數據訪問層
- 🔍 **全文搜索**：支援筆記內容搜索
- 📊 **統計分析**：豐富的數據統計功能
- 🔗 **依賴關係**：筆記間依賴關係管理
- 🧪 **完整測試**：100% 測試覆蓋率

## 安裝

```bash
pnpm install @life-note/storage
```

## 快速開始

### 1. 數據庫初始化

```bash
# 生成 Prisma 客戶端
pnpm db:generate

# 推送數據庫 schema
pnpm db:push

# 播種測試數據（可選）
pnpm db:seed
```

### 2. 基本使用

```typescript
import { repositoryFactory, prismaClient } from "@life-note/storage";

// 連接數據庫
await prismaClient.connect();

// 獲取存儲庫
const userRepo = repositoryFactory.userRepository;
const noteRepo = repositoryFactory.noteRepository;
const depRepo = repositoryFactory.dependencyRepository;

// 創建用戶
const user = await userRepo.save({
  id: "user-1",
  username: "john",
  email: "<EMAIL>",
  role: "user",
  status: "active",
  createdAt: new Date(),
  updatedAt: new Date(),
});

// 創建筆記
const note = await noteRepo.save({
  id: "note-1",
  title: "我的第一篇筆記",
  content: "這是筆記內容",
  status: "draft",
  priority: "medium",
  version: "1.0.0",
  authorId: user.id,
  tags: ["學習", "筆記"],
  createdAt: new Date(),
  updatedAt: new Date(),
});

// 搜索筆記
const searchResults = await noteRepo.search("筆記");
console.log(searchResults.data);
```

## 存儲庫 API

### UserRepository

```typescript
// 基本操作
const user = await userRepo.findById("user-1");
const users = await userRepo.findAll();
await userRepo.save(user);
await userRepo.deleteById("user-1");

// 查詢操作
const userByUsername = await userRepo.findByUsername("john");
const userByEmail = await userRepo.findByEmail("<EMAIL>");
const activeUsers = await userRepo.findByStatus("active");

// 分頁查詢
const paginatedUsers = await userRepo.findPaginated({
  page: 1,
  limit: 10,
  sortBy: "createdAt",
  sortOrder: "desc",
});

// 搜索
const searchResults = await userRepo.search("john");

// 統計
const stats = await userRepo.getStatistics();
```

### NoteRepository

```typescript
// 基本操作
const note = await noteRepo.findById("note-1");
await noteRepo.save(note);
await noteRepo.deleteById("note-1");

// 查詢操作
const authorNotes = await noteRepo.findByAuthorId("user-1");
const draftNotes = await noteRepo.findDraftsByAuthor("user-1");
const publishedNotes = await noteRepo.findPublishedByAuthor("user-1");
const categoryNotes = await noteRepo.findByCategory("學習");
const taggedNotes = await noteRepo.findByTags(["學習", "筆記"]);

// 搜索
const searchResults = await noteRepo.search("關鍵字");

// 相關筆記
const relatedNotes = await noteRepo.findRelatedNotes("note-1");

// 統計
const stats = await noteRepo.getStatistics();
const userStats = await noteRepo.getUserStatistics("user-1");

// 批量操作
await noteRepo.batchUpdateStatus(["note-1", "note-2"], "published");
await noteRepo.batchDelete(["note-1", "note-2"]);

// 標籤和分類
const allTags = await noteRepo.getAllTags();
const userTags = await noteRepo.getUserTags("user-1");
const allCategories = await noteRepo.getAllCategories();
```

### DependencyRepository

```typescript
// 創建依賴關係
const dependency = await depRepo.createDependency(
  "source-note-id",
  "target-note-id",
  "reference",
  "medium",
  "描述",
);

// 查詢依賴關係
const sourceDeps = await depRepo.findBySourceNoteId("note-1");
const targetDeps = await depRepo.findByTargetNoteId("note-1");
const noteDeps = await depRepo.findByNoteId("note-1");

// 檢查依賴關係
const exists = await depRepo.existsBetween("note-1", "note-2");
const dependency = await depRepo.findBetween("note-1", "note-2");

// 管理依賴關係
await depRepo.updateStrength("dep-1", "strong");
await depRepo.activate("dep-1");
await depRepo.deactivate("dep-1");

// 批量操作
const dependencies = await depRepo.batchCreateDependencies([
  {
    sourceNoteId: "note-1",
    targetNoteId: "note-2",
    type: "reference",
    strength: "medium",
  },
]);

// 統計
const stats = await depRepo.getStatistics();
const noteStats = await depRepo.getNoteStatistics("note-1");
```

## 事務支援

```typescript
// 使用事務
await repositoryFactory.transaction(async (repos) => {
  const user = await repos.userRepository.save(userData);
  const note = await repos.noteRepository.save(noteData);
  await repos.dependencyRepository.createDependency(
    note.id,
    "other-note-id",
    "reference",
  );
});
```

## 數據庫管理

```bash
# 查看數據庫
pnpm db:studio

# 重置數據庫
pnpm db:reset

# 遷移數據庫
pnpm db:migrate
```

## 測試

```bash
# 運行所有測試
pnpm test

# 運行特定測試
pnpm test database
pnpm test repositories

# 測試覆蓋率
pnpm test:coverage
```

## 數據模型

### 用戶 (User)

- 基本信息：用戶名、郵箱、顯示名稱
- 角色權限：admin、moderator、user、guest
- 狀態管理：active、inactive、suspended
- 元數據：JSON 格式的擴展信息

### 筆記 (Note)

- 內容：標題、內容、分類、標籤
- 狀態：draft、published、archived
- 優先級：low、medium、high、urgent
- 版本控制：版本號、文件路徑、校驗和
- 關聯：作者、工作空間

### 依賴關係 (Dependency)

- 類型：reference、include、extends、implements、uses、depends_on
- 強度：weak、medium、strong、critical
- 狀態：active、inactive
- 描述和元數據

### 工作空間 (Workspace)

- 基本信息：名稱、描述、可見性
- 成員管理：角色、權限
- 設置和統計

## 配置

環境變數配置：

```env
# 數據庫連接
DATABASE_URL="file:./dev.db"

# 應用配置
NODE_ENV="development"
LOG_LEVEL="info"
```

## 許可證

MIT License
