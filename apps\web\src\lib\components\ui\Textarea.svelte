<script lang="ts">
	import { tv, type VariantProps } from 'tailwind-variants';
	import type { HTMLTextareaAttributes } from 'svelte/elements';

	const textareaVariants = tv({
		base: 'flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
		variants: {
			variant: {
				default: '',
				error: 'border-destructive focus-visible:ring-destructive',
				success: 'border-green-500 focus-visible:ring-green-500'
			},
			size: {
				sm: 'min-h-[60px] px-2 py-1 text-xs',
				default: 'min-h-[80px]',
				lg: 'min-h-[120px] px-4 py-3 text-base'
			},
			resize: {
				none: 'resize-none',
				vertical: 'resize-y',
				horizontal: 'resize-x',
				both: 'resize'
			}
		},
		defaultVariants: {
			variant: 'default',
			size: 'default',
			resize: 'vertical'
		}
	});

	type Variant = VariantProps<typeof textareaVariants>['variant'];
	type Size = VariantProps<typeof textareaVariants>['size'];
	type Resize = VariantProps<typeof textareaVariants>['resize'];

	interface $$Props extends HTMLTextareaAttributes {
		variant?: Variant;
		size?: Size;
		resize?: Resize;
		class?: string;
		error?: string;
		label?: string;
		hint?: string;
	}

	export let variant: Variant = 'default';
	export let size: Size = 'default';
	export let resize: Resize = 'vertical';
	export let error: string = '';
	export let label: string = '';
	export let hint: string = '';
	export let value: string = '';
	let className: string = '';
	export { className as class };

	// Auto-detect error variant
	$: computedVariant = error ? 'error' : variant;
	$: computedClass = textareaVariants({ variant: computedVariant, size, resize, class: className });

	// Generate unique ID for accessibility
	const id = `textarea-${Math.random().toString(36).substr(2, 9)}`;
</script>

<div class="space-y-2">
	{#if label}
		<label
			for={id}
			class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
		>
			{label}
		</label>
	{/if}

	<textarea
		{id}
		bind:value
		class={computedClass}
		on:input
		on:change
		on:focus
		on:blur
		on:keydown
		on:keyup
		on:keypress
		{...$$restProps}
	></textarea>

	{#if error}
		<p class="text-sm text-destructive">{error}</p>
	{:else if hint}
		<p class="text-sm text-muted-foreground">{hint}</p>
	{/if}
</div>
