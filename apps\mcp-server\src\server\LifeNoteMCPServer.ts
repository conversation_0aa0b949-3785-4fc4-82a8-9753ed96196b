import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import {
  CallToolRequestSchema,
  ErrorCode,
  ListResourcesRequestSchema,
  ListToolsRequestSchema,
  McpError,
  ReadResourceRequestSchema,
} from "@modelcontextprotocol/sdk/types.js";

import express from "express";
import cors from "cors";
import helmet from "helmet";
import compression from "compression";
import { createServer } from "http";

import { logger } from "../utils/logger.js";
import { ServerConfig } from "../types/config.js";
import { NotesService } from "../services/NotesService.js";
import { SearchService } from "../services/SearchService.js";
import { DependencyService } from "../services/DependencyService.js";
import { FileWatcherService } from "../services/FileWatcherService.js";
import { setupRoutes } from "../routes/index.js";
import { setupMCPHandlers } from "../handlers/mcpHandlers.js";

/**
 * Life Note MCP Server
 *
 * 提供 Model Context Protocol (MCP) 接口，讓 AI Agent 能夠訪問 Life Note 知識庫
 */
export class LifeNoteMCPServer {
  private server: Server;
  private expressApp: express.Application;
  private httpServer: any;
  private config: ServerConfig;

  // 服務實例
  private notesService: NotesService;
  private searchService: SearchService;
  private dependencyService: DependencyService;
  private fileWatcherService: FileWatcherService;

  constructor(config: ServerConfig) {
    this.config = config;

    // 初始化 MCP Server
    this.server = new Server(
      {
        name: "life-note-mcp-server",
        version: "0.1.0",
      },
      {
        capabilities: {
          resources: {},
          tools: {},
        },
      },
    );

    // 初始化 Express 應用
    this.expressApp = express();

    // 初始化服務
    this.initializeServices();

    // 設置中間件
    this.setupMiddleware();

    // 設置 MCP 處理器
    this.setupMCPHandlers();

    // 設置 REST API 路由
    this.setupRoutes();
  }

  /**
   * 初始化服務
   */
  private initializeServices(): void {
    this.notesService = new NotesService(this.config.notesDir);
    this.searchService = new SearchService(this.notesService);
    this.dependencyService = new DependencyService(this.notesService);
    this.fileWatcherService = new FileWatcherService(this.config.notesDir);
  }

  /**
   * 設置 Express 中間件
   */
  private setupMiddleware(): void {
    // 安全性中間件
    this.expressApp.use(helmet());

    // 壓縮中間件
    this.expressApp.use(compression());

    // CORS 中間件
    if (this.config.enableCors) {
      this.expressApp.use(
        cors({
          origin: this.config.corsOrigins || "*",
          credentials: true,
        }),
      );
    }

    // JSON 解析中間件
    this.expressApp.use(express.json({ limit: "10mb" }));
    this.expressApp.use(express.urlencoded({ extended: true, limit: "10mb" }));

    // 請求日誌中間件
    this.expressApp.use((req, res, next) => {
      logger.debug(`${req.method} ${req.path}`, {
        query: req.query,
        body: req.method !== "GET" ? req.body : undefined,
      });
      next();
    });
  }

  /**
   * 設置 MCP 處理器
   */
  private setupMCPHandlers(): void {
    setupMCPHandlers(this.server, {
      notesService: this.notesService,
      searchService: this.searchService,
      dependencyService: this.dependencyService,
      fileWatcherService: this.fileWatcherService,
    });
  }

  /**
   * 設置 REST API 路由
   */
  private setupRoutes(): void {
    setupRoutes(this.expressApp, {
      notesService: this.notesService,
      searchService: this.searchService,
      dependencyService: this.dependencyService,
      fileWatcherService: this.fileWatcherService,
    });
  }

  /**
   * 啟動服務器
   */
  async start(): Promise<void> {
    try {
      // 初始化服務
      await this.notesService.initialize();
      await this.searchService.initialize();
      await this.dependencyService.initialize();

      // 啟動文件監控
      if (this.config.enableFileWatcher) {
        await this.fileWatcherService.start();

        // 監聽文件變化事件
        this.fileWatcherService.on("noteChanged", (noteId: string) => {
          logger.debug(`Note changed: ${noteId}`);
          // 可以在這裡觸發重新索引或通知客戶端
        });

        this.fileWatcherService.on("noteCreated", (noteId: string) => {
          logger.debug(`Note created: ${noteId}`);
        });

        this.fileWatcherService.on("noteDeleted", (noteId: string) => {
          logger.debug(`Note deleted: ${noteId}`);
        });
      }

      // 啟動 HTTP 服務器（用於 REST API）
      if (this.config.enableHttpServer) {
        this.httpServer = createServer(this.expressApp);

        await new Promise<void>((resolve, reject) => {
          this.httpServer.listen(this.config.port, this.config.host, () => {
            logger.info(
              `HTTP server listening on ${this.config.host}:${this.config.port}`,
            );
            resolve();
          });

          this.httpServer.on("error", reject);
        });
      }

      // 啟動 MCP 服務器（通過 stdio）
      if (this.config.enableMCPServer) {
        const transport = new StdioServerTransport();
        await this.server.connect(transport);
        logger.info("MCP server connected via stdio");
      }

      logger.info("Life Note MCP Server started successfully");
    } catch (error) {
      logger.error("Failed to start server:", error);
      throw error;
    }
  }

  /**
   * 停止服務器
   */
  async stop(): Promise<void> {
    try {
      logger.info("Stopping Life Note MCP Server...");

      // 停止文件監控
      if (this.fileWatcherService) {
        await this.fileWatcherService.stop();
      }

      // 關閉 HTTP 服務器
      if (this.httpServer) {
        await new Promise<void>((resolve, reject) => {
          this.httpServer.close((error: any) => {
            if (error) {
              reject(error);
            } else {
              resolve();
            }
          });
        });
      }

      // 關閉 MCP 服務器
      if (this.server) {
        await this.server.close();
      }

      logger.info("Life Note MCP Server stopped successfully");
    } catch (error) {
      logger.error("Error stopping server:", error);
      throw error;
    }
  }

  /**
   * 獲取服務器狀態
   */
  getStatus(): {
    isRunning: boolean;
    config: ServerConfig;
    services: {
      notes: boolean;
      search: boolean;
      dependency: boolean;
      fileWatcher: boolean;
    };
    stats: {
      notesCount: number;
      uptime: number;
    };
  } {
    return {
      isRunning: !!this.httpServer?.listening,
      config: this.config,
      services: {
        notes: !!this.notesService,
        search: !!this.searchService,
        dependency: !!this.dependencyService,
        fileWatcher: this.fileWatcherService?.isRunning() || false,
      },
      stats: {
        notesCount: this.notesService?.getNotesCount() || 0,
        uptime: process.uptime(),
      },
    };
  }

  /**
   * 獲取服務實例（用於測試或外部訪問）
   */
  getServices() {
    return {
      notesService: this.notesService,
      searchService: this.searchService,
      dependencyService: this.dependencyService,
      fileWatcherService: this.fileWatcherService,
    };
  }

  /**
   * 重新載入配置
   */
  async reloadConfig(newConfig: Partial<ServerConfig>): Promise<void> {
    try {
      logger.info("Reloading server configuration...");

      // 更新配置
      this.config = { ...this.config, ...newConfig };

      // 重新初始化需要更新的服務
      if (newConfig.notesDir && newConfig.notesDir !== this.config.notesDir) {
        await this.notesService.changeNotesDirectory(newConfig.notesDir);
        await this.searchService.reindex();
        await this.dependencyService.reanalyze();
      }

      logger.info("Configuration reloaded successfully");
    } catch (error) {
      logger.error("Failed to reload configuration:", error);
      throw error;
    }
  }
}
