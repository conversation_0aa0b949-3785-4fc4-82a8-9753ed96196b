/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  AggregateRoot,
  BusinessRuleViolationError,
} from "../../shared/Entity.js";
import { User, UserRole, UserStatus } from "../entities/User.js";
import { Note } from "../entities/Note.js";
import { UserId } from "../value-objects/UserId.js";
import { NoteId } from "../value-objects/NoteId.js";
import { Email } from "../value-objects/Email.js";
import { Username } from "../value-objects/Username.js";
import { DisplayName } from "../value-objects/DisplayName.js";
import { Password } from "../value-objects/Password.js";

/**
 * 用戶偏好設置
 */
export interface UserPreferences {
  theme: "light" | "dark" | "auto";
  language: string;
  timezone: string;
  editorSettings: {
    fontSize: number;
    fontFamily: string;
    tabSize: number;
    wordWrap: boolean;
    showLineNumbers: boolean;
    enableVimMode: boolean;
  };
  notificationSettings: {
    emailNotifications: boolean;
    pushNotifications: boolean;
    dependencyUpdates: boolean;
    weeklyDigest: boolean;
  };
  privacySettings: {
    profileVisibility: "public" | "private" | "friends";
    showActivity: boolean;
    allowIndexing: boolean;
  };
}

/**
 * 用戶統計信息
 */
export interface UserStatistics {
  totalNotes: number;
  publishedNotes: number;
  draftNotes: number;
  archivedNotes: number;
  totalWords: number;
  averageWordsPerNote: number;
  notesCreatedThisWeek: number;
  notesCreatedThisMonth: number;
  longestStreak: number;
  currentStreak: number;
  lastActiveDate: Date;
  joinDate: Date;
  favoriteCategories: string[];
  mostUsedTags: string[];
}

/**
 * 用戶聚合根
 * 管理用戶及其相關的筆記、偏好設置、統計信息等
 */
export class UserAggregate extends AggregateRoot<UserId> {
  private _user: User;
  private _ownedNotes: Set<NoteId>;
  private _preferences: UserPreferences;
  private _statistics: UserStatistics;
  private _sessions: Map<
    string,
    { token: string; expiresAt: Date; device: string }
  >;

  constructor(
    user: User,
    options: {
      ownedNotes?: NoteId[];
      preferences?: Partial<UserPreferences>;
      statistics?: Partial<UserStatistics>;
    } = {},
  ) {
    super(user.id, user.createdAt);

    this._user = user;
    this._ownedNotes = new Set(options.ownedNotes || []);
    this._preferences = this.createDefaultPreferences(options.preferences);
    this._statistics = this.createDefaultStatistics(options.statistics);
    this._sessions = new Map();
  }

  /**
   * 創建新的用戶聚合
   */
  static create(
    username: string,
    email: string,
    password: string,
    options: {
      displayName?: string;
      role?: UserRole;
      metadata?: Record<string, unknown>;
    } = {},
  ): UserAggregate {
    const user = User.create({
      username,
      email,
      displayName: options.displayName,
      role: options.role,
      metadata: options.metadata,
    });

    return new UserAggregate(user);
  }

  /**
   * 更新用戶基本信息
   */
  updateProfile(params: {
    displayName?: string;
    email?: string;
    metadata?: Record<string, unknown>;
  }): void {
    this._user.updateProfile(params);
    this.markAsModified();
  }

  /**
   * 更改用戶名
   */
  changeUsername(newUsername: string): void {
    // 驗證新用戶名
    const username = new Username(newUsername);

    this._user.changeUsername(username.value);
    this.markAsModified();
  }

  /**
   * 更改電子郵件
   */
  changeEmail(newEmail: string): void {
    // 驗證新郵箱
    const email = new Email(newEmail);

    this._user.changeEmail(email.value);
    this.markAsModified();
  }

  /**
   * 更改密碼
   */
  changePassword(currentPassword: string, newPassword: string): void {
    // 這裡應該驗證當前密碼，但由於密碼驗證需要在應用層實現，
    // 我們只進行基本的業務規則檢查
    if (currentPassword === newPassword) {
      throw new BusinessRuleViolationError(
        "New password must be different from current password",
      );
    }

    // 驗證新密碼強度
    Password.validatePlainTextPassword(newPassword);

    // 實際的密碼更改邏輯應該在應用層實現
    this.markAsModified();
  }

  /**
   * 激活用戶
   */
  activate(): void {
    this._user.activate();
    this.markAsModified();
  }

  /**
   * 停用用戶
   */
  deactivate(): void {
    this._user.deactivate();
    this.markAsModified();
  }

  /**
   * 暫停用戶
   */
  suspend(reason?: string): void {
    this._user.suspend();
    if (reason) {
      this._user.updateMetadata({ suspensionReason: reason });
    }
    this.markAsModified();
  }

  /**
   * 更新角色
   */
  updateRole(newRole: UserRole): void {
    this._user.updateRole(newRole);
    this.markAsModified();
  }

  /**
   * 添加筆記所有權
   */
  addOwnedNote(noteId: NoteId): void {
    this._ownedNotes.add(noteId);
    this.updateStatistics({ totalNotes: this._ownedNotes.size });
    this.markAsModified();
  }

  /**
   * 移除筆記所有權
   */
  removeOwnedNote(noteId: NoteId): void {
    if (this._ownedNotes.delete(noteId)) {
      this.updateStatistics({ totalNotes: this._ownedNotes.size });
      this.markAsModified();
    }
  }

  /**
   * 更新用戶偏好設置
   */
  updatePreferences(preferences: Partial<UserPreferences>): void {
    this._preferences = { ...this._preferences, ...preferences };
    this.markAsModified();
  }

  /**
   * 更新編輯器設置
   */
  updateEditorSettings(
    settings: Partial<UserPreferences["editorSettings"]>,
  ): void {
    this._preferences.editorSettings = {
      ...this._preferences.editorSettings,
      ...settings,
    };
    this.markAsModified();
  }

  /**
   * 更新通知設置
   */
  updateNotificationSettings(
    settings: Partial<UserPreferences["notificationSettings"]>,
  ): void {
    this._preferences.notificationSettings = {
      ...this._preferences.notificationSettings,
      ...settings,
    };
    this.markAsModified();
  }

  /**
   * 更新隱私設置
   */
  updatePrivacySettings(
    settings: Partial<UserPreferences["privacySettings"]>,
  ): void {
    this._preferences.privacySettings = {
      ...this._preferences.privacySettings,
      ...settings,
    };
    this.markAsModified();
  }

  /**
   * 更新統計信息
   */
  updateStatistics(stats: Partial<UserStatistics>): void {
    this._statistics = { ...this._statistics, ...stats };
    this.markAsModified();
  }

  /**
   * 記錄用戶活動
   */
  recordActivity(): void {
    this._statistics.lastActiveDate = new Date();
    this.markAsModified();
  }

  /**
   * 創建會話
   */
  createSession(token: string, expiresAt: Date, device: string): void {
    this._sessions.set(token, { token, expiresAt, device });
    this.recordActivity();
    this.markAsModified();
  }

  /**
   * 移除會話
   */
  removeSession(token: string): void {
    this._sessions.delete(token);
    this.markAsModified();
  }

  /**
   * 清除所有會話
   */
  clearAllSessions(): void {
    this._sessions.clear();
    this.markAsModified();
  }

  /**
   * 檢查會話是否有效
   */
  isSessionValid(token: string): boolean {
    const session = this._sessions.get(token);
    if (!session) return false;

    if (session.expiresAt < new Date()) {
      this._sessions.delete(token);
      return false;
    }

    return true;
  }

  /**
   * 檢查用戶是否可以執行操作
   */
  canPerformAction(action: string): boolean {
    if (this._user.status !== UserStatus.ACTIVE) {
      return false;
    }

    // 基於角色的權限檢查
    switch (action) {
      case "create_note":
      case "edit_note":
      case "delete_note":
        return true;
      case "admin_action":
        return this._user.role === UserRole.ADMIN;
      case "moderate_content":
        return (
          this._user.role === UserRole.ADMIN ||
          this._user.role === UserRole.MODERATOR
        );
      default:
        return true;
    }
  }

  /**
   * 創建默認偏好設置
   */
  private createDefaultPreferences(
    partial?: Partial<UserPreferences>,
  ): UserPreferences {
    const defaults: UserPreferences = {
      theme: "auto",
      language: "zh-TW",
      timezone: "Asia/Taipei",
      editorSettings: {
        fontSize: 14,
        fontFamily: "Monaco, Consolas, monospace",
        tabSize: 2,
        wordWrap: true,
        showLineNumbers: true,
        enableVimMode: false,
      },
      notificationSettings: {
        emailNotifications: true,
        pushNotifications: true,
        dependencyUpdates: true,
        weeklyDigest: false,
      },
      privacySettings: {
        profileVisibility: "public",
        showActivity: true,
        allowIndexing: true,
      },
    };

    return { ...defaults, ...partial };
  }

  /**
   * 創建默認統計信息
   */
  private createDefaultStatistics(
    partial?: Partial<UserStatistics>,
  ): UserStatistics {
    const now = new Date();
    const defaults: UserStatistics = {
      totalNotes: 0,
      publishedNotes: 0,
      draftNotes: 0,
      archivedNotes: 0,
      totalWords: 0,
      averageWordsPerNote: 0,
      notesCreatedThisWeek: 0,
      notesCreatedThisMonth: 0,
      longestStreak: 0,
      currentStreak: 0,
      lastActiveDate: now,
      joinDate: now,
      favoriteCategories: [],
      mostUsedTags: [],
    };

    return { ...defaults, ...partial };
  }

  // Getters
  get user(): User {
    return this._user;
  }

  get ownedNotes(): NoteId[] {
    return Array.from(this._ownedNotes);
  }

  get preferences(): UserPreferences {
    return { ...this._preferences };
  }

  get statistics(): UserStatistics {
    return { ...this._statistics };
  }

  get activeSessions(): string[] {
    const now = new Date();
    const validSessions: string[] = [];

    for (const [token, session] of this._sessions) {
      if (session.expiresAt > now) {
        validSessions.push(token);
      } else {
        this._sessions.delete(token);
      }
    }

    return validSessions;
  }

  /**
   * 轉換為普通對象
   */
  toPlainObject(): Record<string, unknown> {
    return {
      id: this._id.value,
      user: this._user.toPlainObject(),
      ownedNotes: this.ownedNotes.map((id) => id.value),
      preferences: this._preferences,
      statistics: this._statistics,
      activeSessionsCount: this.activeSessions.length,
      createdAt: this._createdAt.toISOString(),
      updatedAt: this._updatedAt.toISOString(),
    };
  }
}
