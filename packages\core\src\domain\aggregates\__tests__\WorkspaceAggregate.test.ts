import { describe, it, expect, beforeEach } from "vitest";
import {
  WorkspaceAggregate,
  WorkspaceId,
  WorkspaceMemberRole,
} from "../WorkspaceAggregate.js";
import { UserId } from "../../value-objects/UserId.js";
import { NoteId } from "../../value-objects/NoteId.js";
import { Tag } from "../../value-objects/Tag.js";
import { BusinessRuleViolationError } from "../../../shared/Entity.js";

describe("WorkspaceAggregate", () => {
  let workspaceAggregate: WorkspaceAggregate;
  let ownerId: UserId;
  let memberId: UserId;

  beforeEach(() => {
    ownerId = UserId.generate();
    memberId = UserId.generate();
    workspaceAggregate = WorkspaceAggregate.create("Test Workspace", ownerId, {
      description: "A test workspace",
      isPublic: false,
    });
  });

  describe("Creation", () => {
    it("should create workspace with owner as member", () => {
      expect(workspaceAggregate.settings.name).toBe("Test Workspace");
      expect(workspaceAggregate.settings.description).toBe("A test workspace");
      expect(workspaceAggregate.settings.isPublic).toBe(false);
      expect(workspaceAggregate.ownerId).toBe(ownerId);
      expect(workspaceAggregate.isMember(ownerId)).toBe(true);
      expect(workspaceAggregate.getMemberRole(ownerId)).toBe(
        WorkspaceMemberRole.OWNER,
      );
    });

    it("should create workspace with default settings", () => {
      const workspace = WorkspaceAggregate.create("Simple Workspace", ownerId);

      expect(workspace.settings.name).toBe("Simple Workspace");
      expect(workspace.settings.isPublic).toBe(false);
      expect(workspace.settings.enableVersionControl).toBe(true);
      expect(workspace.settings.maxMembers).toBe(50);
    });
  });

  describe("Settings Management", () => {
    it("should update workspace settings", () => {
      workspaceAggregate.updateSettings({
        name: "Updated Workspace",
        isPublic: true,
        maxMembers: 100,
      });

      expect(workspaceAggregate.settings.name).toBe("Updated Workspace");
      expect(workspaceAggregate.settings.isPublic).toBe(true);
      expect(workspaceAggregate.settings.maxMembers).toBe(100);
    });
  });

  describe("Member Management", () => {
    it("should add new member", () => {
      workspaceAggregate.addMember(
        memberId,
        WorkspaceMemberRole.EDITOR,
        ownerId,
      );

      expect(workspaceAggregate.isMember(memberId)).toBe(true);
      expect(workspaceAggregate.getMemberRole(memberId)).toBe(
        WorkspaceMemberRole.EDITOR,
      );
      expect(workspaceAggregate.statistics.totalMembers).toBe(2);
    });

    it("should reject duplicate member", () => {
      workspaceAggregate.addMember(
        memberId,
        WorkspaceMemberRole.EDITOR,
        ownerId,
      );

      expect(() => {
        workspaceAggregate.addMember(
          memberId,
          WorkspaceMemberRole.VIEWER,
          ownerId,
        );
      }).toThrow(BusinessRuleViolationError);
    });

    it("should reject adding member when at max capacity", () => {
      workspaceAggregate.updateSettings({ maxMembers: 1 });

      expect(() => {
        workspaceAggregate.addMember(
          memberId,
          WorkspaceMemberRole.EDITOR,
          ownerId,
        );
      }).toThrow(BusinessRuleViolationError);
    });

    it("should remove member", () => {
      workspaceAggregate.addMember(
        memberId,
        WorkspaceMemberRole.EDITOR,
        ownerId,
      );
      workspaceAggregate.removeMember(memberId, ownerId);

      expect(workspaceAggregate.isMember(memberId)).toBe(false);
      expect(workspaceAggregate.statistics.totalMembers).toBe(1);
    });

    it("should not allow removing owner", () => {
      expect(() => {
        workspaceAggregate.removeMember(ownerId, ownerId);
      }).toThrow(BusinessRuleViolationError);
    });

    it("should not allow non-admin to remove members", () => {
      const viewerId = UserId.generate();
      workspaceAggregate.addMember(
        memberId,
        WorkspaceMemberRole.EDITOR,
        ownerId,
      );
      workspaceAggregate.addMember(
        viewerId,
        WorkspaceMemberRole.VIEWER,
        ownerId,
      );

      expect(() => {
        workspaceAggregate.removeMember(memberId, viewerId);
      }).toThrow(BusinessRuleViolationError);
    });

    it("should update member role", () => {
      workspaceAggregate.addMember(
        memberId,
        WorkspaceMemberRole.VIEWER,
        ownerId,
      );
      workspaceAggregate.updateMemberRole(
        memberId,
        WorkspaceMemberRole.EDITOR,
        ownerId,
      );

      expect(workspaceAggregate.getMemberRole(memberId)).toBe(
        WorkspaceMemberRole.EDITOR,
      );
    });

    it("should not allow changing owner role", () => {
      expect(() => {
        workspaceAggregate.updateMemberRole(
          ownerId,
          WorkspaceMemberRole.ADMIN,
          ownerId,
        );
      }).toThrow(BusinessRuleViolationError);
    });

    it("should not allow non-admin to update roles", () => {
      const viewerId = UserId.generate();
      workspaceAggregate.addMember(
        memberId,
        WorkspaceMemberRole.EDITOR,
        ownerId,
      );
      workspaceAggregate.addMember(
        viewerId,
        WorkspaceMemberRole.VIEWER,
        ownerId,
      );

      expect(() => {
        workspaceAggregate.updateMemberRole(
          memberId,
          WorkspaceMemberRole.ADMIN,
          viewerId,
        );
      }).toThrow(BusinessRuleViolationError);
    });
  });

  describe("Permissions", () => {
    beforeEach(() => {
      workspaceAggregate.addMember(
        memberId,
        WorkspaceMemberRole.EDITOR,
        ownerId,
      );
    });

    it("should grant correct permissions for owner", () => {
      expect(
        workspaceAggregate.hasPermission(ownerId, "manage_workspace"),
      ).toBe(true);
      expect(workspaceAggregate.hasPermission(ownerId, "manage_members")).toBe(
        true,
      );
      expect(workspaceAggregate.hasPermission(ownerId, "create_notes")).toBe(
        true,
      );
      expect(workspaceAggregate.hasPermission(ownerId, "view_notes")).toBe(
        true,
      );
    });

    it("should grant correct permissions for editor", () => {
      expect(workspaceAggregate.hasPermission(memberId, "create_notes")).toBe(
        true,
      );
      expect(workspaceAggregate.hasPermission(memberId, "edit_notes")).toBe(
        true,
      );
      expect(workspaceAggregate.hasPermission(memberId, "view_notes")).toBe(
        true,
      );
      expect(
        workspaceAggregate.hasPermission(memberId, "manage_workspace"),
      ).toBe(false);
    });

    it("should grant correct permissions for viewer", () => {
      const viewerId = UserId.generate();
      workspaceAggregate.addMember(
        viewerId,
        WorkspaceMemberRole.VIEWER,
        ownerId,
      );

      expect(workspaceAggregate.hasPermission(viewerId, "view_notes")).toBe(
        true,
      );
      expect(workspaceAggregate.hasPermission(viewerId, "comment_notes")).toBe(
        true,
      );
      expect(workspaceAggregate.hasPermission(viewerId, "create_notes")).toBe(
        false,
      );
      expect(workspaceAggregate.hasPermission(viewerId, "edit_notes")).toBe(
        false,
      );
    });

    it("should deny permissions for non-members", () => {
      const nonMemberId = UserId.generate();

      expect(workspaceAggregate.hasPermission(nonMemberId, "view_notes")).toBe(
        false,
      );
      expect(
        workspaceAggregate.hasPermission(nonMemberId, "create_notes"),
      ).toBe(false);
    });
  });

  describe("Content Management", () => {
    it("should add note to workspace", () => {
      const noteId = NoteId.generate();
      workspaceAggregate.addNote(noteId);

      expect(workspaceAggregate.notes).toContain(noteId);
      expect(workspaceAggregate.statistics.totalNotes).toBe(1);
    });

    it("should remove note from workspace", () => {
      const noteId = NoteId.generate();
      workspaceAggregate.addNote(noteId);
      workspaceAggregate.removeNote(noteId);

      expect(workspaceAggregate.notes).not.toContain(noteId);
      expect(workspaceAggregate.statistics.totalNotes).toBe(0);
    });

    it("should add category", () => {
      workspaceAggregate.addCategory("Development");
      workspaceAggregate.addCategory("Documentation");

      expect(workspaceAggregate.categories).toContain("Development");
      expect(workspaceAggregate.categories).toContain("Documentation");
    });

    it("should remove category", () => {
      workspaceAggregate.addCategory("Development");
      workspaceAggregate.removeCategory("Development");

      expect(workspaceAggregate.categories).not.toContain("Development");
    });

    it("should add tag", () => {
      const tag = new Tag("javascript");
      workspaceAggregate.addTag(tag);

      expect(workspaceAggregate.tags).toContain(tag);
    });

    it("should remove tag", () => {
      const tag = new Tag("javascript");
      workspaceAggregate.addTag(tag);
      workspaceAggregate.removeTag(tag);

      expect(workspaceAggregate.tags).not.toContain(tag);
    });
  });

  describe("Statistics", () => {
    it("should update statistics", () => {
      workspaceAggregate.updateStatistics({
        totalWords: 5000,
        activeMembers: 3,
        createdThisWeek: 10,
      });

      expect(workspaceAggregate.statistics.totalWords).toBe(5000);
      expect(workspaceAggregate.statistics.activeMembers).toBe(3);
      expect(workspaceAggregate.statistics.createdThisWeek).toBe(10);
    });
  });

  describe("Serialization", () => {
    it("should serialize to plain object", () => {
      workspaceAggregate.addMember(
        memberId,
        WorkspaceMemberRole.EDITOR,
        ownerId,
      );
      const noteId = NoteId.generate();
      workspaceAggregate.addNote(noteId);
      workspaceAggregate.addCategory("Development");
      const tag = new Tag("javascript");
      workspaceAggregate.addTag(tag);

      const plainObject = workspaceAggregate.toPlainObject();

      expect(plainObject).toHaveProperty("id");
      expect(plainObject).toHaveProperty("ownerId");
      expect(plainObject).toHaveProperty("settings");
      expect(plainObject).toHaveProperty("members");
      expect(plainObject).toHaveProperty("notes");
      expect(plainObject).toHaveProperty("categories");
      expect(plainObject).toHaveProperty("tags");
      expect(plainObject).toHaveProperty("statistics");

      expect(plainObject.members).toHaveLength(2);
      expect(plainObject.notes).toContain(noteId.value);
      expect(plainObject.categories).toContain("Development");
    });
  });
});
