# AI 整合配置規範

## AI 整合配置規範

### 整體架構概覽

基於階段2技術規範，實現混合AI架構：

- **本地Agent服務**：輕量級處理，隱私優先
- **遠端AI服務**：複雜分析，多模型協作
- **智慧路由機制**：根據任務複雜度和敏感性自動選擇

## Context7 搜索的 AI 整合實踐

### Google Gemini 2.5 Flash 整合

基於最新的 AI API 整合方式：

```typescript
// src/services/ai/geminiService.ts
import {
  GoogleGenerativeAI,
  HarmCategory,
  HarmBlockThreshold,
} from "@google/generative-ai";

interface GeminiConfig {
  apiKey: string;
  model: string;
  safetySettings: Array<{
    category: HarmCategory;
    threshold: HarmBlockThreshold;
  }>;
  generationConfig: {
    temperature: number;
    topP: number;
    topK: number;
    maxOutputTokens: number;
  };
}

class GeminiService {
  private genAI: GoogleGenerativeAI;
  private model: any;
  private config: GeminiConfig;

  constructor(config: GeminiConfig) {
    this.config = config;
    this.genAI = new GoogleGenerativeAI(config.apiKey);
    this.model = this.genAI.getGenerativeModel({
      model: config.model,
      safetySettings: config.safetySettings,
      generationConfig: config.generationConfig,
    });
  }

  async analyzeNote(
    content: string,
    context?: string,
  ): Promise<{
    summary: string;
    tags: string[];
    dependencies: string[];
    suggestions: string[];
  }> {
    try {
      const prompt = this.buildAnalysisPrompt(content, context);
      const result = await this.model.generateContent(prompt);
      const response = await result.response;

      return this.parseAnalysisResponse(response.text());
    } catch (error) {
      console.error("Gemini analysis failed:", error);
      throw new AIServiceError("筆記分析失敗", error);
    }
  }

  async generateSuggestions(notes: string[], query: string): Promise<string[]> {
    try {
      const prompt = this.buildSuggestionPrompt(notes, query);
      const result = await this.model.generateContent(prompt);
      const response = await result.response;

      return this.parseSuggestions(response.text());
    } catch (error) {
      console.error("Gemini suggestion generation failed:", error);
      throw new AIServiceError("建議生成失敗", error);
    }
  }

  private buildAnalysisPrompt(content: string, context?: string): string {
    return `
請分析以下筆記內容，並以JSON格式返回分析結果：

筆記內容：
${content}

${context ? `上下文：${context}` : ""}

請提供：
1. summary: 簡潔的摘要（50字以內）
2. tags: 相關標籤（3-5個）
3. dependencies: 可能的依賴關係（其他筆記或概念）
4. suggestions: 改進建議（2-3個）

返回格式：
{
  "summary": "...",
  "tags": ["tag1", "tag2"],
  "dependencies": ["concept1", "concept2"],
  "suggestions": ["suggestion1", "suggestion2"]
}
`;
  }

  private parseAnalysisResponse(response: string): any {
    try {
      // 提取JSON部分
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error("無法解析AI回應");
      }

      return JSON.parse(jsonMatch[0]);
    } catch (error) {
      console.error("Failed to parse AI response:", error);
      throw new AIServiceError("AI回應解析失敗", error);
    }
  }
}

export { GeminiService, type GeminiConfig };
```

### OpenAI GPT-4 備用服務

```typescript
// src/services/ai/openaiService.ts
import OpenAI from "openai";

interface OpenAIConfig {
  apiKey: string;
  model: string;
  maxTokens: number;
  temperature: number;
}

class OpenAIService {
  private client: OpenAI;
  private config: OpenAIConfig;

  constructor(config: OpenAIConfig) {
    this.config = config;
    this.client = new OpenAI({
      apiKey: config.apiKey,
    });
  }

  async processComplexQuery(query: string, context: string[]): Promise<string> {
    try {
      const messages: OpenAI.Chat.ChatCompletionMessageParam[] = [
        {
          role: "system",
          content: "你是一個專業的知識管理助手，擅長分析和組織信息。",
        },
        {
          role: "user",
          content: `
基於以下上下文信息，請回答用戶的問題：

上下文：
${context.join("\n\n")}

問題：${query}

請提供詳細且有用的回答。
`,
        },
      ];

      const completion = await this.client.chat.completions.create({
        model: this.config.model,
        messages,
        max_tokens: this.config.maxTokens,
        temperature: this.config.temperature,
      });

      return completion.choices[0]?.message?.content || "無法生成回應";
    } catch (error) {
      console.error("OpenAI processing failed:", error);
      throw new AIServiceError("複雜查詢處理失敗", error);
    }
  }
}

export { OpenAIService, type OpenAIConfig };
```

## 配置標準

### API 金鑰管理

#### 環境變數配置

```typescript
// src/config/aiConfig.ts
import { z } from "zod";

const AIConfigSchema = z.object({
  gemini: z.object({
    apiKey: z.string().min(1, "Gemini API key is required"),
    model: z.string().default("gemini-2.5-flash"),
    maxOutputTokens: z.number().default(2048),
    temperature: z.number().min(0).max(2).default(0.7),
    topP: z.number().min(0).max(1).default(0.8),
    topK: z.number().min(1).default(40),
  }),
  openai: z.object({
    apiKey: z.string().min(1, "OpenAI API key is required"),
    model: z.string().default("gpt-4"),
    maxTokens: z.number().default(2048),
    temperature: z.number().min(0).max(2).default(0.7),
  }),
  anthropic: z.object({
    apiKey: z.string().min(1, "Anthropic API key is required"),
    model: z.string().default("claude-3-sonnet-********"),
    maxTokens: z.number().default(2048),
    temperature: z.number().min(0).max(1).default(0.7),
  }),
  routing: z.object({
    defaultProvider: z
      .enum(["gemini", "openai", "anthropic"])
      .default("gemini"),
    fallbackProvider: z
      .enum(["gemini", "openai", "anthropic"])
      .default("openai"),
    complexityThreshold: z.number().min(0).max(1).default(0.7),
    sensitivityThreshold: z.number().min(0).max(1).default(0.8),
  }),
});

export type AIConfig = z.infer<typeof AIConfigSchema>;

export function loadAIConfig(): AIConfig {
  const config = {
    gemini: {
      apiKey: process.env.VITE_GEMINI_API_KEY || "",
      model: process.env.VITE_GEMINI_MODEL || "gemini-2.5-flash",
      maxOutputTokens: parseInt(process.env.VITE_GEMINI_MAX_TOKENS || "2048"),
      temperature: parseFloat(process.env.VITE_GEMINI_TEMPERATURE || "0.7"),
      topP: parseFloat(process.env.VITE_GEMINI_TOP_P || "0.8"),
      topK: parseInt(process.env.VITE_GEMINI_TOP_K || "40"),
    },
    openai: {
      apiKey: process.env.VITE_OPENAI_API_KEY || "",
      model: process.env.VITE_OPENAI_MODEL || "gpt-4",
      maxTokens: parseInt(process.env.VITE_OPENAI_MAX_TOKENS || "2048"),
      temperature: parseFloat(process.env.VITE_OPENAI_TEMPERATURE || "0.7"),
    },
    anthropic: {
      apiKey: process.env.VITE_ANTHROPIC_API_KEY || "",
      model: process.env.VITE_ANTHROPIC_MODEL || "claude-3-sonnet-********",
      maxTokens: parseInt(process.env.VITE_ANTHROPIC_MAX_TOKENS || "2048"),
      temperature: parseFloat(process.env.VITE_ANTHROPIC_TEMPERATURE || "0.7"),
    },
    routing: {
      defaultProvider:
        (process.env.VITE_AI_DEFAULT_PROVIDER as any) || "gemini",
      fallbackProvider:
        (process.env.VITE_AI_FALLBACK_PROVIDER as any) || "openai",
      complexityThreshold: parseFloat(
        process.env.VITE_AI_COMPLEXITY_THRESHOLD || "0.7",
      ),
      sensitivityThreshold: parseFloat(
        process.env.VITE_AI_SENSITIVITY_THRESHOLD || "0.8",
      ),
    },
  };

  return AIConfigSchema.parse(config);
}
```

#### 安全金鑰存儲

```typescript
// src/services/security/keyManager.ts
import CryptoJS from "crypto-js";

class KeyManager {
  private static instance: KeyManager;
  private encryptionKey: string;

  private constructor() {
    // 從安全存儲獲取加密金鑰
    this.encryptionKey = this.getOrCreateEncryptionKey();
  }

  static getInstance(): KeyManager {
    if (!KeyManager.instance) {
      KeyManager.instance = new KeyManager();
    }
    return KeyManager.instance;
  }

  encryptApiKey(apiKey: string): string {
    return CryptoJS.AES.encrypt(apiKey, this.encryptionKey).toString();
  }

  decryptApiKey(encryptedKey: string): string {
    const bytes = CryptoJS.AES.decrypt(encryptedKey, this.encryptionKey);
    return bytes.toString(CryptoJS.enc.Utf8);
  }

  private getOrCreateEncryptionKey(): string {
    const stored = localStorage.getItem("app_encryption_key");
    if (stored) {
      return stored;
    }

    // 生成新的加密金鑰
    const newKey = CryptoJS.lib.WordArray.random(256 / 8).toString();
    localStorage.setItem("app_encryption_key", newKey);
    return newKey;
  }

  storeApiKey(provider: string, apiKey: string): void {
    const encrypted = this.encryptApiKey(apiKey);
    localStorage.setItem(`ai_key_${provider}`, encrypted);
  }

  getApiKey(provider: string): string | null {
    const encrypted = localStorage.getItem(`ai_key_${provider}`);
    if (!encrypted) return null;

    try {
      return this.decryptApiKey(encrypted);
    } catch (error) {
      console.error("Failed to decrypt API key:", error);
      return null;
    }
  }

  removeApiKey(provider: string): void {
    localStorage.removeItem(`ai_key_${provider}`);
  }
}

export { KeyManager };
```

### 服務整合方式

#### AI 路由器實現

```typescript
// src/services/ai/aiRouter.ts
import { GeminiService } from "./geminiService";
import { OpenAIService } from "./openaiService";
import { AnthropicService } from "./anthropicService";
import { loadAIConfig } from "@/config/aiConfig";

interface TaskMetrics {
  complexity: number; // 0-1, 任務複雜度
  sensitivity: number; // 0-1, 數據敏感度
  urgency: number; // 0-1, 緊急程度
  contextSize: number; // 上下文大小
}

interface AIProvider {
  name: string;
  service: any;
  capabilities: {
    maxTokens: number;
    supportsStreaming: boolean;
    costPerToken: number;
    averageLatency: number;
  };
}

class AIRouter {
  private providers: Map<string, AIProvider> = new Map();
  private config = loadAIConfig();

  constructor() {
    this.initializeProviders();
  }

  private initializeProviders(): void {
    // 初始化 Gemini
    if (this.config.gemini.apiKey) {
      this.providers.set("gemini", {
        name: "gemini",
        service: new GeminiService(this.config.gemini),
        capabilities: {
          maxTokens: this.config.gemini.maxOutputTokens,
          supportsStreaming: true,
          costPerToken: 0.00001, // 假設成本
          averageLatency: 800,
        },
      });
    }

    // 初始化 OpenAI
    if (this.config.openai.apiKey) {
      this.providers.set("openai", {
        name: "openai",
        service: new OpenAIService(this.config.openai),
        capabilities: {
          maxTokens: this.config.openai.maxTokens,
          supportsStreaming: true,
          costPerToken: 0.00003,
          averageLatency: 1200,
        },
      });
    }
  }

  async routeTask(
    task: string,
    metrics: TaskMetrics,
    context?: string[],
  ): Promise<{
    provider: string;
    result: any;
    metrics: {
      latency: number;
      tokensUsed: number;
      cost: number;
    };
  }> {
    const selectedProvider = this.selectProvider(metrics);
    const startTime = Date.now();

    try {
      const provider = this.providers.get(selectedProvider);
      if (!provider) {
        throw new Error(`Provider ${selectedProvider} not available`);
      }

      const result = await this.executeTask(provider, task, context);
      const latency = Date.now() - startTime;

      return {
        provider: selectedProvider,
        result,
        metrics: {
          latency,
          tokensUsed: this.estimateTokens(task, result),
          cost: this.calculateCost(provider, task, result),
        },
      };
    } catch (error) {
      // 嘗試備用提供者
      const fallbackProvider = this.config.routing.fallbackProvider;
      if (fallbackProvider !== selectedProvider) {
        console.warn(
          `Primary provider ${selectedProvider} failed, trying fallback`,
        );
        return this.routeTask(task, metrics, context);
      }
      throw error;
    }
  }

  private selectProvider(metrics: TaskMetrics): string {
    const { complexity, sensitivity, urgency, contextSize } = metrics;
    const { complexityThreshold, sensitivityThreshold } = this.config.routing;

    // 高敏感度任務優先使用本地或隱私友好的提供者
    if (sensitivity > sensitivityThreshold) {
      return "gemini"; // Gemini 通常有更好的隱私政策
    }

    // 高複雜度任務使用最強大的模型
    if (complexity > complexityThreshold) {
      return "openai"; // GPT-4 處理複雜任務能力更強
    }

    // 大上下文任務
    if (contextSize > 50000) {
      return "anthropic"; // Claude 有更大的上下文窗口
    }

    // 緊急任務優先考慮延遲
    if (urgency > 0.8) {
      const fastestProvider = Array.from(this.providers.values()).sort(
        (a, b) => a.capabilities.averageLatency - b.capabilities.averageLatency,
      )[0];
      return fastestProvider.name;
    }

    // 默認提供者
    return this.config.routing.defaultProvider;
  }

  private async executeTask(
    provider: AIProvider,
    task: string,
    context?: string[],
  ): Promise<any> {
    const { service } = provider;

    // 根據任務類型調用不同的方法
    if (task.includes("分析") || task.includes("analyze")) {
      return await service.analyzeNote?.(task, context?.join("\n"));
    }

    if (task.includes("建議") || task.includes("suggest")) {
      return await service.generateSuggestions?.(context || [], task);
    }

    if (task.includes("查詢") || task.includes("query")) {
      return await service.processComplexQuery?.(task, context || []);
    }

    // 默認處理
    return await service.generateContent?.(task, context);
  }

  private estimateTokens(input: string, output: any): number {
    // 簡單的 token 估算（實際應該使用專門的 tokenizer）
    const inputTokens = Math.ceil(input.length / 4);
    const outputTokens = Math.ceil(JSON.stringify(output).length / 4);
    return inputTokens + outputTokens;
  }

  private calculateCost(
    provider: AIProvider,
    input: string,
    output: any,
  ): number {
    const tokens = this.estimateTokens(input, output);
    return tokens * provider.capabilities.costPerToken;
  }
}

export { AIRouter, type TaskMetrics };
```

### 錯誤處理機制

#### 自定義錯誤類型

```typescript
// src/services/ai/errors.ts
export class AIServiceError extends Error {
  constructor(
    message: string,
    public originalError?: any,
    public provider?: string,
    public retryable: boolean = true,
  ) {
    super(message);
    this.name = "AIServiceError";
  }
}

export class RateLimitError extends AIServiceError {
  constructor(provider: string, retryAfter?: number) {
    super(`Rate limit exceeded for ${provider}`, undefined, provider, true);
    this.name = "RateLimitError";
    this.retryAfter = retryAfter;
  }

  retryAfter?: number;
}

export class QuotaExceededError extends AIServiceError {
  constructor(provider: string) {
    super(`Quota exceeded for ${provider}`, undefined, provider, false);
    this.name = "QuotaExceededError";
  }
}

export class InvalidAPIKeyError extends AIServiceError {
  constructor(provider: string) {
    super(`Invalid API key for ${provider}`, undefined, provider, false);
    this.name = "InvalidAPIKeyError";
  }
}
```

#### 重試機制

```typescript
// src/services/ai/retryHandler.ts
import { AIServiceError, RateLimitError } from "./errors";

interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
}

class RetryHandler {
  private config: RetryConfig;

  constructor(config: Partial<RetryConfig> = {}) {
    this.config = {
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 30000,
      backoffMultiplier: 2,
      ...config,
    };
  }

  async executeWithRetry<T>(
    operation: () => Promise<T>,
    context: string = "AI operation",
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 0; attempt <= this.config.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;

        // 不可重試的錯誤直接拋出
        if (error instanceof AIServiceError && !error.retryable) {
          throw error;
        }

        // 最後一次嘗試失敗
        if (attempt === this.config.maxRetries) {
          break;
        }

        // 計算延遲時間
        let delay =
          this.config.baseDelay *
          Math.pow(this.config.backoffMultiplier, attempt);

        // 處理速率限制
        if (error instanceof RateLimitError && error.retryAfter) {
          delay = error.retryAfter * 1000;
        }

        delay = Math.min(delay, this.config.maxDelay);

        console.warn(
          `${context} failed (attempt ${attempt + 1}), retrying in ${delay}ms:`,
          error.message,
        );

        await this.sleep(delay);
      }
    }

    throw new AIServiceError(
      `${context} failed after ${this.config.maxRetries + 1} attempts`,
      lastError,
    );
  }

  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}

export { RetryHandler, type RetryConfig };
```

### 效能優化策略

#### 請求快取機制

```typescript
// src/services/ai/cache.ts
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

class AICache {
  private cache = new Map<string, CacheEntry<any>>();
  private maxSize = 1000;
  private defaultTTL = 5 * 60 * 1000; // 5分鐘

  set<T>(key: string, data: T, ttl: number = this.defaultTTL): void {
    // 清理過期項目
    this.cleanup();

    // 如果快取已滿，移除最舊的項目
    if (this.cache.size >= this.maxSize) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    // 檢查是否過期
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  has(key: string): boolean {
    return this.get(key) !== null;
  }

  delete(key: string): void {
    this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  private cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key);
      }
    }
  }

  generateKey(provider: string, method: string, params: any): string {
    const paramsStr = JSON.stringify(params, Object.keys(params).sort());
    return `${provider}:${method}:${this.hashString(paramsStr)}`;
  }

  private hashString(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // 轉換為32位整數
    }
    return hash.toString(36);
  }
}

export { AICache };
```

#### 批次處理優化

```typescript
// src/services/ai/batchProcessor.ts
interface BatchTask {
  id: string;
  task: string;
  context?: string[];
  resolve: (result: any) => void;
  reject: (error: Error) => void;
}

class BatchProcessor {
  private queue: BatchTask[] = [];
  private processing = false;
  private batchSize = 5;
  private batchTimeout = 2000; // 2秒
  private timeoutId?: NodeJS.Timeout;

  async addTask(task: string, context?: string[]): Promise<any> {
    return new Promise((resolve, reject) => {
      const batchTask: BatchTask = {
        id: this.generateId(),
        task,
        context,
        resolve,
        reject,
      };

      this.queue.push(batchTask);
      this.scheduleProcessing();
    });
  }

  private scheduleProcessing(): void {
    if (this.processing) return;

    // 如果達到批次大小，立即處理
    if (this.queue.length >= this.batchSize) {
      this.processBatch();
      return;
    }

    // 否則設置超時處理
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
    }

    this.timeoutId = setTimeout(() => {
      if (this.queue.length > 0) {
        this.processBatch();
      }
    }, this.batchTimeout);
  }

  private async processBatch(): Promise<void> {
    if (this.processing || this.queue.length === 0) return;

    this.processing = true;

    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = undefined;
    }

    const batch = this.queue.splice(0, this.batchSize);

    try {
      // 並行處理批次中的任務
      const results = await Promise.allSettled(
        batch.map((task) => this.processTask(task)),
      );

      // 處理結果
      results.forEach((result, index) => {
        const task = batch[index];
        if (result.status === "fulfilled") {
          task.resolve(result.value);
        } else {
          task.reject(result.reason);
        }
      });
    } catch (error) {
      // 如果批次處理失敗，拒絕所有任務
      batch.forEach((task) => task.reject(error as Error));
    } finally {
      this.processing = false;

      // 如果還有待處理的任務，繼續處理
      if (this.queue.length > 0) {
        this.scheduleProcessing();
      }
    }
  }

  private async processTask(task: BatchTask): Promise<any> {
    // 這裡應該調用實際的AI服務
    // 為了示例，我們模擬一個異步操作
    await new Promise((resolve) => setTimeout(resolve, 100));
    return `Processed: ${task.task}`;
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }
}

export { BatchProcessor };
```

這個AI整合配置確保了安全、高效和可擴展的AI服務整合。
