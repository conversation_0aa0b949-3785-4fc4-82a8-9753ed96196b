<script lang="ts">
	import { <PERSON>u, <PERSON>, <PERSON>, <PERSON>ting<PERSON>, User, <PERSON>, Sun, Plus, FileText } from 'lucide-svelte';
	import { page } from '$app/stores';
	import { goto } from '$app/navigation';

	import Button from '$components/ui/Button.svelte';
	import DependencyNotifications from '$components/notifications/DependencyNotifications.svelte';
	import AgentStatusIndicator from '$components/agent/AgentStatusIndicator.svelte';
	import { appStore } from '$stores/app';
	import { themeStore } from '$stores/theme';
	import { notificationStore } from '$stores/notification';

	// Reactive values
	$: isDark = $themeStore.mode === 'dark';
	$: unreadCount = $notificationStore.unreadCount;

	// Event handlers
	const toggleSidebar = () => {
		appStore.toggleSidebar();
	};

	const toggleTheme = () => {
		themeStore.toggle();
	};

	const handleSearch = () => {
		goto('/search');
	};

	const handleNotifications = () => {
		goto('/notifications');
	};

	const handleSettings = () => {
		goto('/settings');
	};

	const handleProfile = () => {
		goto('/profile');
	};

	const handleNewNote = () => {
		goto('/notes/new');
	};
</script>

<header
	class="sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60"
>
	<div class="container flex h-14 items-center">
		<!-- Left section -->
		<div class="flex items-center space-x-4">
			<!-- Sidebar toggle -->
			<Button variant="ghost" size="icon" on:click={toggleSidebar}>
				<Menu class="h-5 w-5" />
				<span class="sr-only">切換側邊欄</span>
			</Button>

			<!-- Logo and title -->
			<div class="flex items-center space-x-2">
				<FileText class="h-6 w-6 text-primary" />
				<span class="hidden font-bold sm:inline-block">Life Note</span>
			</div>

			<!-- Breadcrumb -->
			<nav class="hidden md:flex items-center space-x-1 text-sm text-muted-foreground">
				{#if $page.route.id !== '/'}
					<span>/</span>
					<span class="font-medium text-foreground">
						{#if $page.route.id?.includes('/notes')}
							筆記
						{:else if $page.route.id?.includes('/search')}
							搜尋
						{:else if $page.route.id?.includes('/settings')}
							設定
						{:else}
							頁面
						{/if}
					</span>
				{/if}
			</nav>
		</div>

		<!-- Center section - Search -->
		<div class="flex-1 flex justify-center px-4">
			<div class="w-full max-w-sm">
				<Button
					variant="outline"
					class="w-full justify-start text-muted-foreground"
					on:click={handleSearch}
				>
					<Search class="mr-2 h-4 w-4" />
					搜尋筆記...
					<kbd
						class="pointer-events-none ml-auto inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100"
					>
						<span class="text-xs">⌘</span>K
					</kbd>
				</Button>
			</div>
		</div>

		<!-- Right section -->
		<div class="flex items-center space-x-2">
			<!-- New note button -->
			<Button variant="default" size="sm" on:click={handleNewNote} class="hidden sm:flex">
				<Plus class="mr-2 h-4 w-4" />
				新增筆記
			</Button>

			<!-- Mobile new note button -->
			<Button variant="default" size="icon" on:click={handleNewNote} class="sm:hidden">
				<Plus class="h-4 w-4" />
				<span class="sr-only">新增筆記</span>
			</Button>

			<!-- Notifications -->
			<Button variant="ghost" size="icon" on:click={handleNotifications} class="relative">
				<Bell class="h-5 w-5" />
				{#if unreadCount > 0}
					<span
						class="absolute -top-1 -right-1 h-4 w-4 rounded-full bg-destructive text-xs text-destructive-foreground flex items-center justify-center"
					>
						{unreadCount > 9 ? '9+' : unreadCount}
					</span>
				{/if}
				<span class="sr-only">通知</span>
			</Button>

			<!-- Dependency Notifications -->
			<DependencyNotifications position="top-right" />

			<!-- Agent Status -->
			<AgentStatusIndicator size="sm" />

			<!-- Theme toggle -->
			<Button variant="ghost" size="icon" on:click={toggleTheme}>
				{#if isDark}
					<Sun class="h-5 w-5" />
					<span class="sr-only">切換到淺色模式</span>
				{:else}
					<Moon class="h-5 w-5" />
					<span class="sr-only">切換到深色模式</span>
				{/if}
			</Button>

			<!-- Settings -->
			<Button variant="ghost" size="icon" on:click={handleSettings}>
				<Settings class="h-5 w-5" />
				<span class="sr-only">設定</span>
			</Button>

			<!-- Profile -->
			<Button variant="ghost" size="icon" on:click={handleProfile}>
				<User class="h-5 w-5" />
				<span class="sr-only">個人資料</span>
			</Button>
		</div>
	</div>
</header>

<style>
	/* Ensure header stays on top */
	header {
		backdrop-filter: blur(8px);
	}

	/* Smooth transitions */
	header * {
		transition: all 0.2s ease-in-out;
	}

	/* Focus styles */
	:global(.focus-visible) {
		outline: 2px solid hsl(var(--ring));
		outline-offset: 2px;
	}
</style>
