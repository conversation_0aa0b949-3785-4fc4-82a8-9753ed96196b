import type { Note } from '$lib/types/note';
import type { DependencyGraph, DependencyNode, DependencyLink } from '$lib/types/dependency';
import { dependencyService } from './dependencyService';

/**
 * 依賴關係分析報告接口
 */
export interface DependencyAnalysisReport {
	summary: AnalysisSummary;
	networkMetrics: NetworkMetrics;
	nodeAnalysis: NodeAnalysis[];
	clusterAnalysis: ClusterAnalysis[];
	riskAssessment: RiskAssessment;
	recommendations: Recommendation[];
	generatedAt: Date;
}

/**
 * 分析摘要
 */
export interface AnalysisSummary {
	totalNotes: number;
	totalDependencies: number;
	averageDependenciesPerNote: number;
	networkDensity: number;
	stronglyConnectedComponents: number;
	isolatedNodes: number;
}

/**
 * 網絡指標
 */
export interface NetworkMetrics {
	diameter: number;
	averagePathLength: number;
	clusteringCoefficient: number;
	centralityDistribution: {
		mean: number;
		median: number;
		standardDeviation: number;
	};
	degreeDistribution: Record<number, number>;
}

/**
 * 節點分析
 */
export interface NodeAnalysis {
	noteId: string;
	noteTitle: string;
	inDegree: number;
	outDegree: number;
	totalDegree: number;
	betweennessCentrality: number;
	closenessCentrality: number;
	pageRank: number;
	clusteringCoefficient: number;
	role: 'hub' | 'authority' | 'bridge' | 'peripheral' | 'normal';
	riskLevel: 'low' | 'medium' | 'high';
}

/**
 * 聚類分析
 */
export interface ClusterAnalysis {
	clusterId: string;
	nodes: string[];
	size: number;
	density: number;
	cohesion: number;
	mainTopic?: string;
	keywords: string[];
}

/**
 * 風險評估
 */
export interface RiskAssessment {
	circularDependencies: CircularDependency[];
	criticalNodes: CriticalNode[];
	fragileConnections: FragileConnection[];
	overallRiskScore: number;
}

export interface CircularDependency {
	cycle: string[];
	length: number;
	severity: 'low' | 'medium' | 'high';
}

export interface CriticalNode {
	noteId: string;
	noteTitle: string;
	criticalityScore: number;
	reason: string;
	impactRadius: number;
}

export interface FragileConnection {
	sourceId: string;
	targetId: string;
	strength: number;
	reason: string;
}

/**
 * 建議
 */
export interface Recommendation {
	type: 'structure' | 'content' | 'organization' | 'risk';
	priority: 'low' | 'medium' | 'high';
	title: string;
	description: string;
	actionItems: string[];
	affectedNotes: string[];
}

/**
 * 依賴關係分析報告服務
 */
export class DependencyAnalysisReportService {
	/**
	 * 生成完整的依賴關係分析報告
	 */
	async generateReport(notes: Note[]): Promise<DependencyAnalysisReport> {
		const graph = await dependencyService.analyzeDependencies(notes);

		const summary = this.generateSummary(graph, notes);
		const networkMetrics = this.calculateNetworkMetrics(graph);
		const nodeAnalysis = this.analyzeNodes(graph, notes);
		const clusterAnalysis = this.analyzeCluster(graph, notes);
		const riskAssessment = this.assessRisks(graph, notes);
		const recommendations = this.generateRecommendations(graph, notes, riskAssessment);

		return {
			summary,
			networkMetrics,
			nodeAnalysis,
			clusterAnalysis,
			riskAssessment,
			recommendations,
			generatedAt: new Date()
		};
	}

	/**
	 * 生成分析摘要
	 */
	private generateSummary(graph: DependencyGraph, notes: Note[]): AnalysisSummary {
		const totalNotes = graph.nodes.length;
		const totalDependencies = graph.links.length;
		const averageDependenciesPerNote = totalNotes > 0 ? totalDependencies / totalNotes : 0;

		// 計算孤立節點
		const connectedNodes = new Set<string>();
		graph.links.forEach(link => {
			connectedNodes.add(typeof link.source === 'string' ? link.source : link.source.id);
			connectedNodes.add(typeof link.target === 'string' ? link.target : link.target.id);
		});
		const isolatedNodes = totalNotes - connectedNodes.size;

		// 計算強連通分量
		const stronglyConnectedComponents = this.findStronglyConnectedComponents(graph);

		return {
			totalNotes,
			totalDependencies,
			averageDependenciesPerNote,
			networkDensity: graph.metadata.density,
			stronglyConnectedComponents: stronglyConnectedComponents.length,
			isolatedNodes
		};
	}

	/**
	 * 計算網絡指標
	 */
	private calculateNetworkMetrics(graph: DependencyGraph): NetworkMetrics {
		const diameter = this.calculateDiameter(graph);
		const averagePathLength = this.calculateAveragePathLength(graph);
		const clusteringCoefficient = this.calculateGlobalClusteringCoefficient(graph);
		const centralityValues = dependencyService.calculateCentrality(graph);
		const centralityArray = Array.from(centralityValues.values());

		const centralityDistribution = {
			mean: centralityArray.reduce((sum, val) => sum + val, 0) / centralityArray.length || 0,
			median: this.calculateMedian(centralityArray),
			standardDeviation: this.calculateStandardDeviation(centralityArray)
		};

		const degreeDistribution = this.calculateDegreeDistribution(graph);

		return {
			diameter,
			averagePathLength,
			clusteringCoefficient,
			centralityDistribution,
			degreeDistribution
		};
	}

	/**
	 * 分析節點
	 */
	private analyzeNodes(graph: DependencyGraph, notes: Note[]): NodeAnalysis[] {
		const centrality = dependencyService.calculateCentrality(graph);
		const pageRank = dependencyService.calculatePageRank(graph);

		return graph.nodes.map(node => {
			const note = notes.find(n => n.id === node.id);
			const inDegree = this.calculateInDegree(node.id, graph);
			const outDegree = this.calculateOutDegree(node.id, graph);
			const totalDegree = inDegree + outDegree;

			const betweennessCentrality = centrality.get(node.id) || 0;
			const closenessCentrality = this.calculateClosenessCentrality(node.id, graph);
			const nodePageRank = pageRank.get(node.id) || 0;
			const clusteringCoefficient = this.calculateNodeClusteringCoefficient(node.id, graph);

			const role = this.determineNodeRole(node, inDegree, outDegree, betweennessCentrality);
			const riskLevel = this.assessNodeRisk(node, totalDegree, betweennessCentrality);

			return {
				noteId: node.id,
				noteTitle: note?.title || 'Unknown',
				inDegree,
				outDegree,
				totalDegree,
				betweennessCentrality,
				closenessCentrality,
				pageRank: nodePageRank,
				clusteringCoefficient,
				role,
				riskLevel
			};
		});
	}

	/**
	 * 分析聚類
	 */
	private analyzeCluster(graph: DependencyGraph, notes: Note[]): ClusterAnalysis[] {
		const communities = dependencyService.detectCommunities(graph);

		return communities.map((community, index) => {
			const clusterId = `cluster-${index + 1}`;
			const nodes = community.nodes;
			const size = nodes.length;

			// 計算聚類密度
			const internalLinks = graph.links.filter(link => {
				const sourceId = typeof link.source === 'string' ? link.source : link.source.id;
				const targetId = typeof link.target === 'string' ? link.target : link.target.id;
				return nodes.includes(sourceId) && nodes.includes(targetId);
			});

			const maxPossibleLinks = size * (size - 1);
			const density = maxPossibleLinks > 0 ? internalLinks.length / maxPossibleLinks : 0;

			// 計算凝聚力
			const cohesion = this.calculateCohesion(nodes, graph);

			// 提取關鍵詞
			const clusterNotes = notes.filter(note => nodes.includes(note.id));
			const keywords = this.extractKeywords(clusterNotes);
			const mainTopic = this.identifyMainTopic(clusterNotes);

			return {
				clusterId,
				nodes,
				size,
				density,
				cohesion,
				mainTopic,
				keywords
			};
		});
	}

	/**
	 * 評估風險
	 */
	private assessRisks(graph: DependencyGraph, notes: Note[]): RiskAssessment {
		const circularDependencies = this.findCircularDependencies(graph);
		const criticalNodes = this.identifyCriticalNodes(graph, notes);
		const fragileConnections = this.identifyFragileConnections(graph);

		// 計算整體風險分數
		const riskFactors = [
			circularDependencies.length * 0.3,
			criticalNodes.length * 0.4,
			fragileConnections.length * 0.2,
			(1 - graph.metadata.density) * 0.1 // 低密度增加風險
		];

		const overallRiskScore = Math.min(
			riskFactors.reduce((sum, factor) => sum + factor, 0),
			1
		);

		return {
			circularDependencies,
			criticalNodes,
			fragileConnections,
			overallRiskScore
		};
	}

	/**
	 * 生成建議
	 */
	private generateRecommendations(
		graph: DependencyGraph,
		notes: Note[],
		riskAssessment: RiskAssessment
	): Recommendation[] {
		const recommendations: Recommendation[] = [];

		// 結構建議
		if (riskAssessment.circularDependencies.length > 0) {
			recommendations.push({
				type: 'structure',
				priority: 'high',
				title: '解決循環依賴',
				description: '檢測到循環依賴，這可能導致理解困難和維護問題',
				actionItems: [
					'重新組織筆記結構',
					'將循環依賴拆分為更小的模塊',
					'考慮使用中介筆記來打破循環'
				],
				affectedNotes: riskAssessment.circularDependencies.flatMap(cycle => cycle.cycle)
			});
		}

		// 關鍵節點建議
		if (riskAssessment.criticalNodes.length > 0) {
			recommendations.push({
				type: 'risk',
				priority: 'medium',
				title: '關注關鍵筆記',
				description: '某些筆記在網絡中具有關鍵地位，需要特別關注',
				actionItems: [
					'為關鍵筆記創建備份',
					'考慮將關鍵內容分散到多個筆記',
					'定期檢查和更新關鍵筆記'
				],
				affectedNotes: riskAssessment.criticalNodes.map(node => node.noteId)
			});
		}

		// 組織建議
		if (graph.metadata.density < 0.1) {
			recommendations.push({
				type: 'organization',
				priority: 'low',
				title: '增強筆記連接',
				description: '網絡密度較低，考慮增加筆記間的連接',
				actionItems: ['添加相關筆記的交叉引用', '創建主題索引筆記', '使用標籤來建立隱式連接'],
				affectedNotes: []
			});
		}

		return recommendations;
	}

	// 輔助方法實現
	private findStronglyConnectedComponents(graph: DependencyGraph): string[][] {
		// Tarjan's algorithm implementation
		// 簡化實現，返回強連通分量
		return [];
	}

	private calculateDiameter(graph: DependencyGraph): number {
		// 計算網絡直徑
		return 0;
	}

	private calculateAveragePathLength(graph: DependencyGraph): number {
		// 計算平均路徑長度
		return 0;
	}

	private calculateGlobalClusteringCoefficient(graph: DependencyGraph): number {
		// 計算全局聚類係數
		return 0;
	}

	private calculateMedian(values: number[]): number {
		const sorted = values.sort((a, b) => a - b);
		const mid = Math.floor(sorted.length / 2);
		return sorted.length % 2 === 0 ? (sorted[mid - 1] + sorted[mid]) / 2 : sorted[mid];
	}

	private calculateStandardDeviation(values: number[]): number {
		const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
		const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
		return Math.sqrt(variance);
	}

	private calculateDegreeDistribution(graph: DependencyGraph): Record<number, number> {
		const distribution: Record<number, number> = {};

		graph.nodes.forEach(node => {
			const degree =
				this.calculateInDegree(node.id, graph) + this.calculateOutDegree(node.id, graph);
			distribution[degree] = (distribution[degree] || 0) + 1;
		});

		return distribution;
	}

	private calculateInDegree(nodeId: string, graph: DependencyGraph): number {
		return graph.links.filter(link => {
			const targetId = typeof link.target === 'string' ? link.target : link.target.id;
			return targetId === nodeId;
		}).length;
	}

	private calculateOutDegree(nodeId: string, graph: DependencyGraph): number {
		return graph.links.filter(link => {
			const sourceId = typeof link.source === 'string' ? link.source : link.source.id;
			return sourceId === nodeId;
		}).length;
	}

	private calculateClosenessCentrality(nodeId: string, graph: DependencyGraph): number {
		// 計算接近中心性
		return 0;
	}

	private calculateNodeClusteringCoefficient(nodeId: string, graph: DependencyGraph): number {
		// 計算節點聚類係數
		return 0;
	}

	private determineNodeRole(
		node: DependencyNode,
		inDegree: number,
		outDegree: number,
		betweenness: number
	): 'hub' | 'authority' | 'bridge' | 'peripheral' | 'normal' {
		if (betweenness > 0.1) return 'bridge';
		if (inDegree > 5) return 'authority';
		if (outDegree > 5) return 'hub';
		if (inDegree + outDegree < 2) return 'peripheral';
		return 'normal';
	}

	private assessNodeRisk(
		node: DependencyNode,
		degree: number,
		betweenness: number
	): 'low' | 'medium' | 'high' {
		if (betweenness > 0.1 || degree > 10) return 'high';
		if (betweenness > 0.05 || degree > 5) return 'medium';
		return 'low';
	}

	private calculateCohesion(nodes: string[], graph: DependencyGraph): number {
		// 計算聚類凝聚力
		return 0;
	}

	private extractKeywords(notes: Note[]): string[] {
		// 提取關鍵詞
		const allTags = notes.flatMap(note => note.tags);
		const tagCounts = allTags.reduce(
			(acc, tag) => {
				acc[tag] = (acc[tag] || 0) + 1;
				return acc;
			},
			{} as Record<string, number>
		);

		return Object.entries(tagCounts)
			.sort(([, a], [, b]) => b - a)
			.slice(0, 5)
			.map(([tag]) => tag);
	}

	private identifyMainTopic(notes: Note[]): string | undefined {
		if (notes.length === 0) return undefined;

		// 簡單實現：使用最常見的分類
		const categories = notes.map(note => note.category).filter(Boolean);
		if (categories.length === 0) return undefined;

		const categoryCounts = categories.reduce(
			(acc, category) => {
				acc[category!] = (acc[category!] || 0) + 1;
				return acc;
			},
			{} as Record<string, number>
		);

		return Object.entries(categoryCounts).sort(([, a], [, b]) => b - a)[0]?.[0];
	}

	private findCircularDependencies(graph: DependencyGraph): CircularDependency[] {
		// 查找循環依賴
		return [];
	}

	private identifyCriticalNodes(graph: DependencyGraph, notes: Note[]): CriticalNode[] {
		// 識別關鍵節點
		return [];
	}

	private identifyFragileConnections(graph: DependencyGraph): FragileConnection[] {
		// 識別脆弱連接
		return [];
	}
}

// 導出單例實例
export const dependencyAnalysisReportService = new DependencyAnalysisReportService();
