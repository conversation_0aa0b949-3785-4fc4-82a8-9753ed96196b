<script lang="ts">
	import { onMount } from 'svelte';
	import {
		Server,
		Play,
		Square,
		RefreshCw,
		Activity,
		CheckCircle,
		XCircle,
		AlertCircle,
		Clock,
		Terminal,
		Zap
	} from 'lucide-svelte';

	import Button from '$components/ui/Button.svelte';
	import Card from '$components/ui/Card.svelte';
	import { browser } from '$app/environment';

	// Props
	let {
		serverKey,
		serverConfig
	}: {
		serverKey: string;
		serverConfig: any;
	} = $props();

	// 狀態變數
	let serverStatus = $state<'running' | 'stopped' | 'starting' | 'stopping' | 'error'>('stopped');
	let lastPing = $state<Date | null>(null);
	let responseTime = $state<number | null>(null);
	let errorMessage = $state('');
	let isLoading = $state(false);
	let connectionHistory = $state<Array<{
		timestamp: Date;
		status: 'success' | 'error';
		responseTime?: number;
		error?: string;
	}>>([]);

	// 定時器
	let statusInterval: NodeJS.Timeout;
	let pingInterval: NodeJS.Timeout;

	onMount(() => {
		if (browser && serverConfig.enabled) {
			startMonitoring();
		}
	});

	// 使用 $effect 來管理清理邏輯
	$effect(() => {
		return () => {
			stopMonitoring();
		};
	});

	// 開始監控
	function startMonitoring() {
		// 每 10 秒檢查一次狀態
		statusInterval = setInterval(checkServerStatus, 10000);

		// 每 30 秒 ping 一次
		pingInterval = setInterval(pingServer, 30000);

		// 立即檢查一次
		checkServerStatus();
	}

	// 停止監控
	function stopMonitoring() {
		if (statusInterval) {
			clearInterval(statusInterval);
		}
		if (pingInterval) {
			clearInterval(pingInterval);
		}
	}

	// 檢查服務器狀態
	async function checkServerStatus() {
		if (!browser || !serverConfig.enabled) return;

		try {
			// 這裡應該實際檢查 MCP 服務器狀態
			// 目前使用模擬邏輯
			const isRunning = await simulateServerCheck();

			if (isRunning) {
				serverStatus = 'running';
				errorMessage = '';
			} else {
				serverStatus = 'stopped';
			}
		} catch (error) {
			serverStatus = 'error';
			errorMessage = error instanceof Error ? error.message : '未知錯誤';
		}
	}

	// 模擬服務器檢查
	async function simulateServerCheck(): Promise<boolean> {
		// 模擬網絡延遲
		await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 500));

		// 模擬 80% 的成功率
		return Math.random() > 0.2;
	}

	// Ping 服務器
	async function pingServer() {
		if (!browser || !serverConfig.enabled || serverStatus !== 'running') return;

		const startTime = Date.now();

		try {
			// 模擬 ping 操作
			await simulateServerCheck();

			const endTime = Date.now();
			responseTime = endTime - startTime;
			lastPing = new Date();

			// 記錄成功的連接
			connectionHistory.unshift({
				timestamp: new Date(),
				status: 'success',
				responseTime
			});
		} catch (error) {
			// 記錄失敗的連接
			connectionHistory.unshift({
				timestamp: new Date(),
				status: 'error',
				error: error instanceof Error ? error.message : '連接失敗'
			});
		}

		// 只保留最近 20 條記錄
		connectionHistory = connectionHistory.slice(0, 20);
	}

	// 啟動服務器
	async function startServer() {
		if (!browser) return;

		isLoading = true;
		serverStatus = 'starting';
		errorMessage = '';

		try {
			// 模擬啟動過程
			await new Promise(resolve => setTimeout(resolve, 2000));

			// 這裡應該實際啟動 MCP 服務器
			console.log(`Starting MCP server: ${serverConfig.command} ${serverConfig.args.join(' ')}`);

			serverStatus = 'running';
			startMonitoring();
		} catch (error) {
			serverStatus = 'error';
			errorMessage = error instanceof Error ? error.message : '啟動失敗';
		} finally {
			isLoading = false;
		}
	}

	// 停止服務器
	async function stopServer() {
		if (!browser) return;

		isLoading = true;
		serverStatus = 'stopping';
		errorMessage = '';

		try {
			// 模擬停止過程
			await new Promise(resolve => setTimeout(resolve, 1000));

			// 這裡應該實際停止 MCP 服務器
			console.log(`Stopping MCP server: ${serverKey}`);

			serverStatus = 'stopped';
			stopMonitoring();
		} catch (error) {
			serverStatus = 'error';
			errorMessage = error instanceof Error ? error.message : '停止失敗';
		} finally {
			isLoading = false;
		}
	}

	// 重啟服務器
	async function restartServer() {
		await stopServer();
		await new Promise(resolve => setTimeout(resolve, 1000));
		await startServer();
	}

	// 獲取狀態圖標
	function getStatusIcon(status: string) {
		switch (status) {
			case 'running':
				return CheckCircle;
			case 'stopped':
				return XCircle;
			case 'starting':
			case 'stopping':
				return RefreshCw;
			case 'error':
				return AlertCircle;
			default:
				return Server;
		}
	}

	// 獲取狀態顏色
	function getStatusColor(status: string) {
		switch (status) {
			case 'running':
				return 'text-green-600';
			case 'stopped':
				return 'text-gray-600';
			case 'starting':
			case 'stopping':
				return 'text-yellow-600';
			case 'error':
				return 'text-red-600';
			default:
				return 'text-gray-600';
		}
	}

	// 獲取狀態文本
	function getStatusText(status: string) {
		switch (status) {
			case 'running':
				return '運行中';
			case 'stopped':
				return '已停止';
			case 'starting':
				return '啟動中';
			case 'stopping':
				return '停止中';
			case 'error':
				return '錯誤';
			default:
				return '未知';
		}
	}

	// 格式化時間
	function formatTime(date: Date) {
		return date.toLocaleTimeString();
	}

	// 格式化響應時間
	function formatResponseTime(ms: number) {
		if (ms < 1000) {
			return `${ms}ms`;
		} else {
			return `${(ms / 1000).toFixed(1)}s`;
		}
	}

	// 響應式更新
	$effect(() => {
		if (serverConfig.enabled && browser) {
			if (statusInterval) {
				startMonitoring();
			}
		} else {
			stopMonitoring();
			serverStatus = 'stopped';
		}
	});
</script>

<Card class="p-4">
	<div class="flex items-center justify-between mb-4">
		<div class="flex items-center space-x-3">
			<svelte:component
				this={getStatusIcon(serverStatus)}
				class="h-5 w-5 {getStatusColor(serverStatus)} {serverStatus === 'starting' ||
				serverStatus === 'stopping'
					? 'animate-spin'
					: ''}"
			/>
			<div>
				<h3 class="font-medium">{serverConfig.name}</h3>
				<p class="text-sm text-muted-foreground">{getStatusText(serverStatus)}</p>
			</div>
		</div>

		<div class="flex items-center space-x-2">
			{#if serverStatus === 'stopped'}
				<Button
					variant="outline"
					size="sm"
					onclick={startServer}
					disabled={isLoading || !serverConfig.enabled}
				>
					<Play class="h-4 w-4 mr-1" />
					啟動
				</Button>
			{:else if serverStatus === 'running'}
				<Button variant="outline" size="sm" onclick={stopServer} disabled={isLoading}>
					<Square class="h-4 w-4 mr-1" />
					停止
				</Button>
				<Button variant="outline" size="sm" onclick={restartServer} disabled={isLoading}>
					<RefreshCw class="h-4 w-4 mr-1" />
					重啟
				</Button>
			{/if}
		</div>
	</div>

	<!-- 服務器信息 -->
	<div class="space-y-2 text-sm">
		<div class="flex items-center space-x-2">
			<Terminal class="h-4 w-4 text-muted-foreground" />
			<span class="text-muted-foreground">命令:</span>
			<code class="px-2 py-1 bg-muted rounded text-xs">
				{serverConfig.command}
				{serverConfig.args.join(' ')}
			</code>
		</div>

		{#if serverConfig.description}
			<p class="text-muted-foreground">{serverConfig.description}</p>
		{/if}
	</div>

	<!-- 錯誤信息 -->
	{#if errorMessage}
		<div class="mt-3 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-800">
			{errorMessage}
		</div>
	{/if}

	<!-- 運行狀態信息 -->
	{#if serverStatus === 'running'}
		<div class="mt-4 pt-4 border-t border-border">
			<div class="grid grid-cols-2 gap-4 text-sm">
				{#if lastPing}
					<div class="flex items-center space-x-2">
						<Clock class="h-4 w-4 text-muted-foreground" />
						<span class="text-muted-foreground">最後檢查:</span>
						<span>{formatTime(lastPing)}</span>
					</div>
				{/if}

				{#if responseTime !== null}
					<div class="flex items-center space-x-2">
						<Zap class="h-4 w-4 text-muted-foreground" />
						<span class="text-muted-foreground">響應時間:</span>
						<span>{formatResponseTime(responseTime)}</span>
					</div>
				{/if}
			</div>
		</div>
	{/if}

	<!-- 連接歷史 -->
	{#if connectionHistory.length > 0}
		<div class="mt-4 pt-4 border-t border-border">
			<h4 class="text-sm font-medium mb-2 flex items-center space-x-2">
				<Activity class="h-4 w-4" />
				<span>連接歷史</span>
			</h4>

			<div class="space-y-1 max-h-32 overflow-y-auto">
				{#each connectionHistory.slice(0, 10) as record}
					<div class="flex items-center justify-between text-xs">
						<div class="flex items-center space-x-2">
							{#if record.status === 'success'}
								<CheckCircle class="h-3 w-3 text-green-600" />
								<span class="text-green-600">成功</span>
							{:else}
								<XCircle class="h-3 w-3 text-red-600" />
								<span class="text-red-600">失敗</span>
							{/if}
							<span class="text-muted-foreground">{formatTime(record.timestamp)}</span>
						</div>

						{#if record.responseTime}
							<span class="text-muted-foreground">{formatResponseTime(record.responseTime)}</span>
						{:else if record.error}
							<span class="text-red-600 truncate max-w-32" title={record.error}>
								{record.error}
							</span>
						{/if}
					</div>
				{/each}
			</div>
		</div>
	{/if}
</Card>
