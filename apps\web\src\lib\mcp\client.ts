import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import type {
	CallToolRequest,
	ListToolsRequest,
	ListResourcesRequest,
	ReadResourceRequest
} from '@modelcontextprotocol/sdk/types.js';

/**
 * MCP 客戶端用於測試和與 Life Note MCP 服務器交互
 */
export class LifeNoteMCPClient {
	private client: Client;
	private isConnected = false;

	constructor() {
		this.client = new Client(
			{
				name: 'life-note-mcp-client',
				version: '1.0.0'
			},
			{
				capabilities: {}
			}
		);
	}

	/**
	 * 連接到 MCP 服務器
	 */
	async connect(command: string, args: string[] = []): Promise<void> {
		if (this.isConnected) {
			console.warn('[MCP Client] Already connected');
			return;
		}

		try {
			const transport = new StdioClientTransport({
				command,
				args
			});

			await this.client.connect(transport);
			this.isConnected = true;
			console.log('[MCP Client] Connected to MCP server');
		} catch (error) {
			console.error('[MCP Client] Failed to connect:', error);
			throw error;
		}
	}

	/**
	 * 斷開與 MCP 服務器的連接
	 */
	async disconnect(): Promise<void> {
		if (!this.isConnected) {
			return;
		}

		try {
			await this.client.close();
			this.isConnected = false;
			console.log('[MCP Client] Disconnected from MCP server');
		} catch (error) {
			console.error('[MCP Client] Error disconnecting:', error);
			throw error;
		}
	}

	/**
	 * 列出可用工具
	 */
	async listTools(): Promise<any> {
		this.ensureConnected();

		try {
			const request: ListToolsRequest = {
				method: 'tools/list',
				params: {}
			};

			const response = await this.client.request(request);
			return response.tools;
		} catch (error) {
			console.error('[MCP Client] Failed to list tools:', error);
			throw error;
		}
	}

	/**
	 * 調用工具
	 */
	async callTool(name: string, args: any = {}): Promise<any> {
		this.ensureConnected();

		try {
			const request: CallToolRequest = {
				method: 'tools/call',
				params: {
					name,
					arguments: args
				}
			};

			const response = await this.client.request(request);
			return response;
		} catch (error) {
			console.error(`[MCP Client] Failed to call tool "${name}":`, error);
			throw error;
		}
	}

	/**
	 * 列出可用資源
	 */
	async listResources(): Promise<any> {
		this.ensureConnected();

		try {
			const request: ListResourcesRequest = {
				method: 'resources/list',
				params: {}
			};

			const response = await this.client.request(request);
			return response.resources;
		} catch (error) {
			console.error('[MCP Client] Failed to list resources:', error);
			throw error;
		}
	}

	/**
	 * 讀取資源
	 */
	async readResource(uri: string): Promise<any> {
		this.ensureConnected();

		try {
			const request: ReadResourceRequest = {
				method: 'resources/read',
				params: {
					uri
				}
			};

			const response = await this.client.request(request);
			return response.contents;
		} catch (error) {
			console.error(`[MCP Client] Failed to read resource "${uri}":`, error);
			throw error;
		}
	}

	/**
	 * 檢查連接狀態
	 */
	isClientConnected(): boolean {
		return this.isConnected;
	}

	/**
	 * 確保客戶端已連接
	 */
	private ensureConnected(): void {
		if (!this.isConnected) {
			throw new Error('MCP client is not connected. Call connect() first.');
		}
	}

	// 便利方法 - 筆記管理
	async createNote(noteData: {
		title: string;
		content?: string;
		tags?: string[];
		categoryId?: string;
		priority?: 'low' | 'medium' | 'high' | 'urgent';
		status?: 'draft' | 'published' | 'archived';
	}): Promise<any> {
		return await this.callTool('create_note', noteData);
	}

	async updateNote(
		id: string,
		updates: {
			title?: string;
			content?: string;
			tags?: string[];
			categoryId?: string;
			priority?: 'low' | 'medium' | 'high' | 'urgent';
			status?: 'draft' | 'published' | 'archived';
		}
	): Promise<any> {
		return await this.callTool('update_note', { id, ...updates });
	}

	async deleteNote(id: string): Promise<any> {
		return await this.callTool('delete_note', { id });
	}

	async getNote(id: string): Promise<any> {
		return await this.callTool('get_note', { id });
	}

	async searchNotes(
		searchParams: {
			query?: string;
			tags?: string[];
			status?: 'draft' | 'published' | 'archived';
			priority?: 'low' | 'medium' | 'high' | 'urgent';
			categoryId?: string;
			limit?: number;
			offset?: number;
		} = {}
	): Promise<any> {
		return await this.callTool('search_notes', searchParams);
	}

	// 便利方法 - 依賴關係分析
	async analyzeDependencies(
		options: {
			noteIds?: string[];
			includeTagConnections?: boolean;
			includeCategoryConnections?: boolean;
			includeContentSimilarity?: boolean;
			similarityThreshold?: number;
			maxDistance?: number;
			excludeIsolatedNodes?: boolean;
		} = {}
	): Promise<any> {
		return await this.callTool('analyze_dependencies', options);
	}

	async getDependencyGraph(noteId: string, depth: number = 2): Promise<any> {
		return await this.callTool('get_dependency_graph', { noteId, depth });
	}

	// 便利方法 - 資源訪問
	async getAllNotes(
		options: {
			limit?: number;
			offset?: number;
			includeContent?: boolean;
		} = {}
	): Promise<any> {
		const params = new URLSearchParams();
		if (options.limit) params.set('limit', options.limit.toString());
		if (options.offset) params.set('offset', options.offset.toString());
		if (options.includeContent) params.set('includeContent', 'true');

		const uri = `notes://all${params.toString() ? '?' + params.toString() : ''}`;
		return await this.readResource(uri);
	}

	async getNotesStatistics(): Promise<any> {
		return await this.readResource('notes://statistics');
	}

	async getDependencyGraphResource(
		options: {
			includeIsolated?: boolean;
			similarityThreshold?: number;
		} = {}
	): Promise<any> {
		const params = new URLSearchParams();
		if (options.includeIsolated !== undefined) {
			params.set('includeIsolated', options.includeIsolated.toString());
		}
		if (options.similarityThreshold !== undefined) {
			params.set('similarityThreshold', options.similarityThreshold.toString());
		}

		const uri = `dependencies://graph${params.toString() ? '?' + params.toString() : ''}`;
		return await this.readResource(uri);
	}

	async getDependencyAnalysis(): Promise<any> {
		return await this.readResource('dependencies://analysis');
	}

	async getDependencyStatistics(): Promise<any> {
		return await this.readResource('dependencies://statistics');
	}
}

// 創建並導出客戶端實例
export const mcpClient = new LifeNoteMCPClient();

// 導出便利函數
export async function connectToMCPServer(command: string, args: string[] = []): Promise<void> {
	await mcpClient.connect(command, args);
}

export async function disconnectFromMCPServer(): Promise<void> {
	await mcpClient.disconnect();
}

export function isMCPClientConnected(): boolean {
	return mcpClient.isClientConnected();
}

/**
 * 測試 MCP 連接和基本功能
 */
export async function testMCPConnection(
	command: string,
	args: string[] = []
): Promise<{
	success: boolean;
	results: any[];
	errors: string[];
}> {
	const results: any[] = [];
	const errors: string[] = [];

	try {
		// 連接到服務器
		await mcpClient.connect(command, args);
		results.push({ step: 'connect', success: true });

		// 測試列出工具
		try {
			const tools = await mcpClient.listTools();
			results.push({ step: 'listTools', success: true, data: tools });
		} catch (error) {
			errors.push(`Failed to list tools: ${error}`);
		}

		// 測試列出資源
		try {
			const resources = await mcpClient.listResources();
			results.push({ step: 'listResources', success: true, data: resources });
		} catch (error) {
			errors.push(`Failed to list resources: ${error}`);
		}

		// 測試創建筆記
		try {
			const createResult = await mcpClient.createNote({
				title: 'MCP Test Note',
				content: 'This is a test note created via MCP',
				tags: ['test', 'mcp'],
				priority: 'medium',
				status: 'draft'
			});
			results.push({ step: 'createNote', success: true, data: createResult });
		} catch (error) {
			errors.push(`Failed to create note: ${error}`);
		}

		// 測試搜索筆記
		try {
			const searchResult = await mcpClient.searchNotes({
				query: 'test',
				limit: 5
			});
			results.push({ step: 'searchNotes', success: true, data: searchResult });
		} catch (error) {
			errors.push(`Failed to search notes: ${error}`);
		}

		// 斷開連接
		await mcpClient.disconnect();
		results.push({ step: 'disconnect', success: true });

		return {
			success: errors.length === 0,
			results,
			errors
		};
	} catch (error) {
		errors.push(`Connection failed: ${error}`);
		return {
			success: false,
			results,
			errors
		};
	}
}
