<script lang="ts">
	import { onMount } from 'svelte';
	import { Server, Bot, Zap, Code, Database, Settings, Wrench } from 'lucide-svelte';
	import { <PERSON><PERSON>, Card } from '$components/ui';
	import MCPDashboard from '$lib/components/mcp/MCPDashboardNew.svelte';

	let activeTab: 'dashboard' | 'tools' | 'resources' | 'docs' = 'dashboard';

	// MCP 工具文檔
	const toolDocs = [
		{
			name: 'create_note',
			description: '創建新筆記',
			parameters: [
				{ name: 'title', type: 'string', required: true, description: '筆記標題' },
				{ name: 'content', type: 'string', required: false, description: 'Markdown 內容' },
				{ name: 'tags', type: 'string[]', required: false, description: '標籤列表' },
				{
					name: 'priority',
					type: 'enum',
					required: false,
					description: '優先級：low, medium, high, urgent'
				},
				{
					name: 'status',
					type: 'enum',
					required: false,
					description: '狀態：draft, published, archived'
				}
			],
			example: `{
  "title": "我的新筆記",
  "content": "# 標題\\n\\n這是筆記內容",
  "tags": ["工作", "重要"],
  "priority": "high",
  "status": "draft"
}`
		},
		{
			name: 'search_notes',
			description: '搜索筆記',
			parameters: [
				{ name: 'query', type: 'string', required: false, description: '搜索關鍵字' },
				{ name: 'tags', type: 'string[]', required: false, description: '標籤過濾' },
				{ name: 'status', type: 'enum', required: false, description: '狀態過濾' },
				{ name: 'limit', type: 'number', required: false, description: '結果數量限制' }
			],
			example: `{
  "query": "重要會議",
  "tags": ["工作"],
  "status": "published",
  "limit": 10
}`
		},
		{
			name: 'analyze_dependencies',
			description: '分析筆記依賴關係',
			parameters: [
				{ name: 'noteIds', type: 'string[]', required: false, description: '要分析的筆記 ID 列表' },
				{
					name: 'includeTagConnections',
					type: 'boolean',
					required: false,
					description: '包含標籤連接'
				},
				{
					name: 'similarityThreshold',
					type: 'number',
					required: false,
					description: '相似性閾值 (0-1)'
				}
			],
			example: `{
  "includeTagConnections": true,
  "includeContentSimilarity": true,
  "similarityThreshold": 0.3
}`
		}
	];

	// MCP 資源文檔
	const resourceDocs = [
		{
			uri: 'notes://all',
			name: '所有筆記',
			description: '獲取所有筆記的列表，支援分頁',
			parameters: [
				{ name: 'limit', description: '每頁數量 (預設: 50)' },
				{ name: 'offset', description: '偏移量 (預設: 0)' },
				{ name: 'includeContent', description: '是否包含完整內容 (預設: false)' }
			],
			example: 'notes://all?limit=20&offset=0&includeContent=true'
		},
		{
			uri: 'notes://search',
			name: '筆記搜索',
			description: '搜索筆記資源',
			parameters: [
				{ name: 'query', description: '搜索關鍵字' },
				{ name: 'tag', description: '標籤過濾 (可多個)' },
				{ name: 'status', description: '狀態過濾' }
			],
			example: 'notes://search?query=會議&tag=工作&status=published'
		},
		{
			uri: 'dependencies://graph',
			name: '依賴關係圖',
			description: '完整的筆記依賴關係圖',
			parameters: [
				{ name: 'includeIsolated', description: '包含孤立節點 (預設: true)' },
				{ name: 'similarityThreshold', description: '相似性閾值 (預設: 0.3)' }
			],
			example: 'dependencies://graph?includeIsolated=false&similarityThreshold=0.5'
		}
	];
</script>

<div class="container mx-auto px-4 py-8">
	<!-- 標題和導航 -->
	<div class="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4 mb-6">
		<div>
			<h1 class="text-3xl font-bold flex items-center gap-2">
				<Bot class="h-8 w-8 text-primary" />
				MCP Server 管理
			</h1>
			<p class="text-muted-foreground mt-1">Model Context Protocol 服務器管理和 AI 整合</p>
		</div>
	</div>

	<!-- 標籤導航 -->
	<div class="flex items-center gap-1 mb-6 border-b">
		<Button
			variant={activeTab === 'dashboard' ? 'default' : 'ghost'}
			size="sm"
			on:click={() => (activeTab = 'dashboard')}
		>
			<Server class="h-4 w-4 mr-1" />
			控制台
		</Button>
		<Button
			variant={activeTab === 'tools' ? 'default' : 'ghost'}
			size="sm"
			on:click={() => (activeTab = 'tools')}
		>
			<Wrench class="h-4 w-4 mr-1" />
			工具文檔
		</Button>
		<Button
			variant={activeTab === 'resources' ? 'default' : 'ghost'}
			size="sm"
			on:click={() => (activeTab = 'resources')}
		>
			<Database class="h-4 w-4 mr-1" />
			資源文檔
		</Button>
		<Button
			variant={activeTab === 'docs' ? 'default' : 'ghost'}
			size="sm"
			on:click={() => (activeTab = 'docs')}
		>
			<Code class="h-4 w-4 mr-1" />
			使用指南
		</Button>
	</div>

	<!-- 內容區域 -->
	{#if activeTab === 'dashboard'}
		<MCPDashboard />
	{:else if activeTab === 'tools'}
		<div class="space-y-6">
			<div>
				<h2 class="text-2xl font-bold mb-2">MCP 工具文檔</h2>
				<p class="text-muted-foreground">Life Note MCP 服務器提供的所有工具及其使用方法</p>
			</div>

			<div class="grid gap-6">
				{#each toolDocs as tool}
					<Card class="p-6">
						<div class="flex items-center gap-2 mb-4">
							<Wrench class="h-5 w-5 text-primary" />
							<h3 class="text-lg font-semibold">{tool.name}</h3>
						</div>

						<p class="text-muted-foreground mb-4">{tool.description}</p>

						<div class="space-y-4">
							<div>
								<h4 class="font-medium mb-2">參數：</h4>
								<div class="space-y-2">
									{#each tool.parameters as param}
										<div class="flex items-start gap-3 p-3 bg-muted/50 rounded">
											<div class="flex-1">
												<div class="flex items-center gap-2">
													<code class="text-sm font-mono bg-background px-2 py-1 rounded">
														{param.name}
													</code>
													<span class="text-xs text-muted-foreground">
														{param.type}
													</span>
													{#if param.required}
														<span
															class="text-xs bg-destructive text-destructive-foreground px-1 rounded"
														>
															必需
														</span>
													{/if}
												</div>
												<p class="text-sm text-muted-foreground mt-1">
													{param.description}
												</p>
											</div>
										</div>
									{/each}
								</div>
							</div>

							<div>
								<h4 class="font-medium mb-2">使用範例：</h4>
								<pre class="text-sm bg-muted p-4 rounded overflow-x-auto"><code>{tool.example}</code
									></pre>
							</div>
						</div>
					</Card>
				{/each}
			</div>
		</div>
	{:else if activeTab === 'resources'}
		<div class="space-y-6">
			<div>
				<h2 class="text-2xl font-bold mb-2">MCP 資源文檔</h2>
				<p class="text-muted-foreground">Life Note MCP 服務器提供的所有資源及其訪問方法</p>
			</div>

			<div class="grid gap-6">
				{#each resourceDocs as resource}
					<Card class="p-6">
						<div class="flex items-center gap-2 mb-4">
							<Database class="h-5 w-5 text-primary" />
							<h3 class="text-lg font-semibold">{resource.name}</h3>
						</div>

						<p class="text-muted-foreground mb-4">{resource.description}</p>

						<div class="space-y-4">
							<div>
								<h4 class="font-medium mb-2">URI 格式：</h4>
								<code class="text-sm bg-muted px-3 py-2 rounded block">
									{resource.uri}
								</code>
							</div>

							<div>
								<h4 class="font-medium mb-2">查詢參數：</h4>
								<div class="space-y-2">
									{#each resource.parameters as param}
										<div class="flex items-start gap-3 p-3 bg-muted/50 rounded">
											<code class="text-sm font-mono bg-background px-2 py-1 rounded">
												{param.name}
											</code>
											<p class="text-sm text-muted-foreground">
												{param.description}
											</p>
										</div>
									{/each}
								</div>
							</div>

							<div>
								<h4 class="font-medium mb-2">使用範例：</h4>
								<code class="text-sm bg-muted px-3 py-2 rounded block">
									{resource.example}
								</code>
							</div>
						</div>
					</Card>
				{/each}
			</div>
		</div>
	{:else if activeTab === 'docs'}
		<div class="space-y-6">
			<div>
				<h2 class="text-2xl font-bold mb-2">MCP 使用指南</h2>
				<p class="text-muted-foreground">如何在 AI 助手中使用 Life Note MCP 服務器</p>
			</div>

			<div class="grid gap-6">
				<Card class="p-6">
					<h3 class="text-lg font-semibold mb-4 flex items-center gap-2">
						<Settings class="h-5 w-5" />
						配置 MCP 服務器
					</h3>

					<div class="space-y-4">
						<div>
							<h4 class="font-medium mb-2">1. 安裝依賴</h4>
							<pre class="text-sm bg-muted p-4 rounded overflow-x-auto"><code
									>npm install @modelcontextprotocol/sdk</code
								></pre>
						</div>

						<div>
							<h4 class="font-medium mb-2">2. 啟動服務器</h4>
							<pre class="text-sm bg-muted p-4 rounded overflow-x-auto"><code
									>node dist/mcp-server.js</code
								></pre>
						</div>

						<div>
							<h4 class="font-medium mb-2">3. 在 AI 助手中配置</h4>
							<p class="text-sm text-muted-foreground mb-2">
								將以下配置添加到您的 AI 助手的 MCP 設定中：
							</p>
							<pre class="text-sm bg-muted p-4 rounded overflow-x-auto"><code
									>{JSON.stringify(
										{
											mcpServers: {
												'life-note': {
													command: 'node',
													args: ['path/to/dist/mcp-server.js'],
													env: {}
												}
											}
										},
										null,
										2
									)}</code
								></pre>
						</div>
					</div>
				</Card>

				<Card class="p-6">
					<h3 class="text-lg font-semibold mb-4 flex items-center gap-2">
						<Code class="h-5 w-5" />
						使用範例
					</h3>

					<div class="space-y-4">
						<div>
							<h4 class="font-medium mb-2">創建筆記</h4>
							<p class="text-sm text-muted-foreground mb-2">在 AI 助手中，您可以這樣請求：</p>
							<div class="bg-muted p-4 rounded">
								<p class="text-sm italic">
									"請幫我創建一個標題為'會議記錄'的筆記，內容包含今天的重要討論點，並標記為'工作'和'會議'標籤。"
								</p>
							</div>
						</div>

						<div>
							<h4 class="font-medium mb-2">搜索筆記</h4>
							<div class="bg-muted p-4 rounded">
								<p class="text-sm italic">
									"幫我找到所有包含'項目計劃'關鍵字的筆記，並且狀態是已發布的。"
								</p>
							</div>
						</div>

						<div>
							<h4 class="font-medium mb-2">分析依賴關係</h4>
							<div class="bg-muted p-4 rounded">
								<p class="text-sm italic">
									"分析我的筆記之間的依賴關係，特別是與'產品開發'相關的筆記。"
								</p>
							</div>
						</div>
					</div>
				</Card>

				<Card class="p-6">
					<h3 class="text-lg font-semibold mb-4">支援的 AI 助手</h3>

					<div class="space-y-3">
						<div class="flex items-center gap-3">
							<div class="w-2 h-2 bg-green-500 rounded-full"></div>
							<span class="text-sm">Claude Desktop (Anthropic)</span>
						</div>
						<div class="flex items-center gap-3">
							<div class="w-2 h-2 bg-green-500 rounded-full"></div>
							<span class="text-sm">Cline (VS Code Extension)</span>
						</div>
						<div class="flex items-center gap-3">
							<div class="w-2 h-2 bg-yellow-500 rounded-full"></div>
							<span class="text-sm">其他支援 MCP 的 AI 助手</span>
						</div>
					</div>

					<p class="text-sm text-muted-foreground mt-4">
						更多 AI 助手正在添加 MCP 支援。請查看各自的文檔了解配置方法。
					</p>
				</Card>
			</div>
		</div>
	{/if}
</div>
