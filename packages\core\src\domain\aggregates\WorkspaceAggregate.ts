/* eslint-disable @typescript-eslint/no-unused-vars */
import { generateRandomString } from "@life-note/utils";

import {
  AggregateRoot,
  BusinessRuleViolationError,
} from "../../shared/Entity.js";
import { Note, NoteStatus } from "../entities/Note.js";
import { User, UserRole } from "../entities/User.js";
import { NoteId } from "../value-objects/NoteId.js";
import { UserId } from "../value-objects/UserId.js";
import { Tag } from "../value-objects/Tag.js";
import { Version } from "../value-objects/Version.js";

/**
 * 工作空間 ID 值對象
 */
export class WorkspaceId {
  private readonly _value: string;

  constructor(value: string) {
    if (!value || value.trim().length === 0) {
      throw new BusinessRuleViolationError("Workspace ID cannot be empty");
    }
    this._value = value.trim();
  }

  static generate(): WorkspaceId {
    return new WorkspaceId(generateRandomString(16));
  }

  static fromString(value: string): WorkspaceId {
    return new WorkspaceId(value);
  }

  get value(): string {
    return this._value;
  }

  equals(other: WorkspaceId): boolean {
    return this._value === other._value;
  }

  toString(): string {
    return this._value;
  }
}

/**
 * 工作空間成員角色
 */
export enum WorkspaceMemberRole {
  OWNER = "owner",
  ADMIN = "admin",
  EDITOR = "editor",
  VIEWER = "viewer",
}

/**
 * 工作空間成員
 */
export interface WorkspaceMember {
  userId: UserId;
  role: WorkspaceMemberRole;
  joinedAt: Date;
  invitedBy?: UserId;
  permissions: Set<string>;
}

/**
 * 工作空間設置
 */
export interface WorkspaceSettings {
  name: string;
  description?: string;
  isPublic: boolean;
  allowGuestAccess: boolean;
  defaultNoteVisibility: "public" | "private" | "members";
  enableVersionControl: boolean;
  enableDependencyTracking: boolean;
  enableCollaboration: boolean;
  maxMembers: number;
  retentionDays: number;
  customDomain?: string;
  branding: {
    logo?: string;
    primaryColor?: string;
    secondaryColor?: string;
  };
}

/**
 * 工作空間統計
 */
export interface WorkspaceStatistics {
  totalNotes: number;
  publishedNotes: number;
  draftNotes: number;
  archivedNotes: number;
  totalMembers: number;
  activeMembers: number;
  totalWords: number;
  totalDependencies: number;
  averageNotesPerMember: number;
  createdThisWeek: number;
  createdThisMonth: number;
  lastActivityDate: Date;
  mostActiveMembers: UserId[];
  popularTags: string[];
  popularCategories: string[];
}

/**
 * 工作空間聚合根
 * 管理工作空間及其成員、筆記、設置等
 */
export class WorkspaceAggregate extends AggregateRoot<WorkspaceId> {
  private _settings: WorkspaceSettings;
  private _members: Map<string, WorkspaceMember>;
  private _notes: Set<NoteId>;
  private _categories: Set<string>;
  private _tags: Set<Tag>;
  private _statistics: WorkspaceStatistics;
  private _ownerId: UserId;

  constructor(
    id: WorkspaceId,
    ownerId: UserId,
    settings: WorkspaceSettings,
    options: {
      members?: WorkspaceMember[];
      notes?: NoteId[];
      categories?: string[];
      tags?: Tag[];
      statistics?: Partial<WorkspaceStatistics>;
    } = {},
  ) {
    super(id);

    this._ownerId = ownerId;
    this._settings = settings;
    this._members = new Map();
    this._notes = new Set(options.notes || []);
    this._categories = new Set(options.categories || []);
    this._tags = new Set(options.tags || []);
    this._statistics = this.createDefaultStatistics(options.statistics);

    // 添加擁有者作為成員
    this.addMember(ownerId, WorkspaceMemberRole.OWNER);

    // 添加其他成員
    options.members?.forEach((member) => {
      if (!member.userId.equals(ownerId)) {
        this._members.set(member.userId.value, member);
      }
    });
  }

  /**
   * 創建新的工作空間
   */
  static create(
    name: string,
    ownerId: UserId,
    options: {
      description?: string;
      isPublic?: boolean;
      settings?: Partial<WorkspaceSettings>;
    } = {},
  ): WorkspaceAggregate {
    const id = WorkspaceId.generate();

    const defaultSettings: WorkspaceSettings = {
      name,
      description: options.description,
      isPublic: options.isPublic ?? false,
      allowGuestAccess: false,
      defaultNoteVisibility: "private",
      enableVersionControl: true,
      enableDependencyTracking: true,
      enableCollaboration: true,
      maxMembers: 50,
      retentionDays: 365,
      branding: {},
    };

    const settings = { ...defaultSettings, ...options.settings };

    return new WorkspaceAggregate(id, ownerId, settings);
  }

  /**
   * 更新工作空間設置
   */
  updateSettings(settings: Partial<WorkspaceSettings>): void {
    this._settings = { ...this._settings, ...settings };
    this.markAsModified();
  }

  /**
   * 添加成員
   */
  addMember(
    userId: UserId,
    role: WorkspaceMemberRole,
    invitedBy?: UserId,
  ): void {
    if (this._members.has(userId.value)) {
      throw new BusinessRuleViolationError(
        "User is already a member of this workspace",
      );
    }

    if (this._members.size >= this._settings.maxMembers) {
      throw new BusinessRuleViolationError(
        "Workspace has reached maximum member limit",
      );
    }

    const permissions = this.getDefaultPermissions(role);
    const member: WorkspaceMember = {
      userId,
      role,
      joinedAt: new Date(),
      invitedBy,
      permissions,
    };

    this._members.set(userId.value, member);
    this.updateStatistics({ totalMembers: this._members.size });
    this.markAsModified();
  }

  /**
   * 移除成員
   */
  removeMember(userId: UserId, removedBy: UserId): void {
    const member = this._members.get(userId.value);
    if (!member) {
      throw new BusinessRuleViolationError(
        "User is not a member of this workspace",
      );
    }

    // 不能移除擁有者
    if (member.role === WorkspaceMemberRole.OWNER) {
      throw new BusinessRuleViolationError("Cannot remove workspace owner");
    }

    // 檢查權限
    const remover = this._members.get(removedBy.value);
    if (!remover || !this.canRemoveMember(remover, member)) {
      throw new BusinessRuleViolationError(
        "Insufficient permissions to remove member",
      );
    }

    this._members.delete(userId.value);
    this.updateStatistics({ totalMembers: this._members.size });
    this.markAsModified();
  }

  /**
   * 更新成員角色
   */
  updateMemberRole(
    userId: UserId,
    newRole: WorkspaceMemberRole,
    updatedBy: UserId,
  ): void {
    const member = this._members.get(userId.value);
    if (!member) {
      throw new BusinessRuleViolationError(
        "User is not a member of this workspace",
      );
    }

    // 不能更改擁有者角色
    if (member.role === WorkspaceMemberRole.OWNER) {
      throw new BusinessRuleViolationError("Cannot change owner role");
    }

    // 檢查權限
    const updater = this._members.get(updatedBy.value);
    if (!updater || !this.canUpdateMemberRole(updater, member, newRole)) {
      throw new BusinessRuleViolationError(
        "Insufficient permissions to update member role",
      );
    }

    member.role = newRole;
    member.permissions = this.getDefaultPermissions(newRole);
    this.markAsModified();
  }

  /**
   * 添加筆記到工作空間
   */
  addNote(noteId: NoteId): void {
    this._notes.add(noteId);
    this.updateStatistics({ totalNotes: this._notes.size });
    this.markAsModified();
  }

  /**
   * 從工作空間移除筆記
   */
  removeNote(noteId: NoteId): void {
    if (this._notes.delete(noteId)) {
      this.updateStatistics({ totalNotes: this._notes.size });
      this.markAsModified();
    }
  }

  /**
   * 添加分類
   */
  addCategory(category: string): void {
    this._categories.add(category);
    this.markAsModified();
  }

  /**
   * 移除分類
   */
  removeCategory(category: string): void {
    this._categories.delete(category);
    this.markAsModified();
  }

  /**
   * 添加標籤
   */
  addTag(tag: Tag): void {
    this._tags.add(tag);
    this.markAsModified();
  }

  /**
   * 移除標籤
   */
  removeTag(tag: Tag): void {
    this._tags.delete(tag);
    this.markAsModified();
  }

  /**
   * 檢查用戶是否為成員
   */
  isMember(userId: UserId): boolean {
    return this._members.has(userId.value);
  }

  /**
   * 檢查用戶是否有特定權限
   */
  hasPermission(userId: UserId, permission: string): boolean {
    const member = this._members.get(userId.value);
    return member ? member.permissions.has(permission) : false;
  }

  /**
   * 獲取成員角色
   */
  getMemberRole(userId: UserId): WorkspaceMemberRole | null {
    const member = this._members.get(userId.value);
    return member ? member.role : null;
  }

  /**
   * 更新統計信息
   */
  updateStatistics(stats: Partial<WorkspaceStatistics>): void {
    this._statistics = { ...this._statistics, ...stats };
    this.markAsModified();
  }

  /**
   * 獲取默認權限
   */
  private getDefaultPermissions(role: WorkspaceMemberRole): Set<string> {
    const permissions = new Set<string>();

    switch (role) {
      case WorkspaceMemberRole.OWNER:
        permissions.add("manage_workspace");
        permissions.add("manage_members");
        permissions.add("manage_settings");
      // fall through
      case WorkspaceMemberRole.ADMIN:
        permissions.add("delete_notes");
        permissions.add("manage_categories");
        permissions.add("manage_tags");
      // fall through
      case WorkspaceMemberRole.EDITOR:
        permissions.add("create_notes");
        permissions.add("edit_notes");
        permissions.add("publish_notes");
      // fall through
      case WorkspaceMemberRole.VIEWER:
        permissions.add("view_notes");
        permissions.add("comment_notes");
        break;
    }

    return permissions;
  }

  /**
   * 檢查是否可以移除成員
   */
  private canRemoveMember(
    remover: WorkspaceMember,
    target: WorkspaceMember,
  ): boolean {
    if (remover.role === WorkspaceMemberRole.OWNER) return true;
    if (
      remover.role === WorkspaceMemberRole.ADMIN &&
      target.role !== WorkspaceMemberRole.OWNER
    )
      return true;
    return false;
  }

  /**
   * 檢查是否可以更新成員角色
   */
  private canUpdateMemberRole(
    updater: WorkspaceMember,
    target: WorkspaceMember,
    newRole: WorkspaceMemberRole,
  ): boolean {
    if (updater.role === WorkspaceMemberRole.OWNER) return true;
    if (
      updater.role === WorkspaceMemberRole.ADMIN &&
      target.role !== WorkspaceMemberRole.OWNER &&
      newRole !== WorkspaceMemberRole.OWNER
    )
      return true;
    return false;
  }

  /**
   * 創建默認統計信息
   */
  private createDefaultStatistics(
    partial?: Partial<WorkspaceStatistics>,
  ): WorkspaceStatistics {
    const now = new Date();
    const defaults: WorkspaceStatistics = {
      totalNotes: 0,
      publishedNotes: 0,
      draftNotes: 0,
      archivedNotes: 0,
      totalMembers: 0,
      activeMembers: 0,
      totalWords: 0,
      totalDependencies: 0,
      averageNotesPerMember: 0,
      createdThisWeek: 0,
      createdThisMonth: 0,
      lastActivityDate: now,
      mostActiveMembers: [],
      popularTags: [],
      popularCategories: [],
    };

    return { ...defaults, ...partial };
  }

  // Getters
  get settings(): WorkspaceSettings {
    return { ...this._settings };
  }

  get members(): WorkspaceMember[] {
    return Array.from(this._members.values());
  }

  get notes(): NoteId[] {
    return Array.from(this._notes);
  }

  get categories(): string[] {
    return Array.from(this._categories);
  }

  get tags(): Tag[] {
    return Array.from(this._tags);
  }

  get statistics(): WorkspaceStatistics {
    return { ...this._statistics };
  }

  get ownerId(): UserId {
    return this._ownerId;
  }

  /**
   * 轉換為普通對象
   */
  toPlainObject(): Record<string, unknown> {
    return {
      id: this._id.value,
      ownerId: this._ownerId.value,
      settings: this._settings,
      members: this.members.map((member) => ({
        userId: member.userId.value,
        role: member.role,
        joinedAt: member.joinedAt.toISOString(),
        invitedBy: member.invitedBy?.value,
        permissions: Array.from(member.permissions),
      })),
      notes: this.notes.map((id) => id.value),
      categories: this.categories,
      tags: this.tags.map((tag) => tag.toPlainObject()),
      statistics: this._statistics,
      createdAt: this._createdAt.toISOString(),
      updatedAt: this._updatedAt.toISOString(),
    };
  }
}
