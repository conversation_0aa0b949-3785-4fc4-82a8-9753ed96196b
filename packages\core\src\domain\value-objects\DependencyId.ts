import { generateRandomString } from "@life-note/utils";

import {
  ValueObject,
  BusinessRuleViolationError,
} from "../../shared/Entity.js";

/**
 * 依賴關係 ID 值對象
 * 代表依賴關係的唯一標識符
 */
export class DependencyId extends ValueObject {
  private readonly _value: string;

  constructor(value: string) {
    super();
    this.validateId(value);
    this._value = value;
  }

  /**
   * 生成新的依賴關係 ID
   */
  static generate(): DependencyId {
    return new DependencyId(`dep_${generateRandomString(16)}`);
  }

  /**
   * 從字符串創建依賴關係 ID
   */
  static fromString(value: string): DependencyId {
    return new DependencyId(value);
  }

  /**
   * 驗證 ID 格式
   */
  private validateId(value: string): void {
    if (!value || value.trim().length === 0) {
      throw new BusinessRuleViolationError("Dependency ID cannot be empty");
    }

    if (value.length < 8 || value.length > 64) {
      throw new BusinessRuleViolationError(
        "Dependency ID must be between 8 and 64 characters",
      );
    }

    // 檢查是否只包含字母數字字符和下劃線
    if (!/^[a-zA-Z0-9_]+$/.test(value)) {
      throw new BusinessRuleViolationError(
        "Dependency ID can only contain alphanumeric characters and underscores",
      );
    }
  }

  get value(): string {
    return this._value;
  }

  equals(other: ValueObject): boolean {
    if (!(other instanceof DependencyId)) {
      return false;
    }
    return this._value === other._value;
  }

  hashCode(): string {
    return this._value;
  }

  toString(): string {
    return this._value;
  }

  toPlainObject(): Record<string, unknown> {
    return {
      value: this._value,
    };
  }

  static fromPlainObject(data: Record<string, unknown>): DependencyId {
    if (typeof data.value !== "string") {
      throw new BusinessRuleViolationError("Invalid DependencyId data");
    }
    return new DependencyId(data.value);
  }
}
