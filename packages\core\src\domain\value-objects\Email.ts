import {
  ValueObject,
  BusinessRuleViolationError,
} from "../../shared/Entity.js";

/**
 * 電子郵件值對象
 * 代表有效的電子郵件地址
 */
export class Email extends ValueObject {
  private readonly _value: string;

  constructor(value: string) {
    super();
    this.validateEmail(value);
    this._value = value.toLowerCase().trim();
  }

  /**
   * 創建電子郵件值對象
   */
  static create(value: string): Email {
    return new Email(value);
  }

  /**
   * 從字符串創建電子郵件
   */
  static fromString(value: string): Email {
    return new Email(value);
  }

  /**
   * 驗證電子郵件格式
   */
  private validateEmail(value: string): void {
    if (!value || value.trim().length === 0) {
      throw new BusinessRuleViolationError("Email cannot be empty");
    }

    const trimmedValue = value.trim();

    // 基本長度檢查
    if (trimmedValue.length > 254) {
      throw new BusinessRuleViolationError(
        "Email address is too long (max 254 characters)",
      );
    }

    // 電子郵件格式驗證
    const emailRegex =
      /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

    if (!emailRegex.test(trimmedValue)) {
      throw new BusinessRuleViolationError("Invalid email format");
    }

    // 檢查本地部分長度（@符號前的部分）
    const [localPart, domainPart] = trimmedValue.split("@");

    if (localPart.length > 64) {
      throw new BusinessRuleViolationError(
        "Email local part is too long (max 64 characters)",
      );
    }

    if (localPart.length === 0) {
      throw new BusinessRuleViolationError("Email local part cannot be empty");
    }

    // 檢查域名部分
    if (domainPart.length > 253) {
      throw new BusinessRuleViolationError(
        "Email domain part is too long (max 253 characters)",
      );
    }

    if (domainPart.length === 0) {
      throw new BusinessRuleViolationError("Email domain part cannot be empty");
    }

    // 檢查是否包含連續的點
    if (trimmedValue.includes("..")) {
      throw new BusinessRuleViolationError(
        "Email cannot contain consecutive dots",
      );
    }

    // 檢查是否以點開始或結束
    if (localPart.startsWith(".") || localPart.endsWith(".")) {
      throw new BusinessRuleViolationError(
        "Email local part cannot start or end with a dot",
      );
    }

    // 檢查域名是否包含至少一個點
    if (!domainPart.includes(".")) {
      throw new BusinessRuleViolationError(
        "Email domain must contain at least one dot",
      );
    }

    // 檢查域名是否以點開始或結束
    if (domainPart.startsWith(".") || domainPart.endsWith(".")) {
      throw new BusinessRuleViolationError(
        "Email domain cannot start or end with a dot",
      );
    }
  }

  /**
   * 獲取本地部分（@符號前的部分）
   */
  get localPart(): string {
    return this._value.split("@")[0];
  }

  /**
   * 獲取域名部分（@符號後的部分）
   */
  get domainPart(): string {
    return this._value.split("@")[1];
  }

  /**
   * 獲取頂級域名
   */
  get topLevelDomain(): string {
    const parts = this.domainPart.split(".");
    return parts[parts.length - 1];
  }

  /**
   * 檢查是否為特定域名
   */
  isFromDomain(domain: string): boolean {
    return this.domainPart.toLowerCase() === domain.toLowerCase();
  }

  /**
   * 檢查是否為企業郵箱（非常見免費郵箱服務）
   */
  isBusinessEmail(): boolean {
    const commonFreeProviders = [
      "gmail.com",
      "yahoo.com",
      "hotmail.com",
      "outlook.com",
      "live.com",
      "msn.com",
      "aol.com",
      "icloud.com",
      "me.com",
      "mac.com",
      "protonmail.com",
      "tutanota.com",
    ];

    return !commonFreeProviders.includes(this.domainPart.toLowerCase());
  }

  /**
   * 生成顯示用的遮罩郵箱（隱私保護）
   */
  toMaskedString(): string {
    const [local, domain] = this._value.split("@");

    if (local.length <= 2) {
      return `${local[0]}*@${domain}`;
    }

    const maskedLocal = `${local[0]}${"*".repeat(local.length - 2)}${local[local.length - 1]}`;
    return `${maskedLocal}@${domain}`;
  }

  get value(): string {
    return this._value;
  }

  equals(other: ValueObject): boolean {
    if (!(other instanceof Email)) {
      return false;
    }
    return this._value === other._value;
  }

  hashCode(): string {
    return this._value;
  }

  toString(): string {
    return this._value;
  }

  toPlainObject(): Record<string, unknown> {
    return {
      value: this._value,
      localPart: this.localPart,
      domainPart: this.domainPart,
      topLevelDomain: this.topLevelDomain,
      isBusinessEmail: this.isBusinessEmail(),
    };
  }

  static fromPlainObject(data: Record<string, unknown>): Email {
    if (typeof data.value !== "string") {
      throw new BusinessRuleViolationError("Invalid Email data");
    }

    return new Email(data.value);
  }
}
