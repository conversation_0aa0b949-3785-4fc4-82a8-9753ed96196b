import {
  createHash,
  randomBytes,
  createCipheriv,
  createDecipheriv,
} from "crypto";

/**
 * 生成隨機字符串
 */
export function generateRandomString(length: number = 32): string {
  return randomBytes(length).toString("hex");
}

/**
 * 生成 SHA-256 哈希
 */
export function sha256(data: string): string {
  return createHash("sha256").update(data).digest("hex");
}

/**
 * 生成 MD5 哈希
 */
export function md5(data: string): string {
  return createHash("md5").update(data).digest("hex");
}

/**
 * AES-256-GCM 加密
 */
export function encrypt(
  text: string,
  key: string,
): { encrypted: string; iv: string; tag: string } {
  const iv = randomBytes(16);
  const cipher = createCipheriv("aes-256-gcm", Buffer.from(key, "hex"), iv);

  let encrypted = cipher.update(text, "utf8", "hex");
  encrypted += cipher.final("hex");

  const tag = cipher.getAuthTag();

  return {
    encrypted,
    iv: iv.toString("hex"),
    tag: tag.toString("hex"),
  };
}

/**
 * AES-256-GCM 解密
 */
export function decrypt(
  encryptedData: { encrypted: string; iv: string; tag: string },
  key: string,
): string {
  const decipher = createDecipheriv(
    "aes-256-gcm",
    Buffer.from(key, "hex"),
    Buffer.from(encryptedData.iv, "hex"),
  );
  decipher.setAuthTag(Buffer.from(encryptedData.tag, "hex"));

  let decrypted = decipher.update(encryptedData.encrypted, "hex", "utf8");
  decrypted += decipher.final("utf8");

  return decrypted;
}

/**
 * 生成 AES-256 密鑰
 */
export function generateEncryptionKey(): string {
  return randomBytes(32).toString("hex");
}

/**
 * 安全比較兩個字符串（防止時序攻擊）
 */
export function safeCompare(a: string, b: string): boolean {
  if (a.length !== b.length) {
    return false;
  }

  let result = 0;
  for (let i = 0; i < a.length; i++) {
    result |= a.charCodeAt(i) ^ b.charCodeAt(i);
  }

  return result === 0;
}

/**
 * 生成文件指紋（用於依賴關係檢測）
 */
export function generateFileFingerprint(content: string): string {
  return sha256(content);
}

/**
 * 數據脫敏工具
 */
export class DataSanitizer {
  /**
   * 脫敏信用卡號
   */
  static sanitizeCreditCard(text: string): string {
    return text.replace(
      /\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b/g,
      "[CARD_NUMBER]",
    );
  }

  /**
   * 脫敏電子郵件
   */
  static sanitizeEmail(text: string): string {
    return text.replace(
      /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
      "[EMAIL]",
    );
  }

  /**
   * 脫敏電話號碼
   */
  static sanitizePhone(text: string): string {
    return text.replace(/\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/g, "[PHONE]");
  }

  /**
   * 脫敏身份證號
   */
  static sanitizeIdNumber(text: string): string {
    return text.replace(/\b\d{15}|\d{17}[\dXx]\b/g, "[ID_NUMBER]");
  }

  /**
   * 綜合脫敏
   */
  static sanitizeAll(text: string): string {
    let sanitized = text;
    sanitized = this.sanitizeCreditCard(sanitized);
    sanitized = this.sanitizeEmail(sanitized);
    sanitized = this.sanitizePhone(sanitized);
    sanitized = this.sanitizeIdNumber(sanitized);
    return sanitized;
  }
}
