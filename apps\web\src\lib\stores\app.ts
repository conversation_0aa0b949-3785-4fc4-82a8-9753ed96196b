import { writable } from 'svelte/store';
import { browser } from '$app/environment';

export interface AppState {
	loading: boolean;
	error: string | null;
	sidebarOpen: boolean;
	initialized: boolean;
	version: string;
	buildTime: string;
}

const initialState: AppState = {
	loading: false,
	error: null,
	sidebarOpen: true,
	initialized: false,
	version: '1.0.0',
	buildTime: new Date().toISOString()
};

function createAppStore() {
	const { subscribe, set, update } = writable<AppState>(initialState);

	return {
		subscribe,

		// Initialize app
		async initialize() {
			if (!browser) return;

			update(state => ({ ...state, loading: true }));

			try {
				// Load saved preferences
				const savedSidebarState = localStorage.getItem('sidebarOpen');
				if (savedSidebarState !== null) {
					update(state => ({
						...state,
						sidebarOpen: JSON.parse(savedSidebarState)
					}));
				}

				// Initialize other services here
				// await noteStore.initialize();
				// await categoryStore.initialize();

				update(state => ({
					...state,
					loading: false,
					initialized: true
				}));
			} catch (error) {
				console.error('Failed to initialize app:', error);
				update(state => ({
					...state,
					loading: false,
					error: error instanceof Error ? error.message : 'Unknown error'
				}));
			}
		},

		// Loading state
		setLoading(loading: boolean) {
			update(state => ({ ...state, loading }));
		},

		// Error handling
		setError(error: string | null) {
			update(state => ({ ...state, error }));
		},

		clearError() {
			update(state => ({ ...state, error: null }));
		},

		// Sidebar management
		toggleSidebar() {
			update(state => {
				const newSidebarState = !state.sidebarOpen;

				// Save to localStorage
				if (browser) {
					localStorage.setItem('sidebarOpen', JSON.stringify(newSidebarState));
				}

				return { ...state, sidebarOpen: newSidebarState };
			});
		},

		setSidebarOpen(open: boolean) {
			update(state => {
				// Save to localStorage
				if (browser) {
					localStorage.setItem('sidebarOpen', JSON.stringify(open));
				}

				return { ...state, sidebarOpen: open };
			});
		},

		// Reset app state
		reset() {
			set(initialState);
			if (browser) {
				localStorage.removeItem('sidebarOpen');
			}
		}
	};
}

export const appStore = createAppStore();
