import { json } from '@sveltejs/kit';
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './$types';

const GEMINI_BASE_URL = 'https://generativelanguage.googleapis.com/v1beta';
const GEMINI_MODEL = 'gemini-1.5-flash';

export const POST: RequestHandler = async ({ request }) => {
	try {
		const { apiKey, messages, options = {} } = await request.json();

		if (!apiKey) {
			return json({ error: 'API Key is required' }, { status: 400 });
		}

		const url = `${GEMINI_BASE_URL}/models/${GEMINI_MODEL}:generateContent?key=${apiKey}`;

		const payload = {
			contents: messages,
			generationConfig: {
				maxOutputTokens: options.maxOutputTokens || 2048,
				temperature: options.temperature || 0.7,
				topP: options.topP || 0.8,
				topK: options.topK || 40
			}
		};

		console.log('Proxying request to Gemini API');

		const response = await fetch(url, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify(payload)
		});

		if (!response.ok) {
			const errorText = await response.text();
			console.error('Gemini API error response:', errorText);

			let errorMessage = `HTTP ${response.status}`;
			try {
				const errorJson = JSON.parse(errorText);
				errorMessage = errorJson.error?.message || errorText;
			} catch {
				errorMessage = errorText;
			}

			return json({ error: `Gemini API error: ${errorMessage}` }, { status: response.status });
		}

		const result = await response.json();
		console.log('Gemini API response received successfully');
		
		return json(result);
	} catch (error) {
		console.error('Gemini proxy error:', error);
		return json(
			{ error: error instanceof Error ? error.message : 'Internal server error' },
			{ status: 500 }
		);
	}
};
