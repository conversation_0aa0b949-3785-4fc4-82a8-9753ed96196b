<script lang="ts">
	import { onMount } from 'svelte';
	import {
		Share2,
		Grid,
		List,
		BarChart3,
		Settings,
		Download,
		RefreshCw,
		Activity
	} from 'lucide-svelte';
	import { <PERSON><PERSON>, <PERSON> } from '$components/ui';
	import DependencyGraph from '$lib/components/dependency/DependencyGraph.svelte';
	import DependencyControls from '$lib/components/dependency/DependencyControls.svelte';
	import NodeDetails from '$lib/components/dependency/NodeDetails.svelte';
	import AdvancedDependencyAnalysis from '$lib/components/dependencies/AdvancedDependencyAnalysis.svelte';
	import {
		dependencyStore,
		filteredNodes,
		graphMetadata,
		selectedNodeDetails,
		graphStats
	} from '$stores/dependency';
	import { noteStore } from '$stores/notes';

	// 組件引用
	let graphComponent: DependencyGraph;

	// 本地狀態
	let viewMode: 'graph' | 'list' | 'stats' | 'analysis' | 'advanced' = 'graph';
	let showNodeDetails = false;
	let analysisMode: 'communities' | 'importance' | 'paths' = 'communities';
	let pathAnalysis = { source: '', target: '', paths: [] };
	let communities = new Map();
	let nodeImportance = new Map();
	let isRunningAnalysis = false;

	// 響應式狀態
	$: graph = $dependencyStore.graph;
	$: isAnalyzing = $dependencyStore.isAnalyzing;
	$: error = $dependencyStore.error;
	$: selectedNode = $dependencyStore.selectedNode;
	$: options = $dependencyStore.options;
	$: showLabels = $dependencyStore.showLabels;
	$: showLegend = $dependencyStore.showLegend;
	$: interactive = $dependencyStore.interactive;

	// Mock 數據用於演示
	const mockNotes = [
		{
			id: '1',
			title: '前端架構設計',
			content:
				'本文討論現代前端架構的設計原則，包括 [[組件化開發]] 和 [[狀態管理]]。參考 @typescript-guide 和 #架構設計。',
			excerpt: '討論現代前端架構的設計原則...',
			status: 'published',
			priority: 'high',
			tags: [{ name: '前端' }, { name: '架構' }, { name: '設計' }],
			authorId: 'user-1',
			categoryId: 'tech',
			createdAt: new Date(Date.now() - 86400000 * 7),
			updatedAt: new Date(Date.now() - 86400000 * 2)
		},
		{
			id: '2',
			title: '組件化開發',
			content:
				'組件化開發是現代前端開發的核心理念。本文介紹 [[React組件]] 和 [[Vue組件]] 的最佳實踐。',
			excerpt: '組件化開發是現代前端開發的核心理念...',
			status: 'published',
			priority: 'medium',
			tags: [{ name: '前端' }, { name: '組件' }, { name: 'React' }],
			authorId: 'user-1',
			categoryId: 'tech',
			createdAt: new Date(Date.now() - 86400000 * 5),
			updatedAt: new Date(Date.now() - 86400000 * 1)
		},
		{
			id: '3',
			title: '狀態管理',
			content:
				'狀態管理是複雜應用的關鍵。討論 Redux、Zustand 和 Svelte Stores 的使用場景。與 [[前端架構設計]] 密切相關。',
			excerpt: '狀態管理是複雜應用的關鍵...',
			status: 'draft',
			priority: 'medium',
			tags: [{ name: '前端' }, { name: '狀態管理' }, { name: 'Redux' }],
			authorId: 'user-1',
			categoryId: 'tech',
			createdAt: new Date(Date.now() - 86400000 * 3),
			updatedAt: new Date(Date.now() - 86400000 * 1)
		},
		{
			id: '4',
			title: 'TypeScript 指南',
			content: 'TypeScript 提供靜態類型檢查，提升代碼質量。本指南涵蓋基礎語法和高級特性。',
			excerpt: 'TypeScript 提供靜態類型檢查...',
			status: 'published',
			priority: 'high',
			tags: [{ name: 'TypeScript' }, { name: '程式設計' }, { name: '類型' }],
			authorId: 'user-1',
			categoryId: 'tech',
			createdAt: new Date(Date.now() - 86400000 * 10),
			updatedAt: new Date(Date.now() - 86400000 * 3)
		},
		{
			id: '5',
			title: 'React組件',
			content: 'React 組件的設計模式和最佳實踐。包括函數組件、Hooks 和性能優化技巧。',
			excerpt: 'React 組件的設計模式和最佳實踐...',
			status: 'published',
			priority: 'medium',
			tags: [{ name: 'React' }, { name: '組件' }, { name: 'Hooks' }],
			authorId: 'user-1',
			categoryId: 'tech',
			createdAt: new Date(Date.now() - 86400000 * 4),
			updatedAt: new Date(Date.now() - 86400000 * 2)
		}
	];

	onMount(async () => {
		// 初始化 mock 數據
		noteStore.setNotes(mockNotes);

		// 分析依賴關係
		await dependencyStore.analyzeDependencies(mockNotes);
	});

	// 事件處理
	function handleNodeClick(event: CustomEvent) {
		const node = event.detail.node;
		dependencyStore.selectNode(node);
		showNodeDetails = true;
	}

	function handleNodeHover(event: CustomEvent) {
		const node = event.detail.node;
		dependencyStore.highlightNode(node?.id || null);
	}

	function handleLinkClick(event: CustomEvent) {
		console.log('Link clicked:', event.detail.link);
	}

	function handleOptionsChange(event: CustomEvent) {
		dependencyStore.updateOptions(event.detail.options);
	}

	function handleToggleLabels(event: CustomEvent) {
		dependencyStore.toggleLabels();
	}

	function handleToggleLegend(event: CustomEvent) {
		dependencyStore.toggleLegend();
	}

	function handleToggleInteractive(event: CustomEvent) {
		dependencyStore.toggleInteractive();
	}

	function handleCenterGraph() {
		if (graphComponent) {
			graphComponent.centerGraph();
		}
	}

	function handleFitToView() {
		if (graphComponent) {
			graphComponent.fitToView();
		}
	}

	function handleRefresh() {
		dependencyStore.analyzeDependencies(mockNotes);
	}

	function handleCloseNodeDetails() {
		showNodeDetails = false;
		dependencyStore.selectNode(null);
	}

	function handleViewNote(event: CustomEvent) {
		console.log('View note:', event.detail.noteId);
		// 這裡可以導航到筆記詳情頁面
	}

	function handleEditNote(event: CustomEvent) {
		console.log('Edit note:', event.detail.noteId);
		// 這裡可以導航到筆記編輯頁面
	}

	// 高級分析功能
	async function runCommunityAnalysis() {
		if (!graph) return;

		isRunningAnalysis = true;
		try {
			const { dependencyService } = await import('$lib/services/dependencyService');
			communities = await dependencyService.detectCommunities(graph);
			analysisMode = 'communities';
		} catch (error) {
			console.error('Community analysis failed:', error);
		} finally {
			isRunningAnalysis = false;
		}
	}

	async function runImportanceAnalysis() {
		if (!graph) return;

		isRunningAnalysis = true;
		try {
			const { dependencyService } = await import('$lib/services/dependencyService');
			nodeImportance = await dependencyService.calculateNodeImportance(graph);
			analysisMode = 'importance';
		} catch (error) {
			console.error('Importance analysis failed:', error);
		} finally {
			isRunningAnalysis = false;
		}
	}

	async function runPathAnalysis() {
		if (!graph || !pathAnalysis.source || !pathAnalysis.target) return;

		isRunningAnalysis = true;
		try {
			const { dependencyService } = await import('$lib/services/dependencyService');
			pathAnalysis.paths = await dependencyService.analyzeDependencyPaths(
				graph,
				pathAnalysis.source,
				pathAnalysis.target
			);
			analysisMode = 'paths';
		} catch (error) {
			console.error('Path analysis failed:', error);
		} finally {
			isRunningAnalysis = false;
		}
	}

	function exportAnalysisResults() {
		const results = {
			graph: {
				nodes: graph?.nodes.length || 0,
				links: graph?.links.length || 0,
				metadata: graph?.metadata
			},
			communities: Array.from(communities.entries()),
			nodeImportance: Array.from(nodeImportance.entries()),
			pathAnalysis: pathAnalysis.paths,
			timestamp: new Date().toISOString()
		};

		const blob = new Blob([JSON.stringify(results, null, 2)], { type: 'application/json' });
		const url = URL.createObjectURL(blob);
		const a = document.createElement('a');
		a.href = url;
		a.download = `dependency-analysis-${new Date().toISOString().split('T')[0]}.json`;
		a.click();
		URL.revokeObjectURL(url);
	}

	function handleHighlightConnections(event: CustomEvent) {
		if (graphComponent) {
			graphComponent.highlightNode(event.detail.nodeId);
		}
	}

	function exportGraph() {
		// 導出圖形數據
		if (graph) {
			const dataStr = JSON.stringify(graph, null, 2);
			const dataBlob = new Blob([dataStr], { type: 'application/json' });
			const url = URL.createObjectURL(dataBlob);
			const link = document.createElement('a');
			link.href = url;
			link.download = 'dependency-graph.json';
			link.click();
			URL.revokeObjectURL(url);
		}
	}
</script>

<div class="container mx-auto px-4 py-8">
	<!-- 標題和控制 -->
	<div class="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4 mb-6">
		<div>
			<h1 class="text-3xl font-bold flex items-center gap-2">
				<Share2 class="h-8 w-8 text-primary" />
				依賴關係可視化
			</h1>
			<p class="text-muted-foreground mt-1">探索筆記間的關聯和依賴關係</p>
		</div>

		<div class="flex items-center gap-2">
			<!-- 視圖切換 -->
			<div class="flex items-center border rounded-lg">
				<Button
					variant={viewMode === 'graph' ? 'default' : 'ghost'}
					size="sm"
					onclick={() => (viewMode = 'graph')}
				>
					<Share2 class="h-4 w-4" />
				</Button>
				<Button
					variant={viewMode === 'list' ? 'default' : 'ghost'}
					size="sm"
					onclick={() => (viewMode = 'list')}
				>
					<List class="h-4 w-4" />
				</Button>
				<Button
					variant={viewMode === 'stats' ? 'default' : 'ghost'}
					size="sm"
					onclick={() => (viewMode = 'stats')}
				>
					<BarChart3 class="h-4 w-4" />
				</Button>
				<Button
					variant={viewMode === 'analysis' ? 'default' : 'ghost'}
					size="sm"
					onclick={() => (viewMode = 'analysis')}
				>
					<Settings class="h-4 w-4" />
				</Button>
				<Button
					variant={viewMode === 'advanced' ? 'default' : 'ghost'}
					size="sm"
					onclick={() => (viewMode = 'advanced')}
				>
					<Activity class="h-4 w-4" />
				</Button>
			</div>

			<Button variant="outline" size="sm" onclick={handleRefresh} disabled={isAnalyzing}>
				<RefreshCw class="h-4 w-4 mr-1 {isAnalyzing ? 'animate-spin' : ''}" />
				{isAnalyzing ? '分析中...' : '重新分析'}
			</Button>

			<Button variant="outline" size="sm" onclick={exportGraph} disabled={!graph}>
				<Download class="h-4 w-4 mr-1" />
				導出
			</Button>
		</div>
	</div>

	<!-- 錯誤提示 -->
	{#if error}
		<Card class="p-4 mb-6 bg-destructive/10 border-destructive/20">
			<div class="flex items-center gap-2 text-destructive">
				<span class="font-medium">分析失敗：</span>
				<span>{error}</span>
				<Button variant="ghost" size="sm" on:click={() => dependencyStore.clearError()}>
					<span class="sr-only">關閉</span>
					×
				</Button>
			</div>
		</Card>
	{/if}

	<div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
		<!-- 控制面板 -->
		<div class="xl:col-span-1">
			<DependencyControls
				{options}
				{showLabels}
				{showLegend}
				{interactive}
				metadata={$graphMetadata}
				on:optionsChange={handleOptionsChange}
				on:toggleLabels={handleToggleLabels}
				on:toggleLegend={handleToggleLegend}
				on:toggleInteractive={handleToggleInteractive}
				on:centerGraph={handleCenterGraph}
				on:fitToView={handleFitToView}
				on:refresh={handleRefresh}
			/>
		</div>

		<!-- 主要內容區域 -->
		<div class="xl:col-span-3">
			{#if viewMode === 'graph'}
				<!-- 圖形視圖 -->
				{#if graph && graph.nodes.length > 0}
					<Card class="p-0 overflow-hidden">
						<DependencyGraph
							bind:this={graphComponent}
							{graph}
							width={800}
							height={600}
							{showLabels}
							{showLegend}
							{interactive}
							on:nodeClick={handleNodeClick}
							on:nodeHover={handleNodeHover}
							on:linkClick={handleLinkClick}
						/>
					</Card>
				{:else if isAnalyzing}
					<Card class="p-12 text-center">
						<div
							class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"
						></div>
						<p class="text-muted-foreground">正在分析依賴關係...</p>
					</Card>
				{:else}
					<Card class="p-12 text-center">
						<Share2 class="h-12 w-12 mx-auto mb-4 opacity-50" />
						<h3 class="text-lg font-semibold mb-2">沒有依賴關係數據</h3>
						<p class="text-muted-foreground mb-4">請確保有筆記數據，然後點擊「重新分析」按鈕</p>
						<Button onclick={handleRefresh}>
							<RefreshCw class="h-4 w-4 mr-2" />
							重新分析
						</Button>
					</Card>
				{/if}
			{:else if viewMode === 'list'}
				<!-- 列表視圖 -->
				<Card class="p-4">
					<h3 class="text-lg font-semibold mb-4">節點列表</h3>
					{#if $filteredNodes.length > 0}
						<div class="space-y-2">
							{#each $filteredNodes as node}
								<div
									class="flex items-center justify-between p-3 border rounded-lg hover:bg-accent cursor-pointer"
									onclick={() => handleNodeClick({ detail: { node } })}
								>
									<div>
										<div class="font-medium">{node.title}</div>
										<div class="text-sm text-muted-foreground">
											{node.status} • {node.priority} • 大小: {Math.round(node.size)}
										</div>
									</div>
									<div class="text-sm text-muted-foreground">
										群組 {node.group}
									</div>
								</div>
							{/each}
						</div>
					{:else}
						<p class="text-muted-foreground text-center py-8">沒有節點數據</p>
					{/if}
				</Card>
			{:else if viewMode === 'stats'}
				<!-- 統計視圖 -->
				<div class="space-y-4">
					{#if $graphStats}
						<div class="grid grid-cols-2 md:grid-cols-4 gap-4">
							<Card class="p-4 text-center">
								<div class="text-2xl font-bold text-primary">{$graphStats.totalNodes}</div>
								<div class="text-sm text-muted-foreground">總節點</div>
							</Card>
							<Card class="p-4 text-center">
								<div class="text-2xl font-bold text-primary">{$graphStats.totalLinks}</div>
								<div class="text-sm text-muted-foreground">總連接</div>
							</Card>
							<Card class="p-4 text-center">
								<div class="text-2xl font-bold text-primary">
									{$graphStats.avgCentrality.toFixed(1)}
								</div>
								<div class="text-sm text-muted-foreground">平均中心性</div>
							</Card>
							<Card class="p-4 text-center">
								<div class="text-2xl font-bold text-primary">
									{($graphStats.density * 100).toFixed(1)}%
								</div>
								<div class="text-sm text-muted-foreground">圖密度</div>
							</Card>
						</div>

						<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
							<!-- 狀態分布 -->
							<Card class="p-4">
								<h4 class="font-medium mb-3">狀態分布</h4>
								<div class="space-y-2">
									{#each Object.entries($graphStats.nodesByStatus) as [status, count]}
										<div class="flex justify-between">
											<span class="capitalize">{status}</span>
											<span class="font-medium">{count}</span>
										</div>
									{/each}
								</div>
							</Card>

							<!-- 連接類型分布 -->
							<Card class="p-4">
								<h4 class="font-medium mb-3">連接類型</h4>
								<div class="space-y-2">
									{#each Object.entries($graphStats.linksByType) as [type, count]}
										<div class="flex justify-between">
											<span class="capitalize">{type}</span>
											<span class="font-medium">{count}</span>
										</div>
									{/each}
								</div>
							</Card>
						</div>
					{:else}
						<Card class="p-12 text-center">
							<BarChart3 class="h-12 w-12 mx-auto mb-4 opacity-50" />
							<p class="text-muted-foreground">沒有統計數據</p>
						</Card>
					{/if}
				</div>
			{:else if viewMode === 'analysis'}
				<!-- 高級分析視圖 -->
				<div class="space-y-6">
					<div class="flex items-center justify-between">
						<h3 class="text-lg font-semibold">高級分析</h3>
						<Button variant="outline" size="sm" on:click={exportAnalysisResults} disabled={!graph}>
							<Download class="h-4 w-4 mr-1" />
							導出分析結果
						</Button>
					</div>

					<!-- 分析工具選項 -->
					<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
						<Card class="p-4">
							<h4 class="font-medium mb-3">社群檢測</h4>
							<p class="text-sm text-muted-foreground mb-3">識別筆記中的相關群組和主題聚類</p>
							<Button
								size="sm"
								onclick={runCommunityAnalysis}
								disabled={isRunningAnalysis || !graph}
								class="w-full"
							>
								{isRunningAnalysis && analysisMode === 'communities' ? '分析中...' : '開始分析'}
							</Button>
						</Card>

						<Card class="p-4">
							<h4 class="font-medium mb-3">重要性分析</h4>
							<p class="text-sm text-muted-foreground mb-3">計算每個筆記在知識網絡中的重要程度</p>
							<Button
								size="sm"
								onclick={runImportanceAnalysis}
								disabled={isRunningAnalysis || !graph}
								class="w-full"
							>
								{isRunningAnalysis && analysisMode === 'importance' ? '分析中...' : '開始分析'}
							</Button>
						</Card>

						<Card class="p-4">
							<h4 class="font-medium mb-3">路徑分析</h4>
							<p class="text-sm text-muted-foreground mb-3">查找兩個筆記之間的連接路徑</p>
							<div class="space-y-2 mb-3">
								<select
									bind:value={pathAnalysis.source}
									class="w-full px-2 py-1 text-sm border rounded"
								>
									<option value="">選擇起點</option>
									{#if graph}
										{#each graph.nodes as node}
											<option value={node.id}>{node.title}</option>
										{/each}
									{/if}
								</select>
								<select
									bind:value={pathAnalysis.target}
									class="w-full px-2 py-1 text-sm border rounded"
								>
									<option value="">選擇終點</option>
									{#if graph}
										{#each graph.nodes as node}
											<option value={node.id}>{node.title}</option>
										{/each}
									{/if}
								</select>
							</div>
							<Button
								size="sm"
								onclick={runPathAnalysis}
								disabled={isRunningAnalysis ||
									!graph ||
									!pathAnalysis.source ||
									!pathAnalysis.target}
								class="w-full"
							>
								{isRunningAnalysis && analysisMode === 'paths' ? '分析中...' : '查找路徑'}
							</Button>
						</Card>
					</div>

					<!-- 分析結果 -->
					{#if analysisMode === 'communities' && communities.size > 0}
						<Card class="p-6">
							<h4 class="font-medium mb-4">社群檢測結果</h4>
							<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
								{#each Array.from(communities.entries()) as [communityId, nodeIds]}
									<div class="border rounded-lg p-3">
										<h5 class="font-medium mb-2">群組 {communityId.split('-')[1]}</h5>
										<div class="space-y-1">
											{#each nodeIds as nodeId}
												{@const node = graph?.nodes.find(n => n.id === nodeId)}
												{#if node}
													<div class="text-sm text-muted-foreground truncate">
														{node.title}
													</div>
												{/if}
											{/each}
										</div>
										<div class="text-xs text-muted-foreground mt-2">
											{nodeIds.length} 個筆記
										</div>
									</div>
								{/each}
							</div>
						</Card>
					{/if}

					{#if analysisMode === 'importance' && nodeImportance.size > 0}
						<Card class="p-6">
							<h4 class="font-medium mb-4">重要性分析結果</h4>
							<div class="space-y-2">
								{#each Array.from(nodeImportance.entries())
									.sort((a, b) => b[1] - a[1])
									.slice(0, 10) as [nodeId, importance]}
									{@const node = graph?.nodes.find(n => n.id === nodeId)}
									{#if node}
										<div class="flex items-center justify-between p-2 border rounded">
											<span class="font-medium">{node.title}</span>
											<div class="flex items-center space-x-2">
												<div class="w-20 bg-muted rounded-full h-2">
													<div
														class="bg-primary h-2 rounded-full"
														style="width: {(importance * 100).toFixed(1)}%"
													></div>
												</div>
												<span class="text-sm text-muted-foreground">
													{(importance * 100).toFixed(1)}%
												</span>
											</div>
										</div>
									{/if}
								{/each}
							</div>
						</Card>
					{/if}

					{#if analysisMode === 'paths' && pathAnalysis.paths.length > 0}
						<Card class="p-6">
							<h4 class="font-medium mb-4">路徑分析結果</h4>
							<div class="space-y-3">
								{#each pathAnalysis.paths as path, index}
									<div class="border rounded-lg p-3">
										<h5 class="font-medium mb-2">路徑 {index + 1}</h5>
										<div class="flex items-center space-x-2 text-sm">
											{#each path as nodeId, i}
												{@const node = graph?.nodes.find(n => n.id === nodeId)}
												{#if node}
													<span class="px-2 py-1 bg-muted rounded">{node.title}</span>
													{#if i < path.length - 1}
														<span class="text-muted-foreground">→</span>
													{/if}
												{/if}
											{/each}
										</div>
										<div class="text-xs text-muted-foreground mt-2">
											長度: {path.length} 步
										</div>
									</div>
								{/each}
							</div>
						</Card>
					{/if}
				</div>
			{:else if viewMode === 'advanced'}
				<!-- 高級依賴關係分析視圖 -->
				<AdvancedDependencyAnalysis />
			{/if}
		</div>
	</div>
</div>

<!-- 節點詳情面板 -->
<NodeDetails
	node={$selectedNodeDetails?.node || null}
	note={$selectedNodeDetails?.note || null}
	{graph}
	bind:show={showNodeDetails}
	on:close={handleCloseNodeDetails}
	on:viewNote={handleViewNote}
	on:editNote={handleEditNote}
	on:highlightConnections={handleHighlightConnections}
/>
