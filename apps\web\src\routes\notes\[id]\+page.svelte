<script lang="ts">
	import { page } from '$app/stores';
	import { goto } from '$app/navigation';
	import { onMount } from 'svelte';
	import {
		ArrowLeft,
		Edit,
		Trash2,
		Share,
		Star,
		Clock,
		Tag,
		User,
		Calendar,
		Eye,
		EyeOff,
		MoreVertical,
		Copy,
		Download,
		History
	} from 'lucide-svelte';
	import { But<PERSON>, Card } from '$components/ui';
	import MarkdownRenderer from '$lib/components/editor/MarkdownRenderer.svelte';
	import TagList from '$lib/components/notes/TagList.svelte';
	import NoteVersionHistory from '$lib/components/notes/NoteVersionHistory.svelte';
	import { noteStore } from '$stores/notes';
	import type { Note } from '$types';

	// 路由參數
	$: noteId = $page.params.id;

	// 狀態變數
	let note: Note | null = null;
	let isLoading = true;
	let error: string | null = null;
	let showVersionHistory = false;
	let showDeleteConfirm = false;
	let isPreviewMode = true;

	// Mock 數據用於演示
	const mockNote: Note = {
		id: '1',
		title: '歡迎使用 Life Note',
		content: `# 歡迎使用 Life Note

Life Note 是一個功能強大的知識管理系統，專為現代知識工作者設計。

## 主要功能

### 📝 Markdown 編輯
- 支援完整的 Markdown 語法
- 實時預覽功能
- 語法高亮顯示
- 快捷鍵支援

### 🔗 依賴關係追蹤
- 自動檢測筆記間的關聯
- 視覺化依賴關係圖
- 智能推薦相關內容

### 🏷️ 標籤系統
- 靈活的標籤分類
- 標籤自動建議
- 多維度過濾

### 📊 版本控制
- 完整的版本歷史
- 版本比較功能
- 一鍵回滾

## 開始使用

1. 點擊「新增筆記」創建您的第一個筆記
2. 使用 Markdown 語法編寫內容
3. 添加標籤進行分類
4. 探索依賴關係功能

> 💡 **提示**：使用 \`Ctrl+S\` 快速保存筆記

## 聯繫我們

如果您有任何問題或建議，歡迎聯繫我們！`,
		excerpt: '這是您的第一個筆記。Life Note 是一個功能強大的知識管理系統...',
		status: 'published',
		priority: 'high',
		tags: [{ name: '歡迎' }, { name: '開始' }, { name: '指南' }],
		authorId: 'user-1',
		categoryId: 'getting-started',
		createdAt: new Date(Date.now() - 86400000 * 7),
		updatedAt: new Date(Date.now() - 86400000 * 2)
	};

	onMount(async () => {
		try {
			isLoading = true;
			// 從存儲中載入筆記
			note = await noteStore.getNote(noteId);
		} catch (err) {
			error = err instanceof Error ? err.message : '載入筆記失敗';
		} finally {
			isLoading = false;
		}
	});

	function handleBack() {
		goto('/notes');
	}

	function handleEdit() {
		if (note) {
			goto(`/notes/${note.id}/edit`);
		}
	}

	function handleDelete() {
		showDeleteConfirm = true;
	}

	async function confirmDelete() {
		if (note) {
			try {
				// await noteStore.deleteNote(note.id);
				goto('/notes');
			} catch (err) {
				error = err instanceof Error ? err.message : '刪除筆記失敗';
			}
		}
		showDeleteConfirm = false;
	}

	function handleShare() {
		if (note) {
			navigator.clipboard.writeText(window.location.href);
			// 顯示成功提示
		}
	}

	function handleCopyContent() {
		if (note) {
			navigator.clipboard.writeText(note.content);
			// 顯示成功提示
		}
	}

	function handleDownload() {
		if (note) {
			const blob = new Blob([note.content], { type: 'text/markdown' });
			const url = URL.createObjectURL(blob);
			const a = document.createElement('a');
			a.href = url;
			a.download = `${note.title}.md`;
			a.click();
			URL.revokeObjectURL(url);
		}
	}

	function toggleVersionHistory() {
		showVersionHistory = !showVersionHistory;
	}

	function togglePreviewMode() {
		isPreviewMode = !isPreviewMode;
	}

	function getStatusColor(status: string): string {
		switch (status) {
			case 'draft':
				return 'bg-yellow-100 text-yellow-800';
			case 'published':
				return 'bg-green-100 text-green-800';
			case 'archived':
				return 'bg-gray-100 text-gray-800';
			default:
				return 'bg-gray-100 text-gray-800';
		}
	}

	function getPriorityColor(priority: string): string {
		switch (priority) {
			case 'urgent':
				return 'bg-red-100 text-red-800';
			case 'high':
				return 'bg-orange-100 text-orange-800';
			case 'medium':
				return 'bg-blue-100 text-blue-800';
			case 'low':
				return 'bg-gray-100 text-gray-800';
			default:
				return 'bg-gray-100 text-gray-800';
		}
	}

	function formatDate(date: Date | string): string {
		const d = typeof date === 'string' ? new Date(date) : date;
		return new Intl.DateTimeFormat('zh-TW', {
			year: 'numeric',
			month: 'long',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit'
		}).format(d);
	}
</script>

<svelte:head>
	<title>{note?.title || '載入中...'} - Life Note</title>
	<meta name="description" content={note?.excerpt || ''} />
</svelte:head>

<div class="container mx-auto px-4 py-8">
	{#if isLoading}
		<div class="flex items-center justify-center min-h-96">
			<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
		</div>
	{:else if error}
		<Card class="p-8 text-center">
			<h2 class="text-xl font-semibold text-destructive mb-2">載入失敗</h2>
			<p class="text-muted-foreground mb-4">{error}</p>
			<Button on:click={handleBack}>返回筆記列表</Button>
		</Card>
	{:else if note}
		<!-- 標題欄 -->
		<div class="flex items-center justify-between mb-6">
			<Button variant="ghost" on:click={handleBack} class="flex items-center gap-2">
				<ArrowLeft class="h-4 w-4" />
				返回
			</Button>

			<div class="flex items-center gap-2">
				<Button variant="outline" size="sm" on:click={togglePreviewMode}>
					{#if isPreviewMode}
						<EyeOff class="h-4 w-4 mr-1" />
						原始碼
					{:else}
						<Eye class="h-4 w-4 mr-1" />
						預覽
					{/if}
				</Button>

				<Button variant="outline" size="sm" on:click={toggleVersionHistory}>
					<History class="h-4 w-4 mr-1" />
					版本歷史
				</Button>

				<Button variant="outline" size="sm" on:click={handleCopyContent}>
					<Copy class="h-4 w-4 mr-1" />
					複製
				</Button>

				<Button variant="outline" size="sm" on:click={handleDownload}>
					<Download class="h-4 w-4 mr-1" />
					下載
				</Button>

				<Button variant="outline" size="sm" on:click={handleShare}>
					<Share class="h-4 w-4 mr-1" />
					分享
				</Button>

				<Button size="sm" on:click={handleEdit}>
					<Edit class="h-4 w-4 mr-1" />
					編輯
				</Button>

				<Button variant="destructive" size="sm" on:click={handleDelete}>
					<Trash2 class="h-4 w-4" />
				</Button>
			</div>
		</div>

		<div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
			<!-- 主要內容 -->
			<div class="lg:col-span-3">
				<Card class="p-6">
					<!-- 筆記標題 -->
					<div class="mb-6">
						<h1 class="text-3xl font-bold mb-4">{note.title}</h1>

						<div class="flex items-center gap-4 text-sm text-muted-foreground">
							<div class="flex items-center gap-1">
								<Calendar class="h-4 w-4" />
								<span>創建於 {formatDate(note.createdAt)}</span>
							</div>
							<div class="flex items-center gap-1">
								<Clock class="h-4 w-4" />
								<span>更新於 {formatDate(note.updatedAt)}</span>
							</div>
						</div>
					</div>

					<!-- 筆記內容 -->
					<div class="prose prose-lg max-w-none">
						{#if isPreviewMode}
							<MarkdownRenderer content={note.content} />
						{:else}
							<pre class="whitespace-pre-wrap bg-muted p-4 rounded-lg text-sm">{note.content}</pre>
						{/if}
					</div>
				</Card>
			</div>

			<!-- 側邊欄 -->
			<div class="lg:col-span-1 space-y-4">
				<!-- 筆記信息 -->
				<Card class="p-4">
					<h3 class="font-semibold mb-3">筆記信息</h3>

					<div class="space-y-3">
						<div>
							<span class="text-sm text-muted-foreground">狀態</span>
							<div class="mt-1">
								<span
									class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {getStatusColor(
										note.status
									)}"
								>
									{note.status === 'draft'
										? '草稿'
										: note.status === 'published'
											? '已發布'
											: '已歸檔'}
								</span>
							</div>
						</div>

						<div>
							<span class="text-sm text-muted-foreground">優先級</span>
							<div class="mt-1">
								<span
									class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {getPriorityColor(
										note.priority
									)}"
								>
									{note.priority === 'urgent'
										? '緊急'
										: note.priority === 'high'
											? '高'
											: note.priority === 'medium'
												? '中'
												: '低'}
								</span>
							</div>
						</div>

						<div>
							<span class="text-sm text-muted-foreground">字數</span>
							<div class="mt-1 text-sm font-medium">
								{note.content.length} 字符
							</div>
						</div>
					</div>
				</Card>

				<!-- 標籤 -->
				{#if note.tags.length > 0}
					<Card class="p-4">
						<h3 class="font-semibold mb-3 flex items-center gap-2">
							<Tag class="h-4 w-4" />
							標籤
						</h3>
						<TagList tags={note.tags} />
					</Card>
				{/if}
			</div>
		</div>

		<!-- 版本歷史面板 -->
		{#if showVersionHistory}
			<div class="mt-6">
				<NoteVersionHistory {note} on:close={() => (showVersionHistory = false)} />
			</div>
		{/if}

		<!-- 刪除確認對話框 -->
		{#if showDeleteConfirm}
			<div class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
				<Card class="p-6 max-w-md mx-4">
					<h3 class="text-lg font-semibold mb-2">確認刪除</h3>
					<p class="text-muted-foreground mb-4">
						您確定要刪除筆記「{note.title}」嗎？此操作無法撤銷。
					</p>
					<div class="flex gap-2 justify-end">
						<Button variant="outline" on:click={() => (showDeleteConfirm = false)}>取消</Button>
						<Button variant="destructive" on:click={confirmDelete}>刪除</Button>
					</div>
				</Card>
			</div>
		{/if}
	{/if}
</div>
