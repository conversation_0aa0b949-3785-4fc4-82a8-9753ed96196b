<script lang="ts">
	import { onMount, createEventDispatcher } from 'svelte';
	import * as d3 from 'd3';
	import type {
		DependencyGraph,
		DependencyNode,
		DependencyLink
	} from '$lib/services/dependencyService';

	let {
		graph,
		width = 800,
		height = 600,
		showLabels = true,
		showLegend = true,
		interactive = true
	}: {
		graph: DependencyGraph;
		width?: number;
		height?: number;
		showLabels?: boolean;
		showLegend?: boolean;
		interactive?: boolean;
	} = $props();

	const dispatch = createEventDispatcher<{
		nodeClick: { node: DependencyNode };
		nodeHover: { node: DependencyNode | null };
		linkClick: { link: DependencyLink };
	}>();

	let container = $state<HTMLDivElement>();
	let svg = $state<d3.Selection<SVGSVGElement, unknown, null, undefined>>();
	let simulation = $state<d3.Simulation<DependencyNode, DependencyLink>>();

	// 顏色配置
	const colorScheme = {
		nodes: {
			1: '#ef4444', // urgent - 紅色
			2: '#f97316', // high - 橙色
			3: '#eab308', // draft - 黃色
			4: '#22c55e', // published - 綠色
			5: '#6b7280' // default - 灰色
		},
		links: {
			reference: '#3b82f6', // 藍色
			tag: '#8b5cf6', // 紫色
			category: '#06b6d4', // 青色
			similar: '#84cc16' // 綠色
		}
	};

	onMount(() => {
		initializeVisualization();
	});

	// 使用 $effect 來管理清理邏輯
	$effect(() => {
		return () => {
			if (simulation) {
				simulation.stop();
			}
		};
	});

	$effect(() => {
		if (svg && graph) {
			updateVisualization();
		}
	});

	function initializeVisualization() {
		// 清除現有內容
		d3.select(container).selectAll('*').remove();

		// 創建 SVG
		svg = d3
			.select(container)
			.append('svg')
			.attr('width', width)
			.attr('height', height)
			.attr('viewBox', `0 0 ${width} ${height}`)
			.style('border', '1px solid #e5e7eb')
			.style('border-radius', '8px');

		// 添加縮放和拖拽
		const zoom = d3
			.zoom<SVGSVGElement, unknown>()
			.scaleExtent([0.1, 4])
			.on('zoom', event => {
				svg.select('.graph-container').attr('transform', event.transform);
			});

		if (interactive) {
			svg.call(zoom);
		}

		// 創建主容器
		svg.append('g').attr('class', 'graph-container');

		// 創建力導向模擬
		simulation = d3
			.forceSimulation<DependencyNode>()
			.force(
				'link',
				d3
					.forceLink<DependencyNode, DependencyLink>()
					.id(d => d.id)
					.distance(d => d.distance || 100)
					.strength(d => d.strength || 0.5)
			)
			.force('charge', d3.forceManyBody().strength(-300))
			.force('center', d3.forceCenter(width / 2, height / 2))
			.force(
				'collision',
				d3.forceCollide().radius(d => d.size + 5)
			);

		updateVisualization();
	}

	function updateVisualization() {
		if (!svg || !graph) return;

		const container = svg.select('.graph-container');

		// 更新連接
		const links = container
			.selectAll('.link')
			.data(graph.links, (d: any) => `${d.source.id || d.source}-${d.target.id || d.target}`);

		links.exit().remove();

		const linkEnter = links
			.enter()
			.append('line')
			.attr('class', 'link')
			.style('stroke', d => colorScheme.links[d.type] || '#999')
			.style('stroke-opacity', 0.6)
			.style('stroke-width', d => Math.sqrt(d.strength * 10))
			.style('cursor', interactive ? 'pointer' : 'default');

		if (interactive) {
			linkEnter.on('click', (event, d) => {
				event.stopPropagation();
				dispatch('linkClick', { link: d });
			});
		}

		const linkUpdate = linkEnter.merge(links);

		// 更新節點
		const nodes = container.selectAll('.node').data(graph.nodes, (d: any) => d.id);

		nodes.exit().remove();

		const nodeEnter = nodes
			.enter()
			.append('g')
			.attr('class', 'node')
			.style('cursor', interactive ? 'pointer' : 'default');

		// 節點圓圈
		nodeEnter
			.append('circle')
			.attr('r', d => d.size)
			.style('fill', d => colorScheme.nodes[d.group] || '#6b7280')
			.style('stroke', '#fff')
			.style('stroke-width', 2);

		// 節點標籤
		if (showLabels) {
			nodeEnter
				.append('text')
				.attr('dx', d => d.size + 5)
				.attr('dy', '.35em')
				.style('font-size', '12px')
				.style('font-family', 'system-ui, sans-serif')
				.style('fill', '#374151')
				.text(d => (d.title.length > 20 ? d.title.substring(0, 20) + '...' : d.title));
		}

		const nodeUpdate = nodeEnter.merge(nodes);

		// 添加交互
		if (interactive) {
			nodeUpdate
				.on('click', (event, d) => {
					event.stopPropagation();
					dispatch('nodeClick', { node: d });
				})
				.on('mouseenter', (event, d) => {
					dispatch('nodeHover', { node: d });
					// 高亮相關節點和連接
					highlightConnections(d.id, true);
				})
				.on('mouseleave', (event, d) => {
					dispatch('nodeHover', { node: null });
					highlightConnections(d.id, false);
				})
				.call(
					d3
						.drag<SVGGElement, DependencyNode>()
						.on('start', dragStarted)
						.on('drag', dragged)
						.on('end', dragEnded)
				);
		}

		// 更新模擬
		simulation.nodes(graph.nodes);
		simulation.force<d3.ForceLink<DependencyNode, DependencyLink>>('link')!.links(graph.links);

		simulation.on('tick', () => {
			linkUpdate
				.attr('x1', d => (d.source as DependencyNode).x!)
				.attr('y1', d => (d.source as DependencyNode).y!)
				.attr('x2', d => (d.target as DependencyNode).x!)
				.attr('y2', d => (d.target as DependencyNode).y!);

			nodeUpdate.attr('transform', d => `translate(${d.x},${d.y})`);
		});

		simulation.alpha(1).restart();
	}

	function highlightConnections(nodeId: string, highlight: boolean) {
		const opacity = highlight ? 0.1 : 0.6;
		const highlightOpacity = highlight ? 1 : 0.6;

		// 重置所有元素
		svg.selectAll('.link').style('stroke-opacity', opacity);

		svg.selectAll('.node').style('opacity', opacity);

		if (highlight) {
			// 高亮相關連接
			svg
				.selectAll('.link')
				.filter((d: any) => {
					const sourceId = d.source.id || d.source;
					const targetId = d.target.id || d.target;
					return sourceId === nodeId || targetId === nodeId;
				})
				.style('stroke-opacity', highlightOpacity);

			// 高亮相關節點
			const connectedNodes = new Set([nodeId]);
			graph.links.forEach(link => {
				const sourceId = typeof link.source === 'string' ? link.source : link.source.id;
				const targetId = typeof link.target === 'string' ? link.target : link.target.id;

				if (sourceId === nodeId) connectedNodes.add(targetId);
				if (targetId === nodeId) connectedNodes.add(sourceId);
			});

			svg
				.selectAll('.node')
				.filter((d: any) => connectedNodes.has(d.id))
				.style('opacity', highlightOpacity);
		}
	}

	function dragStarted(event: d3.D3DragEvent<SVGGElement, DependencyNode, DependencyNode>) {
		if (!event.active) simulation.alphaTarget(0.3).restart();
		event.subject.fx = event.subject.x;
		event.subject.fy = event.subject.y;
	}

	function dragged(event: d3.D3DragEvent<SVGGElement, DependencyNode, DependencyNode>) {
		event.subject.fx = event.x;
		event.subject.fy = event.y;
	}

	function dragEnded(event: d3.D3DragEvent<SVGGElement, DependencyNode, DependencyNode>) {
		if (!event.active) simulation.alphaTarget(0);
		event.subject.fx = null;
		event.subject.fy = null;
	}

	// 公開方法
	export function centerGraph() {
		if (svg) {
			const zoom = d3.zoom<SVGSVGElement, unknown>();
			svg.transition().duration(750).call(zoom.transform, d3.zoomIdentity);
		}
	}

	export function fitToView() {
		if (svg && graph.nodes.length > 0) {
			const bounds = {
				x: d3.min(graph.nodes, d => d.x!) || 0,
				y: d3.min(graph.nodes, d => d.y!) || 0,
				width: (d3.max(graph.nodes, d => d.x!) || 0) - (d3.min(graph.nodes, d => d.x!) || 0),
				height: (d3.max(graph.nodes, d => d.y!) || 0) - (d3.min(graph.nodes, d => d.y!) || 0)
			};

			const scale = Math.min(width / (bounds.width + 100), height / (bounds.height + 100), 2);

			const translate = [
				width / 2 - scale * (bounds.x + bounds.width / 2),
				height / 2 - scale * (bounds.y + bounds.height / 2)
			];

			const zoom = d3.zoom<SVGSVGElement, unknown>();
			svg
				.transition()
				.duration(750)
				.call(zoom.transform, d3.zoomIdentity.translate(translate[0], translate[1]).scale(scale));
		}
	}

	export function highlightNode(nodeId: string | null) {
		if (nodeId) {
			highlightConnections(nodeId, true);
		} else {
			highlightConnections('', false);
		}
	}

	export function getSimulation() {
		return simulation;
	}
</script>

<div class="dependency-graph">
	<div bind:this={container} class="graph-container"></div>

	{#if showLegend}
		<div class="legend">
			<div class="legend-section">
				<h4>節點類型</h4>
				<div class="legend-items">
					<div class="legend-item">
						<div class="legend-color" style="background-color: {colorScheme.nodes[1]}"></div>
						<span>緊急</span>
					</div>
					<div class="legend-item">
						<div class="legend-color" style="background-color: {colorScheme.nodes[2]}"></div>
						<span>高優先級</span>
					</div>
					<div class="legend-item">
						<div class="legend-color" style="background-color: {colorScheme.nodes[3]}"></div>
						<span>草稿</span>
					</div>
					<div class="legend-item">
						<div class="legend-color" style="background-color: {colorScheme.nodes[4]}"></div>
						<span>已發布</span>
					</div>
				</div>
			</div>

			<div class="legend-section">
				<h4>連接類型</h4>
				<div class="legend-items">
					<div class="legend-item">
						<div class="legend-line" style="background-color: {colorScheme.links.reference}"></div>
						<span>引用</span>
					</div>
					<div class="legend-item">
						<div class="legend-line" style="background-color: {colorScheme.links.tag}"></div>
						<span>標籤</span>
					</div>
					<div class="legend-item">
						<div class="legend-line" style="background-color: {colorScheme.links.category}"></div>
						<span>分類</span>
					</div>
					<div class="legend-item">
						<div class="legend-line" style="background-color: {colorScheme.links.similar}"></div>
						<span>相似</span>
					</div>
				</div>
			</div>
		</div>
	{/if}
</div>

<style>
	.dependency-graph {
		position: relative;
		width: 100%;
		height: 100%;
	}

	.graph-container {
		width: 100%;
		height: 100%;
	}

	.legend {
		position: absolute;
		top: 10px;
		right: 10px;
		background: rgba(255, 255, 255, 0.95);
		border: 1px solid #e5e7eb;
		border-radius: 8px;
		padding: 12px;
		font-size: 12px;
		box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
	}

	.legend-section {
		margin-bottom: 12px;
	}

	.legend-section:last-child {
		margin-bottom: 0;
	}

	.legend-section h4 {
		margin: 0 0 6px 0;
		font-weight: 600;
		color: #374151;
	}

	.legend-items {
		display: flex;
		flex-direction: column;
		gap: 4px;
	}

	.legend-item {
		display: flex;
		align-items: center;
		gap: 6px;
	}

	.legend-color {
		width: 12px;
		height: 12px;
		border-radius: 50%;
		border: 1px solid #fff;
	}

	.legend-line {
		width: 16px;
		height: 3px;
		border-radius: 2px;
	}

	:global(.dependency-graph .node) {
		transition: opacity 0.2s ease;
	}

	:global(.dependency-graph .link) {
		transition: stroke-opacity 0.2s ease;
	}
</style>
