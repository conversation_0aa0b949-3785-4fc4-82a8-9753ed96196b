/**
 * 基礎 Repository 接口
 */
export interface Repository<T, ID> {
  /**
   * 根據 ID 查找實體
   */
  findById(id: ID): Promise<T | null>;

  /**
   * 查找所有實體
   */
  findAll(): Promise<T[]>;

  /**
   * 保存實體
   */
  save(entity: T): Promise<T>;

  /**
   * 根據 ID 刪除實體
   */
  deleteById(id: ID): Promise<void>;

  /**
   * 檢查實體是否存在
   */
  existsById(id: ID): Promise<boolean>;

  /**
   * 計算實體總數
   */
  count(): Promise<number>;
}

/**
 * 分頁參數
 */
export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

/**
 * 分頁結果
 */
export interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

/**
 * 搜索參數
 */
export interface SearchParams {
  query?: string;
  filters?: Record<string, unknown>;
  pagination?: PaginationParams;
}

/**
 * 擴展的 Repository 接口，支援分頁和搜索
 */
export interface ExtendedRepository<T, ID> extends Repository<T, ID> {
  /**
   * 分頁查詢
   */
  findWithPagination(params: PaginationParams): Promise<PaginatedResult<T>>;

  /**
   * 搜索實體
   */
  search(params: SearchParams): Promise<PaginatedResult<T>>;

  /**
   * 批量保存
   */
  saveAll(entities: T[]): Promise<T[]>;

  /**
   * 批量刪除
   */
  deleteAll(ids: ID[]): Promise<void>;
}
