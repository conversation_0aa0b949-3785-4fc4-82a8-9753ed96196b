import type { User, UserRole, UserStatus } from "@life-note/core";
import type { UserId } from "@life-note/core";
import type {
  ISearchableRepository,
  PaginatedResult,
  PaginationParams,
} from "./IRepository.js";

/**
 * 用戶查詢條件
 */
export interface UserQueryCondition {
  role?: UserRole;
  status?: UserStatus;
  createdAfter?: Date;
  createdBefore?: Date;
  lastLoginAfter?: Date;
  lastLoginBefore?: Date;
}

/**
 * 用戶統計信息
 */
export interface UserStatistics {
  total: number;
  byRole: Record<UserRole, number>;
  byStatus: Record<UserStatus, number>;
  activeUsers: number;
  newUsersThisWeek: number;
  newUsersThisMonth: number;
  activeUsersThisWeek: number;
  activeUsersThisMonth: number;
  averageNotesPerUser: number;
  totalNotes: number;
}

/**
 * 用戶會話信息
 */
export interface UserSession {
  id: string;
  userId: string;
  token: string;
  device?: string;
  ipAddress?: string;
  userAgent?: string;
  expiresAt: Date;
  createdAt: Date;
}

/**
 * 用戶偏好設置
 */
export interface UserPreferences {
  theme: "light" | "dark" | "auto";
  language: string;
  timezone: string;
  editorSettings: Record<string, unknown>;
  notificationSettings: Record<string, unknown>;
  privacySettings: Record<string, unknown>;
}

/**
 * 用戶存儲庫接口
 */
export interface IUserRepository extends ISearchableRepository<User, UserId> {
  /**
   * 根據用戶名查找用戶
   */
  findByUsername(username: string): Promise<User | null>;

  /**
   * 根據電子郵件查找用戶
   */
  findByEmail(email: string): Promise<User | null>;

  /**
   * 根據角色查找用戶
   */
  findByRole(
    role: UserRole,
    params?: PaginationParams,
  ): Promise<PaginatedResult<User>>;

  /**
   * 根據狀態查找用戶
   */
  findByStatus(
    status: UserStatus,
    params?: PaginationParams,
  ): Promise<PaginatedResult<User>>;

  /**
   * 根據複合條件查找用戶
   */
  findByCondition(
    condition: UserQueryCondition,
    params?: PaginationParams,
  ): Promise<PaginatedResult<User>>;

  /**
   * 查找活躍用戶
   */
  findActiveUsers(params?: PaginationParams): Promise<PaginatedResult<User>>;

  /**
   * 查找最近註冊的用戶
   */
  findRecentlyRegistered(limit?: number): Promise<User[]>;

  /**
   * 查找最近登錄的用戶
   */
  findRecentlyLoggedIn(limit?: number): Promise<User[]>;

  /**
   * 檢查用戶名是否可用
   */
  isUsernameAvailable(username: string): Promise<boolean>;

  /**
   * 檢查電子郵件是否可用
   */
  isEmailAvailable(email: string): Promise<boolean>;

  /**
   * 獲取用戶統計信息
   */
  getStatistics(): Promise<UserStatistics>;

  /**
   * 批量更新用戶狀態
   */
  batchUpdateStatus(userIds: UserId[], status: UserStatus): Promise<void>;

  /**
   * 批量更新用戶角色
   */
  batchUpdateRole(userIds: UserId[], role: UserRole): Promise<void>;

  /**
   * 記錄用戶登錄
   */
  recordLogin(userId: UserId): Promise<void>;

  /**
   * 更新最後活動時間
   */
  updateLastActivity(userId: UserId): Promise<void>;

  // 會話管理
  /**
   * 創建用戶會話
   */
  createSession(
    session: Omit<UserSession, "id" | "createdAt">,
  ): Promise<UserSession>;

  /**
   * 根據 token 查找會話
   */
  findSessionByToken(token: string): Promise<UserSession | null>;

  /**
   * 獲取用戶的所有會話
   */
  getUserSessions(userId: UserId): Promise<UserSession[]>;

  /**
   * 刪除會話
   */
  deleteSession(token: string): Promise<void>;

  /**
   * 刪除用戶的所有會話
   */
  deleteUserSessions(userId: UserId): Promise<void>;

  /**
   * 清理過期會話
   */
  cleanupExpiredSessions(): Promise<void>;

  // 偏好設置管理
  /**
   * 獲取用戶偏好設置
   */
  getUserPreferences(userId: UserId): Promise<UserPreferences | null>;

  /**
   * 保存用戶偏好設置
   */
  saveUserPreferences(
    userId: UserId,
    preferences: UserPreferences,
  ): Promise<void>;

  /**
   * 更新用戶偏好設置
   */
  updateUserPreferences(
    userId: UserId,
    preferences: Partial<UserPreferences>,
  ): Promise<void>;

  // 統計信息管理
  /**
   * 獲取用戶個人統計信息
   */
  getUserPersonalStatistics(userId: UserId): Promise<Record<string, unknown>>;

  /**
   * 更新用戶個人統計信息
   */
  updateUserPersonalStatistics(
    userId: UserId,
    statistics: Record<string, unknown>,
  ): Promise<void>;

  /**
   * 增加用戶筆記計數
   */
  incrementNoteCount(userId: UserId, increment?: number): Promise<void>;

  /**
   * 減少用戶筆記計數
   */
  decrementNoteCount(userId: UserId, decrement?: number): Promise<void>;

  /**
   * 更新用戶字數統計
   */
  updateWordCount(userId: UserId, wordCount: number): Promise<void>;

  /**
   * 更新用戶連續記錄
   */
  updateStreak(
    userId: UserId,
    currentStreak: number,
    longestStreak?: number,
  ): Promise<void>;
}
