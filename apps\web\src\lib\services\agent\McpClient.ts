/**
 * MCP (Model Context Protocol) 客戶端
 * 用於與 MCP 服務器通信和 A2A (Agent to Agent) 協作
 */

interface McpTool {
	name: string;
	description: string;
	inputSchema: any;
}

interface McpResource {
	uri: string;
	name: string;
	description?: string;
	mimeType?: string;
}

interface McpMessage {
	jsonrpc: '2.0';
	id?: string | number;
	method?: string;
	params?: any;
	result?: any;
	error?: any;
}

export class McpClient {
	private ws: WebSocket | null = null;
	private isConnected = false;
	private messageId = 0;
	private pendingRequests = new Map<
		string | number,
		{
			resolve: (value: any) => void;
			reject: (error: any) => void;
		}
	>();
	private tools = new Map<string, McpTool>();
	private resources = new Map<string, McpResource>();
	private eventListeners = new Map<string, Function[]>();

	constructor(private serverUrl: string) {}

	/**
	 * 連接到 MCP 服務器
	 */
	async connect(): Promise<void> {
		return new Promise((resolve, reject) => {
			try {
				this.ws = new WebSocket(this.serverUrl);

				this.ws.onopen = async () => {
					console.log('MCP WebSocket connected');

					try {
						// 初始化握手
						await this.initialize();
						this.isConnected = true;
						this.emit('connected');
						resolve();
					} catch (error) {
						reject(error);
					}
				};

				this.ws.onmessage = event => {
					try {
						const message: McpMessage = JSON.parse(event.data);
						this.handleMessage(message);
					} catch (error) {
						console.error('Failed to parse MCP message:', error);
					}
				};

				this.ws.onclose = () => {
					console.log('MCP WebSocket disconnected');
					this.isConnected = false;
					this.emit('disconnected');
				};

				this.ws.onerror = error => {
					console.error('MCP WebSocket error:', error);
					this.emit('error', error);
					reject(error);
				};
			} catch (error) {
				reject(error);
			}
		});
	}

	/**
	 * 斷開連接
	 */
	async disconnect(): Promise<void> {
		if (this.ws) {
			this.ws.close();
			this.ws = null;
		}
		this.isConnected = false;
		this.pendingRequests.clear();
	}

	/**
	 * 檢查是否已連接
	 */
	isReady(): boolean {
		return this.isConnected && this.ws?.readyState === WebSocket.OPEN;
	}

	/**
	 * 初始化 MCP 連接
	 */
	private async initialize(): Promise<void> {
		// 發送初始化請求
		const initResponse = await this.sendRequest('initialize', {
			protocolVersion: '2024-11-05',
			capabilities: {
				tools: {},
				resources: {}
			},
			clientInfo: {
				name: 'LifeNote-Agent',
				version: '1.0.0'
			}
		});

		console.log('MCP initialization response:', initResponse);

		// 獲取可用工具
		await this.loadTools();

		// 獲取可用資源
		await this.loadResources();

		// 發送初始化完成通知
		await this.sendNotification('notifications/initialized');
	}

	/**
	 * 載入可用工具
	 */
	private async loadTools(): Promise<void> {
		try {
			const response = await this.sendRequest('tools/list');

			if (response.tools) {
				this.tools.clear();
				for (const tool of response.tools) {
					this.tools.set(tool.name, tool);
				}
				console.log(`Loaded ${this.tools.size} MCP tools`);
			}
		} catch (error) {
			console.warn('Failed to load MCP tools:', error);
		}
	}

	/**
	 * 載入可用資源
	 */
	private async loadResources(): Promise<void> {
		try {
			const response = await this.sendRequest('resources/list');

			if (response.resources) {
				this.resources.clear();
				for (const resource of response.resources) {
					this.resources.set(resource.uri, resource);
				}
				console.log(`Loaded ${this.resources.size} MCP resources`);
			}
		} catch (error) {
			console.warn('Failed to load MCP resources:', error);
		}
	}

	/**
	 * 調用 MCP 工具
	 */
	async callTool(name: string, arguments_: any): Promise<any> {
		if (!this.tools.has(name)) {
			throw new Error(`Tool '${name}' not found`);
		}

		const response = await this.sendRequest('tools/call', {
			name,
			arguments: arguments_
		});

		return response.content;
	}

	/**
	 * 讀取 MCP 資源
	 */
	async readResource(uri: string): Promise<any> {
		if (!this.resources.has(uri)) {
			throw new Error(`Resource '${uri}' not found`);
		}

		const response = await this.sendRequest('resources/read', {
			uri
		});

		return response.contents;
	}

	/**
	 * 獲取可用工具列表
	 */
	getAvailableTools(): McpTool[] {
		return Array.from(this.tools.values());
	}

	/**
	 * 獲取可用資源列表
	 */
	getAvailableResources(): McpResource[] {
		return Array.from(this.resources.values());
	}

	/**
	 * 檢查是否有特定工具
	 */
	hasTool(name: string): boolean {
		return this.tools.has(name);
	}

	/**
	 * 檢查是否有特定資源
	 */
	hasResource(uri: string): boolean {
		return this.resources.has(uri);
	}

	/**
	 * 發送請求
	 */
	private async sendRequest(method: string, params?: any): Promise<any> {
		if (!this.isReady()) {
			throw new Error('MCP client not connected');
		}

		const id = ++this.messageId;
		const message: McpMessage = {
			jsonrpc: '2.0',
			id,
			method,
			params
		};

		return new Promise((resolve, reject) => {
			this.pendingRequests.set(id, { resolve, reject });

			this.ws!.send(JSON.stringify(message));

			// 設置超時
			setTimeout(() => {
				if (this.pendingRequests.has(id)) {
					this.pendingRequests.delete(id);
					reject(new Error(`Request timeout for method: ${method}`));
				}
			}, 30000);
		});
	}

	/**
	 * 發送通知
	 */
	private async sendNotification(method: string, params?: any): Promise<void> {
		if (!this.isReady()) {
			throw new Error('MCP client not connected');
		}

		const message: McpMessage = {
			jsonrpc: '2.0',
			method,
			params
		};

		this.ws!.send(JSON.stringify(message));
	}

	/**
	 * 處理收到的消息
	 */
	private handleMessage(message: McpMessage): void {
		if (message.id !== undefined) {
			// 這是對請求的回應
			const pending = this.pendingRequests.get(message.id);
			if (pending) {
				this.pendingRequests.delete(message.id);

				if (message.error) {
					pending.reject(new Error(message.error.message || 'MCP request failed'));
				} else {
					pending.resolve(message.result);
				}
			}
		} else if (message.method) {
			// 這是來自服務器的通知或請求
			this.handleServerMessage(message);
		}
	}

	/**
	 * 處理服務器消息
	 */
	private handleServerMessage(message: McpMessage): void {
		switch (message.method) {
			case 'notifications/tools/list_changed':
				// 工具列表變更，重新載入
				this.loadTools();
				break;
			case 'notifications/resources/list_changed':
				// 資源列表變更，重新載入
				this.loadResources();
				break;
			default:
				console.log('Received server message:', message);
				this.emit('server_message', message);
		}
	}

	/**
	 * 添加事件監聽器
	 */
	addEventListener(event: string, listener: Function): void {
		if (!this.eventListeners.has(event)) {
			this.eventListeners.set(event, []);
		}
		this.eventListeners.get(event)!.push(listener);
	}

	/**
	 * 移除事件監聽器
	 */
	removeEventListener(event: string, listener: Function): void {
		const listeners = this.eventListeners.get(event);
		if (listeners) {
			const index = listeners.indexOf(listener);
			if (index > -1) {
				listeners.splice(index, 1);
			}
		}
	}

	/**
	 * 發送事件
	 */
	private emit(event: string, data?: any): void {
		const listeners = this.eventListeners.get(event);
		if (listeners) {
			listeners.forEach(listener => {
				try {
					listener(data);
				} catch (error) {
					console.error(`Error in MCP event listener for ${event}:`, error);
				}
			});
		}
	}
}

/**
 * A2A (Agent to Agent) 通信管理器
 */
export class A2AManager {
	private mcpClients = new Map<string, McpClient>();
	private agentRegistry = new Map<
		string,
		{
			endpoint: string;
			capabilities: string[];
			lastSeen: Date;
		}
	>();

	/**
	 * 註冊遠程 Agent
	 */
	async registerRemoteAgent(
		agentId: string,
		endpoint: string,
		capabilities: string[]
	): Promise<void> {
		try {
			const client = new McpClient(endpoint);
			await client.connect();

			this.mcpClients.set(agentId, client);
			this.agentRegistry.set(agentId, {
				endpoint,
				capabilities,
				lastSeen: new Date()
			});

			console.log(`Remote agent '${agentId}' registered successfully`);
		} catch (error) {
			console.error(`Failed to register remote agent '${agentId}':`, error);
			throw error;
		}
	}

	/**
	 * 取消註冊遠程 Agent
	 */
	async unregisterRemoteAgent(agentId: string): Promise<void> {
		const client = this.mcpClients.get(agentId);
		if (client) {
			await client.disconnect();
			this.mcpClients.delete(agentId);
		}

		this.agentRegistry.delete(agentId);
		console.log(`Remote agent '${agentId}' unregistered`);
	}

	/**
	 * 向遠程 Agent 發送任務
	 */
	async sendTaskToAgent(agentId: string, taskType: string, input: any): Promise<any> {
		const client = this.mcpClients.get(agentId);
		if (!client) {
			throw new Error(`Remote agent '${agentId}' not found`);
		}

		// 使用 MCP 工具調用遠程任務
		const toolName = `execute_${taskType}`;
		if (!client.hasTool(toolName)) {
			throw new Error(`Remote agent '${agentId}' does not support task type '${taskType}'`);
		}

		return await client.callTool(toolName, input);
	}

	/**
	 * 獲取可用的遠程 Agent
	 */
	getAvailableAgents(): string[] {
		return Array.from(this.agentRegistry.keys());
	}

	/**
	 * 根據能力查找 Agent
	 */
	findAgentsByCapability(capability: string): string[] {
		const agents: string[] = [];

		for (const [agentId, info] of this.agentRegistry) {
			if (info.capabilities.includes(capability)) {
				agents.push(agentId);
			}
		}

		return agents;
	}

	/**
	 * 關閉所有連接
	 */
	async shutdown(): Promise<void> {
		const disconnectPromises = Array.from(this.mcpClients.values()).map(client =>
			client.disconnect()
		);

		await Promise.allSettled(disconnectPromises);

		this.mcpClients.clear();
		this.agentRegistry.clear();

		console.log('A2A Manager shutdown completed');
	}
}

// 導出單例實例
export const a2aManager = new A2AManager();
