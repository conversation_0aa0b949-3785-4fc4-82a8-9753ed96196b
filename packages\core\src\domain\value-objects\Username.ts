import {
  ValueObject,
  BusinessRuleViolationError,
} from "../../shared/Entity.js";

/**
 * 用戶名值對象
 * 代表有效的用戶名
 */
export class Username extends ValueObject {
  private readonly _value: string;

  constructor(value: string) {
    super();
    this.validateUsername(value);
    this._value = value.trim();
  }

  /**
   * 創建用戶名值對象
   */
  static create(value: string): Username {
    return new Username(value);
  }

  /**
   * 從字符串創建用戶名
   */
  static fromString(value: string): Username {
    return new Username(value);
  }

  /**
   * 驗證用戶名格式
   */
  private validateUsername(value: string): void {
    if (!value || value.trim().length === 0) {
      throw new BusinessRuleViolationError("Username cannot be empty");
    }

    const trimmedValue = value.trim();

    // 長度檢查
    if (trimmedValue.length < 3) {
      throw new BusinessRuleViolationError(
        "Username must be at least 3 characters long",
      );
    }

    if (trimmedValue.length > 30) {
      throw new BusinessRuleViolationError(
        "Username cannot be longer than 30 characters",
      );
    }

    // 格式檢查：只允許字母、數字、下劃線和連字符
    const usernameRegex = /^[a-zA-Z0-9_-]+$/;
    if (!usernameRegex.test(trimmedValue)) {
      throw new BusinessRuleViolationError(
        "Username can only contain letters, numbers, underscores, and hyphens",
      );
    }

    // 不能以數字開頭
    if (/^[0-9]/.test(trimmedValue)) {
      throw new BusinessRuleViolationError(
        "Username cannot start with a number",
      );
    }

    // 不能以特殊字符開頭或結尾
    if (/^[-_]/.test(trimmedValue) || /[-_]$/.test(trimmedValue)) {
      throw new BusinessRuleViolationError(
        "Username cannot start or end with special characters",
      );
    }

    // 不能包含連續的特殊字符
    if (/[-_]{2,}/.test(trimmedValue)) {
      throw new BusinessRuleViolationError(
        "Username cannot contain consecutive special characters",
      );
    }

    // 檢查保留用戶名
    const reservedUsernames = [
      "admin",
      "administrator",
      "root",
      "system",
      "user",
      "guest",
      "api",
      "www",
      "mail",
      "email",
      "support",
      "help",
      "info",
      "contact",
      "service",
      "services",
      "test",
      "demo",
      "sample",
      "null",
      "undefined",
      "true",
      "false",
      "anonymous",
      "public",
      "private",
      "internal",
      "external",
      "local",
      "localhost",
      "about",
      "terms",
      "privacy",
      "policy",
      "legal",
      "copyright",
      "trademark",
      "license",
      "faq",
      "blog",
      "news",
      "press",
      "media",
      "download",
      "upload",
      "file",
      "files",
      "image",
      "images",
      "video",
      "videos",
      "audio",
      "document",
      "documents",
    ];

    if (reservedUsernames.includes(trimmedValue.toLowerCase())) {
      throw new BusinessRuleViolationError(
        "This username is reserved and cannot be used",
      );
    }

    // 檢查是否包含不當內容的簡單過濾
    const inappropriatePatterns = [
      /fuck/i,
      /shit/i,
      /damn/i,
      /hell/i,
      /bitch/i,
      /ass/i,
      /sex/i,
      /porn/i,
      /xxx/i,
      /adult/i,
      /nude/i,
      /naked/i,
      /kill/i,
      /die/i,
      /death/i,
      /murder/i,
      /suicide/i,
      /hate/i,
      /nazi/i,
      /hitler/i,
      /terrorist/i,
      /bomb/i,
    ];

    for (const pattern of inappropriatePatterns) {
      if (pattern.test(trimmedValue)) {
        throw new BusinessRuleViolationError(
          "Username contains inappropriate content",
        );
      }
    }
  }

  /**
   * 檢查用戶名是否包含數字
   */
  hasNumbers(): boolean {
    return /[0-9]/.test(this._value);
  }

  /**
   * 檢查用戶名是否包含特殊字符
   */
  hasSpecialCharacters(): boolean {
    return /[_-]/.test(this._value);
  }

  /**
   * 檢查用戶名是否只包含字母
   */
  isAlphaOnly(): boolean {
    return /^[a-zA-Z]+$/.test(this._value);
  }

  /**
   * 檢查用戶名是否為字母數字組合
   */
  isAlphanumeric(): boolean {
    return /^[a-zA-Z0-9]+$/.test(this._value);
  }

  /**
   * 獲取用戶名的顯示格式（首字母大寫）
   */
  toDisplayFormat(): string {
    return (
      this._value.charAt(0).toUpperCase() + this._value.slice(1).toLowerCase()
    );
  }

  /**
   * 獲取用戶名的小寫格式
   */
  toLowerCase(): string {
    return this._value.toLowerCase();
  }

  /**
   * 獲取用戶名的大寫格式
   */
  toUpperCase(): string {
    return this._value.toUpperCase();
  }

  /**
   * 生成用戶名的變體建議（當用戶名已被占用時）
   */
  generateVariants(): string[] {
    const variants: string[] = [];
    const base = this._value;

    // 添加前幾個數字後綴
    for (let i = 1; i <= 5; i++) {
      const variant = `${base}${i}`;
      if (variant.length <= 30) {
        variants.push(variant);
      }
    }

    // 添加下劃線和數字
    for (let i = 1; i <= 3; i++) {
      const variant = `${base}_${i}`;
      if (variant.length <= 30) {
        variants.push(variant);
      }
    }

    // 添加年份（只添加後兩位）
    const currentYear = new Date().getFullYear();
    const shortYear = currentYear.toString().slice(-2);
    const yearVariant1 = `${base}${shortYear}`;
    const yearVariant2 = `${base}_${shortYear}`;

    if (yearVariant1.length <= 30) variants.push(yearVariant1);
    if (yearVariant2.length <= 30) variants.push(yearVariant2);

    // 添加常見後綴（只有在長度允許的情況下）
    const suffixes = ["user", "real"];
    suffixes.forEach((suffix) => {
      const variant1 = `${base}_${suffix}`;
      const variant2 = `${base}${suffix}`;
      if (variant1.length <= 30) variants.push(variant1);
      if (variant2.length <= 30) variants.push(variant2);
    });

    return variants
      .filter((variant) => {
        try {
          new Username(variant);
          return true;
        } catch {
          return false;
        }
      })
      .slice(0, 10); // 返回前10個有效變體
  }

  get value(): string {
    return this._value;
  }

  get length(): number {
    return this._value.length;
  }

  equals(other: ValueObject): boolean {
    if (!(other instanceof Username)) {
      return false;
    }
    return this._value.toLowerCase() === other._value.toLowerCase();
  }

  hashCode(): string {
    return this._value.toLowerCase();
  }

  toString(): string {
    return this._value;
  }

  toPlainObject(): Record<string, unknown> {
    return {
      value: this._value,
      length: this.length,
      hasNumbers: this.hasNumbers(),
      hasSpecialCharacters: this.hasSpecialCharacters(),
      isAlphaOnly: this.isAlphaOnly(),
      isAlphanumeric: this.isAlphanumeric(),
    };
  }

  static fromPlainObject(data: Record<string, unknown>): Username {
    if (typeof data.value !== "string") {
      throw new BusinessRuleViolationError("Invalid Username data");
    }

    return new Username(data.value);
  }
}
