import { LocalAgent } from './LocalAgent';
import { RemoteAgent } from './RemoteAgent';
import { HybridAgent } from './HybridAgent';
import type {
	IAgent,
	IAgentFactory,
	AgentConfig,
	LocalModelConfig,
	RemoteServiceConfig,
	SmartRoutingConfig,
	AgentTaskType
} from '$lib/types/agent';

/**
 * Agent 工廠實現
 * 負責創建和配置不同類型的 AI Agent
 */
export class AgentFactory implements IAgentFactory {
	private static instance: AgentFactory;
	private defaultConfigs: Map<string, any> = new Map();

	private constructor() {
		this.initializeDefaultConfigs();
	}

	/**
	 * 獲取單例實例
	 */
	static getInstance(): AgentFactory {
		if (!AgentFactory.instance) {
			AgentFactory.instance = new AgentFactory();
		}
		return AgentFactory.instance;
	}

	/**
	 * 創建本地 Agent
	 */
	async createLocalAgent(config: AgentConfig & { localModel: LocalModelConfig }): Promise<IAgent> {
		try {
			// 驗證配置
			this.validateLocalConfig(config);

			// 創建本地 Agent
			const agent = new LocalAgent(config, config.localModel);

			console.log(`Created local agent: ${config.id}`);
			return agent;
		} catch (error) {
			console.error(`Failed to create local agent ${config.id}:`, error);
			throw error;
		}
	}

	/**
	 * 創建遠程 Agent
	 */
	async createRemoteAgent(
		config: AgentConfig & { remoteService: RemoteServiceConfig }
	): Promise<IAgent> {
		try {
			// 驗證配置
			this.validateRemoteConfig(config);

			// 創建遠程 Agent
			const agent = new RemoteAgent(config, config.remoteService);

			console.log(`Created remote agent: ${config.id}`);
			return agent;
		} catch (error) {
			console.error(`Failed to create remote agent ${config.id}:`, error);
			throw error;
		}
	}

	/**
	 * 創建混合 Agent
	 */
	async createHybridAgent(
		config: AgentConfig & {
			localModel: LocalModelConfig;
			remoteService: RemoteServiceConfig;
			routingConfig: SmartRoutingConfig;
		}
	): Promise<IAgent> {
		try {
			// 驗證配置
			this.validateHybridConfig(config);

			// 創建混合 Agent
			const agent = new HybridAgent(
				config,
				config.localModel,
				config.remoteService,
				config.routingConfig
			);

			console.log(`Created hybrid agent: ${config.id}`);
			return agent;
		} catch (error) {
			console.error(`Failed to create hybrid agent ${config.id}:`, error);
			throw error;
		}
	}

	/**
	 * 創建預設的筆記分析 Agent
	 */
	async createNoteAnalysisAgent(mode: 'local' | 'remote' | 'hybrid' = 'hybrid'): Promise<IAgent> {
		const baseConfig: AgentConfig = {
			id: `note-analysis-${mode}-${Date.now()}`,
			name: `Note Analysis Agent (${mode})`,
			description: 'Specialized agent for analyzing notes and documents',
			capabilities: ['note_analysis', 'summarization', 'extraction', 'classification'],
			capabilityLevel: 'advanced',
			executionMode: mode,
			maxConcurrentTasks: 3,
			defaultTimeout: 30000,
			enabled: true
		};

		switch (mode) {
			case 'local':
				return this.createLocalAgent({
					...baseConfig,
					localModel: this.getDefaultLocalModelConfig()
				});
			case 'remote':
				return this.createRemoteAgent({
					...baseConfig,
					remoteService: this.getDefaultRemoteServiceConfig()
				});
			case 'hybrid':
				return this.createHybridAgent({
					...baseConfig,
					localModel: this.getDefaultLocalModelConfig(),
					remoteService: this.getDefaultRemoteServiceConfig(),
					routingConfig: this.getDefaultRoutingConfig()
				});
			default:
				throw new Error(`Unsupported mode: ${mode}`);
		}
	}

	/**
	 * 創建預設的內容生成 Agent
	 */
	async createContentGenerationAgent(
		mode: 'local' | 'remote' | 'hybrid' = 'remote'
	): Promise<IAgent> {
		const baseConfig: AgentConfig = {
			id: `content-generation-${mode}-${Date.now()}`,
			name: `Content Generation Agent (${mode})`,
			description: 'Specialized agent for generating and enhancing content',
			capabilities: ['content_generation', 'translation', 'summarization'],
			capabilityLevel: 'expert',
			executionMode: mode,
			maxConcurrentTasks: 2,
			defaultTimeout: 60000,
			enabled: true
		};

		switch (mode) {
			case 'local':
				return this.createLocalAgent({
					...baseConfig,
					localModel: this.getDefaultLocalModelConfig()
				});
			case 'remote':
				return this.createRemoteAgent({
					...baseConfig,
					remoteService: this.getDefaultRemoteServiceConfig()
				});
			case 'hybrid':
				return this.createHybridAgent({
					...baseConfig,
					localModel: this.getDefaultLocalModelConfig(),
					remoteService: this.getDefaultRemoteServiceConfig(),
					routingConfig: this.getDefaultRoutingConfig()
				});
			default:
				throw new Error(`Unsupported mode: ${mode}`);
		}
	}

	/**
	 * 創建預設的問答 Agent
	 */
	async createQuestionAnsweringAgent(
		mode: 'local' | 'remote' | 'hybrid' = 'hybrid'
	): Promise<IAgent> {
		const baseConfig: AgentConfig = {
			id: `qa-${mode}-${Date.now()}`,
			name: `Question Answering Agent (${mode})`,
			description: 'Specialized agent for answering questions and providing information',
			capabilities: ['question_answering', 'search_query', 'extraction'],
			capabilityLevel: 'advanced',
			executionMode: mode,
			maxConcurrentTasks: 5,
			defaultTimeout: 20000,
			enabled: true
		};

		switch (mode) {
			case 'local':
				return this.createLocalAgent({
					...baseConfig,
					localModel: this.getDefaultLocalModelConfig()
				});
			case 'remote':
				return this.createRemoteAgent({
					...baseConfig,
					remoteService: this.getDefaultRemoteServiceConfig()
				});
			case 'hybrid':
				return this.createHybridAgent({
					...baseConfig,
					localModel: this.getDefaultLocalModelConfig(),
					remoteService: this.getDefaultRemoteServiceConfig(),
					routingConfig: this.getDefaultRoutingConfig()
				});
			default:
				throw new Error(`Unsupported mode: ${mode}`);
		}
	}

	/**
	 * 批量創建 Agent
	 */
	async createAgentPool(
		configs: Array<{
			type: 'local' | 'remote' | 'hybrid';
			capabilities: AgentTaskType[];
			count: number;
		}>
	): Promise<IAgent[]> {
		const agents: IAgent[] = [];

		for (const config of configs) {
			for (let i = 0; i < config.count; i++) {
				const agentConfig: AgentConfig = {
					id: `${config.type}-agent-${config.capabilities.join('-')}-${i}`,
					name: `${config.type} Agent ${i + 1}`,
					description: `Auto-generated ${config.type} agent`,
					capabilities: config.capabilities,
					capabilityLevel: 'intermediate',
					executionMode: config.type,
					maxConcurrentTasks: 3,
					defaultTimeout: 30000,
					enabled: true
				};

				let agent: IAgent;
				switch (config.type) {
					case 'local':
						agent = await this.createLocalAgent({
							...agentConfig,
							localModel: this.getDefaultLocalModelConfig()
						});
						break;
					case 'remote':
						agent = await this.createRemoteAgent({
							...agentConfig,
							remoteService: this.getDefaultRemoteServiceConfig()
						});
						break;
					case 'hybrid':
						agent = await this.createHybridAgent({
							...agentConfig,
							localModel: this.getDefaultLocalModelConfig(),
							remoteService: this.getDefaultRemoteServiceConfig(),
							routingConfig: this.getDefaultRoutingConfig()
						});
						break;
				}

				agents.push(agent);
			}
		}

		return agents;
	}

	// 私有方法

	/**
	 * 初始化默認配置
	 */
	private initializeDefaultConfigs(): void {
		// 設置默認的本地模型配置
		this.defaultConfigs.set('localModel', {
			modelPath: '/models/local-llm.onnx',
			modelType: 'onnx',
			maxSequenceLength: 2048,
			batchSize: 1,
			numThreads: 4,
			useGPU: false,
			quantization: 'int8'
		});

		// 設置默認的遠程服務配置
		this.defaultConfigs.set('remoteService', {
			provider: 'openai',
			endpoint: 'https://api.openai.com/v1',
			apiKey: process.env.OPENAI_API_KEY || '',
			model: 'gpt-3.5-turbo',
			timeout: 30000,
			retryAttempts: 3,
			rateLimitPerMinute: 60
		});

		// 設置默認的路由配置
		this.defaultConfigs.set('routing', {
			localThreshold: 0.3,
			remoteThreshold: 0.7,
			complexityWeights: {
				textLength: 0.3,
				taskComplexity: 0.4,
				contextSize: 0.2,
				realTimeRequirement: 0.1
			},
			fallbackStrategy: 'remote',
			maxRetries: 2,
			retryDelay: 1000
		});
	}

	/**
	 * 驗證本地配置
	 */
	private validateLocalConfig(config: AgentConfig & { localModel: LocalModelConfig }): void {
		if (!config.localModel.modelPath) {
			throw new Error('Local model path is required');
		}
		if (!config.localModel.modelType) {
			throw new Error('Local model type is required');
		}
	}

	/**
	 * 驗證遠程配置
	 */
	private validateRemoteConfig(config: AgentConfig & { remoteService: RemoteServiceConfig }): void {
		if (!config.remoteService.endpoint) {
			throw new Error('Remote service endpoint is required');
		}
		if (!config.remoteService.apiKey) {
			throw new Error('Remote service API key is required');
		}
	}

	/**
	 * 驗證混合配置
	 */
	private validateHybridConfig(
		config: AgentConfig & {
			localModel: LocalModelConfig;
			remoteService: RemoteServiceConfig;
			routingConfig: SmartRoutingConfig;
		}
	): void {
		this.validateLocalConfig(config);
		this.validateRemoteConfig(config);

		if (!config.routingConfig) {
			throw new Error('Routing configuration is required for hybrid agent');
		}
	}

	/**
	 * 獲取默認本地模型配置
	 */
	private getDefaultLocalModelConfig(): LocalModelConfig {
		return { ...this.defaultConfigs.get('localModel') };
	}

	/**
	 * 獲取默認遠程服務配置
	 */
	private getDefaultRemoteServiceConfig(): RemoteServiceConfig {
		return { ...this.defaultConfigs.get('remoteService') };
	}

	/**
	 * 獲取默認路由配置
	 */
	private getDefaultRoutingConfig(): SmartRoutingConfig {
		return { ...this.defaultConfigs.get('routing') };
	}
}

// 導出單例實例
export const agentFactory = AgentFactory.getInstance();
