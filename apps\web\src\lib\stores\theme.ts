import { writable } from 'svelte/store';
import { browser } from '$app/environment';

export type Theme = 'light' | 'dark' | 'system';
export type ResolvedTheme = 'light' | 'dark';

export interface ThemeState {
	mode: Theme;
	resolvedTheme: ResolvedTheme;
	systemTheme: ResolvedTheme;
}

const initialState: ThemeState = {
	mode: 'system',
	resolvedTheme: 'light',
	systemTheme: 'light'
};

function createThemeStore() {
	const { subscribe, set, update } = writable<ThemeState>(initialState);

	// Get system theme preference
	const getSystemTheme = (): ResolvedTheme => {
		if (!browser) return 'light';
		return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
	};

	// Apply theme to document
	const applyTheme = (theme: ResolvedTheme) => {
		if (!browser) return;

		const root = document.documentElement;
		root.classList.remove('light', 'dark');
		root.classList.add(theme);

		// Update meta theme-color
		const metaThemeColor = document.querySelector('meta[name="theme-color"]');
		if (metaThemeColor) {
			metaThemeColor.setAttribute('content', theme === 'dark' ? '#0f172a' : '#ffffff');
		}
	};

	// Resolve theme based on mode
	const resolveTheme = (mode: Theme, systemTheme: ResolvedTheme): ResolvedTheme => {
		return mode === 'system' ? systemTheme : (mode as ResolvedTheme);
	};

	// Initialize theme
	const initialize = () => {
		if (!browser) return;

		const savedTheme = localStorage.getItem('theme') as Theme | null;
		const systemTheme = getSystemTheme();
		const mode = savedTheme || 'system';
		const resolvedTheme = resolveTheme(mode, systemTheme);

		set({
			mode,
			resolvedTheme,
			systemTheme
		});

		applyTheme(resolvedTheme);

		// Listen for system theme changes
		const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
		const handleSystemThemeChange = (e: MediaQueryListEvent) => {
			const newSystemTheme = e.matches ? 'dark' : 'light';
			update(state => {
				const newResolvedTheme = resolveTheme(state.mode, newSystemTheme);
				applyTheme(newResolvedTheme);
				return {
					...state,
					systemTheme: newSystemTheme,
					resolvedTheme: newResolvedTheme
				};
			});
		};

		mediaQuery.addEventListener('change', handleSystemThemeChange);

		// Cleanup function
		return () => {
			mediaQuery.removeEventListener('change', handleSystemThemeChange);
		};
	};

	return {
		subscribe,

		// Initialize theme system
		initialize,

		// Set theme mode
		setMode(mode: Theme) {
			update(state => {
				const resolvedTheme = resolveTheme(mode, state.systemTheme);

				// Save to localStorage
				if (browser) {
					localStorage.setItem('theme', mode);
				}

				applyTheme(resolvedTheme);

				return {
					...state,
					mode,
					resolvedTheme
				};
			});
		},

		// Toggle between light and dark
		toggle() {
			update(state => {
				const currentResolved = state.resolvedTheme;
				const newMode: Theme = currentResolved === 'dark' ? 'light' : 'dark';
				const resolvedTheme = newMode;

				// Save to localStorage
				if (browser) {
					localStorage.setItem('theme', newMode);
				}

				applyTheme(resolvedTheme);

				return {
					...state,
					mode: newMode,
					resolvedTheme
				};
			});
		},

		// Reset to system theme
		resetToSystem() {
			update(state => {
				const resolvedTheme = state.systemTheme;

				// Remove from localStorage
				if (browser) {
					localStorage.removeItem('theme');
				}

				applyTheme(resolvedTheme);

				return {
					...state,
					mode: 'system',
					resolvedTheme
				};
			});
		}
	};
}

export const themeStore = createThemeStore();

// Initialize theme on module load
if (browser) {
	themeStore.initialize();
}
