import type { Note } from '$lib/types/note';
import type { DependencyGraph } from '$lib/types/dependency';
import { dependencyService } from './dependencyService';

export interface DependencyNotification {
	id: string;
	type: 'dependency_updated' | 'dependency_deleted' | 'dependency_created';
	sourceNoteId: string;
	sourceNoteTitle: string;
	affectedNoteId: string;
	affectedNoteTitle: string;
	message: string;
	timestamp: Date;
	read: boolean;
	severity: 'low' | 'medium' | 'high';
}

export interface NotificationSubscription {
	noteId: string;
	dependencies: string[];
	lastChecked: Date;
	enabled: boolean;
}

/**
 * 依賴更新通知服務
 * 監控筆記間的依賴關係變化，並發送相關通知
 */
class DependencyNotificationService {
	private notifications: DependencyNotification[] = [];
	private subscriptions: Map<string, NotificationSubscription> = new Map();
	private notificationCallbacks: Array<(notification: DependencyNotification) => void> = [];
	private isMonitoring = false;
	private monitoringInterval: NodeJS.Timeout | null = null;
	private lastGraphSnapshot: DependencyGraph | null = null;

	/**
	 * 開始監控依賴關係變化
	 */
	startMonitoring(notes: Note[], intervalMs: number = 30000): void {
		if (this.isMonitoring) {
			this.stopMonitoring();
		}

		this.isMonitoring = true;

		// 初始化依賴關係快照
		this.updateGraphSnapshot(notes);

		// 設置定期檢查
		this.monitoringInterval = setInterval(() => {
			this.checkForChanges(notes);
		}, intervalMs);

		console.log('Dependency monitoring started');
	}

	/**
	 * 停止監控
	 */
	stopMonitoring(): void {
		if (this.monitoringInterval) {
			clearInterval(this.monitoringInterval);
			this.monitoringInterval = null;
		}
		this.isMonitoring = false;
		console.log('Dependency monitoring stopped');
	}

	/**
	 * 檢查依賴關係變化
	 */
	private async checkForChanges(notes: Note[]): Promise<void> {
		try {
			// 生成新的依賴關係圖
			const newGraph = await dependencyService.analyzeDependencies(notes);

			if (this.lastGraphSnapshot) {
				// 比較新舊圖表，檢測變化
				const changes = this.detectChanges(this.lastGraphSnapshot, newGraph);

				// 為每個變化生成通知
				for (const change of changes) {
					await this.createNotification(change, notes);
				}
			}

			// 更新快照
			this.lastGraphSnapshot = newGraph;
		} catch (error) {
			console.error('Error checking dependency changes:', error);
		}
	}

	/**
	 * 檢測依賴關係圖的變化
	 */
	private detectChanges(oldGraph: DependencyGraph, newGraph: DependencyGraph): DependencyChange[] {
		const changes: DependencyChange[] = [];

		// 檢測新增的連接
		for (const newLink of newGraph.links) {
			const sourceId = typeof newLink.source === 'string' ? newLink.source : newLink.source.id;
			const targetId = typeof newLink.target === 'string' ? newLink.target : newLink.target.id;

			const existingLink = oldGraph.links.find(oldLink => {
				const oldSourceId = typeof oldLink.source === 'string' ? oldLink.source : oldLink.source.id;
				const oldTargetId = typeof oldLink.target === 'string' ? oldLink.target : oldLink.target.id;
				return oldSourceId === sourceId && oldTargetId === targetId;
			});

			if (!existingLink) {
				changes.push({
					type: 'dependency_created',
					sourceId,
					targetId,
					strength: newLink.strength
				});
			} else if (Math.abs(existingLink.strength - newLink.strength) > 0.1) {
				// 連接強度顯著變化
				changes.push({
					type: 'dependency_updated',
					sourceId,
					targetId,
					strength: newLink.strength,
					oldStrength: existingLink.strength
				});
			}
		}

		// 檢測刪除的連接
		for (const oldLink of oldGraph.links) {
			const sourceId = typeof oldLink.source === 'string' ? oldLink.source : oldLink.source.id;
			const targetId = typeof oldLink.target === 'string' ? oldLink.target : oldLink.target.id;

			const existingLink = newGraph.links.find(newLink => {
				const newSourceId = typeof newLink.source === 'string' ? newLink.source : newLink.source.id;
				const newTargetId = typeof newLink.target === 'string' ? newLink.target : newLink.target.id;
				return newSourceId === sourceId && newTargetId === targetId;
			});

			if (!existingLink) {
				changes.push({
					type: 'dependency_deleted',
					sourceId,
					targetId,
					strength: oldLink.strength
				});
			}
		}

		return changes;
	}

	/**
	 * 創建通知
	 */
	private async createNotification(change: DependencyChange, notes: Note[]): Promise<void> {
		const sourceNote = notes.find(note => note.id === change.sourceId);
		const targetNote = notes.find(note => note.id === change.targetId);

		if (!sourceNote || !targetNote) return;

		// 檢查是否有訂閱此依賴關係的用戶
		const affectedSubscriptions = this.getAffectedSubscriptions(change.sourceId, change.targetId);

		for (const subscription of affectedSubscriptions) {
			const notification: DependencyNotification = {
				id: this.generateNotificationId(),
				type: change.type,
				sourceNoteId: change.sourceId,
				sourceNoteTitle: sourceNote.title,
				affectedNoteId: change.targetId,
				affectedNoteTitle: targetNote.title,
				message: this.generateNotificationMessage(change, sourceNote, targetNote),
				timestamp: new Date(),
				read: false,
				severity: this.calculateSeverity(change)
			};

			this.notifications.push(notification);
			this.notifyCallbacks(notification);
		}
	}

	/**
	 * 生成通知消息
	 */
	private generateNotificationMessage(
		change: DependencyChange,
		sourceNote: Note,
		targetNote: Note
	): string {
		switch (change.type) {
			case 'dependency_created':
				return `筆記「${sourceNote.title}」與「${targetNote.title}」建立了新的依賴關係`;
			case 'dependency_updated':
				return `筆記「${sourceNote.title}」與「${targetNote.title}」的依賴關係強度發生變化`;
			case 'dependency_deleted':
				return `筆記「${sourceNote.title}」與「${targetNote.title}」的依賴關係已移除`;
			default:
				return `筆記「${sourceNote.title}」的依賴關係發生變化`;
		}
	}

	/**
	 * 計算通知嚴重程度
	 */
	private calculateSeverity(change: DependencyChange): 'low' | 'medium' | 'high' {
		if (change.type === 'dependency_deleted') {
			return change.strength > 0.7 ? 'high' : 'medium';
		}
		if (change.type === 'dependency_created') {
			return change.strength > 0.8 ? 'high' : change.strength > 0.5 ? 'medium' : 'low';
		}
		if (change.type === 'dependency_updated' && change.oldStrength) {
			const strengthDiff = Math.abs(change.strength - change.oldStrength);
			return strengthDiff > 0.3 ? 'high' : strengthDiff > 0.15 ? 'medium' : 'low';
		}
		return 'low';
	}

	/**
	 * 獲取受影響的訂閱
	 */
	private getAffectedSubscriptions(sourceId: string, targetId: string): NotificationSubscription[] {
		const affected: NotificationSubscription[] = [];

		for (const subscription of this.subscriptions.values()) {
			if (
				subscription.enabled &&
				(subscription.noteId === sourceId ||
					subscription.noteId === targetId ||
					subscription.dependencies.includes(sourceId) ||
					subscription.dependencies.includes(targetId))
			) {
				affected.push(subscription);
			}
		}

		return affected;
	}

	/**
	 * 更新依賴關係圖快照
	 */
	private async updateGraphSnapshot(notes: Note[]): Promise<void> {
		try {
			this.lastGraphSnapshot = await dependencyService.analyzeDependencies(notes);
		} catch (error) {
			console.error('Error updating graph snapshot:', error);
		}
	}

	/**
	 * 訂閱依賴關係通知
	 */
	subscribeToDependencyNotifications(noteId: string, dependencies: string[] = []): void {
		const subscription: NotificationSubscription = {
			noteId,
			dependencies,
			lastChecked: new Date(),
			enabled: true
		};

		this.subscriptions.set(noteId, subscription);
	}

	/**
	 * 取消訂閱
	 */
	unsubscribeFromNotifications(noteId: string): void {
		this.subscriptions.delete(noteId);
	}

	/**
	 * 註冊通知回調
	 */
	onNotification(callback: (notification: DependencyNotification) => void): void {
		this.notificationCallbacks.push(callback);
	}

	/**
	 * 移除通知回調
	 */
	removeNotificationCallback(callback: (notification: DependencyNotification) => void): void {
		if (!callback) {
			console.warn('Attempted to remove null or undefined notification callback');
			return;
		}

		const index = this.notificationCallbacks.indexOf(callback);
		if (index > -1) {
			this.notificationCallbacks.splice(index, 1);
		}
	}

	/**
	 * 通知所有回調
	 */
	private notifyCallbacks(notification: DependencyNotification): void {
		for (const callback of this.notificationCallbacks) {
			try {
				callback(notification);
			} catch (error) {
				console.error('Error in notification callback:', error);
			}
		}
	}

	/**
	 * 獲取所有通知
	 */
	getNotifications(): DependencyNotification[] {
		return [...this.notifications].sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
	}

	/**
	 * 獲取未讀通知
	 */
	getUnreadNotifications(): DependencyNotification[] {
		return this.notifications.filter(n => !n.read);
	}

	/**
	 * 標記通知為已讀
	 */
	markAsRead(notificationId: string): void {
		const notification = this.notifications.find(n => n.id === notificationId);
		if (notification) {
			notification.read = true;
		}
	}

	/**
	 * 標記所有通知為已讀
	 */
	markAllAsRead(): void {
		for (const notification of this.notifications) {
			notification.read = true;
		}
	}

	/**
	 * 清除通知
	 */
	clearNotification(notificationId: string): void {
		const index = this.notifications.findIndex(n => n.id === notificationId);
		if (index > -1) {
			this.notifications.splice(index, 1);
		}
	}

	/**
	 * 清除所有通知
	 */
	clearAllNotifications(): void {
		this.notifications = [];
	}

	/**
	 * 生成通知 ID
	 */
	private generateNotificationId(): string {
		return `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
	}

	/**
	 * 獲取監控狀態
	 */
	isMonitoringActive(): boolean {
		return this.isMonitoring;
	}

	/**
	 * 獲取訂閱列表
	 */
	getSubscriptions(): NotificationSubscription[] {
		return Array.from(this.subscriptions.values());
	}
}

interface DependencyChange {
	type: 'dependency_created' | 'dependency_updated' | 'dependency_deleted';
	sourceId: string;
	targetId: string;
	strength: number;
	oldStrength?: number;
}

// 導出單例實例
export const dependencyNotificationService = new DependencyNotificationService();
