import {
  ValueObject,
  BusinessRuleViolationError,
} from "../../shared/Entity.js";

/**
 * 校驗和算法類型
 */
export enum ChecksumAlgorithm {
  MD5 = "md5",
  SHA1 = "sha1",
  SHA256 = "sha256",
  SHA512 = "sha512",
  CRC32 = "crc32",
}

/**
 * 校驗和值對象
 * 代表文件或內容的校驗和
 */
export class Checksum extends ValueObject {
  private readonly _value: string;
  private readonly _algorithm: ChecksumAlgorithm;

  constructor(
    value: string,
    algorithm: ChecksumAlgorithm = ChecksumAlgorithm.SHA256,
  ) {
    super();
    this.validateChecksum(value, algorithm);
    this._value = value.toLowerCase();
    this._algorithm = algorithm;
  }

  /**
   * 創建校驗和值對象
   */
  static create(
    value: string,
    algorithm: ChecksumAlgorithm = ChecksumAlgorithm.SHA256,
  ): Checksum {
    return new Checksum(value, algorithm);
  }

  /**
   * 從字符串創建校驗和（默認 SHA256）
   */
  static fromString(value: string): Checksum {
    return new Checksum(value, ChecksumAlgorithm.SHA256);
  }

  /**
   * 創建 MD5 校驗和
   */
  static md5(value: string): Checksum {
    return new Checksum(value, ChecksumAlgorithm.MD5);
  }

  /**
   * 創建 SHA1 校驗和
   */
  static sha1(value: string): Checksum {
    return new Checksum(value, ChecksumAlgorithm.SHA1);
  }

  /**
   * 創建 SHA256 校驗和
   */
  static sha256(value: string): Checksum {
    return new Checksum(value, ChecksumAlgorithm.SHA256);
  }

  /**
   * 創建 SHA512 校驗和
   */
  static sha512(value: string): Checksum {
    return new Checksum(value, ChecksumAlgorithm.SHA512);
  }

  /**
   * 創建 CRC32 校驗和
   */
  static crc32(value: string): Checksum {
    return new Checksum(value, ChecksumAlgorithm.CRC32);
  }

  /**
   * 驗證校驗和格式
   */
  private validateChecksum(value: string, algorithm: ChecksumAlgorithm): void {
    if (!value || value.trim().length === 0) {
      throw new BusinessRuleViolationError("Checksum cannot be empty");
    }

    const trimmedValue = value.trim();

    // 檢查是否只包含十六進制字符
    if (!/^[a-fA-F0-9]+$/.test(trimmedValue)) {
      throw new BusinessRuleViolationError(
        "Checksum must contain only hexadecimal characters",
      );
    }

    // 根據算法檢查長度
    const expectedLengths: Record<ChecksumAlgorithm, number> = {
      [ChecksumAlgorithm.MD5]: 32,
      [ChecksumAlgorithm.SHA1]: 40,
      [ChecksumAlgorithm.SHA256]: 64,
      [ChecksumAlgorithm.SHA512]: 128,
      [ChecksumAlgorithm.CRC32]: 8,
    };

    const expectedLength = expectedLengths[algorithm];
    if (trimmedValue.length !== expectedLength) {
      throw new BusinessRuleViolationError(
        `Invalid ${algorithm.toUpperCase()} checksum length. Expected ${expectedLength}, got ${trimmedValue.length}`,
      );
    }
  }

  /**
   * 獲取校驗和值
   */
  get value(): string {
    return this._value;
  }

  /**
   * 獲取算法類型
   */
  get algorithm(): ChecksumAlgorithm {
    return this._algorithm;
  }

  /**
   * 獲取算法名稱（大寫）
   */
  get algorithmName(): string {
    return this._algorithm.toUpperCase();
  }

  /**
   * 獲取校驗和長度
   */
  get length(): number {
    return this._value.length;
  }

  /**
   * 檢查是否為強校驗和算法
   */
  isStrongAlgorithm(): boolean {
    return [ChecksumAlgorithm.SHA256, ChecksumAlgorithm.SHA512].includes(
      this._algorithm,
    );
  }

  /**
   * 檢查是否為弱校驗和算法
   */
  isWeakAlgorithm(): boolean {
    return [ChecksumAlgorithm.MD5, ChecksumAlgorithm.CRC32].includes(
      this._algorithm,
    );
  }

  /**
   * 檢查是否為加密哈希算法
   */
  isCryptographicHash(): boolean {
    return [
      ChecksumAlgorithm.MD5,
      ChecksumAlgorithm.SHA1,
      ChecksumAlgorithm.SHA256,
      ChecksumAlgorithm.SHA512,
    ].includes(this._algorithm);
  }

  /**
   * 獲取校驗和的前綴（用於顯示）
   */
  getPrefix(length: number = 8): string {
    return this._value.substring(0, Math.min(length, this._value.length));
  }

  /**
   * 獲取校驗和的後綴（用於顯示）
   */
  getSuffix(length: number = 8): string {
    return this._value.substring(Math.max(0, this._value.length - length));
  }

  /**
   * 獲取縮短的校驗和（前綴...後綴）
   */
  getShortened(prefixLength: number = 8, suffixLength: number = 8): string {
    if (this._value.length <= prefixLength + suffixLength + 3) {
      return this._value;
    }
    return `${this.getPrefix(prefixLength)}...${this.getSuffix(suffixLength)}`;
  }

  /**
   * 轉換為大寫格式
   */
  toUpperCase(): string {
    return this._value.toUpperCase();
  }

  /**
   * 轉換為帶分隔符的格式
   */
  toFormattedString(separator: string = ":", groupSize: number = 2): string {
    const groups: string[] = [];
    for (let i = 0; i < this._value.length; i += groupSize) {
      groups.push(this._value.substring(i, i + groupSize));
    }
    return groups.join(separator);
  }

  /**
   * 轉換為帶算法前綴的格式
   */
  toAlgorithmPrefixedString(): string {
    return `${this._algorithm}:${this._value}`;
  }

  /**
   * 比較兩個校驗和是否相等（忽略算法）
   */
  valueEquals(other: Checksum): boolean {
    return this._value === other._value;
  }

  /**
   * 比較兩個校驗和是否完全相等（包括算法）
   */
  strictEquals(other: Checksum): boolean {
    return this._value === other._value && this._algorithm === other._algorithm;
  }

  /**
   * 驗證內容是否匹配此校驗和
   * 注意：這裡只是接口定義，實際計算需要在應用層實現
   */
  verify(content: string | Buffer): Promise<boolean> {
    // 這裡應該調用實際的哈希計算函數
    // 由於這是值對象，我們不在這裡實現具體的哈希計算
    throw new Error(
      "Checksum verification must be implemented in the application layer",
    );
  }

  /**
   * 獲取安全等級
   */
  getSecurityLevel(): "low" | "medium" | "high" | "very-high" {
    switch (this._algorithm) {
      case ChecksumAlgorithm.CRC32:
        return "low";
      case ChecksumAlgorithm.MD5:
        return "medium";
      case ChecksumAlgorithm.SHA1:
        return "medium";
      case ChecksumAlgorithm.SHA256:
        return "high";
      case ChecksumAlgorithm.SHA512:
        return "very-high";
      default:
        return "low";
    }
  }

  equals(other: ValueObject): boolean {
    if (!(other instanceof Checksum)) {
      return false;
    }
    return this._value === other._value && this._algorithm === other._algorithm;
  }

  hashCode(): string {
    return `${this._algorithm}:${this._value}`;
  }

  toString(): string {
    return this._value;
  }

  toPlainObject(): Record<string, unknown> {
    return {
      value: this._value,
      algorithm: this._algorithm,
      algorithmName: this.algorithmName,
      length: this.length,
      isStrongAlgorithm: this.isStrongAlgorithm(),
      isWeakAlgorithm: this.isWeakAlgorithm(),
      isCryptographicHash: this.isCryptographicHash(),
      securityLevel: this.getSecurityLevel(),
    };
  }

  static fromPlainObject(data: Record<string, unknown>): Checksum {
    if (typeof data.value !== "string" || typeof data.algorithm !== "string") {
      throw new BusinessRuleViolationError("Invalid Checksum data");
    }

    const algorithm = data.algorithm as ChecksumAlgorithm;
    if (!Object.values(ChecksumAlgorithm).includes(algorithm)) {
      throw new BusinessRuleViolationError("Invalid checksum algorithm");
    }

    return new Checksum(data.value, algorithm);
  }

  /**
   * 從帶算法前綴的字符串解析
   */
  static fromAlgorithmPrefixedString(value: string): Checksum {
    const parts = value.split(":");
    if (parts.length !== 2) {
      throw new BusinessRuleViolationError(
        "Invalid algorithm-prefixed checksum format",
      );
    }

    const [algorithmStr, checksumValue] = parts;
    const algorithm = algorithmStr as ChecksumAlgorithm;

    if (!Object.values(ChecksumAlgorithm).includes(algorithm)) {
      throw new BusinessRuleViolationError("Invalid checksum algorithm");
    }

    return new Checksum(checksumValue, algorithm);
  }
}
