import { z } from 'zod';
import type { Note } from '$types';
import { noteStore } from '$stores/notes';
import { searchService } from '$lib/services/searchService';
import { dependencyService } from '$lib/services/dependencyService';
import { get } from 'svelte/store';
import {
	CreateNoteArgsSchema,
	UpdateNoteArgsSchema,
	DeleteNoteArgsSchema,
	GetNoteArgsSchema,
	SearchNotesArgsSchema,
	AnalyzeDependenciesArgsSchema,
	GetDependencyGraphArgsSchema,
	type CreateNoteArgs,
	type UpdateNoteArgs,
	type DeleteNoteArgs,
	type GetNoteArgs,
	type SearchNotesArgs,
	type AnalyzeDependenciesArgs,
	type GetDependencyGraphArgs
} from './types';

// MCP 工具執行結果類型
export interface MCPToolResult {
	content: Array<{
		type: 'text' | 'image' | 'resource';
		text?: string;
		data?: string;
		mimeType?: string;
	}>;
	isError?: boolean;
}

// 錯誤處理輔助函數
function createErrorResult(message: string, details?: any): MCPToolResult {
	return {
		content: [
			{
				type: 'text',
				text: JSON.stringify(
					{
						error: message,
						details: details || null,
						timestamp: new Date().toISOString()
					},
					null,
					2
				)
			}
		],
		isError: true
	};
}

// 成功結果輔助函數
function createSuccessResult(data: any, message?: string): MCPToolResult {
	return {
		content: [
			{
				type: 'text',
				text: JSON.stringify(
					{
						success: true,
						message: message || 'Operation completed successfully',
						data,
						timestamp: new Date().toISOString()
					},
					null,
					2
				)
			}
		]
	};
}

// 筆記管理工具實現
export class NoteTools {
	/**
	 * 創建新筆記
	 */
	static async createNote(args: unknown): Promise<MCPToolResult> {
		try {
			const validatedArgs = CreateNoteArgsSchema.parse(args);

			const newNote: Omit<Note, 'id' | 'createdAt' | 'updatedAt'> = {
				title: validatedArgs.title,
				content: validatedArgs.content,
				excerpt: validatedArgs.content.substring(0, 200),
				tags: validatedArgs.tags.map(name => ({ name })),
				authorId: 'system', // 系統用戶
				categoryId: validatedArgs.categoryId,
				priority: validatedArgs.priority,
				status: validatedArgs.status
			};

			// 使用 noteStore 創建筆記
			const createdNote = await noteStore.createNote(newNote);

			return createSuccessResult(createdNote, `Note "${validatedArgs.title}" created successfully`);
		} catch (error) {
			if (error instanceof z.ZodError) {
				return createErrorResult('Invalid arguments', error.errors);
			}
			return createErrorResult('Failed to create note', error);
		}
	}

	/**
	 * 更新筆記
	 */
	static async updateNote(args: unknown): Promise<MCPToolResult> {
		try {
			const validatedArgs = UpdateNoteArgsSchema.parse(args);

			// 獲取現有筆記
			const currentNotes = get(noteStore).notes;
			const existingNote = currentNotes.find(note => note.id === validatedArgs.id);

			if (!existingNote) {
				return createErrorResult(`Note with ID "${validatedArgs.id}" not found`);
			}

			// 準備更新數據
			const updateData: Partial<Note> = {};
			if (validatedArgs.title !== undefined) updateData.title = validatedArgs.title;
			if (validatedArgs.content !== undefined) {
				updateData.content = validatedArgs.content;
				updateData.excerpt = validatedArgs.content.substring(0, 200);
			}
			if (validatedArgs.tags !== undefined) {
				updateData.tags = validatedArgs.tags.map(name => ({ name }));
			}
			if (validatedArgs.categoryId !== undefined) updateData.categoryId = validatedArgs.categoryId;
			if (validatedArgs.priority !== undefined) updateData.priority = validatedArgs.priority;
			if (validatedArgs.status !== undefined) updateData.status = validatedArgs.status;

			// 使用 noteStore 更新筆記
			const updatedNote = await noteStore.updateNote(validatedArgs.id, updateData);

			return createSuccessResult(updatedNote, `Note "${validatedArgs.id}" updated successfully`);
		} catch (error) {
			if (error instanceof z.ZodError) {
				return createErrorResult('Invalid arguments', error.errors);
			}
			return createErrorResult('Failed to update note', error);
		}
	}

	/**
	 * 刪除筆記
	 */
	static async deleteNote(args: unknown): Promise<MCPToolResult> {
		try {
			const validatedArgs = DeleteNoteArgsSchema.parse(args);

			// 檢查筆記是否存在
			const currentNotes = get(noteStore).notes;
			const existingNote = currentNotes.find(note => note.id === validatedArgs.id);

			if (!existingNote) {
				return createErrorResult(`Note with ID "${validatedArgs.id}" not found`);
			}

			// 使用 noteStore 刪除筆記
			await noteStore.deleteNote(validatedArgs.id);

			return createSuccessResult(
				{ id: validatedArgs.id, title: existingNote.title },
				`Note "${existingNote.title}" deleted successfully`
			);
		} catch (error) {
			if (error instanceof z.ZodError) {
				return createErrorResult('Invalid arguments', error.errors);
			}
			return createErrorResult('Failed to delete note', error);
		}
	}

	/**
	 * 獲取筆記
	 */
	static async getNote(args: unknown): Promise<MCPToolResult> {
		try {
			const validatedArgs = GetNoteArgsSchema.parse(args);

			const currentNotes = get(noteStore).notes;
			const note = currentNotes.find(note => note.id === validatedArgs.id);

			if (!note) {
				return createErrorResult(`Note with ID "${validatedArgs.id}" not found`);
			}

			return createSuccessResult(note, `Note "${note.title}" retrieved successfully`);
		} catch (error) {
			if (error instanceof z.ZodError) {
				return createErrorResult('Invalid arguments', error.errors);
			}
			return createErrorResult('Failed to get note', error);
		}
	}

	/**
	 * 搜索筆記
	 */
	static async searchNotes(args: unknown): Promise<MCPToolResult> {
		try {
			const validatedArgs = SearchNotesArgsSchema.parse(args);

			const currentNotes = get(noteStore).notes;
			let filteredNotes = [...currentNotes];

			// 應用過濾條件
			if (validatedArgs.query) {
				const searchResults = await searchService.search(validatedArgs.query, {
					includeContent: true,
					includeTags: true,
					fuzzySearch: true
				});
				const searchNoteIds = new Set(searchResults.map(result => result.id));
				filteredNotes = filteredNotes.filter(note => searchNoteIds.has(note.id));
			}

			if (validatedArgs.tags && validatedArgs.tags.length > 0) {
				filteredNotes = filteredNotes.filter(note =>
					validatedArgs.tags!.some(tag => note.tags.some(noteTag => noteTag.name === tag))
				);
			}

			if (validatedArgs.status) {
				filteredNotes = filteredNotes.filter(note => note.status === validatedArgs.status);
			}

			if (validatedArgs.priority) {
				filteredNotes = filteredNotes.filter(note => note.priority === validatedArgs.priority);
			}

			if (validatedArgs.categoryId) {
				filteredNotes = filteredNotes.filter(note => note.categoryId === validatedArgs.categoryId);
			}

			// 應用分頁
			const total = filteredNotes.length;
			const paginatedNotes = filteredNotes.slice(
				validatedArgs.offset,
				validatedArgs.offset + validatedArgs.limit
			);

			const result = {
				notes: paginatedNotes,
				pagination: {
					total,
					limit: validatedArgs.limit,
					offset: validatedArgs.offset,
					hasMore: validatedArgs.offset + validatedArgs.limit < total
				}
			};

			return createSuccessResult(result, `Found ${total} notes matching criteria`);
		} catch (error) {
			if (error instanceof z.ZodError) {
				return createErrorResult('Invalid arguments', error.errors);
			}
			return createErrorResult('Failed to search notes', error);
		}
	}
}

// 依賴關係分析工具實現
export class DependencyTools {
	/**
	 * 分析依賴關係
	 */
	static async analyzeDependencies(args: unknown): Promise<MCPToolResult> {
		try {
			const validatedArgs = AnalyzeDependenciesArgsSchema.parse(args);

			const currentNotes = get(noteStore).notes;
			let notesToAnalyze = currentNotes;

			// 如果指定了特定的筆記 ID，則只分析這些筆記
			if (validatedArgs.noteIds && validatedArgs.noteIds.length > 0) {
				notesToAnalyze = currentNotes.filter(note => validatedArgs.noteIds!.includes(note.id));
			}

			if (notesToAnalyze.length === 0) {
				return createErrorResult('No notes found to analyze');
			}

			// 使用依賴關係服務進行分析
			const analysisOptions = {
				includeTagConnections: validatedArgs.includeTagConnections,
				includeCategoryConnections: validatedArgs.includeCategoryConnections,
				includeContentSimilarity: validatedArgs.includeContentSimilarity,
				similarityThreshold: validatedArgs.similarityThreshold,
				maxDistance: validatedArgs.maxDistance,
				excludeIsolatedNodes: validatedArgs.excludeIsolatedNodes
			};

			const dependencyGraph = await dependencyService.analyzeDependencies(
				notesToAnalyze,
				analysisOptions
			);

			const result = {
				graph: dependencyGraph,
				analysisOptions,
				summary: {
					totalNotesAnalyzed: notesToAnalyze.length,
					nodesInGraph: dependencyGraph.nodes.length,
					connections: dependencyGraph.links.length,
					clusters: dependencyGraph.metadata.clusters,
					density: dependencyGraph.metadata.density
				}
			};

			return createSuccessResult(
				result,
				`Dependency analysis completed for ${notesToAnalyze.length} notes`
			);
		} catch (error) {
			if (error instanceof z.ZodError) {
				return createErrorResult('Invalid arguments', error.errors);
			}
			return createErrorResult('Failed to analyze dependencies', error);
		}
	}

	/**
	 * 獲取特定筆記的依賴關係圖
	 */
	static async getDependencyGraph(args: unknown): Promise<MCPToolResult> {
		try {
			const validatedArgs = GetDependencyGraphArgsSchema.parse(args);

			const currentNotes = get(noteStore).notes;
			const targetNote = currentNotes.find(note => note.id === validatedArgs.noteId);

			if (!targetNote) {
				return createErrorResult(`Note with ID "${validatedArgs.noteId}" not found`);
			}

			// 分析所有筆記的依賴關係
			const fullGraph = await dependencyService.analyzeDependencies(currentNotes);

			// 查找目標筆記的鄰居
			const neighbors = dependencyService.findNeighbors(
				validatedArgs.noteId,
				fullGraph,
				validatedArgs.depth
			);

			// 創建子圖，包含目標節點和其鄰居
			const relevantNodeIds = new Set([validatedArgs.noteId, ...neighbors]);
			const subGraphNodes = fullGraph.nodes.filter(node => relevantNodeIds.has(node.id));
			const subGraphLinks = fullGraph.links.filter(link => {
				const sourceId = typeof link.source === 'string' ? link.source : link.source.id;
				const targetId = typeof link.target === 'string' ? link.target : link.target.id;
				return relevantNodeIds.has(sourceId) && relevantNodeIds.has(targetId);
			});

			const subGraph = {
				nodes: subGraphNodes,
				links: subGraphLinks,
				metadata: {
					totalNodes: subGraphNodes.length,
					totalLinks: subGraphLinks.length,
					clusters: new Set(subGraphNodes.map(node => node.group)).size,
					density:
						subGraphNodes.length > 1
							? subGraphLinks.length / ((subGraphNodes.length * (subGraphNodes.length - 1)) / 2)
							: 0
				}
			};

			// 計算中心性
			const centrality = dependencyService.calculateCentrality(fullGraph);

			const result = {
				targetNote: {
					id: targetNote.id,
					title: targetNote.title,
					centrality: centrality.get(targetNote.id) || 0
				},
				subGraph,
				neighbors: neighbors.map(neighborId => {
					const neighborNote = currentNotes.find(note => note.id === neighborId);
					return {
						id: neighborId,
						title: neighborNote?.title || 'Unknown',
						centrality: centrality.get(neighborId) || 0
					};
				}),
				analysis: {
					depth: validatedArgs.depth,
					totalNeighbors: neighbors.length,
					directConnections: subGraphLinks.filter(link => {
						const sourceId = typeof link.source === 'string' ? link.source : link.source.id;
						const targetId = typeof link.target === 'string' ? link.target : link.target.id;
						return sourceId === validatedArgs.noteId || targetId === validatedArgs.noteId;
					}).length
				}
			};

			return createSuccessResult(
				result,
				`Dependency graph retrieved for note "${targetNote.title}"`
			);
		} catch (error) {
			if (error instanceof z.ZodError) {
				return createErrorResult('Invalid arguments', error.errors);
			}
			return createErrorResult('Failed to get dependency graph', error);
		}
	}
}

// 工具路由器 - 根據工具名稱調用對應的實現
export class MCPToolRouter {
	static async executeTool(toolName: string, args: unknown): Promise<MCPToolResult> {
		try {
			switch (toolName) {
				// 筆記管理工具
				case 'create_note':
					return await NoteTools.createNote(args);
				case 'update_note':
					return await NoteTools.updateNote(args);
				case 'delete_note':
					return await NoteTools.deleteNote(args);
				case 'get_note':
					return await NoteTools.getNote(args);
				case 'search_notes':
					return await NoteTools.searchNotes(args);

				// 依賴關係分析工具
				case 'analyze_dependencies':
					return await DependencyTools.analyzeDependencies(args);
				case 'get_dependency_graph':
					return await DependencyTools.getDependencyGraph(args);

				default:
					return createErrorResult(`Unknown tool: ${toolName}`);
			}
		} catch (error) {
			return createErrorResult(`Tool execution failed: ${toolName}`, error);
		}
	}

	/**
	 * 獲取所有可用工具的列表
	 */
	static getAvailableTools(): string[] {
		return [
			'create_note',
			'update_note',
			'delete_note',
			'get_note',
			'search_notes',
			'analyze_dependencies',
			'get_dependency_graph'
		];
	}

	/**
	 * 檢查工具是否存在
	 */
	static isValidTool(toolName: string): boolean {
		return this.getAvailableTools().includes(toolName);
	}

	/**
	 * 獲取工具的幫助信息
	 */
	static getToolHelp(toolName: string): string | null {
		const helpTexts: Record<string, string> = {
			create_note: 'Create a new note with title, content, tags, and metadata. Required: title',
			update_note: 'Update an existing note by ID. Required: id',
			delete_note: 'Delete a note by ID. Required: id',
			get_note: 'Get a note by ID. Required: id',
			search_notes: 'Search notes with various filters and pagination. All parameters optional',
			analyze_dependencies: 'Analyze dependencies between notes and generate a dependency graph',
			get_dependency_graph: 'Get the dependency graph for a specific note. Required: noteId'
		};

		return helpTexts[toolName] || null;
	}
}
