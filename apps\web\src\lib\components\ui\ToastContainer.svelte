<script lang="ts">
	import { toastStore } from '$stores/toast';
	import Toast from './Toast.svelte';

	$: toasts = $toastStore.toasts;

	const handleToastClose = (id: string) => {
		toastStore.remove(id);
	};
</script>

<!-- Toast Container -->
<div class="fixed top-4 right-4 z-50 flex flex-col space-y-2 w-full max-w-sm">
	{#each toasts as toast (toast.id)}
		<div class="animate-slide-up">
			<Toast
				variant={toast.variant}
				title={toast.title}
				description={toast.description}
				duration={toast.duration}
				closable={toast.closable}
				on:close={() => handleToastClose(toast.id)}
			/>
		</div>
	{/each}
</div>
