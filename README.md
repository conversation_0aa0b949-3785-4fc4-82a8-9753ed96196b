# Life Note Client Vibe

> 本地端智慧知識庫筆記系統 - 混合架構桌面應用程式

## 📋 項目概覽

Life Note Client Vibe 是一個採用本地優先策略的智慧知識庫筆記系統，具備以下核心特色：

- **版本控制知識管理**：基於 Git 概念的 Tag-based 版本控制，支援 draft-v1 → v1 → draft-v2 流程
- **智慧依賴關係檢測**：自動識別和標記專案內文件間的依賴關係
- **混合 AI 助理**：本地輕量級 Agent + 遠端協作 Agent 的分層處理架構
- **多格式筆記支援**：Markdown 檔案存儲 + YAML frontmatter + SQLite 索引
- **跨平台部署**：支援 iOS、Android、Windows 等多平台應用

## 🏗️ 技術架構

### 核心技術棧

- **前端**：React 18+ + TypeScript 5.0+ + Vite 4.0+
- **狀態管理**：Zustand + React Query
- **UI 框架**：Tailwind CSS + AG-UI 核心套件
- **後端**：Node.js 18+ + Fastify 4.0+
- **資料庫**：SQLite 3.40+ + Prisma ORM
- **AI 整合**：MCP + Google Gemini 2.5 Flash
- **桌面應用**：Tauri 1.5+
- **測試**：Vitest + React Testing Library + Playwright

### 項目結構

```
life-note-client-vibe/
├── apps/                           # 應用程式
│   ├── desktop/                    # Tauri 桌面應用
│   ├── mobile/                     # Capacitor 移動應用
│   └── web/                        # Web 應用
├── packages/                       # 共享套件
│   ├── core/                       # 核心業務邏輯
│   ├── ui/                         # UI 組件庫
│   ├── agents/                     # AI Agent 實現
│   ├── storage/                    # 存儲抽象層
│   └── utils/                      # 工具函數
├── services/                       # 後端服務
│   ├── local-agent/                # 本地 Agent 服務
│   ├── sync-service/               # 同步服務
│   └── ai-gateway/                 # AI 網關服務
├── docs/                           # 文檔
├── tools/                          # 開發工具
└── tests/                          # 測試
```

## 🚀 快速開始

### 環境要求

- Node.js 18.0.0 或更高版本
- pnpm 8.0.0 或更高版本
- Git

### 安裝依賴

```bash
# 克隆項目
git clone https://github.com/your-org/life-note-client-vibe.git
cd life-note-client-vibe

# 安裝依賴
pnpm install

# 複製環境變數模板
cp .env.example .env
```

### 配置環境變數

編輯 `.env` 文件，設置必要的環境變數：

```bash
# AI 服務配置
VITE_GEMINI_API_KEY=your_gemini_api_key_here

# 資料庫配置
DATABASE_URL=file:./data/life-note.db

# 安全配置
JWT_SECRET=your_jwt_secret_here
ENCRYPTION_KEY=your_encryption_key_here
```

### 開發模式

```bash
# 啟動所有服務
pnpm dev

# 或分別啟動
pnpm --filter @life-note/web dev          # Web 應用
pnpm --filter @life-note/local-agent dev  # 本地 Agent 服務
```

### 構建

```bash
# 構建所有包
pnpm build

# 構建特定包
pnpm --filter @life-note/web build
```

## 📚 開發指南

### 代碼規範

- 使用 TypeScript 進行類型安全開發
- 遵循 ESLint 和 Prettier 配置
- 提交前自動運行 lint 和格式化

### 測試

```bash
# 運行所有測試
pnpm test

# 運行特定包的測試
pnpm --filter @life-note/core test

# 運行測試覆蓋率
pnpm test:coverage
```

### 代碼提交

項目使用 Conventional Commits 規範：

```bash
# 功能開發
git commit -m "feat: add note creation functionality"

# 錯誤修復
git commit -m "fix: resolve dependency analysis issue"

# 文檔更新
git commit -m "docs: update API documentation"
```

## 🔧 配置說明

### MCP Server 配置

項目集成了 Model Context Protocol (MCP) 服務器，支援：

- 筆記管理工具
- 依賴關係分析
- 版本控制操作
- AI 助理整合

### AI 服務配置

支援多個 AI 提供商：

- Google Gemini 2.5 Flash（主要）
- OpenAI GPT-4（備用）
- Anthropic Claude（備用）

### 安全性

- 本地優先的數據處理
- 分層脫敏處理
- OAuth 2.0 認證
- 加密存儲

## 📖 文檔

- [開發步驟指南](./.serena/memories/03-dev-step.md)
- [MCP 實現指南](./.serena/memories/03-mcp-implementation.md)
- [技術架構文檔](./.serena/memories/02-project-overview.md)
- [API 文檔](./docs/api.md)

## 🤝 貢獻

歡迎貢獻代碼！請閱讀 [貢獻指南](./CONTRIBUTING.md) 了解詳細信息。

## 📄 許可證

本項目採用 MIT 許可證 - 查看 [LICENSE](./LICENSE) 文件了解詳情。

## 🔗 相關鏈接

- [項目主頁](https://github.com/your-org/life-note-client-vibe)
- [問題追蹤](https://github.com/your-org/life-note-client-vibe/issues)
- [討論區](https://github.com/your-org/life-note-client-vibe/discussions)
