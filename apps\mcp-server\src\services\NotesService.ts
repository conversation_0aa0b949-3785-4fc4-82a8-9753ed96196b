import { readdir, readFile, writeFile, stat, mkdir } from "fs/promises";
import { existsSync } from "fs";
import { join, extname, basename, dirname } from "path";
import { v4 as uuidv4 } from "uuid";
import matter from "gray-matter";
import MarkdownIt from "markdown-it";

import { logger } from "../utils/logger.js";

/**
 * 筆記接口
 */
export interface Note {
  id: string;
  title: string;
  content: string;
  excerpt?: string;
  tags: string[];
  category?: string;
  priority?: "low" | "medium" | "high" | "urgent";
  status?: "draft" | "published" | "archived";
  createdAt: Date;
  updatedAt: Date;
  filePath: string;
  metadata?: Record<string, any>;
}

/**
 * 筆記創建參數
 */
export interface CreateNoteParams {
  title: string;
  content: string;
  tags?: string[];
  category?: string;
  priority?: "low" | "medium" | "high" | "urgent";
  status?: "draft" | "published" | "archived";
  metadata?: Record<string, any>;
}

/**
 * 筆記更新參數
 */
export interface UpdateNoteParams {
  title?: string;
  content?: string;
  tags?: string[];
  category?: string;
  priority?: "low" | "medium" | "high" | "urgent";
  status?: "draft" | "published" | "archived";
  metadata?: Record<string, any>;
}

/**
 * 筆記查詢參數
 */
export interface NoteQueryParams {
  tags?: string[];
  category?: string;
  status?: string;
  priority?: string;
  search?: string;
  limit?: number;
  offset?: number;
  sortBy?: "title" | "createdAt" | "updatedAt";
  sortOrder?: "asc" | "desc";
}

/**
 * 筆記服務
 *
 * 負責管理筆記的 CRUD 操作、文件系統交互和元數據處理
 */
export class NotesService {
  private notesDir: string;
  private notesCache: Map<string, Note> = new Map();
  private isInitialized = false;
  private markdown: MarkdownIt;

  constructor(notesDir: string) {
    this.notesDir = notesDir;
    this.markdown = new MarkdownIt({
      html: true,
      linkify: true,
      typographer: true,
    });
  }

  /**
   * 初始化服務
   */
  async initialize(): Promise<void> {
    try {
      // 確保筆記目錄存在
      if (!existsSync(this.notesDir)) {
        await mkdir(this.notesDir, { recursive: true });
        logger.info(`Created notes directory: ${this.notesDir}`);
      }

      // 載入所有筆記到緩存
      await this.loadAllNotes();

      this.isInitialized = true;
      logger.info(
        `NotesService initialized with ${this.notesCache.size} notes`,
      );
    } catch (error) {
      logger.error("Failed to initialize NotesService:", error);
      throw error;
    }
  }

  /**
   * 載入所有筆記
   */
  private async loadAllNotes(): Promise<void> {
    try {
      const files = await this.findMarkdownFiles(this.notesDir);

      for (const filePath of files) {
        try {
          const note = await this.loadNoteFromFile(filePath);
          if (note) {
            this.notesCache.set(note.id, note);
          }
        } catch (error) {
          logger.warn(`Failed to load note from ${filePath}:`, error);
        }
      }
    } catch (error) {
      logger.error("Failed to load notes:", error);
      throw error;
    }
  }

  /**
   * 遞歸查找 Markdown 文件
   */
  private async findMarkdownFiles(dir: string): Promise<string[]> {
    const files: string[] = [];

    try {
      const entries = await readdir(dir, { withFileTypes: true });

      for (const entry of entries) {
        const fullPath = join(dir, entry.name);

        if (entry.isDirectory()) {
          const subFiles = await this.findMarkdownFiles(fullPath);
          files.push(...subFiles);
        } else if (
          entry.isFile() &&
          [".md", ".markdown"].includes(extname(entry.name))
        ) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      logger.warn(`Failed to read directory ${dir}:`, error);
    }

    return files;
  }

  /**
   * 從文件載入筆記
   */
  private async loadNoteFromFile(filePath: string): Promise<Note | null> {
    try {
      const content = await readFile(filePath, "utf-8");
      const { data: frontmatter, content: markdownContent } = matter(content);
      const stats = await stat(filePath);

      // 生成或使用現有的 ID
      const id = frontmatter.id || this.generateNoteId(filePath);

      const note: Note = {
        id,
        title:
          frontmatter.title ||
          this.extractTitleFromContent(markdownContent) ||
          basename(filePath, extname(filePath)),
        content: markdownContent,
        excerpt: this.generateExcerpt(markdownContent),
        tags: Array.isArray(frontmatter.tags) ? frontmatter.tags : [],
        category: frontmatter.category,
        priority: frontmatter.priority || "medium",
        status: frontmatter.status || "draft",
        createdAt: frontmatter.createdAt
          ? new Date(frontmatter.createdAt)
          : stats.birthtime,
        updatedAt: frontmatter.updatedAt
          ? new Date(frontmatter.updatedAt)
          : stats.mtime,
        filePath,
        metadata: frontmatter,
      };

      return note;
    } catch (error) {
      logger.error(`Failed to load note from ${filePath}:`, error);
      return null;
    }
  }

  /**
   * 生成筆記 ID
   */
  private generateNoteId(filePath: string): string {
    // 使用文件路徑的相對路徑作為基礎生成 ID
    const relativePath = filePath.replace(this.notesDir, "").replace(/^\//, "");
    return relativePath.replace(/[\/\\]/g, "-").replace(/\.[^.]+$/, "");
  }

  /**
   * 從內容中提取標題
   */
  private extractTitleFromContent(content: string): string | null {
    const lines = content.split("\n");
    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed.startsWith("# ")) {
        return trimmed.substring(2).trim();
      }
    }
    return null;
  }

  /**
   * 生成摘要
   */
  private generateExcerpt(content: string, maxLength = 200): string {
    // 移除 Markdown 語法
    const plainText = content
      .replace(/#{1,6}\s+/g, "") // 標題
      .replace(/\*\*(.*?)\*\*/g, "$1") // 粗體
      .replace(/\*(.*?)\*/g, "$1") // 斜體
      .replace(/`(.*?)`/g, "$1") // 行內代碼
      .replace(/\[([^\]]+)\]\([^)]+\)/g, "$1") // 鏈接
      .replace(/!\[([^\]]*)\]\([^)]+\)/g, "$1") // 圖片
      .replace(/\n+/g, " ") // 換行
      .trim();

    if (plainText.length <= maxLength) {
      return plainText;
    }

    return plainText.substring(0, maxLength).trim() + "...";
  }

  /**
   * 獲取所有筆記
   */
  async getAllNotes(): Promise<Note[]> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    return Array.from(this.notesCache.values());
  }

  /**
   * 根據 ID 獲取筆記
   */
  async getNoteById(id: string): Promise<Note | null> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    return this.notesCache.get(id) || null;
  }

  /**
   * 查詢筆記
   */
  async queryNotes(params: NoteQueryParams): Promise<Note[]> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    let notes = Array.from(this.notesCache.values());

    // 過濾條件
    if (params.tags && params.tags.length > 0) {
      notes = notes.filter((note) =>
        params.tags!.some((tag) => note.tags.includes(tag)),
      );
    }

    if (params.category) {
      notes = notes.filter((note) => note.category === params.category);
    }

    if (params.status) {
      notes = notes.filter((note) => note.status === params.status);
    }

    if (params.priority) {
      notes = notes.filter((note) => note.priority === params.priority);
    }

    if (params.search) {
      const searchTerm = params.search.toLowerCase();
      notes = notes.filter(
        (note) =>
          note.title.toLowerCase().includes(searchTerm) ||
          note.content.toLowerCase().includes(searchTerm) ||
          note.tags.some((tag) => tag.toLowerCase().includes(searchTerm)),
      );
    }

    // 排序
    const sortBy = params.sortBy || "updatedAt";
    const sortOrder = params.sortOrder || "desc";

    notes.sort((a, b) => {
      let aValue: any = a[sortBy];
      let bValue: any = b[sortBy];

      if (aValue instanceof Date) {
        aValue = aValue.getTime();
      }
      if (bValue instanceof Date) {
        bValue = bValue.getTime();
      }

      if (sortOrder === "asc") {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    // 分頁
    const offset = params.offset || 0;
    const limit = params.limit || notes.length;

    return notes.slice(offset, offset + limit);
  }

  /**
   * 創建筆記
   */
  async createNote(params: CreateNoteParams): Promise<Note> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const id = uuidv4();
    const now = new Date();

    const note: Note = {
      id,
      title: params.title,
      content: params.content,
      excerpt: this.generateExcerpt(params.content),
      tags: params.tags || [],
      category: params.category,
      priority: params.priority || "medium",
      status: params.status || "draft",
      createdAt: now,
      updatedAt: now,
      filePath: await this.generateFilePath(params.title, id),
      metadata: params.metadata || {},
    };

    // 保存到文件
    await this.saveNoteToFile(note);

    // 添加到緩存
    this.notesCache.set(id, note);

    logger.info(`Created note: ${note.title} (${id})`);
    return note;
  }

  /**
   * 更新筆記
   */
  async updateNote(id: string, params: UpdateNoteParams): Promise<Note | null> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const existingNote = this.notesCache.get(id);
    if (!existingNote) {
      return null;
    }

    const updatedNote: Note = {
      ...existingNote,
      ...params,
      id, // 確保 ID 不變
      updatedAt: new Date(),
    };

    // 如果內容改變，重新生成摘要
    if (params.content !== undefined) {
      updatedNote.excerpt = this.generateExcerpt(params.content);
    }

    // 保存到文件
    await this.saveNoteToFile(updatedNote);

    // 更新緩存
    this.notesCache.set(id, updatedNote);

    logger.info(`Updated note: ${updatedNote.title} (${id})`);
    return updatedNote;
  }

  /**
   * 刪除筆記
   */
  async deleteNote(id: string): Promise<boolean> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const note = this.notesCache.get(id);
    if (!note) {
      return false;
    }

    try {
      // 刪除文件
      const { unlink } = await import("fs/promises");
      await unlink(note.filePath);

      // 從緩存中移除
      this.notesCache.delete(id);

      logger.info(`Deleted note: ${note.title} (${id})`);
      return true;
    } catch (error) {
      logger.error(`Failed to delete note ${id}:`, error);
      return false;
    }
  }

  /**
   * 生成文件路徑
   */
  private async generateFilePath(title: string, id: string): Promise<string> {
    // 清理標題作為文件名
    const cleanTitle = title
      .replace(/[^\w\s-]/g, "")
      .replace(/\s+/g, "-")
      .toLowerCase();

    const fileName = `${cleanTitle}-${id.substring(0, 8)}.md`;
    return join(this.notesDir, fileName);
  }

  /**
   * 保存筆記到文件
   */
  private async saveNoteToFile(note: Note): Promise<void> {
    try {
      // 確保目錄存在
      const dir = dirname(note.filePath);
      if (!existsSync(dir)) {
        await mkdir(dir, { recursive: true });
      }

      // 準備 frontmatter
      const frontmatter = {
        id: note.id,
        title: note.title,
        tags: note.tags,
        category: note.category,
        priority: note.priority,
        status: note.status,
        createdAt: note.createdAt.toISOString(),
        updatedAt: note.updatedAt.toISOString(),
        ...note.metadata,
      };

      // 生成文件內容
      const fileContent = matter.stringify(note.content, frontmatter);

      // 寫入文件
      await writeFile(note.filePath, fileContent, "utf-8");
    } catch (error) {
      logger.error(`Failed to save note to file ${note.filePath}:`, error);
      throw error;
    }
  }

  /**
   * 獲取筆記數量
   */
  getNotesCount(): number {
    return this.notesCache.size;
  }

  /**
   * 更改筆記目錄
   */
  async changeNotesDirectory(newNotesDir: string): Promise<void> {
    this.notesDir = newNotesDir;
    this.notesCache.clear();
    this.isInitialized = false;
    await this.initialize();
  }

  /**
   * 重新載入筆記
   */
  async reload(): Promise<void> {
    this.notesCache.clear();
    await this.loadAllNotes();
    logger.info(`Reloaded ${this.notesCache.size} notes`);
  }

  /**
   * 獲取筆記統計
   */
  getStats() {
    const notes = Array.from(this.notesCache.values());

    return {
      total: notes.length,
      byStatus: this.groupBy(notes, "status"),
      byPriority: this.groupBy(notes, "priority"),
      byCategory: this.groupBy(notes, "category"),
      totalTags: new Set(notes.flatMap((note) => note.tags)).size,
    };
  }

  /**
   * 分組統計輔助函數
   */
  private groupBy(items: Note[], key: keyof Note): Record<string, number> {
    return items.reduce(
      (acc, item) => {
        const value = String(item[key] || "unknown");
        acc[value] = (acc[value] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>,
    );
  }
}
