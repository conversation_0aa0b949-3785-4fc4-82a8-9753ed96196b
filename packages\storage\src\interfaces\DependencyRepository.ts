import {
  Dependency,
  DependencyType,
  DependencyStrength,
  DependencyId,
  NoteId,
} from "@life-note/core";

import { ExtendedRepository } from "./Repository.js";

/**
 * 依賴關係查詢參數
 */
export interface DependencyQueryParams {
  sourceNoteId?: NoteId;
  targetNoteId?: NoteId;
  type?: DependencyType;
  strength?: DependencyStrength;
  isActive?: boolean;
  createdAfter?: Date;
  createdBefore?: Date;
}

/**
 * 依賴關係統計信息
 */
export interface DependencyStatistics {
  totalDependencies: number;
  activeDependencies: number;
  dependenciesByType: Record<DependencyType, number>;
  dependenciesByStrength: Record<DependencyStrength, number>;
  averageDependenciesPerNote: number;
  mostDependentNotes: Array<{ noteId: NoteId; dependencyCount: number }>;
  mostReferencedNotes: Array<{ noteId: NoteId; referenceCount: number }>;
}

/**
 * 依賴關係圖節點
 */
export interface DependencyGraphNode {
  noteId: NoteId;
  dependencies: DependencyId[];
  dependents: DependencyId[];
  depth: number;
}

/**
 * 依賴關係 Repository 接口
 */
export interface IDependencyRepository
  extends ExtendedRepository<Dependency, DependencyId> {
  /**
   * 根據源筆記 ID 查找依賴關係
   */
  findBySourceNoteId(sourceNoteId: NoteId): Promise<Dependency[]>;

  /**
   * 根據目標筆記 ID 查找依賴關係
   */
  findByTargetNoteId(targetNoteId: NoteId): Promise<Dependency[]>;

  /**
   * 查找兩個筆記之間的依賴關係
   */
  findBetweenNotes(
    sourceNoteId: NoteId,
    targetNoteId: NoteId,
  ): Promise<Dependency[]>;

  /**
   * 根據類型查找依賴關係
   */
  findByType(type: DependencyType): Promise<Dependency[]>;

  /**
   * 根據強度查找依賴關係
   */
  findByStrength(strength: DependencyStrength): Promise<Dependency[]>;

  /**
   * 查找活躍的依賴關係
   */
  findActive(): Promise<Dependency[]>;

  /**
   * 複合查詢
   */
  findByQuery(params: DependencyQueryParams): Promise<Dependency[]>;

  /**
   * 獲取筆記的所有依賴（遞歸）
   */
  getAllDependencies(
    noteId: NoteId,
    maxDepth?: number,
  ): Promise<DependencyGraphNode[]>;

  /**
   * 獲取依賴於該筆記的所有筆記（遞歸）
   */
  getAllDependents(
    noteId: NoteId,
    maxDepth?: number,
  ): Promise<DependencyGraphNode[]>;

  /**
   * 檢查是否存在循環依賴
   */
  checkForCycles(): Promise<NoteId[][]>;

  /**
   * 檢查添加依賴關係是否會創建循環
   */
  wouldCreateCycle(
    sourceNoteId: NoteId,
    targetNoteId: NoteId,
  ): Promise<boolean>;

  /**
   * 獲取依賴關係統計信息
   */
  getStatistics(): Promise<DependencyStatistics>;

  /**
   * 獲取筆記的直接依賴
   */
  getDirectDependencies(noteId: NoteId): Promise<Dependency[]>;

  /**
   * 獲取筆記的直接被依賴關係
   */
  getDirectDependents(noteId: NoteId): Promise<Dependency[]>;

  /**
   * 批量停用依賴關係
   */
  deactivateBatch(dependencyIds: DependencyId[]): Promise<void>;

  /**
   * 批量激活依賴關係
   */
  activateBatch(dependencyIds: DependencyId[]): Promise<void>;

  /**
   * 刪除筆記的所有依賴關係
   */
  deleteByNoteId(noteId: NoteId): Promise<void>;

  /**
   * 更新依賴關係強度
   */
  updateStrength(
    dependencyId: DependencyId,
    strength: DependencyStrength,
  ): Promise<void>;

  /**
   * 查找孤立的筆記（沒有任何依賴關係的筆記）
   */
  findOrphanedNotes(): Promise<NoteId[]>;

  /**
   * 獲取依賴關係圖的鄰接表表示
   */
  getAdjacencyList(): Promise<Map<string, string[]>>;
}
