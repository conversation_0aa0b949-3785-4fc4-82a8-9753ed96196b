/* eslint-disable @typescript-eslint/no-unused-vars */
import { generateRandomString } from "@life-note/utils";

/**
 * 基礎實體類
 * 所有領域實體都應該繼承此類
 */
export abstract class Entity<T = string> {
  protected readonly _id: T;
  protected readonly _createdAt: Date;
  protected _updatedAt: Date;

  constructor(id?: T, createdAt?: Date) {
    this._id = id ?? (generateRandomString() as T);
    this._createdAt = createdAt ?? new Date();
    this._updatedAt = new Date();
  }

  get id(): T {
    return this._id;
  }

  get createdAt(): Date {
    return this._createdAt;
  }

  get updatedAt(): Date {
    return this._updatedAt;
  }

  protected touch(): void {
    this._updatedAt = new Date();
  }

  /**
   * 比較兩個實體是否相等
   */
  equals(other: Entity<T>): boolean {
    if (!(other instanceof Entity)) {
      return false;
    }
    return this._id === other._id;
  }

  /**
   * 獲取實體的哈希值
   */
  hashCode(): string {
    return String(this._id);
  }

  /**
   * 將實體轉換為普通對象
   */
  abstract toPlainObject(): Record<string, unknown>;
}

/**
 * 值對象基類
 * 值對象是不可變的，沒有標識符
 */
export abstract class ValueObject {
  /**
   * 比較兩個值對象是否相等
   */
  abstract equals(_other: ValueObject): boolean;

  /**
   * 獲取值對象的哈希值
   */
  abstract hashCode(): string;

  /**
   * 將值對象轉換為普通對象
   */
  abstract toPlainObject(): Record<string, unknown>;
}

/**
 * 聚合根基類
 * 聚合根是領域模型的入口點，負責維護業務不變性
 */
export abstract class AggregateRoot<T = string> extends Entity<T> {
  private _domainEvents: DomainEvent[] = [];

  /**
   * 添加領域事件
   */
  protected addDomainEvent(event: DomainEvent): void {
    this._domainEvents.push(event);
  }

  /**
   * 獲取所有領域事件
   */
  getDomainEvents(): DomainEvent[] {
    return [...this._domainEvents];
  }

  /**
   * 清除所有領域事件
   */
  clearDomainEvents(): void {
    this._domainEvents = [];
  }

  /**
   * 標記聚合根已被修改
   */
  protected markAsModified(): void {
    this.touch();
  }
}

/**
 * 領域事件基類
 */
export abstract class DomainEvent {
  public readonly occurredOn: Date;
  public readonly aggregateId: string;
  public readonly eventType: string;

  constructor(aggregateId: string, eventType: string) {
    this.aggregateId = aggregateId;
    this.eventType = eventType;
    this.occurredOn = new Date();
  }

  abstract toPlainObject(): Record<string, unknown>;
}

/**
 * 領域異常基類
 */
export abstract class DomainError extends Error {
  public readonly code: string;
  public readonly details?: Record<string, unknown>;

  constructor(
    message: string,
    code: string,
    details?: Record<string, unknown>,
  ) {
    super(message);
    this.name = this.constructor.name;
    this.code = code;
    this.details = details;
  }
}

/**
 * 業務規則違反異常
 */
export class BusinessRuleViolationError extends DomainError {
  constructor(message: string, details?: Record<string, unknown>) {
    super(message, "BUSINESS_RULE_VIOLATION", details);
  }
}

/**
 * 實體未找到異常
 */
export class EntityNotFoundError extends DomainError {
  constructor(entityType: string, id: string) {
    super(`${entityType} with id ${id} not found`, "ENTITY_NOT_FOUND", {
      entityType,
      id,
    });
  }
}

/**
 * 無效操作異常
 */
export class InvalidOperationError extends DomainError {
  constructor(message: string, details?: Record<string, unknown>) {
    super(message, "INVALID_OPERATION", details);
  }
}
