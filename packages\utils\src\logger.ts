export type LogLevel = "error" | "warn" | "info" | "debug";

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  meta?: Record<string, unknown>;
}

export class Logger {
  private level: LogLevel;
  private context?: string;

  constructor(level: LogLevel = "info", context?: string) {
    this.level = level;
    this.context = context;
  }

  private shouldLog(level: LogLevel): boolean {
    const levels: Record<LogLevel, number> = {
      error: 0,
      warn: 1,
      info: 2,
      debug: 3,
    };

    return levels[level] <= levels[this.level];
  }

  private formatMessage(
    level: LogLevel,
    message: string,
    meta?: Record<string, unknown>,
  ): string {
    const timestamp = new Date().toISOString();
    const contextStr = this.context ? `[${this.context}] ` : "";
    const metaStr = meta ? ` ${JSON.stringify(meta)}` : "";

    return `${timestamp} [${level.toUpperCase()}] ${contextStr}${message}${metaStr}`;
  }

  error(message: string, meta?: Record<string, unknown>): void {
    if (this.shouldLog("error")) {
      // eslint-disable-next-line no-console
      console.error(this.formatMessage("error", message, meta));
    }
  }

  warn(message: string, meta?: Record<string, unknown>): void {
    if (this.shouldLog("warn")) {
      // eslint-disable-next-line no-console
      console.warn(this.formatMessage("warn", message, meta));
    }
  }

  info(message: string, meta?: Record<string, unknown>): void {
    if (this.shouldLog("info")) {
      // eslint-disable-next-line no-console
      console.info(this.formatMessage("info", message, meta));
    }
  }

  debug(message: string, meta?: Record<string, unknown>): void {
    if (this.shouldLog("debug")) {
      // eslint-disable-next-line no-console
      console.debug(this.formatMessage("debug", message, meta));
    }
  }

  child(context: string): Logger {
    const childContext = this.context ? `${this.context}:${context}` : context;
    return new Logger(this.level, childContext);
  }
}

// 默認 logger 實例
export const logger = new Logger();

// 創建帶上下文的 logger
export function createLogger(context: string, level?: LogLevel): Logger {
  return new Logger(level || "info", context);
}
