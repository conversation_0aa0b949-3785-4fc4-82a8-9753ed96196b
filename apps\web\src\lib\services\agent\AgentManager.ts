import type {
	<PERSON>Agent,
	IAgentManager,
	AgentTaskRequest,
	AgentTaskResult,
	AgentTaskType,
	AgentSystemStatus,
	AgentEvent,
	AgentEventListener,
	TaskPriority
} from '$lib/types/agent';

/**
 * Agent 管理器實現
 * 負責管理所有 Agent 的註冊、任務分發和系統監控
 */
export class AgentManager implements IAgentManager {
	private agents = new Map<string, IAgent>();
	private taskQueue: AgentTaskRequest[] = [];
	private runningTasks = new Map<string, { agent: IAgent; request: AgentTaskRequest }>();
	private eventListeners = new Map<string, AgentEventListener[]>();
	private isProcessingQueue = false;
	private systemStartTime = Date.now();

	constructor() {
		// 啟動任務隊列處理
		this.startQueueProcessor();
	}

	/**
	 * 註冊 Agent
	 */
	async registerAgent(agent: IAgent): Promise<void> {
		if (this.agents.has(agent.id)) {
			throw new Error(`Agent with id ${agent.id} is already registered`);
		}

		try {
			// 初始化 Agent
			await agent.initialize();

			// 註冊 Agent
			this.agents.set(agent.id, agent);

			// 設置事件監聽
			this.setupAgentEventListeners(agent);

			this.emitEvent('agent_registered', { agentId: agent.id });
			console.log(`Agent ${agent.id} registered successfully`);
		} catch (error) {
			console.error(`Failed to register agent ${agent.id}:`, error);
			throw error;
		}
	}

	/**
	 * 取消註冊 Agent
	 */
	async unregisterAgent(agentId: string): Promise<void> {
		const agent = this.agents.get(agentId);
		if (!agent) {
			throw new Error(`Agent with id ${agentId} is not registered`);
		}

		try {
			// 停止 Agent
			await agent.stop();

			// 取消正在運行的任務
			const runningTasksToCancel = Array.from(this.runningTasks.entries())
				.filter(([_, { agent: taskAgent }]) => taskAgent.id === agentId)
				.map(([taskId]) => taskId);

			for (const taskId of runningTasksToCancel) {
				this.runningTasks.delete(taskId);
			}

			// 移除 Agent
			this.agents.delete(agentId);

			this.emitEvent('agent_unregistered', { agentId });
			console.log(`Agent ${agentId} unregistered successfully`);
		} catch (error) {
			console.error(`Failed to unregister agent ${agentId}:`, error);
			throw error;
		}
	}

	/**
	 * 獲取 Agent
	 */
	getAgent(agentId: string): IAgent | null {
		return this.agents.get(agentId) || null;
	}

	/**
	 * 獲取所有 Agent
	 */
	getAllAgents(): IAgent[] {
		return Array.from(this.agents.values());
	}

	/**
	 * 根據任務類型獲取可用 Agent
	 */
	getAvailableAgents(taskType: AgentTaskType): IAgent[] {
		return Array.from(this.agents.values()).filter(
			agent => agent.canHandle(taskType) && agent.status === 'idle' && agent.config.enabled
		);
	}

	/**
	 * 選擇最佳 Agent
	 */
	selectBestAgent(request: AgentTaskRequest): IAgent | null {
		const availableAgents = this.getAvailableAgents(request.type);

		if (availableAgents.length === 0) {
			return null;
		}

		// 根據多個因素選擇最佳 Agent
		return availableAgents.reduce((best, current) => {
			const bestScore = this.calculateAgentScore(best, request);
			const currentScore = this.calculateAgentScore(current, request);
			return currentScore > bestScore ? current : best;
		});
	}

	/**
	 * 執行任務
	 */
	async executeTask(request: AgentTaskRequest): Promise<AgentTaskResult> {
		// 選擇最佳 Agent
		const agent = this.selectBestAgent(request);

		if (!agent) {
			// 如果沒有可用的 Agent，加入隊列
			this.taskQueue.push(request);
			this.sortTaskQueue();

			// 等待任務被處理
			return new Promise((resolve, reject) => {
				const checkInterval = setInterval(() => {
					const runningTask = this.runningTasks.get(request.id);
					if (runningTask) {
						clearInterval(checkInterval);
						// 任務已開始執行，等待結果
						this.waitForTaskCompletion(request.id).then(resolve).catch(reject);
					}
				}, 100);

				// 設置超時
				const timeout = request.timeout || 30000;
				setTimeout(() => {
					clearInterval(checkInterval);
					// 從隊列中移除任務
					const queueIndex = this.taskQueue.findIndex(task => task.id === request.id);
					if (queueIndex > -1) {
						this.taskQueue.splice(queueIndex, 1);
					}
					reject(new Error('Task execution timeout'));
				}, timeout);
			});
		}

		// 直接執行任務
		this.runningTasks.set(request.id, { agent, request });

		try {
			const result = await agent.executeTask(request);
			this.runningTasks.delete(request.id);
			return result;
		} catch (error) {
			this.runningTasks.delete(request.id);
			throw error;
		}
	}

	/**
	 * 獲取系統狀態
	 */
	getSystemStatus(): AgentSystemStatus {
		const agents = Array.from(this.agents.values());
		const activeAgents = agents.filter(agent => agent.status !== 'offline').length;
		const totalTasks = agents.reduce((sum, agent) => sum + agent.metrics.totalTasks, 0);
		const runningTasks = this.runningTasks.size;
		const queuedTasks = this.taskQueue.length;

		// 計算系統負載
		const systemLoad =
			agents.length > 0
				? agents.reduce((sum, agent) => sum + agent.getCurrentLoad(), 0) / agents.length
				: 0;

		return {
			totalAgents: agents.length,
			activeAgents,
			totalTasks,
			runningTasks,
			queuedTasks,
			systemLoad,
			uptime: Date.now() - this.systemStartTime
		};
	}

	/**
	 * 添加事件監聽器
	 */
	addEventListener(eventType: string, listener: AgentEventListener): void {
		if (!this.eventListeners.has(eventType)) {
			this.eventListeners.set(eventType, []);
		}
		this.eventListeners.get(eventType)!.push(listener);
	}

	/**
	 * 移除事件監聽器
	 */
	removeEventListener(eventType: string, listener: AgentEventListener): void {
		const listeners = this.eventListeners.get(eventType);
		if (listeners) {
			const index = listeners.indexOf(listener);
			if (index > -1) {
				listeners.splice(index, 1);
			}
		}
	}

	/**
	 * 計算 Agent 分數
	 */
	private calculateAgentScore(agent: IAgent, request: AgentTaskRequest): number {
		let score = 0;

		// 基於成功率
		score += agent.metrics.successRate * 40;

		// 基於當前負載（負載越低分數越高）
		score += (1 - agent.getCurrentLoad()) * 30;

		// 基於平均執行時間（時間越短分數越高）
		const avgTime = agent.metrics.averageExecutionTime;
		if (avgTime > 0) {
			score += Math.max(0, 30 - avgTime / 1000); // 假設 30 秒為基準
		} else {
			score += 30; // 新 Agent 給予基準分數
		}

		// 基於能力級別
		const capabilityBonus = {
			basic: 0,
			intermediate: 5,
			advanced: 10,
			expert: 15
		};
		score += capabilityBonus[agent.config.capabilityLevel] || 0;

		return score;
	}

	/**
	 * 設置 Agent 事件監聽
	 */
	private setupAgentEventListeners(agent: IAgent): void {
		// 這裡可以添加對 Agent 事件的監聽
		// 例如任務完成、狀態變更等
	}

	/**
	 * 啟動任務隊列處理器
	 */
	private startQueueProcessor(): void {
		setInterval(() => {
			if (!this.isProcessingQueue && this.taskQueue.length > 0) {
				this.processQueue();
			}
		}, 1000); // 每秒檢查一次隊列
	}

	/**
	 * 處理任務隊列
	 */
	private async processQueue(): Promise<void> {
		if (this.isProcessingQueue || this.taskQueue.length === 0) {
			return;
		}

		this.isProcessingQueue = true;

		try {
			const task = this.taskQueue[0];
			const agent = this.selectBestAgent(task);

			if (agent) {
				// 移除任務從隊列
				this.taskQueue.shift();

				// 執行任務
				this.runningTasks.set(task.id, { agent, request: task });

				try {
					await agent.executeTask(task);
				} catch (error) {
					console.error(`Task ${task.id} failed:`, error);
				} finally {
					this.runningTasks.delete(task.id);
				}
			}
		} finally {
			this.isProcessingQueue = false;
		}
	}

	/**
	 * 排序任務隊列
	 */
	private sortTaskQueue(): void {
		const priorityOrder: Record<TaskPriority, number> = {
			urgent: 4,
			high: 3,
			normal: 2,
			low: 1
		};

		this.taskQueue.sort((a, b) => {
			// 首先按優先級排序
			const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
			if (priorityDiff !== 0) {
				return priorityDiff;
			}

			// 然後按創建時間排序（先進先出）
			return a.createdAt.getTime() - b.createdAt.getTime();
		});
	}

	/**
	 * 等待任務完成
	 */
	private async waitForTaskCompletion(taskId: string): Promise<AgentTaskResult> {
		return new Promise((resolve, reject) => {
			const checkInterval = setInterval(() => {
				const runningTask = this.runningTasks.get(taskId);
				if (!runningTask) {
					clearInterval(checkInterval);
					reject(new Error('Task not found or completed'));
				}
			}, 100);

			// 這裡需要實現任務完成的監聽機制
			// 簡化實現，實際應該使用事件監聽
			setTimeout(() => {
				clearInterval(checkInterval);
				reject(new Error('Task completion timeout'));
			}, 60000);
		});
	}

	/**
	 * 發送事件
	 */
	private emitEvent(type: string, data?: any): void {
		const event: AgentEvent = {
			type: type as any,
			data,
			timestamp: new Date()
		};

		const listeners = this.eventListeners.get(type);
		if (listeners) {
			listeners.forEach(listener => {
				try {
					listener(event);
				} catch (error) {
					console.error(`Error in event listener for ${type}:`, error);
				}
			});
		}
	}
}

// 導出單例實例
export const agentManager = new AgentManager();
