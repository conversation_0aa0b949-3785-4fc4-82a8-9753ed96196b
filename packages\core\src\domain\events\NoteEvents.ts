import { DomainEvent } from "../../shared/Entity.js";

/**
 * 筆記創建事件
 */
export class NoteCreatedEvent extends DomainEvent {
  public readonly title: string;

  constructor(noteId: string, title: string) {
    super(noteId, "NoteCreated");
    this.title = title;
  }

  toPlainObject(): Record<string, unknown> {
    return {
      aggregateId: this.aggregateId,
      eventType: this.eventType,
      occurredOn: this.occurredOn.toISOString(),
      title: this.title,
    };
  }
}

/**
 * 筆記更新事件
 */
export class NoteUpdatedEvent extends DomainEvent {
  public readonly title: string;

  constructor(noteId: string, title: string) {
    super(noteId, "NoteUpdated");
    this.title = title;
  }

  toPlainObject(): Record<string, unknown> {
    return {
      aggregateId: this.aggregateId,
      eventType: this.eventType,
      occurredOn: this.occurredOn.toISOString(),
      title: this.title,
    };
  }
}

/**
 * 筆記發布事件
 */
export class NotePublishedEvent extends DomainEvent {
  public readonly version: string;

  constructor(noteId: string, version: string) {
    super(noteId, "NotePublished");
    this.version = version;
  }

  toPlainObject(): Record<string, unknown> {
    return {
      aggregateId: this.aggregateId,
      eventType: this.eventType,
      occurredOn: this.occurredOn.toISOString(),
      version: this.version,
    };
  }
}

/**
 * 筆記歸檔事件
 */
export class NoteArchivedEvent extends DomainEvent {
  constructor(noteId: string) {
    super(noteId, "NoteArchived");
  }

  toPlainObject(): Record<string, unknown> {
    return {
      aggregateId: this.aggregateId,
      eventType: this.eventType,
      occurredOn: this.occurredOn.toISOString(),
    };
  }
}

/**
 * 筆記刪除事件
 */
export class NoteDeletedEvent extends DomainEvent {
  public readonly title: string;

  constructor(noteId: string, title: string) {
    super(noteId, "NoteDeleted");
    this.title = title;
  }

  toPlainObject(): Record<string, unknown> {
    return {
      aggregateId: this.aggregateId,
      eventType: this.eventType,
      occurredOn: this.occurredOn.toISOString(),
      title: this.title,
    };
  }
}

/**
 * 筆記標籤添加事件
 */
export class NoteTagAddedEvent extends DomainEvent {
  public readonly tagName: string;

  constructor(noteId: string, tagName: string) {
    super(noteId, "NoteTagAdded");
    this.tagName = tagName;
  }

  toPlainObject(): Record<string, unknown> {
    return {
      aggregateId: this.aggregateId,
      eventType: this.eventType,
      occurredOn: this.occurredOn.toISOString(),
      tagName: this.tagName,
    };
  }
}

/**
 * 筆記標籤移除事件
 */
export class NoteTagRemovedEvent extends DomainEvent {
  public readonly tagName: string;

  constructor(noteId: string, tagName: string) {
    super(noteId, "NoteTagRemoved");
    this.tagName = tagName;
  }

  toPlainObject(): Record<string, unknown> {
    return {
      aggregateId: this.aggregateId,
      eventType: this.eventType,
      occurredOn: this.occurredOn.toISOString(),
      tagName: this.tagName,
    };
  }
}
