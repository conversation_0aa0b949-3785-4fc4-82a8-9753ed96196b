<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { <PERSON><PERSON>, Card } from '$components/ui';
	import { Plus, FileText, Search, Filter, Grid, List, SortAsc, SortDesc } from 'lucide-svelte';
	import SearchBox from '$lib/components/search/SearchBox.svelte';
	import SearchFilters from '$lib/components/search/SearchFilters.svelte';
	import SearchResultCard from '$lib/components/search/SearchResultCard.svelte';
	import SearchPagination from '$lib/components/search/SearchPagination.svelte';
	import { searchStore, searchResults, searchPagination, hasActiveFilters } from '$stores/search';

	// Mock data for demonstration
	const mockNotes = [
		{
			id: '1',
			title: '歡迎使用 Life Note',
			content:
				'這是您的第一個筆記。Life Note 是一個功能強大的知識管理系統，支援 Markdown 編輯、版本控制、依賴關係追蹤等功能。',
			excerpt: '這是您的第一個筆記。Life Note 是一個功能強大的知識管理系統...',
			status: 'published',
			priority: 'high',
			tags: [{ name: '歡迎' }, { name: '開始' }],
			authorId: 'user-1',
			createdAt: new Date(),
			updatedAt: new Date()
		},
		{
			id: '2',
			title: 'Markdown 語法指南',
			content:
				'學習如何使用 Markdown 語法來格式化您的筆記內容。包括標題、列表、連結、圖片等基本語法。',
			excerpt: '學習如何使用 Markdown 語法來格式化您的筆記內容...',
			status: 'draft',
			priority: 'medium',
			tags: [{ name: 'Markdown' }, { name: '教學' }],
			authorId: 'user-1',
			createdAt: new Date(Date.now() - 86400000),
			updatedAt: new Date(Date.now() - 3600000)
		},
		{
			id: '3',
			title: '項目規劃筆記',
			content: '記錄項目的重要里程碑和待辦事項。包括時程安排、資源分配、風險評估等內容。',
			excerpt: '記錄項目的重要里程碑和待辦事項...',
			status: 'published',
			priority: 'urgent',
			tags: [{ name: '項目' }, { name: '規劃' }],
			authorId: 'user-1',
			createdAt: new Date(Date.now() - 172800000),
			updatedAt: new Date(Date.now() - 7200000)
		},
		{
			id: '4',
			title: 'TypeScript 學習筆記',
			content:
				'TypeScript 是 JavaScript 的超集，提供靜態類型檢查。學習基本類型、介面、泛型等概念。',
			excerpt: 'TypeScript 是 JavaScript 的超集，提供靜態類型檢查...',
			status: 'published',
			priority: 'medium',
			tags: [{ name: 'TypeScript' }, { name: '程式設計' }, { name: '學習' }],
			authorId: 'user-1',
			createdAt: new Date(Date.now() - 259200000),
			updatedAt: new Date(Date.now() - 86400000)
		},
		{
			id: '5',
			title: 'Svelte 框架介紹',
			content:
				'Svelte 是一個現代的前端框架，編譯時優化，運行時性能優異。學習組件、狀態管理、路由等。',
			excerpt: 'Svelte 是一個現代的前端框架，編譯時優化...',
			status: 'draft',
			priority: 'low',
			tags: [{ name: 'Svelte' }, { name: '前端' }, { name: '框架' }],
			authorId: 'user-1',
			createdAt: new Date(Date.now() - 345600000),
			updatedAt: new Date(Date.now() - 172800000)
		}
	];

	// UI 狀態
	let showCreateForm = false;
	let showAdvancedFilters = false;
	let viewMode: 'grid' | 'list' = 'grid';
	let sortBy = 'updatedAt';
	let sortOrder: 'asc' | 'desc' = 'desc';

	// 搜索狀態
	$: isSearchMode = $hasActiveFilters || $searchStore.hasSearched;
	$: displayNotes = isSearchMode ? $searchResults : mockSearchResults;
	$: pagination = $searchPagination;

	// 將 mock 數據轉換為搜索結果格式
	$: mockSearchResults = mockNotes.map(note => ({
		note,
		score: 1,
		highlights: {},
		matchedTags: []
	}));

	onMount(async () => {
		// 初始化搜索服務
		await searchStore.initialize();

		// 使用 mock 數據初始化搜索索引
		// 在真實應用中，這裡會從 API 加載數據
	});

	const handleCreateNote = () => {
		showCreateForm = true;
	};

	const handleSearch = async (event: CustomEvent<{ query: string }>) => {
		await searchStore.search({ query: event.detail.query });
	};

	const handleClearSearch = () => {
		searchStore.clearSearch();
	};

	const handleFilterChange = async (event: CustomEvent) => {
		const filters = event.detail;
		await searchStore.search(filters);
	};

	const handlePageChange = async (event: CustomEvent<{ page: number }>) => {
		await searchStore.goToPage(event.detail.page);
	};

	const toggleAdvancedFilters = () => {
		showAdvancedFilters = !showAdvancedFilters;
	};

	const toggleViewMode = () => {
		viewMode = viewMode === 'grid' ? 'list' : 'grid';
	};

	const toggleSortOrder = () => {
		sortOrder = sortOrder === 'asc' ? 'desc' : 'asc';
	};

	const handleViewNote = (event: CustomEvent<{ note: any }>) => {
		const noteId = event.detail.note.id;
		goto(`/notes/${noteId}`);
	};

	const handleEditNote = (event: CustomEvent<{ note: any }>) => {
		const noteId = event.detail.note.id;
		goto(`/notes/${noteId}/edit`);
	};

	const handleSelectNote = (event: CustomEvent<{ note: any }>) => {
		const noteId = event.detail.note.id;
		goto(`/notes/${noteId}`);
	};
</script>

<svelte:head>
	<title>筆記管理 - Life Note</title>
	<meta name="description" content="管理和組織您的筆記" />
</svelte:head>

<div class="container mx-auto px-4 py-8 space-y-6">
	<!-- Header -->
	<div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
		<div>
			<h1 class="text-3xl font-bold">我的筆記</h1>
			<p class="text-muted-foreground mt-1">
				{#if isSearchMode}
					搜索結果 {#if $searchStore.results}({$searchStore.results.total} 項){/if}
				{:else}
					管理和組織您的筆記
				{/if}
			</p>
		</div>

		<div class="flex items-center gap-2">
			<Button variant="outline" size="sm" onclick={toggleViewMode}>
				{#if viewMode === 'grid'}
					<List class="h-4 w-4" />
				{:else}
					<Grid class="h-4 w-4" />
				{/if}
			</Button>
			<Button onclick={handleCreateNote}>
				<Plus class="mr-2 h-4 w-4" />
				新建筆記
			</Button>
		</div>
	</div>

	<!-- Statistics -->
	{#if !isSearchMode}
		<div class="grid grid-cols-2 md:grid-cols-4 gap-4">
			<Card class="p-4">
				<div class="text-2xl font-bold">{mockNotes.length}</div>
				<div class="text-sm text-muted-foreground">總筆記</div>
			</Card>
			<Card class="p-4">
				<div class="text-2xl font-bold">{mockNotes.filter(n => n.status === 'draft').length}</div>
				<div class="text-sm text-muted-foreground">草稿</div>
			</Card>
			<Card class="p-4">
				<div class="text-2xl font-bold">
					{mockNotes.filter(n => n.status === 'published').length}
				</div>
				<div class="text-sm text-muted-foreground">已發布</div>
			</Card>
			<Card class="p-4">
				<div class="text-2xl font-bold">0</div>
				<div class="text-sm text-muted-foreground">已歸檔</div>
			</Card>
		</div>
	{/if}

	<!-- Search and Filters -->
	<Card class="p-4">
		<div class="space-y-4">
			<!-- Search Box -->
			<SearchBox
				placeholder="搜索筆記標題、內容、標籤..."
				on:search={handleSearch}
				on:clear={handleClearSearch}
				showSuggestions={true}
				showRecentSearches={true}
			/>

			<!-- Filter Controls -->
			<div class="flex items-center justify-between">
				<div class="flex items-center gap-2">
					<Button
						variant={showAdvancedFilters ? 'default' : 'outline'}
						size="sm"
						onclick={toggleAdvancedFilters}
					>
						<Filter class="mr-2 h-4 w-4" />
						高級篩選
						{#if $hasActiveFilters}
							<span
								class="ml-1 px-1.5 py-0.5 bg-primary-foreground text-primary text-xs rounded-full"
							>
								{$searchStore.filters.tags?.length ||
									0 + $searchStore.filters.status?.length ||
									0 + $searchStore.filters.priority?.length ||
									0}
							</span>
						{/if}
					</Button>

					{#if isSearchMode}
						<Button variant="ghost" size="sm" onclick={handleClearSearch}>清除搜索</Button>
					{/if}
				</div>

				<div class="flex items-center gap-2">
					<Button variant="ghost" size="sm" on:click={toggleSortOrder}>
						{#if sortOrder === 'desc'}
							<SortDesc class="h-4 w-4" />
						{:else}
							<SortAsc class="h-4 w-4" />
						{/if}
					</Button>
				</div>
			</div>
		</div>
	</Card>

	<!-- Advanced Filters -->
	<SearchFilters
		bind:show={showAdvancedFilters}
		filters={$searchStore.filters}
		popularTags={$searchStore.popularTags}
		on:update={handleFilterChange}
		on:clear={handleClearSearch}
	/>

	<!-- Search Results / Notes List -->
	{#if $searchStore.isSearching}
		<div class="flex items-center justify-center py-12">
			<div class="text-center">
				<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
				<p class="text-muted-foreground">搜索中...</p>
			</div>
		</div>
	{:else if displayNotes.length > 0}
		<div class="space-y-4">
			<!-- Results Header -->
			{#if isSearchMode && $searchStore.results}
				<div class="flex items-center justify-between text-sm text-muted-foreground">
					<span>
						找到 {$searchStore.results.total} 個結果
						{#if $searchStore.filters.query}
							，關鍵字："{$searchStore.filters.query}"
						{/if}
					</span>
					<span>
						第 {$searchStore.results.page} 頁，共 {$searchStore.results.totalPages} 頁
					</span>
				</div>
			{/if}

			<!-- Notes Grid/List -->
			<div
				class="grid gap-4 {viewMode === 'grid' ? 'md:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1'}"
			>
				{#each displayNotes as result (result.note.id)}
					<SearchResultCard
						{result}
						showScore={isSearchMode}
						on:view={handleViewNote}
						on:edit={handleEditNote}
						on:select={handleSelectNote}
					/>
				{/each}
			</div>

			<!-- Pagination -->
			{#if pagination && pagination.totalPages > 1}
				<SearchPagination
					page={pagination.page}
					totalPages={pagination.totalPages}
					total={pagination.total}
					limit={pagination.limit}
					hasNext={pagination.hasNext}
					hasPrev={pagination.hasPrev}
					onpageChange={handlePageChange}
				/>
			{/if}
		</div>
	{:else if isSearchMode}
		<!-- No Search Results -->
		<Card class="p-12 text-center">
			<Search class="h-12 w-12 mx-auto mb-4 opacity-50" />
			<h3 class="text-lg font-semibold mb-2">沒有找到相關筆記</h3>
			<p class="text-muted-foreground mb-4">嘗試調整搜索關鍵字或篩選條件</p>
			<Button variant="outline" onclick={handleClearSearch}>清除搜索</Button>
		</Card>
	{:else}
		<!-- Empty State -->
		<Card class="p-12 text-center">
			<FileText class="h-12 w-12 mx-auto mb-4 opacity-50" />
			<h3 class="text-lg font-semibold mb-2">還沒有筆記</h3>
			<p class="text-muted-foreground mb-4">開始創建您的第一個筆記吧</p>
			<Button on:click={handleCreateNote}>
				<Plus class="mr-2 h-4 w-4" />
				新建筆記
			</Button>
		</Card>
	{/if}

	<!-- Feature Demo Notice -->
	<Card class="p-6 text-center bg-gradient-to-r from-primary/5 to-secondary/5 border-primary/20">
		<Search class="h-12 w-12 mx-auto mb-4 text-primary" />
		<h3 class="text-lg font-semibold mb-2">🎉 搜索和過濾系統已上線！</h3>
		<p class="text-muted-foreground mb-4">
			全新的搜索功能現已可用！支援全文搜索、標籤過濾、狀態篩選、優先級分類和日期範圍查詢。
		</p>
		<div class="flex flex-wrap gap-2 justify-center">
			<span class="px-3 py-1 bg-green-500/10 text-green-600 rounded-full text-sm">✅ 全文搜索</span>
			<span class="px-3 py-1 bg-green-500/10 text-green-600 rounded-full text-sm">✅ 智能建議</span>
			<span class="px-3 py-1 bg-green-500/10 text-green-600 rounded-full text-sm">✅ 高級篩選</span>
			<span class="px-3 py-1 bg-green-500/10 text-green-600 rounded-full text-sm">✅ 分頁導航</span>
			<span class="px-3 py-1 bg-green-500/10 text-green-600 rounded-full text-sm">✅ 搜索歷史</span>
		</div>
		<div class="mt-4 text-sm text-muted-foreground">
			<p>💡 試試搜索 "TypeScript"、"學習" 或使用高級篩選功能！</p>
		</div>
	</Card>
</div>
