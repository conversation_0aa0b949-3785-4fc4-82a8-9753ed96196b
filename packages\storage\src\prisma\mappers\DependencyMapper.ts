/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  Dependency,
  DependencyId,
  DependencyType,
  DependencyStrength,
  NoteId,
} from "@life-note/core";

import type { Dependency as PrismaDependency } from "../generated/index.js";

/**
 * 依賴關係數據映射器
 * 負責在領域對象和數據庫對象之間進行轉換
 */
export class DependencyMapper {
  /**
   * 將 Prisma 依賴關係對象轉換為領域依賴關係對象
   */
  static toDomain(prismaDependency: PrismaDependency): Dependency {
    // 解析元數據 JSON
    let metadata: Record<string, unknown> = {};
    try {
      metadata = JSON.parse(prismaDependency.metadata);
    } catch (error) {
      metadata = {};
    }

    return new Dependency(
      DependencyId.fromString(prismaDependency.id),
      NoteId.fromString(prismaDependency.sourceNoteId),
      NoteId.fromString(prismaDependency.targetNoteId),
      prismaDependency.type as DependencyType,
      {
        strength: prismaDependency.strength as DependencyStrength,
        description: prismaDependency.description || undefined,
        metadata,
        isActive: prismaDependency.isActive,
        createdAt: prismaDependency.createdAt,
      },
    );
  }

  /**
   * 將領域依賴關係對象轉換為 Prisma 創建數據
   */
  static toPrismaCreate(
    dependency: Dependency,
  ): Omit<PrismaDependency, "createdAt" | "updatedAt"> {
    return {
      id: dependency.id.value,
      sourceNoteId: dependency.sourceNoteId.value,
      targetNoteId: dependency.targetNoteId.value,
      type: dependency.type,
      strength: dependency.strength,
      description: dependency.description || null,
      metadata: JSON.stringify(dependency.metadata),
      isActive: dependency.isActive,
    };
  }

  /**
   * 將領域依賴關係對象轉換為 Prisma 更新數據
   */
  static toPrismaUpdate(
    dependency: Dependency,
  ): Partial<
    Omit<
      PrismaDependency,
      "id" | "sourceNoteId" | "targetNoteId" | "createdAt" | "updatedAt"
    >
  > {
    return {
      type: dependency.type,
      strength: dependency.strength,
      description: dependency.description || null,
      metadata: JSON.stringify(dependency.metadata),
      isActive: dependency.isActive,
    };
  }

  /**
   * 批量轉換 Prisma 依賴關係對象為領域對象
   */
  static toDomainList(prismaDependencies: PrismaDependency[]): Dependency[] {
    return prismaDependencies.map((prismaDependency) =>
      this.toDomain(prismaDependency),
    );
  }

  /**
   * 將查詢參數轉換為 Prisma where 條件
   */
  static toWhereClause(params: {
    sourceNoteId?: NoteId;
    targetNoteId?: NoteId;
    type?: DependencyType;
    strength?: DependencyStrength;
    isActive?: boolean;
    createdAfter?: Date;
    createdBefore?: Date;
  }): Record<string, unknown> {
    const where: Record<string, unknown> = {};

    if (params.sourceNoteId) {
      where.sourceNoteId = params.sourceNoteId.value;
    }

    if (params.targetNoteId) {
      where.targetNoteId = params.targetNoteId.value;
    }

    if (params.type) {
      where.type = params.type;
    }

    if (params.strength) {
      where.strength = params.strength;
    }

    if (params.isActive !== undefined) {
      where.isActive = params.isActive;
    }

    if (params.createdAfter || params.createdBefore) {
      where.createdAt = {};
      if (params.createdAfter) {
        (where.createdAt as Record<string, unknown>).gte = params.createdAfter;
      }
      if (params.createdBefore) {
        (where.createdAt as Record<string, unknown>).lte = params.createdBefore;
      }
    }

    return where;
  }

  /**
   * 將排序參數轉換為 Prisma orderBy 條件
   */
  static toOrderByClause(
    sortBy?: string,
    sortOrder?: "asc" | "desc",
  ): Record<string, string> | undefined {
    if (!sortBy) {
      return { createdAt: "desc" }; // 默認按創建時間降序
    }

    const validSortFields = [
      "type",
      "strength",
      "createdAt",
      "updatedAt",
      "isActive",
    ];
    if (!validSortFields.includes(sortBy)) {
      return { createdAt: "desc" };
    }

    return { [sortBy]: sortOrder || "asc" };
  }

  /**
   * 創建雙向依賴關係的 where 條件
   */
  static toBidirectionalWhereClause(
    noteId1: NoteId,
    noteId2: NoteId,
  ): Record<string, unknown> {
    return {
      OR: [
        {
          sourceNoteId: noteId1.value,
          targetNoteId: noteId2.value,
        },
        {
          sourceNoteId: noteId2.value,
          targetNoteId: noteId1.value,
        },
      ],
    };
  }

  /**
   * 創建涉及特定筆記的 where 條件
   */
  static toInvolvingNoteWhereClause(noteId: NoteId): Record<string, unknown> {
    return {
      OR: [{ sourceNoteId: noteId.value }, { targetNoteId: noteId.value }],
    };
  }

  /**
   * 驗證依賴關係數據完整性
   */
  static validatePrismaDependency(prismaDependency: PrismaDependency): boolean {
    try {
      // 驗證必需字段
      if (
        !prismaDependency.id ||
        !prismaDependency.sourceNoteId ||
        !prismaDependency.targetNoteId
      ) {
        return false;
      }

      // 驗證不能自己依賴自己
      if (prismaDependency.sourceNoteId === prismaDependency.targetNoteId) {
        return false;
      }

      // 驗證 JSON 字段
      JSON.parse(prismaDependency.metadata);

      // 驗證枚舉值
      const validTypes = Object.values(DependencyType);
      const validStrengths = Object.values(DependencyStrength);

      if (!validTypes.includes(prismaDependency.type as DependencyType)) {
        return false;
      }

      if (
        !validStrengths.includes(
          prismaDependency.strength as DependencyStrength,
        )
      ) {
        return false;
      }

      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 創建依賴關係統計查詢的 groupBy 條件
   */
  static createStatisticsGroupBy(): {
    byType: Record<string, unknown>;
    byStrength: Record<string, unknown>;
    bySourceNote: Record<string, unknown>;
    byTargetNote: Record<string, unknown>;
  } {
    return {
      byType: {
        by: ["type"],
        _count: { type: true },
        where: { isActive: true },
      },
      byStrength: {
        by: ["strength"],
        _count: { strength: true },
        where: { isActive: true },
      },
      bySourceNote: {
        by: ["sourceNoteId"],
        _count: { sourceNoteId: true },
        where: { isActive: true },
        orderBy: { _count: { sourceNoteId: "desc" } },
        take: 10,
      },
      byTargetNote: {
        by: ["targetNoteId"],
        _count: { targetNoteId: true },
        where: { isActive: true },
        orderBy: { _count: { targetNoteId: "desc" } },
        take: 10,
      },
    };
  }
}
