import {
  Note,
  NoteStatus,
  NotePriority,
  Tag,
  NoteId,
  UserId,
} from "@life-note/core";

import { ExtendedRepository, PaginatedResult } from "./Repository.js";

/**
 * 筆記查詢參數
 */
export interface NoteQueryParams {
  authorId?: UserId;
  status?: NoteStatus;
  priority?: NotePriority;
  category?: string;
  tags?: Tag[];
  title?: string;
  content?: string;
  createdAfter?: Date;
  createdBefore?: Date;
  updatedAfter?: Date;
  updatedBefore?: Date;
}

/**
 * 筆記統計信息
 */
export interface NoteStatistics {
  totalNotes: number;
  notesByStatus: Record<NoteStatus, number>;
  notesByPriority: Record<NotePriority, number>;
  notesByCategory: Record<string, number>;
  averageNotesPerDay: number;
  mostUsedTags: Array<{ tag: Tag; count: number }>;
}

/**
 * 筆記 Repository 接口
 */
export interface INoteRepository extends ExtendedRepository<Note, NoteId> {
  /**
   * 根據作者 ID 查找筆記
   */
  findByAuthorId(authorId: UserId): Promise<Note[]>;

  /**
   * 根據狀態查找筆記
   */
  findByStatus(status: NoteStatus): Promise<Note[]>;

  /**
   * 根據標籤查找筆記
   */
  findByTags(tags: Tag[]): Promise<Note[]>;

  /**
   * 根據分類查找筆記
   */
  findByCategory(category: string): Promise<Note[]>;

  /**
   * 全文搜索筆記
   */
  searchByContent(query: string): Promise<Note[]>;

  /**
   * 複合查詢
   */
  findByQuery(params: NoteQueryParams): Promise<PaginatedResult<Note>>;

  /**
   * 獲取筆記統計信息
   */
  getStatistics(authorId?: UserId): Promise<NoteStatistics>;

  /**
   * 獲取最近更新的筆記
   */
  findRecentlyUpdated(limit: number, authorId?: UserId): Promise<Note[]>;

  /**
   * 獲取最近創建的筆記
   */
  findRecentlyCreated(limit: number, authorId?: UserId): Promise<Note[]>;

  /**
   * 根據文件路徑查找筆記
   */
  findByFilePath(filePath: string): Promise<Note | null>;

  /**
   * 根據校驗和查找筆記
   */
  findByChecksum(checksum: string): Promise<Note | null>;

  /**
   * 獲取所有分類
   */
  getAllCategories(): Promise<string[]>;

  /**
   * 獲取所有標籤
   */
  getAllTags(): Promise<Tag[]>;

  /**
   * 更新筆記的文件路徑
   */
  updateFilePath(noteId: NoteId, filePath: string): Promise<void>;

  /**
   * 更新筆記的校驗和
   */
  updateChecksum(noteId: NoteId, checksum: string): Promise<void>;

  /**
   * 批量更新筆記狀態
   */
  updateStatusBatch(noteIds: NoteId[], status: NoteStatus): Promise<void>;
}
