# 業務邏輯設計

## 核心業務領域

### 1. 知識管理領域 (Knowledge Management Domain)

#### 筆記生命週期管理

```typescript
enum NoteStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  ARCHIVED = 'archived',
  DELETED = 'deleted',
}

class Note {
  id: string;
  title: string;
  content: string;
  status: NoteStatus;
  version: string;
  tags: string[];
  dependencies: string[];
  metadata: NoteMetadata;

  // 業務方法
  publish(): void {
    if (this.status !== NoteStatus.DRAFT) {
      throw new Error('Only draft notes can be published');
    }
    this.status = NoteStatus.PUBLISHED;
    this.version = this.generateNextVersion();
  }

  createDraft(): Note {
    if (this.status !== NoteStatus.PUBLISHED) {
      throw new Error('Can only create draft from published notes');
    }
    return new Note({
      ...this,
      id: generateId(),
      status: NoteStatus.DRAFT,
      version: `draft-${this.getNextDraftVersion()}`,
    });
  }
}
```

#### 版本控制業務邏輯

```typescript
class VersionController {
  async createVersion(noteId: string, type: 'major' | 'minor' | 'patch'): Promise<Version> {
    const currentNote = await this.noteRepository.findById(noteId);
    const newVersion = this.calculateNextVersion(currentNote.version, type);

    // 業務規則：只有已發布的筆記才能創建新版本
    if (currentNote.status !== NoteStatus.PUBLISHED) {
      throw new BusinessError('Cannot version unpublished note');
    }

    // 檢查依賴關係
    await this.validateDependencies(currentNote);

    return this.versionRepository.create({
      noteId,
      version: newVersion,
      content: currentNote.content,
      timestamp: new Date(),
      author: currentNote.author,
    });
  }

  async rollback(noteId: string, targetVersion: string): Promise<void> {
    const note = await this.noteRepository.findById(noteId);
    const version = await this.versionRepository.findByVersion(noteId, targetVersion);

    // 業務規則：檢查回滾影響
    const dependentNotes = await this.dependencyService.findDependents(noteId);
    if (dependentNotes.length > 0) {
      throw new BusinessError('Cannot rollback note with dependencies');
    }

    note.content = version.content;
    note.version = targetVersion;
    await this.noteRepository.save(note);
  }
}
```

### 2. 依賴關係管理領域 (Dependency Management Domain)

#### 依賴檢測業務邏輯

```typescript
interface DependencyRule {
  pattern: RegExp;
  type: DependencyType;
  weight: number;
}

class DependencyAnalyzer {
  private rules: DependencyRule[] = [
    { pattern: /\[\[([^\]]+)\]\]/, type: 'wiki_link', weight: 1.0 },
    { pattern: /\[([^\]]+)\]\(([^)]+)\)/, type: 'markdown_link', weight: 0.8 },
    { pattern: /@import\s+([^\s]+)/, type: 'explicit_import', weight: 1.0 },
    { pattern: /(?:參考|引用|基於)\s*[:：]\s*([^\n]+)/, type: 'reference', weight: 0.6 },
  ];

  async analyzeDependencies(note: Note): Promise<Dependency[]> {
    const dependencies: Dependency[] = [];

    for (const rule of this.rules) {
      const matches = note.content.matchAll(rule.pattern);
      for (const match of matches) {
        const targetId = await this.resolveTarget(match[1]);
        if (targetId) {
          dependencies.push({
            sourceId: note.id,
            targetId,
            type: rule.type,
            weight: rule.weight,
            context: this.extractContext(note.content, match.index!),
          });
        }
      }
    }

    return this.deduplicateDependencies(dependencies);
  }

  async validateCircularDependency(sourceId: string, targetId: string): Promise<boolean> {
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    return this.hasCycleDFS(targetId, sourceId, visited, recursionStack);
  }
}
```

#### 依賴影響分析

```typescript
class ImpactAnalyzer {
  async analyzeImpact(noteId: string, changeType: ChangeType): Promise<ImpactReport> {
    const dependents = await this.dependencyService.findAllDependents(noteId);
    const impacts: Impact[] = [];

    for (const dependent of dependents) {
      const impact = await this.calculateImpact(dependent, changeType);
      impacts.push(impact);
    }

    return {
      totalAffected: impacts.length,
      highImpact: impacts.filter(i => i.severity === 'high'),
      mediumImpact: impacts.filter(i => i.severity === 'medium'),
      lowImpact: impacts.filter(i => i.severity === 'low'),
      recommendations: this.generateRecommendations(impacts),
    };
  }

  private async calculateImpact(dependent: Note, changeType: ChangeType): Promise<Impact> {
    const dependency = await this.dependencyService.findDependency(dependent.id, noteId);

    // 業務規則：根據依賴類型和變更類型計算影響
    let severity: ImpactSeverity = 'low';

    if (dependency.type === 'explicit_import' && changeType === 'structure_change') {
      severity = 'high';
    } else if (dependency.weight > 0.8 && changeType === 'content_change') {
      severity = 'medium';
    }

    return {
      noteId: dependent.id,
      severity,
      reason: this.generateReason(dependency, changeType),
      suggestedActions: this.generateActions(dependency, changeType),
    };
  }
}
```

### 3. AI助理領域 (AI Assistant Domain)

#### 智慧路由業務邏輯

```typescript
class TaskRouter {
  async routeTask(task: Task, context: UserContext): Promise<ProcessingPlan> {
    const analysis = await this.analyzeTask(task);

    // 業務規則：敏感性檢查
    if (analysis.sensitivityScore > 0.7) {
      return this.createLocalProcessingPlan(task, 'high_sensitivity');
    }

    // 業務規則：複雜度檢查
    if (analysis.complexityScore > 0.8) {
      if (context.networkAvailable && context.remoteServicesHealthy) {
        return this.createHybridProcessingPlan(task);
      } else {
        return this.createDegradedProcessingPlan(task);
      }
    }

    // 業務規則：本地優先
    if (analysis.localCapable) {
      return this.createLocalProcessingPlan(task, 'local_capable');
    }

    return this.createRemoteProcessingPlan(task);
  }

  private async analyzeTask(task: Task): Promise<TaskAnalysis> {
    return {
      sensitivityScore: await this.calculateSensitivity(task),
      complexityScore: await this.calculateComplexity(task),
      localCapable: await this.checkLocalCapability(task),
      estimatedDuration: await this.estimateDuration(task),
      resourceRequirements: await this.analyzeResourceNeeds(task),
    };
  }
}
```

#### 多Agent協作邏輯

```typescript
class AgentOrchestrator {
  async executeWorkflow(workflow: Workflow, context: ExecutionContext): Promise<WorkflowResult> {
    const executionPlan = await this.planExecution(workflow);
    const results: StepResult[] = [];

    for (const step of executionPlan.steps) {
      try {
        const agent = await this.getAgent(step.agentType);
        const stepContext = this.buildStepContext(context, results);

        // 業務規則：前置條件檢查
        if (!(await this.validatePreconditions(step, stepContext))) {
          throw new WorkflowError(`Preconditions not met for step ${step.id}`);
        }

        const result = await agent.execute(step.task, stepContext);
        results.push(result);

        // 業務規則：後置條件檢查
        if (!(await this.validatePostconditions(step, result))) {
          throw new WorkflowError(`Postconditions not met for step ${step.id}`);
        }
      } catch (error) {
        return this.handleStepFailure(step, error, results);
      }
    }

    return this.aggregateResults(results);
  }
}
```

### 4. 數據脫敏領域 (Data Anonymization Domain)

#### 分層脫敏業務邏輯

```typescript
class DataAnonymizer {
  async anonymize(content: string, level: AnonymizationLevel): Promise<AnonymizedContent> {
    const pipeline = this.buildPipeline(level);
    let result = content;
    const mappings: AnonymizationMapping[] = [];

    for (const processor of pipeline) {
      const processed = await processor.process(result);
      result = processed.content;
      mappings.push(...processed.mappings);
    }

    return {
      content: result,
      mappings,
      level,
      reversible: this.isReversible(level),
    };
  }

  private buildPipeline(level: AnonymizationLevel): AnonymizationProcessor[] {
    switch (level) {
      case 'basic':
        return [new PersonalInfoMasker(), new EmailMasker(), new PhoneMasker()];

      case 'advanced':
        return [
          new PersonalInfoMasker(),
          new EmailMasker(),
          new PhoneMasker(),
          new EntityReplacer(),
          new ContextualReplacer(),
        ];

      case 'maximum':
        return [
          new PersonalInfoMasker(),
          new EmailMasker(),
          new PhoneMasker(),
          new EntityReplacer(),
          new ContextualReplacer(),
          new DataShuffler(),
          new StructuralObfuscator(),
        ];
    }
  }
}
```

### 5. 同步與協作領域 (Sync & Collaboration Domain)

#### 衝突解決業務邏輯

```typescript
class ConflictResolver {
  async resolveConflict(conflict: Conflict): Promise<Resolution> {
    const strategy = this.selectStrategy(conflict);

    switch (strategy) {
      case 'auto_merge':
        return this.autoMerge(conflict);

      case 'user_intervention':
        return this.requestUserIntervention(conflict);

      case 'last_writer_wins':
        return this.lastWriterWins(conflict);

      case 'semantic_merge':
        return this.semanticMerge(conflict);
    }
  }

  private selectStrategy(conflict: Conflict): ConflictResolutionStrategy {
    // 業務規則：根據衝突類型選擇策略
    if (conflict.type === 'metadata_only') {
      return 'auto_merge';
    }

    if (conflict.affectsStructure) {
      return 'user_intervention';
    }

    if (conflict.semanticSimilarity > 0.9) {
      return 'semantic_merge';
    }

    return 'last_writer_wins';
  }
}
```

## 業務規則引擎

### 規則定義與執行

```typescript
interface BusinessRule {
  id: string;
  name: string;
  condition: (context: RuleContext) => boolean;
  action: (context: RuleContext) => Promise<void>;
  priority: number;
}

class BusinessRuleEngine {
  private rules: Map<string, BusinessRule[]> = new Map();

  async executeRules(domain: string, context: RuleContext): Promise<RuleExecutionResult> {
    const domainRules = this.rules.get(domain) || [];
    const applicableRules = domainRules
      .filter(rule => rule.condition(context))
      .sort((a, b) => b.priority - a.priority);

    const results: RuleResult[] = [];

    for (const rule of applicableRules) {
      try {
        await rule.action(context);
        results.push({ ruleId: rule.id, status: 'success' });
      } catch (error) {
        results.push({ ruleId: rule.id, status: 'failed', error });
      }
    }

    return { results, executedCount: results.length };
  }
}
```

## 業務事件處理

### 領域事件

```typescript
abstract class DomainEvent {
  readonly id: string = generateId();
  readonly timestamp: Date = new Date();
  readonly version: number = 1;

  abstract get eventType(): string;
  abstract get aggregateId(): string;
}

class NotePublishedEvent extends DomainEvent {
  constructor(
    public readonly noteId: string,
    public readonly title: string,
    public readonly authorId: string
  ) {
    super();
  }

  get eventType(): string {
    return 'note.published';
  }
  get aggregateId(): string {
    return this.noteId;
  }
}

class DependencyDetectedEvent extends DomainEvent {
  constructor(
    public readonly sourceNoteId: string,
    public readonly targetNoteId: string,
    public readonly dependencyType: string
  ) {
    super();
  }

  get eventType(): string {
    return 'dependency.detected';
  }
  get aggregateId(): string {
    return this.sourceNoteId;
  }
}
```

### 事件處理器

```typescript
class NoteEventHandler {
  @EventHandler(NotePublishedEvent)
  async handleNotePublished(event: NotePublishedEvent): Promise<void> {
    // 更新搜索索引
    await this.searchIndexService.indexNote(event.noteId);

    // 通知相關用戶
    await this.notificationService.notifySubscribers(event.noteId, event);

    // 觸發依賴分析
    await this.dependencyAnalyzer.analyzeNote(event.noteId);
  }

  @EventHandler(DependencyDetectedEvent)
  async handleDependencyDetected(event: DependencyDetectedEvent): Promise<void> {
    // 更新依賴圖
    await this.dependencyGraphService.addDependency(
      event.sourceNoteId,
      event.targetNoteId,
      event.dependencyType
    );

    // 檢查循環依賴
    const hasCycle = await this.dependencyAnalyzer.checkCircularDependency(
      event.sourceNoteId,
      event.targetNoteId
    );

    if (hasCycle) {
      await this.eventBus.publish(
        new CircularDependencyDetectedEvent(event.sourceNoteId, event.targetNoteId)
      );
    }
  }
}
```

這些業務邏輯設計確保了系統的核心功能正確實現，同時保持了良好的可擴展性和可維護性。
