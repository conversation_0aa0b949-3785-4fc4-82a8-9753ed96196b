import type {
  Dependency,
  DependencyType,
  DependencyStrength,
} from "@life-note/core";
import type { NoteId } from "@life-note/core";
import type {
  IRepository,
  PaginatedResult,
  PaginationParams,
} from "./IRepository.js";

/**
 * 依賴關係 ID 類型
 */
export type DependencyId = string;

/**
 * 依賴關係查詢條件
 */
export interface DependencyQueryCondition {
  sourceNoteId?: string;
  targetNoteId?: string;
  type?: DependencyType;
  strength?: DependencyStrength;
  isActive?: boolean;
  createdAfter?: Date;
  createdBefore?: Date;
}

/**
 * 依賴關係統計信息
 */
export interface DependencyStatistics {
  total: number;
  active: number;
  inactive: number;
  byType: Record<DependencyType, number>;
  byStrength: Record<DependencyStrength, number>;
  averageDependenciesPerNote: number;
  mostConnectedNotes: Array<{
    noteId: string;
    connectionCount: number;
  }>;
  circularDependencies: number;
}

/**
 * 依賴路徑信息
 */
export interface DependencyPath {
  path: NoteId[];
  length: number;
  isCircular: boolean;
  totalStrength: number;
  weakestLink: {
    sourceId: NoteId;
    targetId: NoteId;
    strength: DependencyStrength;
  };
}

/**
 * 依賴圖節點信息
 */
export interface DependencyNode {
  noteId: NoteId;
  title: string;
  incomingCount: number;
  outgoingCount: number;
  totalConnections: number;
  dependencies: Array<{
    targetId: NoteId;
    type: DependencyType;
    strength: DependencyStrength;
  }>;
  dependents: Array<{
    sourceId: NoteId;
    type: DependencyType;
    strength: DependencyStrength;
  }>;
}

/**
 * 依賴關係存儲庫接口
 */
export interface IDependencyRepository
  extends IRepository<Dependency, DependencyId> {
  /**
   * 根據源筆記 ID 查找依賴關係
   */
  findBySourceNoteId(
    sourceNoteId: NoteId,
    params?: PaginationParams,
  ): Promise<PaginatedResult<Dependency>>;

  /**
   * 根據目標筆記 ID 查找依賴關係
   */
  findByTargetNoteId(
    targetNoteId: NoteId,
    params?: PaginationParams,
  ): Promise<PaginatedResult<Dependency>>;

  /**
   * 根據筆記 ID 查找所有相關依賴關係
   */
  findByNoteId(
    noteId: NoteId,
    params?: PaginationParams,
  ): Promise<PaginatedResult<Dependency>>;

  /**
   * 根據依賴類型查找依賴關係
   */
  findByType(
    type: DependencyType,
    params?: PaginationParams,
  ): Promise<PaginatedResult<Dependency>>;

  /**
   * 根據依賴強度查找依賴關係
   */
  findByStrength(
    strength: DependencyStrength,
    params?: PaginationParams,
  ): Promise<PaginatedResult<Dependency>>;

  /**
   * 根據複合條件查找依賴關係
   */
  findByCondition(
    condition: DependencyQueryCondition,
    params?: PaginationParams,
  ): Promise<PaginatedResult<Dependency>>;

  /**
   * 查找活躍的依賴關係
   */
  findActive(params?: PaginationParams): Promise<PaginatedResult<Dependency>>;

  /**
   * 查找非活躍的依賴關係
   */
  findInactive(params?: PaginationParams): Promise<PaginatedResult<Dependency>>;

  /**
   * 檢查兩個筆記之間是否存在依賴關係
   */
  existsBetween(
    sourceNoteId: NoteId,
    targetNoteId: NoteId,
    type?: DependencyType,
  ): Promise<boolean>;

  /**
   * 獲取兩個筆記之間的依賴關係
   */
  findBetween(
    sourceNoteId: NoteId,
    targetNoteId: NoteId,
    type?: DependencyType,
  ): Promise<Dependency | null>;

  /**
   * 創建依賴關係
   */
  createDependency(
    sourceNoteId: NoteId,
    targetNoteId: NoteId,
    type: DependencyType,
    strength?: DependencyStrength,
    description?: string,
  ): Promise<Dependency>;

  /**
   * 批量創建依賴關係
   */
  batchCreateDependencies(
    dependencies: Array<{
      sourceNoteId: NoteId;
      targetNoteId: NoteId;
      type: DependencyType;
      strength?: DependencyStrength;
      description?: string;
    }>,
  ): Promise<Dependency[]>;

  /**
   * 更新依賴關係強度
   */
  updateStrength(
    dependencyId: DependencyId,
    strength: DependencyStrength,
  ): Promise<void>;

  /**
   * 激活依賴關係
   */
  activate(dependencyId: DependencyId): Promise<void>;

  /**
   * 停用依賴關係
   */
  deactivate(dependencyId: DependencyId): Promise<void>;

  /**
   * 批量激活依賴關係
   */
  batchActivate(dependencyIds: DependencyId[]): Promise<void>;

  /**
   * 批量停用依賴關係
   */
  batchDeactivate(dependencyIds: DependencyId[]): Promise<void>;

  /**
   * 刪除筆記的所有依賴關係
   */
  deleteByNoteId(noteId: NoteId): Promise<void>;

  /**
   * 獲取依賴關係統計信息
   */
  getStatistics(): Promise<DependencyStatistics>;

  /**
   * 獲取筆記的依賴統計信息
   */
  getNoteStatistics(noteId: NoteId): Promise<{
    incomingCount: number;
    outgoingCount: number;
    totalConnections: number;
    byType: Record<DependencyType, number>;
    byStrength: Record<DependencyStrength, number>;
  }>;

  // 依賴圖分析
  /**
   * 檢測循環依賴
   */
  detectCircularDependencies(): Promise<DependencyPath[]>;

  /**
   * 檢測從特定筆記開始的循環依賴
   */
  detectCircularDependenciesFromNote(noteId: NoteId): Promise<DependencyPath[]>;

  /**
   * 查找兩個筆記之間的路徑
   */
  findPath(
    sourceNoteId: NoteId,
    targetNoteId: NoteId,
  ): Promise<DependencyPath | null>;

  /**
   * 查找兩個筆記之間的所有路徑
   */
  findAllPaths(
    sourceNoteId: NoteId,
    targetNoteId: NoteId,
    maxDepth?: number,
  ): Promise<DependencyPath[]>;

  /**
   * 獲取筆記的依賴樹
   */
  getDependencyTree(noteId: NoteId, maxDepth?: number): Promise<DependencyNode>;

  /**
   * 獲取筆記的依賴圖
   */
  getDependencyGraph(noteIds: NoteId[]): Promise<DependencyNode[]>;

  /**
   * 查找最強依賴路徑
   */
  findStrongestPath(
    sourceNoteId: NoteId,
    targetNoteId: NoteId,
  ): Promise<DependencyPath | null>;

  /**
   * 查找最弱依賴路徑
   */
  findWeakestPath(
    sourceNoteId: NoteId,
    targetNoteId: NoteId,
  ): Promise<DependencyPath | null>;

  /**
   * 獲取依賴關係的影響範圍
   */
  getImpactScope(noteId: NoteId, maxDepth?: number): Promise<NoteId[]>;

  /**
   * 獲取筆記的依賴範圍
   */
  getDependencyScope(noteId: NoteId, maxDepth?: number): Promise<NoteId[]>;

  /**
   * 查找孤立的筆記（沒有依賴關係的筆記）
   */
  findOrphanedNotes(): Promise<NoteId[]>;

  /**
   * 查找最連接的筆記
   */
  findMostConnectedNotes(limit?: number): Promise<
    Array<{
      noteId: NoteId;
      connectionCount: number;
    }>
  >;

  /**
   * 查找依賴關係的瓶頸
   */
  findBottlenecks(): Promise<
    Array<{
      noteId: NoteId;
      bottleneckScore: number;
    }>
  >;
}
