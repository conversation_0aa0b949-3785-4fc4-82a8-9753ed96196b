/* eslint-disable @typescript-eslint/no-unused-vars */
import { generateRandomString } from "@life-note/utils";

import {
  AggregateRoot,
  BusinessRuleViolationError,
} from "../../shared/Entity.js";
import {
  Dependency,
  DependencyType,
  DependencyStrength,
} from "../entities/Dependency.js";
import { NoteId } from "../value-objects/NoteId.js";

/**
 * 依賴關係圖節點
 */
export interface DependencyNode {
  noteId: NoteId;
  dependencies: NoteId[];
  dependents: NoteId[];
  depth: number;
}

/**
 * 依賴關係路徑
 */
export interface DependencyPath {
  from: NoteId;
  to: NoteId;
  path: NoteId[];
  strength: DependencyStrength;
}

/**
 * 依賴關係圖聚合根
 * 管理整個系統的依賴關係圖
 */
export class DependencyGraph extends AggregateRoot<string> {
  private _dependencies: Map<string, Dependency>;
  private _adjacencyList: Map<string, Set<string>>; // sourceId -> Set<targetId>
  private _reverseAdjacencyList: Map<string, Set<string>>; // targetId -> Set<sourceId>

  constructor(dependencies: Dependency[] = []) {
    super(generateRandomString(16));

    this._dependencies = new Map();
    this._adjacencyList = new Map();
    this._reverseAdjacencyList = new Map();

    // 初始化圖結構
    dependencies.forEach((dep) => this.addDependencyToGraph(dep));
  }

  /**
   * 創建新的依賴關係圖
   */
  static create(): DependencyGraph {
    return new DependencyGraph();
  }

  /**
   * 從依賴關係列表創建圖
   */
  static fromDependencies(dependencies: Dependency[]): DependencyGraph {
    return new DependencyGraph(dependencies);
  }

  /**
   * 添加依賴關係
   */
  addDependency(dependency: Dependency): void {
    // 檢查是否會創建循環
    if (
      this.wouldCreateCycle(dependency.sourceNoteId, dependency.targetNoteId)
    ) {
      throw new BusinessRuleViolationError(
        "Adding this dependency would create a cycle",
      );
    }

    this.addDependencyToGraph(dependency);
    this.markAsModified();
  }

  /**
   * 移除依賴關係
   */
  removeDependency(dependencyId: string): void {
    const dependency = this._dependencies.get(dependencyId);
    if (!dependency) {
      throw new BusinessRuleViolationError("Dependency not found");
    }

    this.removeDependencyFromGraph(dependency);
    this.markAsModified();
  }

  /**
   * 檢查是否會創建循環依賴
   */
  wouldCreateCycle(sourceId: NoteId, targetId: NoteId): boolean {
    // 使用 DFS 檢查從 target 是否能到達 source
    return this.hasPath(targetId, sourceId);
  }

  /**
   * 檢查兩個節點之間是否存在路徑
   */
  hasPath(from: NoteId, to: NoteId): boolean {
    const visited = new Set<string>();
    const stack = [from.value];

    while (stack.length > 0) {
      const current = stack.pop()!;

      if (current === to.value) {
        return true;
      }

      if (visited.has(current)) {
        continue;
      }

      visited.add(current);
      const neighbors = this._adjacencyList.get(current);
      if (neighbors) {
        stack.push(...Array.from(neighbors));
      }
    }

    return false;
  }

  /**
   * 獲取兩個節點之間的最短路徑
   */
  getShortestPath(from: NoteId, to: NoteId): DependencyPath | null {
    const queue = [{ nodeId: from.value, path: [from.value] }];
    const visited = new Set<string>();

    while (queue.length > 0) {
      const { nodeId, path } = queue.shift()!;

      if (nodeId === to.value) {
        const pathNoteIds = path.map((id) => NoteId.fromString(id));
        return {
          from,
          to,
          path: pathNoteIds,
          strength: this.calculatePathStrength(pathNoteIds),
        };
      }

      if (visited.has(nodeId)) {
        continue;
      }

      visited.add(nodeId);
      const neighbors = this._adjacencyList.get(nodeId);
      if (neighbors) {
        for (const neighbor of neighbors) {
          queue.push({ nodeId: neighbor, path: [...path, neighbor] });
        }
      }
    }

    return null;
  }

  /**
   * 獲取節點的所有依賴（遞歸）
   */
  getAllDependencies(noteId: NoteId, maxDepth: number = 10): DependencyNode[] {
    const result: DependencyNode[] = [];
    const visited = new Set<string>();
    const queue = [{ noteId: noteId.value, depth: 0 }];

    while (queue.length > 0) {
      const { noteId: currentId, depth } = queue.shift()!;

      if (visited.has(currentId) || depth > maxDepth) {
        continue;
      }

      visited.add(currentId);

      const dependencies = Array.from(
        this._adjacencyList.get(currentId) || [],
      ).map((id) => NoteId.fromString(id));

      const dependents = Array.from(
        this._reverseAdjacencyList.get(currentId) || [],
      ).map((id) => NoteId.fromString(id));

      result.push({
        noteId: NoteId.fromString(currentId),
        dependencies,
        dependents,
        depth,
      });

      // 添加依賴節點到隊列
      const neighbors = this._adjacencyList.get(currentId);
      if (neighbors) {
        for (const neighbor of neighbors) {
          queue.push({ noteId: neighbor, depth: depth + 1 });
        }
      }
    }

    return result;
  }

  /**
   * 獲取節點的直接依賴
   */
  getDirectDependencies(noteId: NoteId): NoteId[] {
    const dependencies = this._adjacencyList.get(noteId.value);
    return dependencies
      ? Array.from(dependencies).map((id) => NoteId.fromString(id))
      : [];
  }

  /**
   * 獲取依賴於該節點的直接節點
   */
  getDirectDependents(noteId: NoteId): NoteId[] {
    const dependents = this._reverseAdjacencyList.get(noteId.value);
    return dependents
      ? Array.from(dependents).map((id) => NoteId.fromString(id))
      : [];
  }

  /**
   * 檢測所有循環依賴
   */
  detectCycles(): NoteId[][] {
    const cycles: NoteId[][] = [];
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    for (const nodeId of this._adjacencyList.keys()) {
      if (!visited.has(nodeId)) {
        this.detectCyclesUtil(nodeId, visited, recursionStack, [], cycles);
      }
    }

    return cycles;
  }

  /**
   * 獲取圖的統計信息
   */
  getStatistics(): {
    totalNodes: number;
    totalEdges: number;
    averageDependencies: number;
    maxDependencies: number;
    isolatedNodes: number;
    stronglyConnectedComponents: number;
  } {
    const totalNodes = this._adjacencyList.size;
    const totalEdges = this._dependencies.size;

    let maxDependencies = 0;
    let isolatedNodes = 0;

    for (const [nodeId, dependencies] of this._adjacencyList) {
      const dependencyCount = dependencies.size;
      const dependentCount = this._reverseAdjacencyList.get(nodeId)?.size || 0;

      maxDependencies = Math.max(maxDependencies, dependencyCount);

      if (dependencyCount === 0 && dependentCount === 0) {
        isolatedNodes++;
      }
    }

    const averageDependencies = totalNodes > 0 ? totalEdges / totalNodes : 0;

    return {
      totalNodes,
      totalEdges,
      averageDependencies,
      maxDependencies,
      isolatedNodes,
      stronglyConnectedComponents: this.countStronglyConnectedComponents(),
    };
  }

  /**
   * 添加依賴關係到圖中
   */
  private addDependencyToGraph(dependency: Dependency): void {
    const sourceId = dependency.sourceNoteId.value;
    const targetId = dependency.targetNoteId.value;

    this._dependencies.set(dependency.id.value, dependency);

    // 更新鄰接表
    if (!this._adjacencyList.has(sourceId)) {
      this._adjacencyList.set(sourceId, new Set());
    }
    this._adjacencyList.get(sourceId)!.add(targetId);

    // 更新反向鄰接表
    if (!this._reverseAdjacencyList.has(targetId)) {
      this._reverseAdjacencyList.set(targetId, new Set());
    }
    this._reverseAdjacencyList.get(targetId)!.add(sourceId);
  }

  /**
   * 從圖中移除依賴關係
   */
  private removeDependencyFromGraph(dependency: Dependency): void {
    const sourceId = dependency.sourceNoteId.value;
    const targetId = dependency.targetNoteId.value;

    this._dependencies.delete(dependency.id.value);

    // 更新鄰接表
    this._adjacencyList.get(sourceId)?.delete(targetId);
    if (this._adjacencyList.get(sourceId)?.size === 0) {
      this._adjacencyList.delete(sourceId);
    }

    // 更新反向鄰接表
    this._reverseAdjacencyList.get(targetId)?.delete(sourceId);
    if (this._reverseAdjacencyList.get(targetId)?.size === 0) {
      this._reverseAdjacencyList.delete(targetId);
    }
  }

  /**
   * 計算路徑強度
   */
  private calculatePathStrength(path: NoteId[]): DependencyStrength {
    let minStrength = DependencyStrength.CRITICAL;

    for (let i = 0; i < path.length - 1; i++) {
      const sourceId = path[i];
      const targetId = path[i + 1];

      // 找到對應的依賴關係
      const dependency = Array.from(this._dependencies.values()).find(
        (dep) =>
          dep.sourceNoteId.equals(sourceId) &&
          dep.targetNoteId.equals(targetId),
      );

      if (dependency) {
        const strengthOrder = {
          [DependencyStrength.WEAK]: 1,
          [DependencyStrength.MEDIUM]: 2,
          [DependencyStrength.STRONG]: 3,
          [DependencyStrength.CRITICAL]: 4,
        };

        if (strengthOrder[dependency.strength] < strengthOrder[minStrength]) {
          minStrength = dependency.strength;
        }
      }
    }

    return minStrength;
  }

  /**
   * 循環檢測輔助函數
   */
  private detectCyclesUtil(
    nodeId: string,
    visited: Set<string>,
    recursionStack: Set<string>,
    currentPath: string[],
    cycles: NoteId[][],
  ): void {
    visited.add(nodeId);
    recursionStack.add(nodeId);
    currentPath.push(nodeId);

    const neighbors = this._adjacencyList.get(nodeId);
    if (neighbors) {
      for (const neighbor of neighbors) {
        if (!visited.has(neighbor)) {
          this.detectCyclesUtil(
            neighbor,
            visited,
            recursionStack,
            currentPath,
            cycles,
          );
        } else if (recursionStack.has(neighbor)) {
          // 找到循環
          const cycleStart = currentPath.indexOf(neighbor);
          const cycle = currentPath
            .slice(cycleStart)
            .map((id) => NoteId.fromString(id));
          cycles.push(cycle);
        }
      }
    }

    recursionStack.delete(nodeId);
    currentPath.pop();
  }

  /**
   * 計算強連通分量數量
   */
  private countStronglyConnectedComponents(): number {
    // 簡化實現，實際應該使用 Tarjan 算法
    return this._adjacencyList.size;
  }

  // Getters
  get dependencies(): Dependency[] {
    return Array.from(this._dependencies.values());
  }

  get nodeCount(): number {
    return this._adjacencyList.size;
  }

  get edgeCount(): number {
    return this._dependencies.size;
  }

  /**
   * 轉換為普通對象
   */
  toPlainObject(): Record<string, unknown> {
    return {
      id: this._id,
      dependencies: Array.from(this._dependencies.values()).map((dep) =>
        dep.toPlainObject(),
      ),
      statistics: this.getStatistics(),
      createdAt: this._createdAt.toISOString(),
      updatedAt: this._updatedAt.toISOString(),
    };
  }
}
