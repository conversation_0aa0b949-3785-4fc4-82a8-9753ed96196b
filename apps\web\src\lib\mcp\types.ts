import { z } from 'zod';

// MCP 工具參數的 Zod 驗證 schemas
export const CreateNoteArgsSchema = z.object({
	title: z.string().min(1, 'Title is required'),
	content: z.string().default(''),
	tags: z.array(z.string()).default([]),
	categoryId: z.string().optional(),
	priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
	status: z.enum(['draft', 'published', 'archived']).default('draft')
});

export const UpdateNoteArgsSchema = z.object({
	id: z.string().min(1, 'Note ID is required'),
	title: z.string().min(1).optional(),
	content: z.string().optional(),
	tags: z.array(z.string()).optional(),
	categoryId: z.string().optional(),
	priority: z.enum(['low', 'medium', 'high', 'urgent']).optional(),
	status: z.enum(['draft', 'published', 'archived']).optional()
});

export const DeleteNoteArgsSchema = z.object({
	id: z.string().min(1, 'Note ID is required')
});

export const GetNoteArgsSchema = z.object({
	id: z.string().min(1, 'Note ID is required')
});

export const SearchNotesArgsSchema = z.object({
	query: z.string().optional(),
	tags: z.array(z.string()).optional(),
	status: z.enum(['draft', 'published', 'archived']).optional(),
	priority: z.enum(['low', 'medium', 'high', 'urgent']).optional(),
	categoryId: z.string().optional(),
	limit: z.number().int().positive().max(100).default(20),
	offset: z.number().int().min(0).default(0)
});

export const AnalyzeDependenciesArgsSchema = z.object({
	noteIds: z.array(z.string()).optional(),
	includeTagConnections: z.boolean().default(true),
	includeCategoryConnections: z.boolean().default(true),
	includeContentSimilarity: z.boolean().default(true),
	similarityThreshold: z.number().min(0).max(1).default(0.3),
	maxDistance: z.number().int().min(1).max(5).default(3),
	excludeIsolatedNodes: z.boolean().default(true)
});

export const GetDependencyGraphArgsSchema = z.object({
	noteId: z.string().min(1, 'Note ID is required'),
	depth: z.number().int().min(1).max(3).default(2)
});

// TypeScript 類型定義
export type CreateNoteArgs = z.infer<typeof CreateNoteArgsSchema>;
export type UpdateNoteArgs = z.infer<typeof UpdateNoteArgsSchema>;
export type DeleteNoteArgs = z.infer<typeof DeleteNoteArgsSchema>;
export type GetNoteArgs = z.infer<typeof GetNoteArgsSchema>;
export type SearchNotesArgs = z.infer<typeof SearchNotesArgsSchema>;
export type AnalyzeDependenciesArgs = z.infer<typeof AnalyzeDependenciesArgsSchema>;
export type GetDependencyGraphArgs = z.infer<typeof GetDependencyGraphArgsSchema>;

// MCP 工具定義
export interface MCPTool {
	name: string;
	description: string;
	inputSchema: {
		type: 'object';
		properties: Record<string, any>;
		required?: string[];
	};
}

// MCP 資源定義
export interface MCPResource {
	uri: string;
	name: string;
	description?: string;
	mimeType?: string;
}

// MCP 服務器配置
export interface MCPServerConfig {
	name: string;
	version: string;
	description: string;
	tools: MCPTool[];
	resources: MCPResource[];
}

// 筆記相關的 MCP 工具定義
export const NOTE_TOOLS: MCPTool[] = [
	{
		name: 'create_note',
		description: 'Create a new note with title, content, tags, and metadata',
		inputSchema: {
			type: 'object',
			properties: {
				title: {
					type: 'string',
					description: 'The title of the note'
				},
				content: {
					type: 'string',
					description: 'The content of the note in Markdown format',
					default: ''
				},
				tags: {
					type: 'array',
					items: { type: 'string' },
					description: 'Array of tags for the note',
					default: []
				},
				categoryId: {
					type: 'string',
					description: 'Optional category ID for the note'
				},
				priority: {
					type: 'string',
					enum: ['low', 'medium', 'high', 'urgent'],
					description: 'Priority level of the note',
					default: 'medium'
				},
				status: {
					type: 'string',
					enum: ['draft', 'published', 'archived'],
					description: 'Status of the note',
					default: 'draft'
				}
			},
			required: ['title']
		}
	},
	{
		name: 'update_note',
		description: 'Update an existing note by ID',
		inputSchema: {
			type: 'object',
			properties: {
				id: {
					type: 'string',
					description: 'The ID of the note to update'
				},
				title: {
					type: 'string',
					description: 'New title for the note'
				},
				content: {
					type: 'string',
					description: 'New content for the note in Markdown format'
				},
				tags: {
					type: 'array',
					items: { type: 'string' },
					description: 'New array of tags for the note'
				},
				categoryId: {
					type: 'string',
					description: 'New category ID for the note'
				},
				priority: {
					type: 'string',
					enum: ['low', 'medium', 'high', 'urgent'],
					description: 'New priority level of the note'
				},
				status: {
					type: 'string',
					enum: ['draft', 'published', 'archived'],
					description: 'New status of the note'
				}
			},
			required: ['id']
		}
	},
	{
		name: 'delete_note',
		description: 'Delete a note by ID',
		inputSchema: {
			type: 'object',
			properties: {
				id: {
					type: 'string',
					description: 'The ID of the note to delete'
				}
			},
			required: ['id']
		}
	},
	{
		name: 'get_note',
		description: 'Get a note by ID',
		inputSchema: {
			type: 'object',
			properties: {
				id: {
					type: 'string',
					description: 'The ID of the note to retrieve'
				}
			},
			required: ['id']
		}
	},
	{
		name: 'search_notes',
		description: 'Search notes with various filters and pagination',
		inputSchema: {
			type: 'object',
			properties: {
				query: {
					type: 'string',
					description: 'Search query for full-text search'
				},
				tags: {
					type: 'array',
					items: { type: 'string' },
					description: 'Filter by tags'
				},
				status: {
					type: 'string',
					enum: ['draft', 'published', 'archived'],
					description: 'Filter by status'
				},
				priority: {
					type: 'string',
					enum: ['low', 'medium', 'high', 'urgent'],
					description: 'Filter by priority'
				},
				categoryId: {
					type: 'string',
					description: 'Filter by category ID'
				},
				limit: {
					type: 'number',
					description: 'Maximum number of results to return',
					minimum: 1,
					maximum: 100,
					default: 20
				},
				offset: {
					type: 'number',
					description: 'Number of results to skip',
					minimum: 0,
					default: 0
				}
			}
		}
	}
];

// 依賴關係相關的 MCP 工具定義
export const DEPENDENCY_TOOLS: MCPTool[] = [
	{
		name: 'analyze_dependencies',
		description: 'Analyze dependencies between notes and generate a dependency graph',
		inputSchema: {
			type: 'object',
			properties: {
				noteIds: {
					type: 'array',
					items: { type: 'string' },
					description: 'Optional array of note IDs to analyze. If not provided, analyzes all notes'
				},
				includeTagConnections: {
					type: 'boolean',
					description: 'Include connections based on shared tags',
					default: true
				},
				includeCategoryConnections: {
					type: 'boolean',
					description: 'Include connections based on shared categories',
					default: true
				},
				includeContentSimilarity: {
					type: 'boolean',
					description: 'Include connections based on content similarity',
					default: true
				},
				similarityThreshold: {
					type: 'number',
					description: 'Threshold for content similarity (0-1)',
					minimum: 0,
					maximum: 1,
					default: 0.3
				},
				maxDistance: {
					type: 'number',
					description: 'Maximum distance for dependency traversal',
					minimum: 1,
					maximum: 5,
					default: 3
				},
				excludeIsolatedNodes: {
					type: 'boolean',
					description: 'Exclude nodes with no connections',
					default: true
				}
			}
		}
	},
	{
		name: 'get_dependency_graph',
		description: 'Get the dependency graph for a specific note',
		inputSchema: {
			type: 'object',
			properties: {
				noteId: {
					type: 'string',
					description: 'The ID of the note to get dependencies for'
				},
				depth: {
					type: 'number',
					description: 'Depth of dependency traversal',
					minimum: 1,
					maximum: 3,
					default: 2
				}
			},
			required: ['noteId']
		}
	}
];

// MCP 資源定義
export const MCP_RESOURCES: MCPResource[] = [
	{
		uri: 'notes://all',
		name: 'All Notes',
		description: 'Access to all notes in the system',
		mimeType: 'application/json'
	},
	{
		uri: 'notes://search',
		name: 'Note Search',
		description: 'Search interface for notes',
		mimeType: 'application/json'
	},
	{
		uri: 'dependencies://graph',
		name: 'Dependency Graph',
		description: 'Complete dependency graph of all notes',
		mimeType: 'application/json'
	},
	{
		uri: 'dependencies://analysis',
		name: 'Dependency Analysis',
		description: 'Dependency analysis results and statistics',
		mimeType: 'application/json'
	}
];

// 完整的 MCP 服務器配置
export const MCP_SERVER_CONFIG: MCPServerConfig = {
	name: 'life-note-mcp-server',
	version: '1.0.0',
	description:
		'Life Note MCP Server for AI integration with note management and dependency analysis',
	tools: [...NOTE_TOOLS, ...DEPENDENCY_TOOLS],
	resources: MCP_RESOURCES
};
