# UI/UX 實作標準

## 基於項目需求的樣式指引

### 設計系統概覽

基於階段2技術規範，採用現代化、可訪問性優先的設計系統：

#### 核心設計原則

- **本地優先體驗**：快速響應，離線可用
- **智慧化介面**：AI 輔助但不干擾用戶
- **知識工作者友好**：專注於內容創作和組織
- **跨平台一致性**：桌面、移動端統一體驗

## Context7 搜索的設計系統

### Tailwind CSS 3.0+ 配置

基於最新的原子化 CSS 最佳實踐：

```javascript
// tailwind.config.js
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
    "./packages/ui/src/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: "class",
  theme: {
    extend: {
      // 設計令牌 (Design Tokens)
      colors: {
        // 主色調 - 知識管理主題
        primary: {
          50: "#f0f9ff",
          100: "#e0f2fe",
          200: "#bae6fd",
          300: "#7dd3fc",
          400: "#38bdf8",
          500: "#0ea5e9", // 主色
          600: "#0284c7",
          700: "#0369a1",
          800: "#075985",
          900: "#0c4a6e",
          950: "#082f49",
        },
        // 語義色彩
        success: {
          50: "#f0fdf4",
          500: "#22c55e",
          600: "#16a34a",
        },
        warning: {
          50: "#fffbeb",
          500: "#f59e0b",
          600: "#d97706",
        },
        error: {
          50: "#fef2f2",
          500: "#ef4444",
          600: "#dc2626",
        },
        // 中性色調
        gray: {
          50: "#f9fafb",
          100: "#f3f4f6",
          200: "#e5e7eb",
          300: "#d1d5db",
          400: "#9ca3af",
          500: "#6b7280",
          600: "#4b5563",
          700: "#374151",
          800: "#1f2937",
          900: "#111827",
          950: "#030712",
        },
        // AI 相關色彩
        ai: {
          50: "#faf5ff",
          100: "#f3e8ff",
          200: "#e9d5ff",
          300: "#d8b4fe",
          400: "#c084fc",
          500: "#a855f7", // AI 主色
          600: "#9333ea",
          700: "#7c3aed",
          800: "#6b21a8",
          900: "#581c87",
        },
        // 依賴關係色彩
        dependency: {
          incoming: "#10b981", // 綠色 - 被依賴
          outgoing: "#f59e0b", // 橙色 - 依賴他人
          circular: "#ef4444", // 紅色 - 循環依賴
          weak: "#6b7280", // 灰色 - 弱依賴
        },
      },

      // 字體系統
      fontFamily: {
        sans: [
          "Inter",
          "-apple-system",
          "BlinkMacSystemFont",
          "Segoe UI",
          "Roboto",
          "Helvetica Neue",
          "Arial",
          "sans-serif",
        ],
        mono: [
          "JetBrains Mono",
          "Fira Code",
          "Monaco",
          "Consolas",
          "Liberation Mono",
          "Courier New",
          "monospace",
        ],
        serif: ["Crimson Text", "Georgia", "Times New Roman", "serif"],
      },

      // 字體大小
      fontSize: {
        xs: ["0.75rem", { lineHeight: "1rem" }],
        sm: ["0.875rem", { lineHeight: "1.25rem" }],
        base: ["1rem", { lineHeight: "1.5rem" }],
        lg: ["1.125rem", { lineHeight: "1.75rem" }],
        xl: ["1.25rem", { lineHeight: "1.75rem" }],
        "2xl": ["1.5rem", { lineHeight: "2rem" }],
        "3xl": ["1.875rem", { lineHeight: "2.25rem" }],
        "4xl": ["2.25rem", { lineHeight: "2.5rem" }],
        "5xl": ["3rem", { lineHeight: "1" }],
        "6xl": ["3.75rem", { lineHeight: "1" }],
      },

      // 間距系統
      spacing: {
        18: "4.5rem",
        88: "22rem",
        128: "32rem",
      },

      // 陰影系統
      boxShadow: {
        soft: "0 2px 8px 0 rgba(0, 0, 0, 0.05)",
        medium: "0 4px 16px 0 rgba(0, 0, 0, 0.1)",
        strong: "0 8px 32px 0 rgba(0, 0, 0, 0.15)",
        "ai-glow": "0 0 20px rgba(168, 85, 247, 0.3)",
        dependency: "0 2px 12px rgba(16, 185, 129, 0.2)",
      },

      // 動畫系統
      animation: {
        "fade-in": "fadeIn 0.2s ease-in-out",
        "slide-up": "slideUp 0.3s ease-out",
        "slide-down": "slideDown 0.3s ease-out",
        "scale-in": "scaleIn 0.2s ease-out",
        "ai-pulse": "aiPulse 2s ease-in-out infinite",
        "dependency-flow": "dependencyFlow 3s ease-in-out infinite",
      },

      keyframes: {
        fadeIn: {
          "0%": { opacity: "0" },
          "100%": { opacity: "1" },
        },
        slideUp: {
          "0%": { transform: "translateY(10px)", opacity: "0" },
          "100%": { transform: "translateY(0)", opacity: "1" },
        },
        slideDown: {
          "0%": { transform: "translateY(-10px)", opacity: "0" },
          "100%": { transform: "translateY(0)", opacity: "1" },
        },
        scaleIn: {
          "0%": { transform: "scale(0.95)", opacity: "0" },
          "100%": { transform: "scale(1)", opacity: "1" },
        },
        aiPulse: {
          "0%, 100%": { boxShadow: "0 0 20px rgba(168, 85, 247, 0.3)" },
          "50%": { boxShadow: "0 0 30px rgba(168, 85, 247, 0.5)" },
        },
        dependencyFlow: {
          "0%": { strokeDashoffset: "20" },
          "100%": { strokeDashoffset: "0" },
        },
      },

      // 斷點系統
      screens: {
        xs: "475px",
        sm: "640px",
        md: "768px",
        lg: "1024px",
        xl: "1280px",
        "2xl": "1536px",
        "3xl": "1920px",
      },
    },
  },
  plugins: [
    require("@tailwindcss/forms"),
    require("@tailwindcss/typography"),
    require("@tailwindcss/aspect-ratio"),
    require("@headlessui/tailwindcss"),
  ],
};
```

### CSS 變數系統

```css
/* src/styles/variables.css */
:root {
  /* 色彩變數 */
  --color-primary: theme("colors.primary.500");
  --color-primary-hover: theme("colors.primary.600");
  --color-secondary: theme("colors.gray.600");
  --color-success: theme("colors.success.500");
  --color-warning: theme("colors.warning.500");
  --color-error: theme("colors.error.500");
  --color-ai: theme("colors.ai.500");

  /* 背景色 */
  --bg-primary: theme("colors.white");
  --bg-secondary: theme("colors.gray.50");
  --bg-tertiary: theme("colors.gray.100");
  --bg-overlay: rgba(0, 0, 0, 0.5);

  /* 文字色 */
  --text-primary: theme("colors.gray.900");
  --text-secondary: theme("colors.gray.600");
  --text-tertiary: theme("colors.gray.400");
  --text-inverse: theme("colors.white");

  /* 邊框色 */
  --border-primary: theme("colors.gray.200");
  --border-secondary: theme("colors.gray.300");
  --border-focus: theme("colors.primary.500");

  /* 陰影 */
  --shadow-sm: theme("boxShadow.soft");
  --shadow-md: theme("boxShadow.medium");
  --shadow-lg: theme("boxShadow.strong");

  /* 圓角 */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;

  /* 間距 */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  /* 字體 */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;

  /* 行高 */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* 轉場動畫 */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.2s ease-in-out;
  --transition-slow: 0.3s ease-in-out;

  /* Z-index 層級 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* 深色模式 */
.dark {
  --bg-primary: theme("colors.gray.900");
  --bg-secondary: theme("colors.gray.800");
  --bg-tertiary: theme("colors.gray.700");
  --bg-overlay: rgba(0, 0, 0, 0.7);

  --text-primary: theme("colors.gray.100");
  --text-secondary: theme("colors.gray.300");
  --text-tertiary: theme("colors.gray.500");

  --border-primary: theme("colors.gray.700");
  --border-secondary: theme("colors.gray.600");
}
```

## 實作標準

### 組件設計原則

#### 原子化設計系統

```typescript
// src/components/atoms/Button/Button.tsx
import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/utils/classNames';

const buttonVariants = cva(
  // 基礎樣式
  'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground hover:bg-primary/90',
        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
        outline: 'border border-input hover:bg-accent hover:text-accent-foreground',
        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'underline-offset-4 hover:underline text-primary',
        ai: 'bg-ai text-white hover:bg-ai/90 shadow-ai-glow',
      },
      size: {
        default: 'h-10 py-2 px-4',
        sm: 'h-9 px-3 rounded-md',
        lg: 'h-11 px-8 rounded-md',
        icon: 'h-10 w-10',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  loading?: boolean;
  icon?: React.ReactNode;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, loading = false, icon, children, ...props }, ref) => {
    return (
      <button
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        disabled={loading || props.disabled}
        {...props}
      >
        {loading && (
          <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
          </svg>
        )}
        {icon && !loading && <span className="mr-2">{icon}</span>}
        {children}
      </button>
    );
  }
);

Button.displayName = 'Button';

export { Button, buttonVariants };
```

#### 複合組件設計

```typescript
// src/components/molecules/SearchBox/SearchBox.tsx
import React, { useState, useCallback } from 'react';
import { Search, X } from 'lucide-react';

import { Button } from '@/components/atoms/Button';
import { Input } from '@/components/atoms/Input';
import { cn } from '@/utils/classNames';

interface SearchBoxProps {
  placeholder?: string;
  value?: string;
  onSearch?: (query: string) => void;
  onClear?: () => void;
  className?: string;
  autoFocus?: boolean;
  loading?: boolean;
}

export const SearchBox: React.FC<SearchBoxProps> = ({
  placeholder = '搜尋筆記...',
  value = '',
  onSearch,
  onClear,
  className,
  autoFocus = false,
  loading = false,
}) => {
  const [query, setQuery] = useState(value);

  const handleSearch = useCallback(() => {
    onSearch?.(query);
  }, [query, onSearch]);

  const handleClear = useCallback(() => {
    setQuery('');
    onClear?.();
  }, [onClear]);

  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  }, [handleSearch]);

  return (
    <div className={cn('relative flex items-center', className)}>
      <div className="relative flex-1">
        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
        <Input
          type="text"
          placeholder={placeholder}
          value={query}
          onChange={e => setQuery(e.target.value)}
          onKeyPress={handleKeyPress}
          className="pl-10 pr-10"
          autoFocus={autoFocus}
          disabled={loading}
        />
        {query && (
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-1 top-1/2 h-6 w-6 -translate-y-1/2"
            onClick={handleClear}
          >
            <X className="h-3 w-3" />
          </Button>
        )}
      </div>
      <Button
        onClick={handleSearch}
        disabled={!query.trim() || loading}
        loading={loading}
        className="ml-2"
      >
        搜尋
      </Button>
    </div>
  );
};
```

### 響應式設計標準

#### 斷點策略

```css
/* 移動優先的響應式設計 */
.responsive-container {
  /* 手機 (默認) */
  @apply px-4 py-2;

  /* 平板 */
  @screen sm {
    @apply px-6 py-3;
  }

  /* 桌面 */
  @screen lg {
    @apply px-8 py-4;
  }

  /* 大屏幕 */
  @screen xl {
    @apply px-12 py-6;
  }
}

/* 網格系統 */
.grid-responsive {
  @apply grid gap-4;

  /* 手機：1列 */
  grid-template-columns: 1fr;

  /* 平板：2列 */
  @screen sm {
    grid-template-columns: repeat(2, 1fr);
  }

  /* 桌面：3列 */
  @screen lg {
    grid-template-columns: repeat(3, 1fr);
  }

  /* 大屏幕：4列 */
  @screen xl {
    grid-template-columns: repeat(4, 1fr);
  }
}
```

#### 自適應佈局組件

```typescript
// src/components/layouts/ResponsiveLayout.tsx
import React from 'react';
import { cn } from '@/utils/classNames';

interface ResponsiveLayoutProps {
  children: React.ReactNode;
  className?: string;
  sidebar?: React.ReactNode;
  header?: React.ReactNode;
  footer?: React.ReactNode;
}

export const ResponsiveLayout: React.FC<ResponsiveLayoutProps> = ({
  children,
  className,
  sidebar,
  header,
  footer,
}) => {
  return (
    <div className={cn('min-h-screen bg-background', className)}>
      {/* 頭部 */}
      {header && (
        <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          {header}
        </header>
      )}

      {/* 主要內容區域 */}
      <div className="flex flex-1">
        {/* 側邊欄 */}
        {sidebar && (
          <aside className="hidden w-64 border-r bg-background lg:block">
            <div className="sticky top-16 h-[calc(100vh-4rem)] overflow-y-auto">
              {sidebar}
            </div>
          </aside>
        )}

        {/* 主內容 */}
        <main className="flex-1 overflow-hidden">
          <div className="container mx-auto px-4 py-6 lg:px-8">
            {children}
          </div>
        </main>
      </div>

      {/* 底部 */}
      {footer && (
        <footer className="border-t bg-background">
          {footer}
        </footer>
      )}
    </div>
  );
};
```

### 可訪問性要求

#### ARIA 標準實現

```typescript
// src/components/organisms/NoteEditor/NoteEditor.tsx
import React, { useId } from 'react';

interface NoteEditorProps {
  title: string;
  content: string;
  onTitleChange: (title: string) => void;
  onContentChange: (content: string) => void;
  onSave: () => void;
  loading?: boolean;
  error?: string;
}

export const NoteEditor: React.FC<NoteEditorProps> = ({
  title,
  content,
  onTitleChange,
  onContentChange,
  onSave,
  loading = false,
  error,
}) => {
  const titleId = useId();
  const contentId = useId();
  const errorId = useId();

  return (
    <div
      role="form"
      aria-labelledby={titleId}
      aria-describedby={error ? errorId : undefined}
      className="space-y-4"
    >
      {/* 標題輸入 */}
      <div>
        <label
          htmlFor={titleId}
          className="block text-sm font-medium text-gray-700 mb-1"
        >
          筆記標題
        </label>
        <input
          id={titleId}
          type="text"
          value={title}
          onChange={e => onTitleChange(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          aria-required="true"
          aria-invalid={!!error}
          disabled={loading}
        />
      </div>

      {/* 內容編輯器 */}
      <div>
        <label
          htmlFor={contentId}
          className="block text-sm font-medium text-gray-700 mb-1"
        >
          筆記內容
        </label>
        <textarea
          id={contentId}
          value={content}
          onChange={e => onContentChange(e.target.value)}
          rows={12}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-vertical"
          aria-required="true"
          aria-invalid={!!error}
          disabled={loading}
          placeholder="開始寫下你的想法..."
        />
      </div>

      {/* 錯誤訊息 */}
      {error && (
        <div
          id={errorId}
          role="alert"
          aria-live="polite"
          className="text-sm text-error bg-error/10 border border-error/20 rounded-md p-3"
        >
          {error}
        </div>
      )}

      {/* 操作按鈕 */}
      <div className="flex justify-end space-x-2">
        <Button
          onClick={onSave}
          disabled={loading || !title.trim() || !content.trim()}
          loading={loading}
          aria-describedby={loading ? 'saving-status' : undefined}
        >
          儲存筆記
        </Button>
        {loading && (
          <span id="saving-status" className="sr-only">
            正在儲存筆記，請稍候...
          </span>
        )}
      </div>
    </div>
  );
};
```

#### 鍵盤導航支援

```typescript
// src/hooks/useKeyboardNavigation.ts
import { useEffect, useCallback } from "react";

interface KeyboardNavigationOptions {
  onEscape?: () => void;
  onEnter?: () => void;
  onArrowUp?: () => void;
  onArrowDown?: () => void;
  onArrowLeft?: () => void;
  onArrowRight?: () => void;
  enabled?: boolean;
}

export function useKeyboardNavigation({
  onEscape,
  onEnter,
  onArrowUp,
  onArrowDown,
  onArrowLeft,
  onArrowRight,
  enabled = true,
}: KeyboardNavigationOptions): void {
  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      if (!enabled) return;

      switch (event.key) {
        case "Escape":
          onEscape?.();
          break;
        case "Enter":
          if (!event.shiftKey && !event.ctrlKey && !event.metaKey) {
            onEnter?.();
          }
          break;
        case "ArrowUp":
          event.preventDefault();
          onArrowUp?.();
          break;
        case "ArrowDown":
          event.preventDefault();
          onArrowDown?.();
          break;
        case "ArrowLeft":
          onArrowLeft?.();
          break;
        case "ArrowRight":
          onArrowRight?.();
          break;
      }
    },
    [
      enabled,
      onEscape,
      onEnter,
      onArrowUp,
      onArrowDown,
      onArrowLeft,
      onArrowRight,
    ],
  );

  useEffect(() => {
    if (enabled) {
      document.addEventListener("keydown", handleKeyDown);
      return () => document.removeEventListener("keydown", handleKeyDown);
    }
  }, [enabled, handleKeyDown]);
}
```

### 主題系統實現

```typescript
// src/contexts/ThemeContext.tsx
import React, { createContext, useContext, useEffect, useState } from 'react';

type Theme = 'light' | 'dark' | 'system';

interface ThemeContextType {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  actualTheme: 'light' | 'dark';
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme, setTheme] = useState<Theme>(() => {
    const stored = localStorage.getItem('theme') as Theme;
    return stored || 'system';
  });

  const [actualTheme, setActualTheme] = useState<'light' | 'dark'>('light');

  useEffect(() => {
    const root = window.document.documentElement;

    if (theme === 'system') {
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
      setActualTheme(systemTheme);
      root.classList.toggle('dark', systemTheme === 'dark');
    } else {
      setActualTheme(theme);
      root.classList.toggle('dark', theme === 'dark');
    }

    localStorage.setItem('theme', theme);
  }, [theme]);

  // 監聽系統主題變化
  useEffect(() => {
    if (theme === 'system') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const handleChange = () => {
        const systemTheme = mediaQuery.matches ? 'dark' : 'light';
        setActualTheme(systemTheme);
        document.documentElement.classList.toggle('dark', systemTheme === 'dark');
      };

      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    }
  }, [theme]);

  return (
    <ThemeContext.Provider value={{ theme, setTheme, actualTheme }}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}
```

這些樣式指引確保了一致的用戶體驗、良好的可訪問性和現代化的設計標準。
