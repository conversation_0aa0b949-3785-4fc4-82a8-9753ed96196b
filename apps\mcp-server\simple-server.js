#!/usr/bin/env node

/**
 * 簡化的 Life Note MCP Server
 * 用於測試基本的 MCP 功能
 */

import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import {
  CallToolRequestSchema,
  ListResourcesRequestSchema,
  ListToolsRequestSchema,
  ReadResourceRequestSchema,
} from "@modelcontextprotocol/sdk/types.js";

// 簡單的內存數據存儲
const notes = new Map();
let nextId = 1;

// 初始化一些示例數據
notes.set("1", {
  id: "1",
  title: "歡迎使用 Life Note MCP Server",
  content: "這是一個示例筆記，展示 MCP 服務器的功能。",
  tags: ["歡迎", "MCP"],
  status: "published",
  priority: "medium",
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
});

nextId = 2;

// 工具定義
const tools = [
  {
    name: "create_note",
    description: "Create a new note",
    inputSchema: {
      type: "object",
      properties: {
        title: { type: "string", description: "Note title" },
        content: { type: "string", description: "Note content" },
        tags: {
          type: "array",
          items: { type: "string" },
          description: "Note tags",
        },
      },
      required: ["title"],
    },
  },
  {
    name: "get_note",
    description: "Get a note by ID",
    inputSchema: {
      type: "object",
      properties: {
        id: { type: "string", description: "Note ID" },
      },
      required: ["id"],
    },
  },
  {
    name: "list_notes",
    description: "List all notes",
    inputSchema: {
      type: "object",
      properties: {
        limit: { type: "number", description: "Maximum number of notes" },
      },
    },
  },
];

// 資源定義
const resources = [
  {
    uri: "notes://all",
    name: "All Notes",
    description: "List of all notes",
    mimeType: "application/json",
  },
];

// 工具執行函數
async function executeTool(name, args) {
  console.error(`[MCP Server] Executing tool: ${name}`);

  switch (name) {
    case "create_note":
      const newNote = {
        id: String(nextId++),
        title: args.title,
        content: args.content || "",
        tags: args.tags || [],
        status: "published",
        priority: "medium",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      notes.set(newNote.id, newNote);
      return [
        {
          type: "text",
          text: JSON.stringify({ success: true, note: newNote }, null, 2),
        },
      ];

    case "get_note":
      const note = notes.get(args.id);
      if (!note) {
        throw new Error(`Note with ID ${args.id} not found`);
      }
      return [
        {
          type: "text",
          text: JSON.stringify({ success: true, note }, null, 2),
        },
      ];

    case "list_notes":
      const allNotes = Array.from(notes.values());
      const limitedNotes = args.limit
        ? allNotes.slice(0, args.limit)
        : allNotes;
      return [
        {
          type: "text",
          text: JSON.stringify(
            { success: true, notes: limitedNotes, total: allNotes.length },
            null,
            2,
          ),
        },
      ];

    default:
      throw new Error(`Unknown tool: ${name}`);
  }
}

// 資源處理函數
async function handleResource(uri) {
  console.error(`[MCP Server] Handling resource: ${uri}`);

  switch (uri) {
    case "notes://all":
      return {
        uri,
        mimeType: "application/json",
        text: JSON.stringify(
          {
            notes: Array.from(notes.values()),
            total: notes.size,
            timestamp: new Date().toISOString(),
          },
          null,
          2,
        ),
      };

    default:
      throw new Error(`Unknown resource: ${uri}`);
  }
}

async function main() {
  console.error("[MCP Server] Starting Simple Life Note MCP Server...");

  const server = new Server(
    {
      name: "simple-life-note-mcp-server",
      version: "1.0.0",
    },
    {
      capabilities: {
        tools: {},
        resources: {},
      },
    },
  );

  // 設置工具處理器
  server.setRequestHandler(ListToolsRequestSchema, async () => {
    console.error("[MCP Server] Listing tools");
    return { tools };
  });

  server.setRequestHandler(CallToolRequestSchema, async (request) => {
    const { name, arguments: args } = request.params;
    console.error(
      `[MCP Server] Tool call: ${name} with args:`,
      JSON.stringify(args),
    );

    try {
      const content = await executeTool(name, args);
      return { content };
    } catch (error) {
      console.error(`[MCP Server] Tool error:`, error.message);
      return {
        content: [
          {
            type: "text",
            text: JSON.stringify(
              {
                error: error.message,
                tool: name,
                timestamp: new Date().toISOString(),
              },
              null,
              2,
            ),
          },
        ],
        isError: true,
      };
    }
  });

  // 設置資源處理器
  server.setRequestHandler(ListResourcesRequestSchema, async () => {
    console.error("[MCP Server] Listing resources");
    return { resources };
  });

  server.setRequestHandler(ReadResourceRequestSchema, async (request) => {
    const { uri } = request.params;
    console.error(`[MCP Server] Reading resource: ${uri}`);

    try {
      const content = await handleResource(uri);
      return { contents: [content] };
    } catch (error) {
      console.error(`[MCP Server] Resource error:`, error.message);
      return {
        contents: [
          {
            uri,
            mimeType: "application/json",
            text: JSON.stringify(
              {
                error: error.message,
                uri,
                timestamp: new Date().toISOString(),
              },
              null,
              2,
            ),
          },
        ],
      };
    }
  });

  // 錯誤處理
  server.onerror = (error) => {
    console.error("[MCP Server] Server error:", error);
  };

  // 優雅關閉
  const shutdown = async (signal) => {
    console.error(`[MCP Server] Received ${signal}, shutting down...`);
    try {
      await server.close();
      process.exit(0);
    } catch (error) {
      console.error("[MCP Server] Error during shutdown:", error);
      process.exit(1);
    }
  };

  process.on("SIGINT", () => shutdown("SIGINT"));
  process.on("SIGTERM", () => shutdown("SIGTERM"));

  // 連接到 stdio 傳輸
  try {
    const transport = new StdioServerTransport();
    await server.connect(transport);

    console.error(
      "[MCP Server] Simple Life Note MCP Server started successfully",
    );
    console.error(`[MCP Server] Available tools: ${tools.length}`);
    console.error(`[MCP Server] Available resources: ${resources.length}`);
    console.error("[MCP Server] Ready to accept requests...");

    // 保持進程運行
    process.stdin.resume();
  } catch (error) {
    console.error("[MCP Server] Failed to start:", error);
    process.exit(1);
  }
}

// 啟動服務器
main().catch((error) => {
  console.error("[MCP Server] Startup failed:", error);
  process.exit(1);
});
