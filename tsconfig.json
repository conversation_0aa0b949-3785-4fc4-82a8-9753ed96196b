{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "esModuleInterop": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": ".", "paths": {"@life-note/core": ["./packages/core/src"], "@life-note/core/*": ["./packages/core/src/*"], "@life-note/utils": ["./packages/utils/src"], "@life-note/utils/*": ["./packages/utils/src/*"], "@life-note/storage": ["./packages/storage/src"], "@life-note/storage/*": ["./packages/storage/src/*"]}}, "include": ["packages/*/src/**/*", "apps/*/src/**/*", "services/*/src/**/*"], "exclude": ["node_modules", "dist", "build", ".next", ".svelte-kit", "**/*.test.ts", "**/*.spec.ts"]}