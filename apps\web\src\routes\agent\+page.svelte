<script lang="ts">
	import { onMount } from 'svelte';
	import {
		Bot,
		Settings,
		MessageSquare,
		FileText,
		Languages,
		Search,
		Lightbulb,
		BarChart3
	} from 'lucide-svelte';

	import Button from '$lib/components/ui/Button.svelte';
	import Card from '$lib/components/ui/Card.svelte';
	import AgentSettings from '$lib/components/agent/AgentSettings.svelte';
	import AgentTaskRunner from '$lib/components/agent/AgentTaskRunner.svelte';
	import { simpleAgentManager } from '$lib/services/agent/SimpleAgentManager';
	import type { AgentTaskType } from '$lib/types/agent';

	// 狀態
	let activeTab: 'settings' | 'tasks' = 'settings';
	let selectedTaskType: AgentTaskType = 'question_answering';
	let isAgentReady = false;

	// 任務類型配置
	const taskTypes = [
		{
			type: 'question_answering' as AgentTaskType,
			icon: MessageSquare,
			label: '問答',
			description: '回答問題和提供信息'
		},
		{
			type: 'content_generation' as AgentTaskType,
			icon: FileText,
			label: '內容生成',
			description: '生成文章、摘要等內容'
		},
		{
			type: 'summarization' as AgentTaskType,
			icon: BarChart3,
			label: '文本摘要',
			description: '提取文本的關鍵信息'
		},
		{
			type: 'translation' as AgentTaskType,
			icon: Languages,
			label: '翻譯',
			description: '多語言文本翻譯'
		},
		{
			type: 'classification' as AgentTaskType,
			icon: Search,
			label: '文本分類',
			description: '自動分類文本內容'
		},
		{
			type: 'note_analysis' as AgentTaskType,
			icon: Lightbulb,
			label: '筆記分析',
			description: '分析筆記內容和結構'
		}
	];

	onMount(() => {
		// 檢查 Agent 狀態
		checkAgentStatus();

		// 監聽 Agent 事件
		simpleAgentManager.addEventListener('agent_registered', handleAgentEvent);
		simpleAgentManager.addEventListener('agent_status_changed', handleAgentEvent);

		return () => {
			simpleAgentManager.removeEventListener('agent_registered', handleAgentEvent);
			simpleAgentManager.removeEventListener('agent_status_changed', handleAgentEvent);
		};
	});

	const handleAgentEvent = () => {
		checkAgentStatus();
	};

	const checkAgentStatus = () => {
		isAgentReady = simpleAgentManager.isReady();

		// 如果 Agent 就緒且當前在設置頁面，自動切換到任務頁面
		if (isAgentReady && activeTab === 'settings') {
			activeTab = 'tasks';
		}
	};

	const switchTab = (tab: 'settings' | 'tasks') => {
		activeTab = tab;
	};

	const selectTaskType = (taskType: AgentTaskType) => {
		selectedTaskType = taskType;
	};

	let selectedTask = $derived(taskTypes.find(t => t.type === selectedTaskType) || taskTypes[0]);
</script>

<svelte:head>
	<title>AI Agent - Life Note</title>
</svelte:head>

<div class="container mx-auto px-4 py-8 max-w-6xl">
	<!-- 頁面標題 -->
	<div class="flex items-center space-x-3 mb-8">
		<Bot class="h-8 w-8 text-primary" />
		<div>
			<h1 class="text-3xl font-bold">AI Agent</h1>
			<p class="text-muted-foreground">智能助手，提供強大的 AI 功能支援</p>
		</div>
	</div>

	<!-- 標籤頁導航 -->
	<div class="border-b border-border mb-8">
		<nav class="flex space-x-8">
			<button
				class="py-2 px-1 border-b-2 font-medium text-sm {activeTab === 'settings'
					? 'border-primary text-primary'
					: 'border-transparent text-muted-foreground hover:text-foreground'}"
				onclick={() => switchTab('settings')}
			>
				<Settings class="h-4 w-4 inline mr-2" />
				設置
			</button>

			<button
				class="py-2 px-1 border-b-2 font-medium text-sm {activeTab === 'tasks'
					? 'border-primary text-primary'
					: 'border-transparent text-muted-foreground hover:text-foreground'}"
				onclick={() => switchTab('tasks')}
				disabled={!isAgentReady}
			>
				<Bot class="h-4 w-4 inline mr-2" />
				AI 任務
			</button>
		</nav>
	</div>

	<!-- 標籤頁內容 -->
	<div class="tab-content">
		{#if activeTab === 'settings'}
			<!-- 設置標籤頁 -->
			<div class="max-w-2xl">
				<AgentSettings />
			</div>
		{:else if activeTab === 'tasks'}
			<!-- 任務標籤頁 -->
			{#if !isAgentReady}
				<Card class="p-8 text-center">
					<Bot class="h-16 w-16 text-muted-foreground mx-auto mb-4" />
					<h3 class="text-lg font-semibold mb-2">Agent 未就緒</h3>
					<p class="text-muted-foreground mb-4">請先在設置頁面配置 Gemini API Key</p>
					<Button onclick={() => switchTab('settings')}>
						<Settings class="h-4 w-4 mr-2" />
						前往設置
					</Button>
				</Card>
			{:else}
				<div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
					<!-- 任務類型選擇 -->
					<div class="lg:col-span-1">
						<Card class="p-4">
							<h3 class="font-semibold mb-4">選擇任務類型</h3>
							<div class="space-y-2">
								{#each taskTypes as taskType}
									<button
										class="w-full text-left p-3 rounded-lg border transition-colors {selectedTaskType ===
										taskType.type
											? 'border-primary bg-primary/5'
											: 'border-border hover:border-primary/50'}"
										onclick={() => selectTaskType(taskType.type)}
									>
										<div class="flex items-center space-x-3">
											<svelte:component
												this={taskType.icon}
												class="h-5 w-5 {selectedTaskType === taskType.type
													? 'text-primary'
													: 'text-muted-foreground'}"
											/>
											<div>
												<div class="font-medium text-sm">{taskType.label}</div>
												<div class="text-xs text-muted-foreground">{taskType.description}</div>
											</div>
										</div>
									</button>
								{/each}
							</div>
						</Card>
					</div>

					<!-- 任務執行器 -->
					<div class="lg:col-span-3">
						<AgentTaskRunner
							taskType={selectedTaskType}
							title={selectedTask.label}
							description={selectedTask.description}
						/>
					</div>
				</div>
			{/if}
		{/if}
	</div>

	<!-- 功能介紹 -->
	<div class="mt-12 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
		<Card class="p-6">
			<MessageSquare class="h-8 w-8 text-blue-500 mb-4" />
			<h3 class="font-semibold mb-2">智能問答</h3>
			<p class="text-sm text-muted-foreground">
				基於 Gemini 2.5 Flash 的強大問答能力，可以回答各種問題並提供詳細解釋。
			</p>
		</Card>

		<Card class="p-6">
			<FileText class="h-8 w-8 text-green-500 mb-4" />
			<h3 class="font-semibold mb-2">內容生成</h3>
			<p class="text-sm text-muted-foreground">
				自動生成高質量的文章、摘要、報告等內容，支援多種寫作風格。
			</p>
		</Card>

		<Card class="p-6">
			<Languages class="h-8 w-8 text-purple-500 mb-4" />
			<h3 class="font-semibold mb-2">多語言翻譯</h3>
			<p class="text-sm text-muted-foreground">
				支援多種語言之間的準確翻譯，保持原文的語義和語調。
			</p>
		</Card>

		<Card class="p-6">
			<Lightbulb class="h-8 w-8 text-yellow-500 mb-4" />
			<h3 class="font-semibold mb-2">筆記分析</h3>
			<p class="text-sm text-muted-foreground">
				深度分析筆記內容，提供情感分析、主題提取、改進建議等洞察。
			</p>
		</Card>

		<Card class="p-6">
			<Search class="h-8 w-8 text-orange-500 mb-4" />
			<h3 class="font-semibold mb-2">智能分類</h3>
			<p class="text-sm text-muted-foreground">自動分類文本內容，幫助組織和管理大量信息。</p>
		</Card>

		<Card class="p-6">
			<Bot class="h-8 w-8 text-red-500 mb-4" />
			<h3 class="font-semibold mb-2">MCP 整合</h3>
			<p class="text-sm text-muted-foreground">
				支援 Model Context Protocol，可與其他 AI 服務進行 A2A 通信。
			</p>
		</Card>
	</div>

	<!-- 使用提示 -->
	<div class="mt-8 p-6 bg-blue-50 rounded-lg">
		<h3 class="font-semibold text-blue-900 mb-2">使用提示</h3>
		<ul class="text-sm text-blue-800 space-y-1">
			<li>• 首次使用需要設置 Google Gemini API Key</li>
			<li>• API Key 會安全地存儲在本地瀏覽器中</li>
			<li>• 支援與筆記系統的深度整合</li>
			<li>• 可通過 MCP 協議與其他 AI Agent 協作</li>
			<li>• 所有任務都會記錄執行歷史以便追蹤</li>
		</ul>
	</div>
</div>
