import type { Note, Tag, SearchParams, PaginatedResult } from '$types';

export interface SearchFilters {
	query?: string;
	tags?: string[];
	status?: string[];
	priority?: string[];
	dateRange?: {
		start?: Date;
		end?: Date;
	};
	author?: string;
	category?: string;
}

export interface SearchOptions {
	page?: number;
	limit?: number;
	sortBy?: 'relevance' | 'title' | 'createdAt' | 'updatedAt';
	sortOrder?: 'asc' | 'desc';
	includeContent?: boolean;
	fuzzySearch?: boolean;
}

export interface SearchResult {
	note: Note;
	score: number;
	highlights: {
		title?: string;
		content?: string;
		excerpt?: string;
	};
	matchedTags: Tag[];
}

export class SearchService {
	private searchIndex: Map<string, Note> = new Map();
	private tagIndex: Map<string, Set<string>> = new Map();
	private contentIndex: Map<string, Set<string>> = new Map();

	/**
	 * 初始化搜索索引
	 */
	async initializeIndex(notes: Note[]): Promise<void> {
		this.searchIndex.clear();
		this.tagIndex.clear();
		this.contentIndex.clear();

		for (const note of notes) {
			this.addToIndex(note);
		}
	}

	/**
	 * 添加筆記到搜索索引
	 */
	addToIndex(note: Note): void {
		this.searchIndex.set(note.id, note);

		// 建立標籤索引
		for (const tag of note.tags) {
			if (!this.tagIndex.has(tag.name.toLowerCase())) {
				this.tagIndex.set(tag.name.toLowerCase(), new Set());
			}
			this.tagIndex.get(tag.name.toLowerCase())!.add(note.id);
		}

		// 建立內容索引
		const words = this.extractWords(note.title + ' ' + note.content);
		for (const word of words) {
			if (!this.contentIndex.has(word)) {
				this.contentIndex.set(word, new Set());
			}
			this.contentIndex.get(word)!.add(note.id);
		}
	}

	/**
	 * 從索引中移除筆記
	 */
	removeFromIndex(noteId: string): void {
		const note = this.searchIndex.get(noteId);
		if (!note) return;

		this.searchIndex.delete(noteId);

		// 從標籤索引中移除
		for (const tag of note.tags) {
			const tagSet = this.tagIndex.get(tag.name.toLowerCase());
			if (tagSet) {
				tagSet.delete(noteId);
				if (tagSet.size === 0) {
					this.tagIndex.delete(tag.name.toLowerCase());
				}
			}
		}

		// 從內容索引中移除
		const words = this.extractWords(note.title + ' ' + note.content);
		for (const word of words) {
			const wordSet = this.contentIndex.get(word);
			if (wordSet) {
				wordSet.delete(noteId);
				if (wordSet.size === 0) {
					this.contentIndex.delete(word);
				}
			}
		}
	}

	/**
	 * 執行搜索
	 */
	async search(
		filters: SearchFilters,
		options: SearchOptions = {}
	): Promise<PaginatedResult<SearchResult>> {
		const {
			page = 1,
			limit = 20,
			sortBy = 'relevance',
			sortOrder = 'desc',
			includeContent = true,
			fuzzySearch = false
		} = options;

		let candidateNoteIds = new Set<string>();
		let isFirstFilter = true;

		// 文本搜索
		if (filters.query) {
			const queryResults = this.searchByQuery(filters.query, fuzzySearch);
			if (isFirstFilter) {
				candidateNoteIds = new Set(queryResults.map(r => r.noteId));
				isFirstFilter = false;
			} else {
				candidateNoteIds = this.intersectSets(
					candidateNoteIds,
					new Set(queryResults.map(r => r.noteId))
				);
			}
		}

		// 標籤過濾
		if (filters.tags && filters.tags.length > 0) {
			const tagResults = this.searchByTags(filters.tags);
			if (isFirstFilter) {
				candidateNoteIds = tagResults;
				isFirstFilter = false;
			} else {
				candidateNoteIds = this.intersectSets(candidateNoteIds, tagResults);
			}
		}

		// 如果沒有任何過濾條件，返回所有筆記
		if (isFirstFilter) {
			candidateNoteIds = new Set(this.searchIndex.keys());
		}

		// 應用其他過濾條件
		const filteredNotes = Array.from(candidateNoteIds)
			.map(id => this.searchIndex.get(id)!)
			.filter(note => this.applyFilters(note, filters));

		// 計算相關性分數並生成搜索結果
		const searchResults = filteredNotes.map(note => this.createSearchResult(note, filters.query));

		// 排序
		this.sortResults(searchResults, sortBy, sortOrder);

		// 分頁
		const startIndex = (page - 1) * limit;
		const endIndex = startIndex + limit;
		const paginatedResults = searchResults.slice(startIndex, endIndex);

		return {
			items: paginatedResults,
			page,
			limit,
			total: searchResults.length,
			totalPages: Math.ceil(searchResults.length / limit),
			hasNext: endIndex < searchResults.length,
			hasPrev: page > 1
		};
	}

	/**
	 * 獲取搜索建議
	 */
	async getSuggestions(query: string, limit: number = 5): Promise<string[]> {
		if (!query || query.length < 2) return [];

		const suggestions = new Set<string>();
		const queryLower = query.toLowerCase();

		// 從標籤中獲取建議
		for (const tag of this.tagIndex.keys()) {
			if (tag.includes(queryLower)) {
				suggestions.add(tag);
			}
		}

		// 從內容詞彙中獲取建議
		for (const word of this.contentIndex.keys()) {
			if (word.includes(queryLower) && word.length > 2) {
				suggestions.add(word);
			}
		}

		return Array.from(suggestions).slice(0, limit);
	}

	/**
	 * 獲取熱門標籤
	 */
	getPopularTags(limit: number = 10): Array<{ name: string; count: number }> {
		const tagCounts = Array.from(this.tagIndex.entries())
			.map(([tag, noteIds]) => ({ name: tag, count: noteIds.size }))
			.sort((a, b) => b.count - a.count);

		return tagCounts.slice(0, limit);
	}

	/**
	 * 根據查詢文本搜索
	 */
	private searchByQuery(
		query: string,
		fuzzySearch: boolean
	): Array<{ noteId: string; score: number }> {
		const words = this.extractWords(query);
		const results = new Map<string, number>();

		for (const word of words) {
			const matchingNoteIds = fuzzySearch ? this.fuzzySearchWord(word) : this.exactSearchWord(word);

			for (const [noteId, score] of matchingNoteIds) {
				results.set(noteId, (results.get(noteId) || 0) + score);
			}
		}

		return Array.from(results.entries()).map(([noteId, score]) => ({ noteId, score }));
	}

	/**
	 * 根據標籤搜索
	 */
	private searchByTags(tags: string[]): Set<string> {
		const results = new Set<string>();
		let isFirst = true;

		for (const tag of tags) {
			const tagNoteIds = this.tagIndex.get(tag.toLowerCase()) || new Set();

			if (isFirst) {
				tagNoteIds.forEach(id => results.add(id));
				isFirst = false;
			} else {
				// 取交集
				const intersection = new Set<string>();
				for (const id of results) {
					if (tagNoteIds.has(id)) {
						intersection.add(id);
					}
				}
				results.clear();
				intersection.forEach(id => results.add(id));
			}
		}

		return results;
	}

	/**
	 * 精確搜索單詞
	 */
	private exactSearchWord(word: string): Array<[string, number]> {
		const wordLower = word.toLowerCase();
		const noteIds = this.contentIndex.get(wordLower) || new Set();
		return Array.from(noteIds).map(id => [id, 1]);
	}

	/**
	 * 模糊搜索單詞
	 */
	private fuzzySearchWord(word: string): Array<[string, number]> {
		const results = new Map<string, number>();
		const wordLower = word.toLowerCase();

		for (const [indexWord, noteIds] of this.contentIndex.entries()) {
			const similarity = this.calculateSimilarity(wordLower, indexWord);
			if (similarity > 0.6) {
				// 相似度閾值
				for (const noteId of noteIds) {
					results.set(noteId, (results.get(noteId) || 0) + similarity);
				}
			}
		}

		return Array.from(results.entries());
	}

	/**
	 * 應用其他過濾條件
	 */
	private applyFilters(note: Note, filters: SearchFilters): boolean {
		// 狀態過濾
		if (filters.status && filters.status.length > 0) {
			if (!filters.status.includes(note.status)) return false;
		}

		// 優先級過濾
		if (filters.priority && filters.priority.length > 0) {
			if (!filters.priority.includes(note.priority)) return false;
		}

		// 日期範圍過濾
		if (filters.dateRange) {
			const noteDate = new Date(note.updatedAt);
			if (filters.dateRange.start && noteDate < filters.dateRange.start) return false;
			if (filters.dateRange.end && noteDate > filters.dateRange.end) return false;
		}

		// 作者過濾
		if (filters.author && note.authorId !== filters.author) return false;

		// 分類過濾
		if (filters.category && note.categoryId !== filters.category) return false;

		return true;
	}

	/**
	 * 創建搜索結果
	 */
	private createSearchResult(note: Note, query?: string): SearchResult {
		const highlights: SearchResult['highlights'] = {};

		if (query) {
			highlights.title = this.highlightText(note.title, query);
			highlights.content = this.highlightText(note.content, query);
			highlights.excerpt = this.highlightText(note.excerpt || '', query);
		}

		return {
			note,
			score: this.calculateRelevanceScore(note, query),
			highlights,
			matchedTags: query
				? note.tags.filter(tag => tag.name.toLowerCase().includes(query.toLowerCase()))
				: []
		};
	}

	/**
	 * 排序搜索結果
	 */
	private sortResults(results: SearchResult[], sortBy: string, sortOrder: string): void {
		results.sort((a, b) => {
			let comparison = 0;

			switch (sortBy) {
				case 'relevance':
					comparison = b.score - a.score;
					break;
				case 'title':
					comparison = a.note.title.localeCompare(b.note.title);
					break;
				case 'createdAt':
					comparison = new Date(a.note.createdAt).getTime() - new Date(b.note.createdAt).getTime();
					break;
				case 'updatedAt':
					comparison = new Date(a.note.updatedAt).getTime() - new Date(b.note.updatedAt).getTime();
					break;
			}

			return sortOrder === 'desc' ? -comparison : comparison;
		});
	}

	/**
	 * 提取文本中的詞彙
	 */
	private extractWords(text: string): string[] {
		return text
			.toLowerCase()
			.replace(/[^\w\s\u4e00-\u9fff]/g, ' ') // 保留中文字符
			.split(/\s+/)
			.filter(word => word.length > 1);
	}

	/**
	 * 計算字符串相似度
	 */
	private calculateSimilarity(str1: string, str2: string): number {
		const longer = str1.length > str2.length ? str1 : str2;
		const shorter = str1.length > str2.length ? str2 : str1;

		if (longer.length === 0) return 1.0;

		const distance = this.levenshteinDistance(longer, shorter);
		return (longer.length - distance) / longer.length;
	}

	/**
	 * 計算編輯距離
	 */
	private levenshteinDistance(str1: string, str2: string): number {
		const matrix = Array(str2.length + 1)
			.fill(null)
			.map(() => Array(str1.length + 1).fill(null));

		for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
		for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;

		for (let j = 1; j <= str2.length; j++) {
			for (let i = 1; i <= str1.length; i++) {
				const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
				matrix[j][i] = Math.min(
					matrix[j][i - 1] + 1,
					matrix[j - 1][i] + 1,
					matrix[j - 1][i - 1] + indicator
				);
			}
		}

		return matrix[str2.length][str1.length];
	}

	/**
	 * 計算相關性分數
	 */
	private calculateRelevanceScore(note: Note, query?: string): number {
		if (!query) return 1;

		let score = 0;
		const queryLower = query.toLowerCase();
		const titleLower = note.title.toLowerCase();
		const contentLower = note.content.toLowerCase();

		// 標題匹配權重更高
		if (titleLower.includes(queryLower)) score += 10;
		if (contentLower.includes(queryLower)) score += 5;

		// 標籤匹配
		for (const tag of note.tags) {
			if (tag.name.toLowerCase().includes(queryLower)) score += 3;
		}

		// 優先級加權
		switch (note.priority) {
			case 'urgent':
				score *= 1.5;
				break;
			case 'high':
				score *= 1.3;
				break;
			case 'medium':
				score *= 1.1;
				break;
		}

		return score;
	}

	/**
	 * 高亮文本
	 */
	private highlightText(text: string, query: string): string {
		if (!query) return text;

		const regex = new RegExp(`(${query})`, 'gi');
		return text.replace(regex, '<mark>$1</mark>');
	}

	/**
	 * 計算集合交集
	 */
	private intersectSets<T>(set1: Set<T>, set2: Set<T>): Set<T> {
		const result = new Set<T>();
		for (const item of set1) {
			if (set2.has(item)) {
				result.add(item);
			}
		}
		return result;
	}
}

// 導出單例實例
export const searchService = new SearchService();
