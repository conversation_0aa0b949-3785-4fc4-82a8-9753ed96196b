<script lang="ts">
	import { onMount } from 'svelte';
	import { FileText, Plus, Search, TrendingUp, Clock, Tag, GitBranch, Zap } from 'lucide-svelte';

	import Button from '$components/ui/Button.svelte';
	import Card from '$components/ui/Card.svelte';
	import { noteStore, allNotes, noteStats } from '$stores/notes';
	import { goto } from '$app/navigation';

	// Reactive data
	$: recentNotes = $allNotes ? $allNotes.slice(0, 5) : []; // 顯示最近 5 個筆記
	$: statistics = $noteStats;

	// 計算標籤總數
	$: totalTags = $allNotes
		? $allNotes.reduce((acc, note) => {
				const uniqueTags = new Set(note.tags.map(tag => tag.name));
				return acc + uniqueTags.size;
			}, 0)
		: 0;

	onMount(async () => {
		// Load initial data
		await noteStore.loadNotes();
		await noteStore.getStats();
	});

	// Event handlers
	const handleCreateNote = () => {
		goto('/notes/new');
	};

	const handleSearchNotes = () => {
		goto('/notes/search');
	};

	const handleViewNote = (noteId: string) => {
		goto(`/notes/${noteId}`);
	};
</script>

<svelte:head>
	<title>首頁 - Life Note</title>
	<meta name="description" content="Life Note 智慧知識庫筆記系統首頁" />
</svelte:head>

<div class="container mx-auto px-4 py-8">
	<!-- Welcome Section -->
	<section class="mb-12">
		<div class="text-center">
			<h1 class="mb-4 text-4xl font-bold gradient-text">歡迎使用 Life Note</h1>
			<p class="mb-8 text-xl text-muted-foreground max-w-2xl mx-auto">
				本地優先的智慧知識庫筆記系統，支援版本控制、依賴關係檢測和 AI 助理
			</p>

			<!-- Quick Actions -->
			<div class="flex flex-wrap justify-center gap-4">
				<Button variant="default" size="lg" onclick={() => goto('/notes')} class="shadow-glow">
					<Plus class="mr-2 h-5 w-5" />
					新增筆記
				</Button>

				<Button variant="outline" size="lg" onclick={handleSearchNotes}>
					<Search class="mr-2 h-5 w-5" />
					搜尋筆記
				</Button>

				<Button variant="ghost" size="lg" onclick={() => goto('/components')}>UI 組件展示</Button>

				<Button variant="secondary" size="lg" onclick={() => goto('/notes')}>筆記管理</Button>
			</div>
		</div>
	</section>

	<!-- Statistics Cards -->
	<section class="mb-12">
		<h2 class="mb-6 text-2xl font-semibold">統計概覽</h2>

		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
			<Card class="p-6">
				<div class="flex items-center justify-between">
					<div>
						<p class="text-sm font-medium text-muted-foreground">總筆記數</p>
						<p class="text-2xl font-bold">{statistics?.total || 0}</p>
					</div>
					<FileText class="h-8 w-8 text-primary" />
				</div>
			</Card>

			<Card class="p-6">
				<div class="flex items-center justify-between">
					<div>
						<p class="text-sm font-medium text-muted-foreground">本週新增</p>
						<p class="text-2xl font-bold">{statistics?.recentActivity?.created || 0}</p>
					</div>
					<TrendingUp class="h-8 w-8 text-green-500" />
				</div>
			</Card>

			<Card class="p-6">
				<div class="flex items-center justify-between">
					<div>
						<p class="text-sm font-medium text-muted-foreground">標籤數量</p>
						<p class="text-2xl font-bold">{totalTags}</p>
					</div>
					<Tag class="h-8 w-8 text-blue-500" />
				</div>
			</Card>

			<Card class="p-6">
				<div class="flex items-center justify-between">
					<div>
						<p class="text-sm font-medium text-muted-foreground">依賴關係</p>
						<p class="text-2xl font-bold">{statistics?.totalDependencies || 0}</p>
					</div>
					<GitBranch class="h-8 w-8 text-purple-500" />
				</div>
			</Card>
		</div>
	</section>

	<!-- Recent Notes -->
	<section class="mb-12">
		<div class="flex items-center justify-between mb-6">
			<h2 class="text-2xl font-semibold">最近筆記</h2>
			<Button variant="ghost" onclick={() => goto('/notes')}>查看全部</Button>
		</div>

		{#if recentNotes.length > 0}
			<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
				{#each recentNotes as note (note.id)}
					<Card
						class="p-6 hover:shadow-lg transition-shadow cursor-pointer"
						onclick={() => handleViewNote(note.id)}
					>
						<div class="mb-4">
							<h3 class="font-semibold text-lg mb-2 line-clamp-2">{note.title}</h3>
							<p class="text-muted-foreground text-sm line-clamp-3">{note.content}</p>
						</div>

						<div class="flex items-center justify-between text-xs text-muted-foreground">
							<div class="flex items-center">
								<Clock class="mr-1 h-3 w-3" />
								{new Date(note.updatedAt).toLocaleDateString('zh-TW')}
							</div>

							<div class="flex items-center space-x-2">
								{#if note.status === 'published'}
									<span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
										已發布
									</span>
								{:else}
									<span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">
										草稿
									</span>
								{/if}

								{#if note.priority === 'high' || note.priority === 'urgent'}
									<Zap class="h-3 w-3 text-orange-500" />
								{/if}
							</div>
						</div>
					</Card>
				{/each}
			</div>
		{:else}
			<Card class="p-12 text-center">
				<FileText class="mx-auto h-12 w-12 text-muted-foreground mb-4" />
				<h3 class="text-lg font-semibold mb-2">還沒有筆記</h3>
				<p class="text-muted-foreground mb-4">開始創建您的第一個筆記吧！</p>
				<Button on:click={handleCreateNote}>
					<Plus class="mr-2 h-4 w-4" />
					新增筆記
				</Button>
			</Card>
		{/if}
	</section>

	<!-- Quick Tips -->
	<section>
		<h2 class="mb-6 text-2xl font-semibold">快速提示</h2>

		<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
			<Card class="p-6">
				<h3 class="font-semibold mb-2">版本控制</h3>
				<p class="text-muted-foreground text-sm">
					Life Note 支援 Git-like 的版本控制，您可以創建草稿、發布版本，並在需要時回滾到之前的版本。
				</p>
			</Card>

			<Card class="p-6">
				<h3 class="font-semibold mb-2">依賴關係</h3>
				<p class="text-muted-foreground text-sm">
					系統會自動檢測筆記間的依賴關係，幫助您建立知識網絡，並在相關筆記更新時提供通知。
				</p>
			</Card>

			<Card class="p-6">
				<h3 class="font-semibold mb-2">AI 助理</h3>
				<p class="text-muted-foreground text-sm">
					內建的 AI 助理可以幫助您整理筆記、生成摘要、回答問題，並提供智慧建議。
				</p>
			</Card>

			<Card class="p-6">
				<h3 class="font-semibold mb-2">本地優先</h3>
				<p class="text-muted-foreground text-sm">
					所有數據都存儲在本地，確保您的隱私和數據安全，同時支援離線使用。
				</p>
			</Card>
		</div>
	</section>
</div>

<style>
	.line-clamp-2 {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	.line-clamp-3 {
		display: -webkit-box;
		-webkit-line-clamp: 3;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
</style>
