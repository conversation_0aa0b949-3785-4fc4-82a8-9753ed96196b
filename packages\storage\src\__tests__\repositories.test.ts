import { describe, it, expect, beforeAll, afterAll, beforeEach } from "vitest";
import { prismaClient } from "../prisma/client.js";
import { RepositoryFactory } from "../repositories/RepositoryFactory.js";
import type { User } from "../repositories/UserRepository.js";
import type { Dependency } from "../repositories/DependencyRepository.js";

describe("Repository Tests", () => {
  let repositoryFactory: RepositoryFactory;

  beforeAll(async () => {
    await prismaClient.connect();
    repositoryFactory = new RepositoryFactory();
  });

  afterAll(async () => {
    await prismaClient.disconnect();
  });

  beforeEach(async () => {
    // 清理測試數據
    await prismaClient.client.dependency.deleteMany({
      where: { id: { startsWith: "test-" } },
    });
    await prismaClient.client.note.deleteMany({
      where: { id: { startsWith: "test-" } },
    });
    await prismaClient.client.user.deleteMany({
      where: { id: { startsWith: "test-" } },
    });
  });

  describe("UserRepository", () => {
    it("should create and retrieve a user", async () => {
      const userRepo = repositoryFactory.userRepository;

      const userData: User = {
        id: "test-user-1",
        username: "testuser",
        email: "<EMAIL>",
        displayName: "測試用戶",
        role: "user",
        status: "active",
        metadata: { test: true },
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // 創建用戶
      const createdUser = await userRepo.save(userData);
      expect(createdUser).toBeDefined();
      expect(createdUser.username).toBe("testuser");

      // 檢索用戶
      const retrievedUser = await userRepo.findById("test-user-1");
      expect(retrievedUser).toBeDefined();
      expect(retrievedUser?.email).toBe("<EMAIL>");

      // 根據用戶名查找
      const userByUsername = await userRepo.findByUsername("testuser");
      expect(userByUsername).toBeDefined();
      expect(userByUsername?.id).toBe("test-user-1");

      // 根據郵箱查找
      const userByEmail = await userRepo.findByEmail("<EMAIL>");
      expect(userByEmail).toBeDefined();
      expect(userByEmail?.id).toBe("test-user-1");

      // 檢查用戶名可用性
      const isUsernameAvailable =
        await userRepo.isUsernameAvailable("testuser");
      expect(isUsernameAvailable).toBe(false);

      const isNewUsernameAvailable =
        await userRepo.isUsernameAvailable("newuser");
      expect(isNewUsernameAvailable).toBe(true);
    });

    it("should search users", async () => {
      const userRepo = repositoryFactory.userRepository;

      // 創建測試用戶
      const users: User[] = [
        {
          id: "test-user-search-1",
          username: "alice",
          email: "<EMAIL>",
          displayName: "Alice Smith",
          role: "user",
          status: "active",
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: "test-user-search-2",
          username: "bob",
          email: "<EMAIL>",
          displayName: "Bob Johnson",
          role: "admin",
          status: "active",
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      for (const user of users) {
        await userRepo.save(user);
      }

      // 搜索用戶
      const searchResults = await userRepo.search("alice");
      expect(searchResults.data).toHaveLength(1);
      expect(searchResults.data[0].username).toBe("alice");

      // 根據角色查找
      const adminUsers = await userRepo.findByRole("admin");
      expect(adminUsers.data).toHaveLength(1);
      expect(adminUsers.data[0].username).toBe("bob");
    });

    it("should get user statistics", async () => {
      const userRepo = repositoryFactory.userRepository;

      // 創建測試用戶
      const users: User[] = [
        {
          id: "test-user-stats-1",
          username: "user1",
          email: "<EMAIL>",
          role: "user",
          status: "active",
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: "test-user-stats-2",
          username: "admin1",
          email: "<EMAIL>",
          role: "admin",
          status: "active",
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      for (const user of users) {
        await userRepo.save(user);
      }

      const stats = await userRepo.getStatistics();
      expect(stats.total).toBeGreaterThanOrEqual(2);
      expect(stats.byRole.user).toBeGreaterThanOrEqual(1);
      expect(stats.byRole.admin).toBeGreaterThanOrEqual(1);
    });
  });

  describe("DependencyRepository", () => {
    let testUserId: string;
    let sourceNoteId: string;
    let targetNoteId: string;

    beforeEach(async () => {
      // 創建測試用戶
      testUserId = "test-user-dep";
      await prismaClient.client.user.create({
        data: {
          id: testUserId,
          username: "depuser",
          email: "<EMAIL>",
          role: "user",
          status: "active",
        },
      });

      // 創建測試筆記
      sourceNoteId = "test-note-source";
      targetNoteId = "test-note-target";

      await prismaClient.client.note.create({
        data: {
          id: sourceNoteId,
          title: "源筆記",
          content: "這是源筆記",
          status: "draft",
          priority: "medium",
          version: "1.0.0",
          authorId: testUserId,
        },
      });

      await prismaClient.client.note.create({
        data: {
          id: targetNoteId,
          title: "目標筆記",
          content: "這是目標筆記",
          status: "draft",
          priority: "medium",
          version: "1.0.0",
          authorId: testUserId,
        },
      });
    });

    it("should create and retrieve dependencies", async () => {
      const depRepo = repositoryFactory.dependencyRepository;

      // 創建依賴關係
      const dependency = await depRepo.createDependency(
        sourceNoteId,
        targetNoteId,
        "reference",
        "medium",
        "測試依賴關係",
      );

      expect(dependency).toBeDefined();
      expect(dependency.sourceNoteId).toBe(sourceNoteId);
      expect(dependency.targetNoteId).toBe(targetNoteId);
      expect(dependency.type).toBe("reference");

      // 檢查依賴關係是否存在
      const exists = await depRepo.existsBetween(sourceNoteId, targetNoteId);
      expect(exists).toBe(true);

      // 查找依賴關係
      const foundDependency = await depRepo.findBetween(
        sourceNoteId,
        targetNoteId,
      );
      expect(foundDependency).toBeDefined();
      expect(foundDependency?.id).toBe(dependency.id);

      // 根據源筆記查找
      const sourceDepResults = await depRepo.findBySourceNoteId(sourceNoteId);
      expect(sourceDepResults.data).toHaveLength(1);

      // 根據目標筆記查找
      const targetDepResults = await depRepo.findByTargetNoteId(targetNoteId);
      expect(targetDepResults.data).toHaveLength(1);

      // 根據筆記 ID 查找所有相關依賴
      const noteDepResults = await depRepo.findByNoteId(sourceNoteId);
      expect(noteDepResults.data).toHaveLength(1);
    });

    it("should manage dependency states", async () => {
      const depRepo = repositoryFactory.dependencyRepository;

      // 創建依賴關係
      const dependency = await depRepo.createDependency(
        sourceNoteId,
        targetNoteId,
        "reference",
        "medium",
      );

      expect(dependency.isActive).toBe(true);

      // 停用依賴關係
      await depRepo.deactivate(dependency.id);

      const deactivatedDep = await depRepo.findById(dependency.id);
      expect(deactivatedDep?.isActive).toBe(false);

      // 重新激活
      await depRepo.activate(dependency.id);

      const reactivatedDep = await depRepo.findById(dependency.id);
      expect(reactivatedDep?.isActive).toBe(true);

      // 更新強度
      await depRepo.updateStrength(dependency.id, "strong");

      const updatedDep = await depRepo.findById(dependency.id);
      expect(updatedDep?.strength).toBe("strong");
    });

    it("should batch create dependencies", async () => {
      const depRepo = repositoryFactory.dependencyRepository;

      // 創建額外的目標筆記
      const targetNote2Id = "test-note-target-2";
      await prismaClient.client.note.create({
        data: {
          id: targetNote2Id,
          title: "目標筆記2",
          content: "這是目標筆記2",
          status: "draft",
          priority: "medium",
          version: "1.0.0",
          authorId: testUserId,
        },
      });

      const dependenciesToCreate = [
        {
          sourceNoteId,
          targetNoteId,
          type: "reference" as const,
          strength: "medium" as const,
          description: "第一個依賴",
        },
        {
          sourceNoteId,
          targetNoteId: targetNote2Id,
          type: "include" as const,
          strength: "strong" as const,
          description: "第二個依賴",
        },
      ];

      const createdDependencies =
        await depRepo.batchCreateDependencies(dependenciesToCreate);
      expect(createdDependencies).toHaveLength(2);
      expect(createdDependencies[0].type).toBe("reference");
      expect(createdDependencies[1].type).toBe("include");

      // 驗證依賴關係已創建
      const sourceDepResults = await depRepo.findBySourceNoteId(sourceNoteId);
      expect(sourceDepResults.data).toHaveLength(2);
    });

    it("should get dependency statistics", async () => {
      const depRepo = repositoryFactory.dependencyRepository;

      // 創建多個依賴關係
      await depRepo.createDependency(
        sourceNoteId,
        targetNoteId,
        "reference",
        "medium",
      );

      const stats = await depRepo.getStatistics();
      expect(stats.total).toBeGreaterThanOrEqual(1);
      expect(stats.active).toBeGreaterThanOrEqual(1);
      expect(stats.byType.reference).toBeGreaterThanOrEqual(1);

      // 獲取筆記的依賴統計
      const noteStats = await depRepo.getNoteStatistics(sourceNoteId);
      expect(noteStats.outgoingCount).toBe(1);
      expect(noteStats.totalConnections).toBe(1);

      const targetNoteStats = await depRepo.getNoteStatistics(targetNoteId);
      expect(targetNoteStats.incomingCount).toBe(1);
      expect(targetNoteStats.totalConnections).toBe(1);
    });
  });

  describe("Repository Factory", () => {
    it("should provide access to all repositories", async () => {
      const factory = new RepositoryFactory();

      expect(factory.noteRepository).toBeDefined();
      expect(factory.userRepository).toBeDefined();
      expect(factory.dependencyRepository).toBeDefined();

      const allRepos = factory.getAllRepositories();
      expect(allRepos.noteRepository).toBeDefined();
      expect(allRepos.userRepository).toBeDefined();
      expect(allRepos.dependencyRepository).toBeDefined();
    });

    it("should support transactions", async () => {
      const factory = new RepositoryFactory();

      await factory.transaction(async (repos) => {
        // 在事務中創建用戶
        const user: User = {
          id: "test-user-transaction",
          username: "transactionuser",
          email: "<EMAIL>",
          role: "user",
          status: "active",
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        const createdUser = await repos.userRepository.save(user);
        expect(createdUser).toBeDefined();

        // 驗證用戶已創建
        const retrievedUser = await repos.userRepository.findById(
          "test-user-transaction",
        );
        expect(retrievedUser).toBeDefined();
      });

      // 驗證事務外也能訪問
      const userRepo = factory.userRepository;
      const user = await userRepo.findById("test-user-transaction");
      expect(user).toBeDefined();
    });
  });
});
