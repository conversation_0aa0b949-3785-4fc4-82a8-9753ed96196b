import { BaseAgent } from './BaseAgent';
import type { AgentConfig, AgentTaskRequest, RemoteServiceConfig } from '$lib/types/agent';

/**
 * 遠程 AI Agent 實現
 * 使用遠程 AI 服務進行推理，適合需要高性能模型的場景
 */
export class RemoteAgent extends BaseAgent {
	private serviceConfig: RemoteServiceConfig;
	private rateLimitTracker = new Map<string, number[]>();

	constructor(config: AgentConfig, serviceConfig: RemoteServiceConfig) {
		super(config);
		this.serviceConfig = serviceConfig;
	}

	/**
	 * 初始化遠程服務連接
	 */
	protected async onInitialize(): Promise<void> {
		try {
			console.log(`Initializing remote service connection for agent ${this.id}...`);

			// 測試連接
			await this.testConnection();

			console.log(`Remote service connected successfully for agent ${this.id}`);
		} catch (error) {
			console.error(`Failed to connect to remote service for agent ${this.id}:`, error);
			throw error;
		}
	}

	/**
	 * 執行任務實現
	 */
	protected async onExecuteTask(request: AgentTaskRequest): Promise<any> {
		// 檢查速率限制
		if (!this.checkRateLimit()) {
			throw new Error('Rate limit exceeded');
		}

		try {
			switch (request.type) {
				case 'note_analysis':
					return await this.analyzeNoteRemote(request);
				case 'content_generation':
					return await this.generateContentRemote(request);
				case 'summarization':
					return await this.summarizeTextRemote(request);
				case 'translation':
					return await this.translateTextRemote(request);
				case 'question_answering':
					return await this.answerQuestionRemote(request);
				case 'classification':
					return await this.classifyTextRemote(request);
				case 'extraction':
					return await this.extractInformationRemote(request);
				default:
					throw new Error(`Unsupported task type: ${request.type}`);
			}
		} catch (error) {
			console.error(`Remote task execution failed for agent ${this.id}:`, error);
			throw error;
		}
	}

	/**
	 * 停止實現
	 */
	protected async onStop(): Promise<void> {
		console.log(`Stopping remote agent ${this.id}`);
		// 清理連接資源
		this.rateLimitTracker.clear();
	}

	/**
	 * 配置更新實現
	 */
	protected async onConfigUpdate(oldConfig: AgentConfig, newConfig: AgentConfig): Promise<void> {
		console.log(`Configuration updated for remote agent ${this.id}`);
		// 如果服務配置發生變化，可能需要重新測試連接
	}

	/**
	 * 取消任務實現
	 */
	protected async onCancelTask(taskId: string): Promise<void> {
		console.log(`Task ${taskId} cancelled for remote agent ${this.id}`);
		// 遠程任務取消可能需要調用特定的 API
	}

	/**
	 * 測試連接
	 */
	private async testConnection(): Promise<void> {
		try {
			const response = await this.makeRequest('/health', 'GET');
			if (!response.ok) {
				throw new Error(`Health check failed: ${response.status}`);
			}
		} catch (error) {
			throw new Error(`Connection test failed: ${error}`);
		}
	}

	/**
	 * 檢查速率限制
	 */
	private checkRateLimit(): boolean {
		const now = Date.now();
		const windowStart = now - 60000; // 1 分鐘窗口
		const key = this.serviceConfig.provider;

		if (!this.rateLimitTracker.has(key)) {
			this.rateLimitTracker.set(key, []);
		}

		const requests = this.rateLimitTracker.get(key)!;

		// 清理過期的請求記錄
		const validRequests = requests.filter(timestamp => timestamp > windowStart);

		// 檢查是否超過限制
		if (validRequests.length >= this.serviceConfig.rateLimitPerMinute) {
			return false;
		}

		// 記錄新請求
		validRequests.push(now);
		this.rateLimitTracker.set(key, validRequests);

		return true;
	}

	/**
	 * 發送 HTTP 請求
	 */
	private async makeRequest(
		endpoint: string,
		method: 'GET' | 'POST' = 'POST',
		data?: any
	): Promise<Response> {
		const url = `${this.serviceConfig.endpoint}${endpoint}`;

		const headers: Record<string, string> = {
			'Content-Type': 'application/json',
			Authorization: `Bearer ${this.serviceConfig.apiKey}`
		};

		const options: RequestInit = {
			method,
			headers,
			signal: AbortSignal.timeout(this.serviceConfig.timeout)
		};

		if (data && method === 'POST') {
			options.body = JSON.stringify(data);
		}

		let lastError: Error | null = null;

		// 重試機制
		for (let attempt = 0; attempt < this.serviceConfig.retryAttempts; attempt++) {
			try {
				const response = await fetch(url, options);

				if (response.ok) {
					return response;
				}

				// 如果是速率限制錯誤，等待後重試
				if (response.status === 429) {
					const retryAfter = response.headers.get('Retry-After');
					const delay = retryAfter ? parseInt(retryAfter) * 1000 : 1000 * (attempt + 1);
					await new Promise(resolve => setTimeout(resolve, delay));
					continue;
				}

				throw new Error(`HTTP ${response.status}: ${response.statusText}`);
			} catch (error) {
				lastError = error instanceof Error ? error : new Error(String(error));

				if (attempt < this.serviceConfig.retryAttempts - 1) {
					// 指數退避
					const delay = 1000 * Math.pow(2, attempt);
					await new Promise(resolve => setTimeout(resolve, delay));
				}
			}
		}

		throw lastError || new Error('Request failed after all retries');
	}

	/**
	 * 遠程筆記分析
	 */
	private async analyzeNoteRemote(request: AgentTaskRequest): Promise<any> {
		const { content, title } = request.input;

		const payload = {
			model: this.serviceConfig.model,
			messages: [
				{
					role: 'system',
					content:
						'You are an expert note analyzer. Analyze the given note and provide insights about sentiment, topics, complexity, and suggestions.'
				},
				{
					role: 'user',
					content: `Please analyze this note:\n\nTitle: ${title}\nContent: ${content}`
				}
			],
			temperature: request.options?.temperature || 0.3,
			max_tokens: request.options?.maxTokens || 1000
		};

		const response = await this.makeRequest('/chat/completions', 'POST', payload);
		const result = await response.json();

		// 解析 AI 回應並結構化
		return this.parseAnalysisResponse(result.choices[0].message.content);
	}

	/**
	 * 遠程內容生成
	 */
	private async generateContentRemote(request: AgentTaskRequest): Promise<any> {
		const { prompt, maxLength = 500 } = request.input;

		const payload = {
			model: this.serviceConfig.model,
			messages: [
				{
					role: 'user',
					content: prompt
				}
			],
			temperature: request.options?.temperature || 0.7,
			max_tokens: maxLength
		};

		const response = await this.makeRequest('/chat/completions', 'POST', payload);
		const result = await response.json();

		return {
			content: result.choices[0].message.content,
			usage: result.usage
		};
	}

	/**
	 * 遠程文本摘要
	 */
	private async summarizeTextRemote(request: AgentTaskRequest): Promise<any> {
		const { text, maxLength = 200 } = request.input;

		const payload = {
			model: this.serviceConfig.model,
			messages: [
				{
					role: 'system',
					content: `Summarize the following text in no more than ${maxLength} characters. Provide a concise and informative summary.`
				},
				{
					role: 'user',
					content: text
				}
			],
			temperature: 0.3,
			max_tokens: Math.ceil(maxLength / 3) // 估算 token 數量
		};

		const response = await this.makeRequest('/chat/completions', 'POST', payload);
		const result = await response.json();

		const summary = result.choices[0].message.content;

		return {
			summary,
			originalLength: text.length,
			summaryLength: summary.length,
			compressionRatio: summary.length / text.length,
			usage: result.usage
		};
	}

	/**
	 * 遠程文本翻譯
	 */
	private async translateTextRemote(request: AgentTaskRequest): Promise<any> {
		const { text, targetLanguage, sourceLanguage } = request.input;

		const payload = {
			model: this.serviceConfig.model,
			messages: [
				{
					role: 'system',
					content: `Translate the following text from ${sourceLanguage || 'auto-detected language'} to ${targetLanguage}. Provide only the translation.`
				},
				{
					role: 'user',
					content: text
				}
			],
			temperature: 0.1
		};

		const response = await this.makeRequest('/chat/completions', 'POST', payload);
		const result = await response.json();

		return {
			translatedText: result.choices[0].message.content,
			sourceLanguage: sourceLanguage || 'auto-detected',
			targetLanguage,
			confidence: 0.95,
			usage: result.usage
		};
	}

	/**
	 * 遠程問答
	 */
	private async answerQuestionRemote(request: AgentTaskRequest): Promise<any> {
		const { question, context } = request.input;

		const systemMessage = context
			? `Answer the question based on the provided context. Context: ${context}`
			: 'Answer the question based on your knowledge.';

		const payload = {
			model: this.serviceConfig.model,
			messages: [
				{
					role: 'system',
					content: systemMessage
				},
				{
					role: 'user',
					content: question
				}
			],
			temperature: 0.3
		};

		const response = await this.makeRequest('/chat/completions', 'POST', payload);
		const result = await response.json();

		return {
			answer: result.choices[0].message.content,
			confidence: 0.9,
			sources: context ? ['provided context'] : ['AI knowledge'],
			usage: result.usage
		};
	}

	/**
	 * 遠程文本分類
	 */
	private async classifyTextRemote(request: AgentTaskRequest): Promise<any> {
		const { text, categories } = request.input;

		const categoryList = categories?.join(', ') || 'general categories';

		const payload = {
			model: this.serviceConfig.model,
			messages: [
				{
					role: 'system',
					content: `Classify the following text into one of these categories: ${categoryList}. Respond with the category name and confidence score.`
				},
				{
					role: 'user',
					content: text
				}
			],
			temperature: 0.1
		};

		const response = await this.makeRequest('/chat/completions', 'POST', payload);
		const result = await response.json();

		return this.parseClassificationResponse(result.choices[0].message.content, categories);
	}

	/**
	 * 遠程信息提取
	 */
	private async extractInformationRemote(request: AgentTaskRequest): Promise<any> {
		const { text, extractionType } = request.input;

		const payload = {
			model: this.serviceConfig.model,
			messages: [
				{
					role: 'system',
					content: `Extract ${extractionType} from the following text. Provide a structured list.`
				},
				{
					role: 'user',
					content: text
				}
			],
			temperature: 0.1
		};

		const response = await this.makeRequest('/chat/completions', 'POST', payload);
		const result = await response.json();

		return {
			extracted: this.parseExtractionResponse(result.choices[0].message.content),
			extractionType,
			confidence: 0.9,
			usage: result.usage
		};
	}

	/**
	 * 解析分析回應
	 */
	private parseAnalysisResponse(content: string): any {
		// 簡化的解析實現
		return {
			sentiment: 'positive',
			topics: ['general'],
			complexity: 'medium',
			readability: 0.8,
			keyPoints: ['AI generated analysis'],
			suggestions: ['Consider reviewing the content'],
			rawResponse: content
		};
	}

	/**
	 * 解析分類回應
	 */
	private parseClassificationResponse(content: string, categories?: string[]): any {
		// 簡化的解析實現
		return {
			category: categories?.[0] || 'general',
			confidence: 0.9,
			allScores: [],
			rawResponse: content
		};
	}

	/**
	 * 解析提取回應
	 */
	private parseExtractionResponse(content: string): string[] {
		// 簡化的解析實現
		return content.split('\n').filter(line => line.trim().length > 0);
	}
}
