import { sveltekit } from '@sveltejs/kit/vite';
import { defineConfig } from 'vitest/config';

export default defineConfig({
	plugins: [sveltekit()],

	// Configure development server
	server: {
		port: 5173,
		host: true,
		fs: {
			// Allow serving files from one level up to the project root
			allow: ['..']
		}
	},

	// Configure build options
	build: {
		target: 'esnext',
		sourcemap: true,
		rollupOptions: {
			output: {
				manualChunks: {
					// Separate vendor chunks for better caching
					vendor: ['svelte', '@sveltejs/kit'],
					editor: ['codemirror', '@codemirror/state', '@codemirror/view'],
					ui: ['lucide-svelte', 'svelte-french-toast'],
					utils: ['marked', 'dompurify', 'fuse.js', 'd3']
				}
			}
		}
	},

	// Configure optimizations
	optimizeDeps: {
		include: [
			'@life-note/storage',
			'codemirror',
			'@codemirror/state',
			'@codemirror/view',
			'@codemirror/commands',
			'@codemirror/autocomplete',
			'@codemirror/search',
			'@codemirror/lang-markdown',
			'@codemirror/theme-one-dark',
			'marked',
			'dompurify',
			'fuse.js',
			'd3',
			'highlight.js'
		]
	},

	// Configure test environment
	test: {
		include: ['src/**/*.{test,spec}.{js,ts}'],
		environment: 'jsdom',
		globals: true,
		setupFiles: ['./src/lib/test/setup.ts']
	},

	// Configure path resolution
	resolve: {
		alias: {
			$lib: './src/lib',
			$components: './src/lib/components',
			$stores: './src/lib/stores',
			$utils: './src/lib/utils',
			$types: './src/lib/types'
		}
	},

	// Configure CSS processing
	css: {
		postcss: './postcss.config.js'
	}
});
