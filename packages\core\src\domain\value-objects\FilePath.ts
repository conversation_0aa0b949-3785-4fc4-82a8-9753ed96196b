import {
  ValueObject,
  BusinessRuleViolationError,
} from "../../shared/Entity.js";

/**
 * 文件路徑值對象
 * 代表有效的文件路徑
 */
export class FilePath extends ValueObject {
  private readonly _value: string;
  private readonly _isAbsolute: boolean;

  constructor(value: string) {
    super();
    this.validateFilePath(value);
    this._value = this.normalizePath(value);
    this._isAbsolute = this.checkIfAbsolute(this._value);
  }

  /**
   * 創建文件路徑值對象
   */
  static create(value: string): FilePath {
    return new FilePath(value);
  }

  /**
   * 從字符串創建文件路徑
   */
  static fromString(value: string): FilePath {
    return new FilePath(value);
  }

  /**
   * 驗證文件路徑格式
   */
  private validateFilePath(value: string): void {
    if (!value || value.trim().length === 0) {
      throw new BusinessRuleViolationError("File path cannot be empty");
    }

    const trimmedValue = value.trim();

    // 長度檢查
    if (trimmedValue.length > 4096) {
      throw new BusinessRuleViolationError(
        "File path is too long (max 4096 characters)",
      );
    }

    // 檢查非法字符（Windows 和 Unix 通用，但允許 Windows 驅動器字母中的冒號）
    const illegalChars = /[<>"|?*\x00-\x1f]/;
    if (illegalChars.test(trimmedValue)) {
      throw new BusinessRuleViolationError(
        "File path contains illegal characters",
      );
    }

    // 檢查冒號的特殊情況（只允許在 Windows 驅動器字母後）
    const colonMatches = trimmedValue.match(/:/g);
    if (colonMatches) {
      // 如果有冒號，檢查是否為有效的 Windows 驅動器格式
      if (colonMatches.length > 1 || !/^[a-zA-Z]:/.test(trimmedValue)) {
        throw new BusinessRuleViolationError(
          "File path contains illegal characters",
        );
      }
    }

    // 檢查保留名稱（Windows）
    const reservedNames = [
      "CON",
      "PRN",
      "AUX",
      "NUL",
      "COM1",
      "COM2",
      "COM3",
      "COM4",
      "COM5",
      "COM6",
      "COM7",
      "COM8",
      "COM9",
      "LPT1",
      "LPT2",
      "LPT3",
      "LPT4",
      "LPT5",
      "LPT6",
      "LPT7",
      "LPT8",
      "LPT9",
    ];

    const pathParts = trimmedValue.split(/[/\\]/);
    for (const part of pathParts) {
      const baseName = part.split(".")[0].toUpperCase();
      if (reservedNames.includes(baseName)) {
        throw new BusinessRuleViolationError(
          `File path contains reserved name: ${part}`,
        );
      }
    }

    // 檢查是否以點結尾（Windows 不允許）
    if (trimmedValue.endsWith(".")) {
      throw new BusinessRuleViolationError("File path cannot end with a dot");
    }

    // 檢查是否包含連續的路徑分隔符
    if (/[/\\]{2,}/.test(trimmedValue)) {
      throw new BusinessRuleViolationError(
        "File path cannot contain consecutive separators",
      );
    }

    // 檢查相對路徑的安全性（防止路徑遍歷攻擊）
    if (trimmedValue.includes("../") || trimmedValue.includes("..\\")) {
      throw new BusinessRuleViolationError(
        "File path cannot contain parent directory references",
      );
    }
  }

  /**
   * 標準化路徑（統一使用正斜杠）
   */
  private normalizePath(value: string): string {
    return value.trim().replace(/\\/g, "/");
  }

  /**
   * 檢查是否為絕對路徑
   */
  private checkIfAbsolute(path: string): boolean {
    // Unix 絕對路徑
    if (path.startsWith("/")) {
      return true;
    }

    // Windows 絕對路徑
    if (/^[a-zA-Z]:\//.test(path)) {
      return true;
    }

    // UNC 路徑
    if (path.startsWith("//")) {
      return true;
    }

    return false;
  }

  /**
   * 獲取文件名（包含擴展名）
   */
  get fileName(): string {
    const parts = this._value.split("/");
    return parts[parts.length - 1];
  }

  /**
   * 獲取文件名（不包含擴展名）
   */
  get baseName(): string {
    const fileName = this.fileName;
    const lastDotIndex = fileName.lastIndexOf(".");
    return lastDotIndex > 0 ? fileName.substring(0, lastDotIndex) : fileName;
  }

  /**
   * 獲取文件擴展名
   */
  get extension(): string {
    const fileName = this.fileName;
    const lastDotIndex = fileName.lastIndexOf(".");
    return lastDotIndex > 0 ? fileName.substring(lastDotIndex + 1) : "";
  }

  /**
   * 獲取目錄路徑
   */
  get directory(): string {
    const parts = this._value.split("/");
    if (parts.length <= 1) {
      return this._isAbsolute ? "/" : ".";
    }
    return parts.slice(0, -1).join("/") || (this._isAbsolute ? "/" : ".");
  }

  /**
   * 獲取路徑深度
   */
  get depth(): number {
    if (this._value === "/" || this._value === ".") {
      return 0;
    }
    return this._value.split("/").filter((part) => part.length > 0).length - 1;
  }

  /**
   * 檢查是否為絕對路徑
   */
  get isAbsolute(): boolean {
    return this._isAbsolute;
  }

  /**
   * 檢查是否為相對路徑
   */
  get isRelative(): boolean {
    return !this._isAbsolute;
  }

  /**
   * 檢查是否為文件（有擴展名）
   */
  isFile(): boolean {
    return this.extension.length > 0;
  }

  /**
   * 檢查是否為目錄（無擴展名）
   */
  isDirectory(): boolean {
    return !this.isFile();
  }

  /**
   * 檢查是否為特定類型的文件
   */
  hasExtension(extension: string): boolean {
    return this.extension.toLowerCase() === extension.toLowerCase();
  }

  /**
   * 檢查是否為圖片文件
   */
  isImageFile(): boolean {
    const imageExtensions = [
      "jpg",
      "jpeg",
      "png",
      "gif",
      "bmp",
      "svg",
      "webp",
      "ico",
    ];
    return imageExtensions.includes(this.extension.toLowerCase());
  }

  /**
   * 檢查是否為文檔文件
   */
  isDocumentFile(): boolean {
    const docExtensions = ["txt", "md", "pdf", "doc", "docx", "rtf", "odt"];
    return docExtensions.includes(this.extension.toLowerCase());
  }

  /**
   * 檢查是否為代碼文件
   */
  isCodeFile(): boolean {
    const codeExtensions = [
      "js",
      "ts",
      "jsx",
      "tsx",
      "vue",
      "svelte",
      "html",
      "css",
      "scss",
      "sass",
      "less",
      "json",
      "xml",
      "yaml",
      "yml",
      "toml",
      "py",
      "java",
      "c",
      "cpp",
      "h",
      "hpp",
      "cs",
      "php",
      "rb",
      "go",
      "rs",
      "swift",
      "kt",
      "scala",
      "clj",
      "hs",
      "ml",
      "fs",
    ];
    return codeExtensions.includes(this.extension.toLowerCase());
  }

  /**
   * 連接路徑
   */
  join(path: string): FilePath {
    if (!path) {
      return this;
    }

    const cleanPath = path.replace(/^\/+/, "");
    const joinedPath = this._value.endsWith("/")
      ? this._value + cleanPath
      : this._value + "/" + cleanPath;

    return new FilePath(joinedPath);
  }

  /**
   * 獲取相對於指定目錄的路徑
   */
  relativeTo(basePath: string): FilePath {
    const base = new FilePath(basePath);

    if (this._isAbsolute !== base._isAbsolute) {
      throw new BusinessRuleViolationError(
        "Cannot create relative path between absolute and relative paths",
      );
    }

    const thisParts = this._value.split("/").filter((part) => part.length > 0);
    const baseParts = base._value.split("/").filter((part) => part.length > 0);

    // 找到共同前綴
    let commonLength = 0;
    while (
      commonLength < thisParts.length &&
      commonLength < baseParts.length &&
      thisParts[commonLength] === baseParts[commonLength]
    ) {
      commonLength++;
    }

    // 構建相對路徑
    const upLevels = baseParts.length - commonLength;
    const downParts = thisParts.slice(commonLength);

    const relativeParts = Array(upLevels).fill("..").concat(downParts);
    const relativePath = relativeParts.join("/") || ".";

    // 創建新的 FilePath 實例，但跳過父目錄引用的驗證
    const result = Object.create(FilePath.prototype);
    result._value = relativePath;
    result._isAbsolute = false;
    return result;
  }

  /**
   * 更改文件擴展名
   */
  changeExtension(newExtension: string): FilePath {
    const newFileName =
      this.baseName +
      (newExtension.startsWith(".") ? newExtension : "." + newExtension);
    const directory = this.directory;
    return new FilePath(
      directory === "." ? newFileName : directory + "/" + newFileName,
    );
  }

  get value(): string {
    return this._value;
  }

  equals(other: ValueObject): boolean {
    if (!(other instanceof FilePath)) {
      return false;
    }
    return this._value === other._value;
  }

  hashCode(): string {
    return this._value;
  }

  toString(): string {
    return this._value;
  }

  toPlainObject(): Record<string, unknown> {
    return {
      value: this._value,
      fileName: this.fileName,
      baseName: this.baseName,
      extension: this.extension,
      directory: this.directory,
      depth: this.depth,
      isAbsolute: this.isAbsolute,
      isFile: this.isFile(),
      isDirectory: this.isDirectory(),
    };
  }

  static fromPlainObject(data: Record<string, unknown>): FilePath {
    if (typeof data.value !== "string") {
      throw new BusinessRuleViolationError("Invalid FilePath data");
    }

    return new FilePath(data.value);
  }
}
