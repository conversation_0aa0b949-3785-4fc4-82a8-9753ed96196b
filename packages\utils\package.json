{"name": "@life-note/utils", "version": "0.1.0", "description": "Life Note Utility Functions", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage"}, "dependencies": {"zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20.19.2", "typescript": "^5.6.3", "vitest": "^2.1.8"}, "files": ["dist"], "keywords": ["utils", "utilities", "helpers"], "author": "Life Note Team", "license": "MIT"}