# 測試和品質控制規範

## 測試和品質控制規範

### 整體測試策略

基於階段2技術規範，建立全面的測試體系：

- **測試金字塔**：70% 單元測試，20% 整合測試，10% E2E 測試
- **測試驅動開發**：先寫測試，後寫實現
- **持續整合**：每次提交都觸發自動化測試
- **品質門檻**：代碼覆蓋率 ≥ 80%，所有測試必須通過

## Context7 搜索的測試最佳實踐

### Vitest 單元測試框架

基於最新的測試框架和方法論：

```typescript
// vitest.config.ts
import { defineConfig } from "vitest/config";
import { resolve } from "path";

export default defineConfig({
  test: {
    globals: true,
    environment: "jsdom",
    setupFiles: ["./src/test/setup.ts"],
    coverage: {
      provider: "v8",
      reporter: ["text", "json", "html"],
      exclude: [
        "node_modules/",
        "src/test/",
        "**/*.d.ts",
        "**/*.config.*",
        "**/dist/**",
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80,
        },
      },
    },
    testTimeout: 10000,
    hookTimeout: 10000,
  },
  resolve: {
    alias: {
      "@": resolve(__dirname, "./src"),
      "@/test": resolve(__dirname, "./src/test"),
    },
  },
});
```

### React Testing Library 配置

```typescript
// src/test/setup.ts
import "@testing-library/jest-dom";
import { cleanup } from "@testing-library/react";
import { afterEach, beforeAll, afterAll, vi } from "vitest";
import { server } from "./mocks/server";

// 每個測試後清理
afterEach(() => {
  cleanup();
  vi.clearAllMocks();
});

// 啟動 MSW 服務器
beforeAll(() => {
  server.listen({ onUnhandledRequest: "error" });
});

// 每個測試後重置處理器
afterEach(() => {
  server.resetHandlers();
});

// 測試完成後關閉服務器
afterAll(() => {
  server.close();
});

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock matchMedia
Object.defineProperty(window, "matchMedia", {
  writable: true,
  value: vi.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});
```

## 測試標準

### 單元測試配置

#### 組件測試範例

```typescript
// src/components/NoteEditor/NoteEditor.test.tsx
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor, userEvent } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

import { NoteEditor } from './NoteEditor';
import { noteService } from '@/services/noteService';
import type { Note } from '@/types/note';

// Mock 服務
vi.mock('@/services/noteService');

// 測試工具函數
function renderWithProviders(ui: React.ReactElement) {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return render(
    <QueryClientProvider client={queryClient}>
      {ui}
    </QueryClientProvider>
  );
}

describe('NoteEditor', () => {
  const mockNote: Note = {
    id: '1',
    title: '測試筆記',
    content: '測試內容',
    tags: ['test'],
    status: 'draft',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('渲染測試', () => {
    it('應該正確渲染初始狀態', () => {
      renderWithProviders(<NoteEditor />);

      expect(screen.getByLabelText('筆記標題')).toBeInTheDocument();
      expect(screen.getByLabelText('筆記內容')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: '儲存筆記' })).toBeInTheDocument();
    });

    it('應該顯示載入中的筆記數據', () => {
      renderWithProviders(<NoteEditor noteId="1" />);

      expect(screen.getByText('載入中...')).toBeInTheDocument();
    });

    it('應該正確顯示筆記內容', async () => {
      vi.mocked(noteService.findById).mockResolvedValue(mockNote);

      renderWithProviders(<NoteEditor noteId="1" />);

      await waitFor(() => {
        expect(screen.getByDisplayValue('測試筆記')).toBeInTheDocument();
        expect(screen.getByDisplayValue('測試內容')).toBeInTheDocument();
      });
    });
  });

  describe('交互測試', () => {
    it('應該允許編輯標題', async () => {
      const user = userEvent.setup();
      renderWithProviders(<NoteEditor />);

      const titleInput = screen.getByLabelText('筆記標題');
      await user.type(titleInput, '新標題');

      expect(titleInput).toHaveValue('新標題');
    });

    it('應該允許編輯內容', async () => {
      const user = userEvent.setup();
      renderWithProviders(<NoteEditor />);

      const contentTextarea = screen.getByLabelText('筆記內容');
      await user.type(contentTextarea, '新內容');

      expect(contentTextarea).toHaveValue('新內容');
    });

    it('應該在點擊儲存時調用保存函數', async () => {
      const mockOnSave = vi.fn();
      const user = userEvent.setup();
      vi.mocked(noteService.save).mockResolvedValue(mockNote);

      renderWithProviders(<NoteEditor onSave={mockOnSave} />);

      // 填寫表單
      await user.type(screen.getByLabelText('筆記標題'), '測試標題');
      await user.type(screen.getByLabelText('筆記內容'), '測試內容');

      // 點擊儲存
      await user.click(screen.getByRole('button', { name: '儲存筆記' }));

      await waitFor(() => {
        expect(noteService.save).toHaveBeenCalledWith({
          title: '測試標題',
          content: '測試內容',
          status: 'draft',
        });
        expect(mockOnSave).toHaveBeenCalledWith(mockNote);
      });
    });
  });

  describe('錯誤處理測試', () => {
    it('應該顯示保存錯誤', async () => {
      const user = userEvent.setup();
      const error = new Error('保存失敗');
      vi.mocked(noteService.save).mockRejectedValue(error);

      renderWithProviders(<NoteEditor />);

      // 填寫表單並嘗試保存
      await user.type(screen.getByLabelText('筆記標題'), '測試標題');
      await user.type(screen.getByLabelText('筆記內容'), '測試內容');
      await user.click(screen.getByRole('button', { name: '儲存筆記' }));

      await waitFor(() => {
        expect(screen.getByRole('alert')).toHaveTextContent('保存失敗');
      });
    });

    it('應該在空標題時禁用保存按鈕', () => {
      renderWithProviders(<NoteEditor />);

      const saveButton = screen.getByRole('button', { name: '儲存筆記' });
      expect(saveButton).toBeDisabled();
    });
  });

  describe('可訪問性測試', () => {
    it('應該有正確的 ARIA 標籤', () => {
      renderWithProviders(<NoteEditor />);

      expect(screen.getByRole('form')).toBeInTheDocument();
      expect(screen.getByLabelText('筆記標題')).toHaveAttribute('aria-required', 'true');
      expect(screen.getByLabelText('筆記內容')).toHaveAttribute('aria-required', 'true');
    });

    it('應該支援鍵盤導航', async () => {
      const user = userEvent.setup();
      renderWithProviders(<NoteEditor />);

      // Tab 導航
      await user.tab();
      expect(screen.getByLabelText('筆記標題')).toHaveFocus();

      await user.tab();
      expect(screen.getByLabelText('筆記內容')).toHaveFocus();

      await user.tab();
      expect(screen.getByRole('button', { name: '儲存筆記' })).toHaveFocus();
    });
  });
});
```

#### 服務層測試範例

```typescript
// src/services/noteService.test.ts
import { describe, it, expect, vi, beforeEach } from "vitest";
import { noteService } from "./noteService";
import { noteRepository } from "@/repositories/noteRepository";
import { ValidationError, NotFoundError } from "@/errors";

// Mock 依賴
vi.mock("@/repositories/noteRepository");

describe("NoteService", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("create", () => {
    it("應該成功創建筆記", async () => {
      const noteData = {
        title: "測試筆記",
        content: "測試內容",
        tags: ["test"],
      };

      const expectedNote = {
        id: "1",
        ...noteData,
        status: "draft",
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      vi.mocked(noteRepository.create).mockResolvedValue(expectedNote);

      const result = await noteService.create(noteData);

      expect(noteRepository.create).toHaveBeenCalledWith({
        ...noteData,
        status: "draft",
      });
      expect(result).toEqual(expectedNote);
    });

    it("應該驗證必填字段", async () => {
      await expect(
        noteService.create({ title: "", content: "test" }),
      ).rejects.toThrow(ValidationError);

      await expect(
        noteService.create({ title: "test", content: "" }),
      ).rejects.toThrow(ValidationError);
    });

    it("應該限制標題長度", async () => {
      const longTitle = "a".repeat(201);

      await expect(
        noteService.create({ title: longTitle, content: "test" }),
      ).rejects.toThrow(ValidationError);
    });
  });

  describe("findById", () => {
    it("應該返回存在的筆記", async () => {
      const mockNote = {
        id: "1",
        title: "測試筆記",
        content: "測試內容",
        status: "draft",
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      vi.mocked(noteRepository.findById).mockResolvedValue(mockNote);

      const result = await noteService.findById("1");

      expect(noteRepository.findById).toHaveBeenCalledWith("1");
      expect(result).toEqual(mockNote);
    });

    it("應該在筆記不存在時拋出錯誤", async () => {
      vi.mocked(noteRepository.findById).mockResolvedValue(null);

      await expect(noteService.findById("nonexistent")).rejects.toThrow(
        NotFoundError,
      );
    });
  });

  describe("update", () => {
    it("應該成功更新筆記", async () => {
      const existingNote = {
        id: "1",
        title: "原標題",
        content: "原內容",
        status: "draft",
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const updateData = {
        title: "新標題",
        content: "新內容",
      };

      const updatedNote = {
        ...existingNote,
        ...updateData,
        updatedAt: new Date(),
      };

      vi.mocked(noteRepository.findById).mockResolvedValue(existingNote);
      vi.mocked(noteRepository.update).mockResolvedValue(updatedNote);

      const result = await noteService.update("1", updateData);

      expect(noteRepository.update).toHaveBeenCalledWith("1", updateData);
      expect(result).toEqual(updatedNote);
    });
  });
});
```

### 整合測試策略

#### API 整合測試

```typescript
// src/test/integration/api.test.ts
import { describe, it, expect, beforeAll, afterAll, beforeEach } from "vitest";
import request from "supertest";
import { app } from "@/app";
import { database } from "@/database";

describe("API Integration Tests", () => {
  beforeAll(async () => {
    await database.connect();
    await database.migrate();
  });

  afterAll(async () => {
    await database.disconnect();
  });

  beforeEach(async () => {
    await database.clear();
  });

  describe("POST /api/notes", () => {
    it("應該創建新筆記", async () => {
      const noteData = {
        title: "測試筆記",
        content: "測試內容",
        tags: ["test"],
      };

      const response = await request(app)
        .post("/api/notes")
        .send(noteData)
        .expect(201);

      expect(response.body).toMatchObject({
        id: expect.any(String),
        title: noteData.title,
        content: noteData.content,
        tags: noteData.tags,
        status: "draft",
        createdAt: expect.any(String),
        updatedAt: expect.any(String),
      });
    });

    it("應該驗證請求數據", async () => {
      const response = await request(app)
        .post("/api/notes")
        .send({ title: "", content: "test" })
        .expect(400);

      expect(response.body).toMatchObject({
        error: "Validation Error",
        details: expect.arrayContaining([
          expect.objectContaining({
            field: "title",
            message: expect.any(String),
          }),
        ]),
      });
    });
  });

  describe("GET /api/notes/:id", () => {
    it("應該返回存在的筆記", async () => {
      // 先創建筆記
      const createResponse = await request(app).post("/api/notes").send({
        title: "測試筆記",
        content: "測試內容",
      });

      const noteId = createResponse.body.id;

      // 獲取筆記
      const response = await request(app)
        .get(`/api/notes/${noteId}`)
        .expect(200);

      expect(response.body).toMatchObject({
        id: noteId,
        title: "測試筆記",
        content: "測試內容",
      });
    });

    it("應該在筆記不存在時返回 404", async () => {
      await request(app).get("/api/notes/nonexistent").expect(404);
    });
  });
});
```

#### 資料庫整合測試

```typescript
// src/test/integration/database.test.ts
import { describe, it, expect, beforeAll, afterAll, beforeEach } from "vitest";
import { database } from "@/database";
import { noteRepository } from "@/repositories/noteRepository";

describe("Database Integration Tests", () => {
  beforeAll(async () => {
    await database.connect();
    await database.migrate();
  });

  afterAll(async () => {
    await database.disconnect();
  });

  beforeEach(async () => {
    await database.clear();
  });

  describe("Note Repository", () => {
    it("應該創建和檢索筆記", async () => {
      const noteData = {
        title: "測試筆記",
        content: "測試內容",
        tags: ["test"],
        status: "draft" as const,
      };

      const createdNote = await noteRepository.create(noteData);
      expect(createdNote.id).toBeDefined();

      const retrievedNote = await noteRepository.findById(createdNote.id);
      expect(retrievedNote).toMatchObject(noteData);
    });

    it("應該支援複雜查詢", async () => {
      // 創建多個筆記
      await noteRepository.create({
        title: "筆記1",
        content: "內容1",
        tags: ["tag1", "common"],
        status: "draft",
      });

      await noteRepository.create({
        title: "筆記2",
        content: "內容2",
        tags: ["tag2", "common"],
        status: "published",
      });

      // 按標籤搜索
      const notesByTag = await noteRepository.findByTags(["common"]);
      expect(notesByTag).toHaveLength(2);

      // 按狀態搜索
      const draftNotes = await noteRepository.findByStatus("draft");
      expect(draftNotes).toHaveLength(1);
      expect(draftNotes[0].title).toBe("筆記1");
    });
  });
});
```

### E2E 測試規範

#### Playwright E2E 測試

```typescript
// playwright.config.ts
import { defineConfig, devices } from "@playwright/test";

export default defineConfig({
  testDir: "./e2e",
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: "html",
  use: {
    baseURL: "http://localhost:3000",
    trace: "on-first-retry",
    screenshot: "only-on-failure",
  },
  projects: [
    {
      name: "chromium",
      use: { ...devices["Desktop Chrome"] },
    },
    {
      name: "firefox",
      use: { ...devices["Desktop Firefox"] },
    },
    {
      name: "webkit",
      use: { ...devices["Desktop Safari"] },
    },
    {
      name: "Mobile Chrome",
      use: { ...devices["Pixel 5"] },
    },
  ],
  webServer: {
    command: "npm run dev",
    url: "http://localhost:3000",
    reuseExistingServer: !process.env.CI,
  },
});
```

```typescript
// e2e/note-management.spec.ts
import { test, expect } from "@playwright/test";

test.describe("筆記管理", () => {
  test.beforeEach(async ({ page }) => {
    await page.goto("/");
  });

  test("應該能創建新筆記", async ({ page }) => {
    // 點擊創建筆記按鈕
    await page.click('[data-testid="create-note-button"]');

    // 填寫筆記表單
    await page.fill('[data-testid="note-title-input"]', "我的第一個筆記");
    await page.fill('[data-testid="note-content-textarea"]', "這是筆記內容");

    // 添加標籤
    await page.fill('[data-testid="note-tags-input"]', "test");
    await page.press('[data-testid="note-tags-input"]', "Enter");

    // 保存筆記
    await page.click('[data-testid="save-note-button"]');

    // 驗證筆記已創建
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="note-title"]')).toHaveText(
      "我的第一個筆記",
    );
  });

  test("應該能搜索筆記", async ({ page }) => {
    // 先創建一些測試筆記
    await createTestNote(page, "筆記1", "關於 React 的內容");
    await createTestNote(page, "筆記2", "關於 Vue 的內容");

    // 使用搜索功能
    await page.fill('[data-testid="search-input"]', "React");
    await page.press('[data-testid="search-input"]', "Enter");

    // 驗證搜索結果
    await expect(page.locator('[data-testid="search-results"]')).toBeVisible();
    await expect(page.locator('[data-testid="note-item"]')).toHaveCount(1);
    await expect(
      page.locator('[data-testid="note-item"]').first(),
    ).toContainText("筆記1");
  });

  test("應該能編輯現有筆記", async ({ page }) => {
    // 創建測試筆記
    await createTestNote(page, "原始標題", "原始內容");

    // 點擊編輯按鈕
    await page.click('[data-testid="edit-note-button"]');

    // 修改內容
    await page.fill('[data-testid="note-title-input"]', "修改後的標題");
    await page.fill('[data-testid="note-content-textarea"]', "修改後的內容");

    // 保存修改
    await page.click('[data-testid="save-note-button"]');

    // 驗證修改已保存
    await expect(page.locator('[data-testid="note-title"]')).toHaveText(
      "修改後的標題",
    );
    await expect(page.locator('[data-testid="note-content"]')).toContainText(
      "修改後的內容",
    );
  });

  test("應該能刪除筆記", async ({ page }) => {
    // 創建測試筆記
    await createTestNote(page, "要刪除的筆記", "這個筆記將被刪除");

    // 點擊刪除按鈕
    await page.click('[data-testid="delete-note-button"]');

    // 確認刪除
    await page.click('[data-testid="confirm-delete-button"]');

    // 驗證筆記已刪除
    await expect(page.locator('[data-testid="note-item"]')).toHaveCount(0);
  });
});

async function createTestNote(page: any, title: string, content: string) {
  await page.click('[data-testid="create-note-button"]');
  await page.fill('[data-testid="note-title-input"]', title);
  await page.fill('[data-testid="note-content-textarea"]', content);
  await page.click('[data-testid="save-note-button"]');
  await page.waitForSelector('[data-testid="success-message"]');
}
```

### 程式碼覆蓋率要求

#### 覆蓋率配置

```typescript
// src/test/coverage.config.ts
export const coverageConfig = {
  // 全局覆蓋率要求
  global: {
    branches: 80,
    functions: 80,
    lines: 80,
    statements: 80,
  },

  // 特定文件類型的要求
  fileTypes: {
    // 核心業務邏輯要求更高覆蓋率
    services: {
      branches: 90,
      functions: 90,
      lines: 90,
      statements: 90,
    },

    // 工具函數要求完全覆蓋
    utils: {
      branches: 95,
      functions: 95,
      lines: 95,
      statements: 95,
    },

    // UI 組件允許較低覆蓋率
    components: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70,
    },
  },

  // 排除的文件
  exclude: [
    "src/test/**",
    "src/**/*.d.ts",
    "src/**/*.config.*",
    "src/main.tsx",
    "src/vite-env.d.ts",
  ],
};
```

#### 覆蓋率報告腳本

```json
// package.json scripts
{
  "scripts": {
    "test": "vitest",
    "test:ui": "vitest --ui",
    "test:run": "vitest run",
    "test:coverage": "vitest run --coverage",
    "test:coverage:open": "vitest run --coverage && open coverage/index.html",
    "test:e2e": "playwright test",
    "test:e2e:ui": "playwright test --ui",
    "test:all": "npm run test:run && npm run test:e2e"
  }
}
```

#### 品質門檻檢查

```typescript
// scripts/check-quality.ts
import { readFileSync } from "fs";
import { join } from "path";

interface CoverageReport {
  total: {
    lines: { pct: number };
    functions: { pct: number };
    statements: { pct: number };
    branches: { pct: number };
  };
}

function checkCoverage(): void {
  try {
    const coverageFile = join(process.cwd(), "coverage/coverage-summary.json");
    const coverage: CoverageReport = JSON.parse(
      readFileSync(coverageFile, "utf8"),
    );

    const { lines, functions, statements, branches } = coverage.total;
    const thresholds = {
      lines: 80,
      functions: 80,
      statements: 80,
      branches: 80,
    };

    const failures: string[] = [];

    if (lines.pct < thresholds.lines) {
      failures.push(`Lines coverage ${lines.pct}% < ${thresholds.lines}%`);
    }

    if (functions.pct < thresholds.functions) {
      failures.push(
        `Functions coverage ${functions.pct}% < ${thresholds.functions}%`,
      );
    }

    if (statements.pct < thresholds.statements) {
      failures.push(
        `Statements coverage ${statements.pct}% < ${thresholds.statements}%`,
      );
    }

    if (branches.pct < thresholds.branches) {
      failures.push(
        `Branches coverage ${branches.pct}% < ${thresholds.branches}%`,
      );
    }

    if (failures.length > 0) {
      console.error("❌ Coverage thresholds not met:");
      failures.forEach((failure) => console.error(`  ${failure}`));
      process.exit(1);
    }

    console.log("✅ All coverage thresholds met!");
  } catch (error) {
    console.error("❌ Failed to check coverage:", error);
    process.exit(1);
  }
}

checkCoverage();
```

這個測試和品質控制規範確保了代碼的可靠性、可維護性和高品質標準。
