<script lang="ts">
	import { onMount } from 'svelte';
	import { marked } from 'marked';
	import DOMPurify from 'dompurify';
	import hljs from 'highlight.js';

	// Props
	export let content = '';
	export let className = '';

	// State
	let previewContainer: HTMLDivElement;
	let renderedHtml = '';

	// Configure marked
	const configureMarked = () => {
		marked.setOptions({
			breaks: true,
			gfm: true
		});

		// Custom renderer for better styling
		const renderer = new marked.Renderer();

		// Custom heading renderer with anchor links
		renderer.heading = (text: string, level: number) => {
			const escapedText = text.toLowerCase().replace(/[^\w]+/g, '-');
			return `
        <h${level} id="${escapedText}" class="heading-${level}">
          <a href="#${escapedText}" class="heading-anchor">#</a>
          ${text}
        </h${level}>
      `;
		};

		// Custom code block renderer
		renderer.code = (code: string, language?: string) => {
			let highlighted = code;

			if (language && hljs.getLanguage(language)) {
				try {
					highlighted = hljs.highlight(code, { language }).value;
				} catch (err) {
					console.warn('Highlight.js error:', err);
					highlighted = hljs.highlightAuto(code).value;
				}
			} else {
				highlighted = hljs.highlightAuto(code).value;
			}

			const validLanguage = language || 'plaintext';

			return `
        <div class="code-block">
          <div class="code-header">
            <span class="code-language">${validLanguage}</span>
            <button class="copy-button" onclick="copyToClipboard(this)" data-code="${encodeURIComponent(code)}">
              複製
            </button>
          </div>
          <pre><code class="hljs language-${validLanguage}">${highlighted}</code></pre>
        </div>
      `;
		};

		// Custom table renderer
		renderer.table = (header: string, body: string) => {
			return `
        <div class="table-wrapper">
          <table class="markdown-table">
            <thead>${header}</thead>
            <tbody>${body}</tbody>
          </table>
        </div>
      `;
		};

		// Custom link renderer with external link handling
		renderer.link = (href: string | null, title: string | null, text: string) => {
			const isExternal = href?.startsWith('http') || href?.startsWith('//');
			const target = isExternal ? ' target="_blank" rel="noopener noreferrer"' : '';
			const titleAttr = title ? ` title="${title}"` : '';

			return `<a href="${href}"${titleAttr}${target} class="markdown-link ${isExternal ? 'external-link' : 'internal-link'}">${text}</a>`;
		};

		// Custom image renderer with lazy loading
		renderer.image = (href: string | null, title: string | null, text: string) => {
			const titleAttr = title ? ` title="${title}"` : '';
			return `
        <div class="image-wrapper">
          <img src="${href}" alt="${text}"${titleAttr} loading="lazy" class="markdown-image" />
          ${title ? `<figcaption class="image-caption">${title}</figcaption>` : ''}
        </div>
      `;
		};

		// Custom blockquote renderer
		renderer.blockquote = (quote: string) => {
			return `<blockquote class="markdown-blockquote">${quote}</blockquote>`;
		};

		marked.use({ renderer });
	};

	// Render markdown to HTML
	const renderMarkdown = async (markdown: string) => {
		if (!markdown.trim()) {
			renderedHtml = '<div class="empty-preview">開始寫作以查看預覽...</div>';
			return;
		}

		try {
			const rawHtml = await marked.parse(markdown);
			renderedHtml = DOMPurify.sanitize(rawHtml, {
				ALLOWED_TAGS: [
					'h1',
					'h2',
					'h3',
					'h4',
					'h5',
					'h6',
					'p',
					'br',
					'strong',
					'em',
					'u',
					's',
					'del',
					'a',
					'img',
					'figure',
					'figcaption',
					'ul',
					'ol',
					'li',
					'blockquote',
					'pre',
					'code',
					'table',
					'thead',
					'tbody',
					'tr',
					'th',
					'td',
					'div',
					'span',
					'hr',
					'button'
				],
				ALLOWED_ATTR: [
					'href',
					'title',
					'alt',
					'src',
					'loading',
					'id',
					'class',
					'target',
					'rel',
					'onclick',
					'data-code'
				]
			});
		} catch (error) {
			console.error('Markdown rendering error:', error);
			renderedHtml = '<div class="error-preview">預覽渲染錯誤</div>';
		}
	};

	// Copy to clipboard function (global for onclick handlers)
	const setupCopyFunction = () => {
		if (typeof window !== 'undefined') {
			(window as any).copyToClipboard = async (button: HTMLButtonElement) => {
				const code = decodeURIComponent(button.dataset.code || '');
				try {
					await navigator.clipboard.writeText(code);
					button.textContent = '已複製!';
					setTimeout(() => {
						button.textContent = '複製';
					}, 2000);
				} catch (err) {
					console.error('Failed to copy:', err);
					button.textContent = '複製失敗';
					setTimeout(() => {
						button.textContent = '複製';
					}, 2000);
				}
			};
		}
	};

	// Initialize
	onMount(() => {
		configureMarked();
		setupCopyFunction();
	});

	// Reactive rendering
	$: renderMarkdown(content);

	// Scroll to top when content changes
	$: if (previewContainer && content) {
		previewContainer.scrollTop = 0;
	}
</script>

<div bind:this={previewContainer} class="markdown-preview {className}">
	{@html renderedHtml}
</div>

<style>
	.markdown-preview {
		height: 100%;
		overflow-y: auto;
		padding: 1rem;
		background: hsl(var(--background));
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
		line-height: 1.6;
		color: hsl(var(--foreground));
	}

	/* Typography */
	:global(.markdown-preview h1),
	:global(.markdown-preview h2),
	:global(.markdown-preview h3),
	:global(.markdown-preview h4),
	:global(.markdown-preview h5),
	:global(.markdown-preview h6) {
		margin-top: 1.5em;
		margin-bottom: 0.5em;
		font-weight: 600;
		line-height: 1.25;
		position: relative;
	}

	:global(.markdown-preview h1) {
		font-size: 2em;
		border-bottom: 1px solid hsl(var(--border));
		padding-bottom: 0.3em;
	}
	:global(.markdown-preview h2) {
		font-size: 1.5em;
		border-bottom: 1px solid hsl(var(--border));
		padding-bottom: 0.3em;
	}
	:global(.markdown-preview h3) {
		font-size: 1.25em;
	}
	:global(.markdown-preview h4) {
		font-size: 1em;
	}
	:global(.markdown-preview h5) {
		font-size: 0.875em;
	}
	:global(.markdown-preview h6) {
		font-size: 0.85em;
		color: hsl(var(--muted-foreground));
	}

	/* Heading anchors */
	:global(.markdown-preview .heading-anchor) {
		position: absolute;
		left: -1.5em;
		opacity: 0;
		transition: opacity 0.2s;
		text-decoration: none;
		color: hsl(var(--muted-foreground));
	}

	:global(.markdown-preview h1:hover .heading-anchor),
	:global(.markdown-preview h2:hover .heading-anchor),
	:global(.markdown-preview h3:hover .heading-anchor),
	:global(.markdown-preview h4:hover .heading-anchor),
	:global(.markdown-preview h5:hover .heading-anchor),
	:global(.markdown-preview h6:hover .heading-anchor) {
		opacity: 1;
	}

	/* Paragraphs */
	:global(.markdown-preview p) {
		margin-bottom: 1em;
	}

	/* Links */
	:global(.markdown-preview .markdown-link) {
		color: hsl(var(--primary));
		text-decoration: none;
		border-bottom: 1px solid transparent;
		transition: border-color 0.2s;
	}

	:global(.markdown-preview .markdown-link:hover) {
		border-bottom-color: hsl(var(--primary));
	}

	:global(.markdown-preview .external-link::after) {
		content: '↗';
		font-size: 0.8em;
		margin-left: 0.2em;
		opacity: 0.7;
	}

	/* Code */
	:global(.markdown-preview code) {
		background: hsl(var(--muted));
		padding: 0.2em 0.4em;
		border-radius: 3px;
		font-size: 0.9em;
		font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
	}

	:global(.markdown-preview .code-block) {
		margin: 1em 0;
		border-radius: 6px;
		overflow: hidden;
		border: 1px solid hsl(var(--border));
	}

	:global(.markdown-preview .code-header) {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0.5em 1em;
		background: hsl(var(--muted));
		border-bottom: 1px solid hsl(var(--border));
		font-size: 0.8em;
	}

	:global(.markdown-preview .code-language) {
		font-weight: 500;
		color: hsl(var(--muted-foreground));
	}

	:global(.markdown-preview .copy-button) {
		background: hsl(var(--background));
		border: 1px solid hsl(var(--border));
		border-radius: 4px;
		padding: 0.25em 0.5em;
		font-size: 0.8em;
		cursor: pointer;
		transition: background-color 0.2s;
	}

	:global(.markdown-preview .copy-button:hover) {
		background: hsl(var(--accent));
	}

	:global(.markdown-preview pre) {
		margin: 0;
		padding: 1em;
		overflow-x: auto;
		background: hsl(var(--background));
		font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
		font-size: 0.9em;
		line-height: 1.4;
	}

	/* Lists */
	:global(.markdown-preview ul),
	:global(.markdown-preview ol) {
		margin-bottom: 1em;
		padding-left: 2em;
	}

	:global(.markdown-preview li) {
		margin-bottom: 0.25em;
	}

	/* Blockquotes */
	:global(.markdown-preview .markdown-blockquote) {
		margin: 1em 0;
		padding: 0 1em;
		border-left: 4px solid hsl(var(--primary));
		background: hsl(var(--muted) / 0.3);
		border-radius: 0 4px 4px 0;
	}

	/* Tables */
	:global(.markdown-preview .table-wrapper) {
		overflow-x: auto;
		margin: 1em 0;
	}

	:global(.markdown-preview .markdown-table) {
		width: 100%;
		border-collapse: collapse;
		border: 1px solid hsl(var(--border));
		border-radius: 6px;
		overflow: hidden;
	}

	:global(.markdown-preview .markdown-table th),
	:global(.markdown-preview .markdown-table td) {
		padding: 0.75em;
		text-align: left;
		border-bottom: 1px solid hsl(var(--border));
	}

	:global(.markdown-preview .markdown-table th) {
		background: hsl(var(--muted));
		font-weight: 600;
	}

	:global(.markdown-preview .markdown-table tr:last-child td) {
		border-bottom: none;
	}

	/* Images */
	:global(.markdown-preview .image-wrapper) {
		margin: 1em 0;
		text-align: center;
	}

	:global(.markdown-preview .markdown-image) {
		max-width: 100%;
		height: auto;
		border-radius: 6px;
		box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
	}

	:global(.markdown-preview .image-caption) {
		margin-top: 0.5em;
		font-size: 0.9em;
		color: hsl(var(--muted-foreground));
		font-style: italic;
	}

	/* Horizontal rule */
	:global(.markdown-preview hr) {
		margin: 2em 0;
		border: none;
		border-top: 1px solid hsl(var(--border));
	}

	/* Empty state */
	:global(.markdown-preview .empty-preview) {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 200px;
		color: hsl(var(--muted-foreground));
		font-style: italic;
	}

	/* Error state */
	:global(.markdown-preview .error-preview) {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 200px;
		color: hsl(var(--destructive));
		font-style: italic;
	}

	/* Scrollbar */
	.markdown-preview::-webkit-scrollbar {
		width: 8px;
	}

	.markdown-preview::-webkit-scrollbar-track {
		background: hsl(var(--muted));
	}

	.markdown-preview::-webkit-scrollbar-thumb {
		background: hsl(var(--muted-foreground) / 0.3);
		border-radius: 4px;
	}

	.markdown-preview::-webkit-scrollbar-thumb:hover {
		background: hsl(var(--muted-foreground) / 0.5);
	}
</style>
