{"$schema": "https://turbo.build/schema.json", "tasks": {"build": {"outputs": ["dist/**", ".next/**", "!.next/cache/**"]}, "lint": {"outputs": []}, "lint:fix": {"outputs": []}, "format": {"outputs": []}, "test": {"outputs": ["coverage/**"]}, "test:watch": {"cache": false}, "test:coverage": {"outputs": ["coverage/**"]}, "typecheck": {"outputs": []}, "db:generate": {"outputs": []}, "db:push": {"outputs": []}, "db:migrate": {"outputs": []}, "db:reset": {"outputs": []}, "db:seed": {"outputs": []}, "db:studio": {"cache": false}, "dev": {"cache": false}, "clean": {"cache": false}}}