import type { Note, NoteStatus, NotePriority } from "@life-note/core";
import type { NoteId, UserId, Tag } from "@life-note/core";
import type {
  ISearchableRepository,
  PaginatedResult,
  PaginationParams,
} from "./IRepository.js";

/**
 * 筆記查詢條件
 */
export interface NoteQueryCondition {
  authorId?: string;
  workspaceId?: string;
  status?: NoteStatus;
  priority?: NotePriority;
  category?: string;
  tags?: string[];
  createdAfter?: Date;
  createdBefore?: Date;
  updatedAfter?: Date;
  updatedBefore?: Date;
}

/**
 * 筆記統計信息
 */
export interface NoteStatistics {
  total: number;
  byStatus: Record<NoteStatus, number>;
  byPriority: Record<NotePriority, number>;
  byCategory: Record<string, number>;
  byAuthor: Record<string, number>;
  byWorkspace: Record<string, number>;
  totalWords: number;
  averageWordsPerNote: number;
  createdThisWeek: number;
  createdThisMonth: number;
  updatedThisWeek: number;
  updatedThisMonth: number;
}

/**
 * 筆記存儲庫接口
 */
export interface INoteRepository extends ISearchableRepository<Note, NoteId> {
  /**
   * 根據作者 ID 查找筆記
   */
  findByAuthorId(
    authorId: UserId,
    params?: PaginationParams,
  ): Promise<PaginatedResult<Note>>;

  /**
   * 根據工作空間 ID 查找筆記
   */
  findByWorkspaceId(
    workspaceId: string,
    params?: PaginationParams,
  ): Promise<PaginatedResult<Note>>;

  /**
   * 根據狀態查找筆記
   */
  findByStatus(
    status: NoteStatus,
    params?: PaginationParams,
  ): Promise<PaginatedResult<Note>>;

  /**
   * 根據分類查找筆記
   */
  findByCategory(
    category: string,
    params?: PaginationParams,
  ): Promise<PaginatedResult<Note>>;

  /**
   * 根據標籤查找筆記
   */
  findByTags(
    tags: Tag[],
    params?: PaginationParams,
  ): Promise<PaginatedResult<Note>>;

  /**
   * 根據複合條件查找筆記
   */
  findByCondition(
    condition: NoteQueryCondition,
    params?: PaginationParams,
  ): Promise<PaginatedResult<Note>>;

  /**
   * 查找最近更新的筆記
   */
  findRecentlyUpdated(limit?: number): Promise<Note[]>;

  /**
   * 查找最近創建的筆記
   */
  findRecentlyCreated(limit?: number): Promise<Note[]>;

  /**
   * 查找用戶的草稿筆記
   */
  findDraftsByAuthor(
    authorId: UserId,
    params?: PaginationParams,
  ): Promise<PaginatedResult<Note>>;

  /**
   * 查找用戶的已發布筆記
   */
  findPublishedByAuthor(
    authorId: UserId,
    params?: PaginationParams,
  ): Promise<PaginatedResult<Note>>;

  /**
   * 查找相關筆記（基於標籤和分類的相似性）
   */
  findRelatedNotes(noteId: NoteId, limit?: number): Promise<Note[]>;

  /**
   * 獲取筆記統計信息
   */
  getStatistics(): Promise<NoteStatistics>;

  /**
   * 獲取用戶的筆記統計信息
   */
  getUserStatistics(authorId: UserId): Promise<NoteStatistics>;

  /**
   * 獲取工作空間的筆記統計信息
   */
  getWorkspaceStatistics(workspaceId: string): Promise<NoteStatistics>;

  /**
   * 批量更新筆記狀態
   */
  batchUpdateStatus(noteIds: NoteId[], status: NoteStatus): Promise<void>;

  /**
   * 批量刪除筆記
   */
  batchDelete(noteIds: NoteId[]): Promise<void>;

  /**
   * 檢查筆記標題是否重複
   */
  isTitleDuplicate(
    title: string,
    authorId: UserId,
    excludeId?: NoteId,
  ): Promise<boolean>;

  /**
   * 獲取所有使用的分類
   */
  getAllCategories(): Promise<string[]>;

  /**
   * 獲取所有使用的標籤
   */
  getAllTags(): Promise<Tag[]>;

  /**
   * 獲取用戶使用的分類
   */
  getUserCategories(authorId: UserId): Promise<string[]>;

  /**
   * 獲取用戶使用的標籤
   */
  getUserTags(authorId: UserId): Promise<Tag[]>;

  /**
   * 獲取工作空間使用的分類
   */
  getWorkspaceCategories(workspaceId: string): Promise<string[]>;

  /**
   * 獲取工作空間使用的標籤
   */
  getWorkspaceTags(workspaceId: string): Promise<Tag[]>;

  /**
   * 更新筆記的搜索索引
   */
  updateSearchIndex(noteId: NoteId): Promise<void>;

  /**
   * 重建所有筆記的搜索索引
   */
  rebuildSearchIndex(): Promise<void>;
}
