# 技術棧詳細配置

## 前端技術棧

### 核心框架

- **React 18+**：主要UI框架，支援Concurrent Features
- **TypeScript 5.0+**：類型安全開發，提升代碼品質
- **Vite 4.0+**：快速構建工具，支援HMR和ESM

### UI組件庫

- **AG-UI核心套件**：企業級組件庫
  - 表格組件：ag-Grid Professional
  - 圖表組件：ag-Charts Community
  - 表單組件：自定義React Hook Form整合
- **Tailwind CSS 3.0+**：原子化CSS框架
- **Headless UI**：無樣式可訪問性組件

### 狀態管理

- **Zustand**：輕量級狀態管理，支援持久化
- **React Query (TanStack Query)**：服務端狀態管理
- **Jotai**：原子化狀態管理，處理複雜依賴

### 路由與導航

- **React Router v6**：聲明式路由
- **React Router DOM**：瀏覽器路由支援

## 後端技術棧

### 本地Agent服務

- **Node.js 18+ LTS**：主要運行環境
- **Fastify 4.0+**：高性能Web框架
- **TypeScript**：類型安全的後端開發

### Python AI服務

- **Python 3.11+**：AI處理專用環境
- **FastAPI**：現代異步Web框架
- **Pydantic v2**：數據驗證和序列化
- **asyncio**：異步處理支援

### 資料存儲

- **SQLite 3.40+**：本地關係型資料庫
- **better-sqlite3**：Node.js SQLite驅動
- **Prisma**：類型安全的ORM
- **LevelDB**：高性能鍵值存儲（快取）

### 檔案處理

- **unified.js**：Markdown處理生態系統
- **remark**：Markdown解析器
- **rehype**：HTML處理器
- **gray-matter**：YAML frontmatter解析

## AI與機器學習

### AI模型整合

- **Google Gemini 2.5 Flash**：主要LLM服務
- **OpenAI GPT-4**：備用LLM服務
- **Anthropic Claude**：特定任務LLM

### MCP (Model Context Protocol)

- **@modelcontextprotocol/sdk**：MCP標準實現
- **自定義MCP Server**：本地知識庫整合
- **MCP Client**：多模型統一介面

### 本地AI能力

- **Transformers.js**：瀏覽器端AI推理
- **ONNX Runtime Web**：模型推理引擎
- **sentence-transformers**：文本嵌入模型

## 跨平台與打包

### 桌面應用

- **Tauri 1.5+**：Rust後端 + Web前端
- **tauri-plugin-store**：持久化存儲
- **tauri-plugin-fs-extra**：檔案系統操作
- **tauri-plugin-shell**：系統命令執行

### 移動端支援

- **Capacitor 5.0+**：跨平台移動應用框架
- **Ionic React**：移動端UI組件
- **Capacitor Plugins**：原生功能訪問

## 開發工具鏈

### 代碼品質

- **ESLint 8.0+**：JavaScript/TypeScript代碼檢查
- **Prettier 3.0+**：代碼格式化
- **husky**：Git hooks管理
- **lint-staged**：暫存區代碼檢查

### 測試框架

- **Vitest**：單元測試框架
- **React Testing Library**：React組件測試
- **Playwright**：端對端測試
- **MSW (Mock Service Worker)**：API模擬

### 構建與部署

- **Docker**：容器化部署
- **GitHub Actions**：CI/CD流水線
- **Electron Builder**：桌面應用打包
- **Vercel**：前端部署平台

## 通訊與協議

### 網路通訊

- **Axios**：HTTP客戶端
- **Socket.io**：實時雙向通訊
- **Server-Sent Events**：服務端推送
- **WebRTC**：點對點通訊（未來功能）

### 數據格式

- **JSON-RPC 2.0**：標準化API協議
- **Protocol Buffers**：高效二進制序列化
- **MessagePack**：緊湊型序列化格式

### 安全與認證

- **OAuth 2.0 / OpenID Connect**：身份認證
- **JWT (jsonwebtoken)**：無狀態認證
- **bcrypt**：密碼哈希
- **crypto-js**：客戶端加密

## 監控與日誌

### 日誌系統

- **Winston**：Node.js日誌庫
- **Pino**：高性能日誌庫
- **React Error Boundary**：前端錯誤捕獲

### 性能監控

- **Web Vitals**：前端性能指標
- **Node.js Performance Hooks**：後端性能監控
- **Sentry**：錯誤追蹤和性能監控

## 版本控制與依賴

### 包管理

- **pnpm**：高效的包管理器
- **npm workspaces**：monorepo支援
- **Renovate**：自動依賴更新

### 版本控制

- **Git**：源代碼版本控制
- **Conventional Commits**：標準化提交信息
- **Semantic Release**：自動化版本發布

這個技術棧配置確保了系統的高性能、可擴展性和維護性，同時支援混合架構的複雜需求。
