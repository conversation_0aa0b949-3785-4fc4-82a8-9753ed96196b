import { writable, derived } from 'svelte/store';
import type { Note, NoteStatus, NotePriority, Tag, PaginatedResult, SearchParams } from '$types';
import { noteService, type CreateNoteData, type UpdateNoteData } from '$lib/services/noteService';
import { toastStore } from './toast';

interface NoteManagementState {
	notes: Note[];
	currentNote: Note | null;
	loading: boolean;
	error: string | null;
	searchQuery: string;
	selectedTags: Tag[];
	statusFilter: NoteStatus | 'all';
	priorityFilter: NotePriority | 'all';
	pagination: {
		page: number;
		limit: number;
		total: number;
		totalPages: number;
		hasNext: boolean;
		hasPrev: boolean;
	};
}

const initialState: NoteManagementState = {
	notes: [],
	currentNote: null,
	loading: false,
	error: null,
	searchQuery: '',
	selectedTags: [],
	statusFilter: 'all',
	priorityFilter: 'all',
	pagination: {
		page: 1,
		limit: 20,
		total: 0,
		totalPages: 0,
		hasNext: false,
		hasPrev: false
	}
};

function createNoteManagementStore() {
	const { subscribe, set, update } = writable<NoteManagementState>(initialState);

	return {
		subscribe,

		// Basic state setters
		setNotes: (notes: Note[]) => update(state => ({ ...state, notes })),
		setCurrentNote: (note: Note | null) => update(state => ({ ...state, currentNote: note })),
		setLoading: (loading: boolean) => update(state => ({ ...state, loading })),
		setError: (error: string | null) => update(state => ({ ...state, error })),
		setSearchQuery: (query: string) => update(state => ({ ...state, searchQuery: query })),
		setSelectedTags: (tags: Tag[]) => update(state => ({ ...state, selectedTags: tags })),
		setStatusFilter: (status: NoteStatus | 'all') =>
			update(state => ({ ...state, statusFilter: status })),
		setPriorityFilter: (priority: NotePriority | 'all') =>
			update(state => ({ ...state, priorityFilter: priority })),

		// Pagination
		setPagination: (pagination: Partial<NoteManagementState['pagination']>) =>
			update(state => ({
				...state,
				pagination: { ...state.pagination, ...pagination }
			})),

		// CRUD Operations
		async createNote(authorId: string, data: CreateNoteData): Promise<Note | null> {
			update(state => ({ ...state, loading: true, error: null }));

			try {
				const note = await noteService.createNote(authorId, data);

				update(state => ({
					...state,
					notes: [note, ...state.notes],
					currentNote: note,
					loading: false
				}));

				toastStore.success('筆記創建成功', `筆記 "${note.title}" 已創建`);
				return note;
			} catch (error) {
				const errorMessage = error instanceof Error ? error.message : '創建筆記失敗';
				update(state => ({ ...state, loading: false, error: errorMessage }));
				toastStore.error('創建失敗', errorMessage);
				return null;
			}
		},

		async updateNote(noteId: string, data: UpdateNoteData): Promise<Note | null> {
			update(state => ({ ...state, loading: true, error: null }));

			try {
				const updatedNote = await noteService.updateNote(noteId, data);

				update(state => ({
					...state,
					notes: state.notes.map(note => (note.id === updatedNote.id ? updatedNote : note)),
					currentNote: state.currentNote?.id === updatedNote.id ? updatedNote : state.currentNote,
					loading: false
				}));

				toastStore.success('筆記更新成功', `筆記 "${updatedNote.title}" 已更新`);
				return updatedNote;
			} catch (error) {
				const errorMessage = error instanceof Error ? error.message : '更新筆記失敗';
				update(state => ({ ...state, loading: false, error: errorMessage }));
				toastStore.error('更新失敗', errorMessage);
				return null;
			}
		},

		async deleteNote(noteId: string): Promise<boolean> {
			update(state => ({ ...state, loading: true, error: null }));

			try {
				await noteService.deleteNote(noteId);

				update(state => ({
					...state,
					notes: state.notes.filter(note => note.id !== noteId),
					currentNote: state.currentNote?.id === noteId ? null : state.currentNote,
					loading: false
				}));

				toastStore.success('筆記刪除成功', '筆記已成功刪除');
				return true;
			} catch (error) {
				const errorMessage = error instanceof Error ? error.message : '刪除筆記失敗';
				update(state => ({ ...state, loading: false, error: errorMessage }));
				toastStore.error('刪除失敗', errorMessage);
				return false;
			}
		},

		async loadNote(noteId: string): Promise<Note | null> {
			update(state => ({ ...state, loading: true, error: null }));

			try {
				const note = await noteService.getNote(noteId);

				update(state => ({
					...state,
					currentNote: note,
					loading: false
				}));

				return note;
			} catch (error) {
				const errorMessage = error instanceof Error ? error.message : '載入筆記失敗';
				update(state => ({ ...state, loading: false, error: errorMessage }));
				toastStore.error('載入失敗', errorMessage);
				return null;
			}
		},

		async loadUserNotes(authorId: string, params: Partial<SearchParams> = {}): Promise<void> {
			update(state => ({ ...state, loading: true, error: null }));

			try {
				const result = await noteService.getUserNotes(authorId, params);

				update(state => ({
					...state,
					notes: result.items,
					pagination: {
						page: result.page,
						limit: result.limit,
						total: result.total,
						totalPages: result.totalPages,
						hasNext: result.hasNext,
						hasPrev: result.hasPrev
					},
					loading: false
				}));
			} catch (error) {
				const errorMessage = error instanceof Error ? error.message : '載入筆記列表失敗';
				update(state => ({ ...state, loading: false, error: errorMessage }));
				toastStore.error('載入失敗', errorMessage);
			}
		},

		async searchNotes(params: SearchParams): Promise<void> {
			update(state => ({ ...state, loading: true, error: null }));

			try {
				const result = await noteService.searchNotes(params);

				update(state => ({
					...state,
					notes: result.items,
					pagination: {
						page: result.page,
						limit: result.limit,
						total: result.total,
						totalPages: result.totalPages,
						hasNext: result.hasNext,
						hasPrev: result.hasPrev
					},
					loading: false
				}));
			} catch (error) {
				const errorMessage = error instanceof Error ? error.message : '搜索失敗';
				update(state => ({ ...state, loading: false, error: errorMessage }));
				toastStore.error('搜索失敗', errorMessage);
			}
		},

		async publishNote(noteId: string): Promise<Note | null> {
			try {
				const note = await noteService.publishNote(noteId);

				update(state => ({
					...state,
					notes: state.notes.map(n => (n.id === noteId ? note : n)),
					currentNote: state.currentNote?.id === noteId ? note : state.currentNote
				}));

				toastStore.success('筆記發布成功', `筆記 "${note.title}" 已發布`);
				return note;
			} catch (error) {
				const errorMessage = error instanceof Error ? error.message : '發布失敗';
				toastStore.error('發布失敗', errorMessage);
				return null;
			}
		},

		async archiveNote(noteId: string): Promise<Note | null> {
			try {
				const note = await noteService.archiveNote(noteId);

				update(state => ({
					...state,
					notes: state.notes.map(n => (n.id === noteId ? note : n)),
					currentNote: state.currentNote?.id === noteId ? note : state.currentNote
				}));

				toastStore.success('筆記歸檔成功', `筆記 "${note.title}" 已歸檔`);
				return note;
			} catch (error) {
				const errorMessage = error instanceof Error ? error.message : '歸檔失敗';
				toastStore.error('歸檔失敗', errorMessage);
				return null;
			}
		},

		async duplicateNote(noteId: string, authorId: string): Promise<Note | null> {
			try {
				const note = await noteService.duplicateNote(noteId, authorId);

				update(state => ({
					...state,
					notes: [note, ...state.notes]
				}));

				toastStore.success('筆記複製成功', `筆記 "${note.title}" 已複製`);
				return note;
			} catch (error) {
				const errorMessage = error instanceof Error ? error.message : '複製失敗';
				toastStore.error('複製失敗', errorMessage);
				return null;
			}
		},

		// Clear all
		clear: () => set(initialState)
	};
}

export const noteManagementStore = createNoteManagementStore();

// Derived stores for filtering and statistics
export const filteredNotes = derived(noteManagementStore, $store => {
	let filtered = $store.notes;

	// Filter by search query
	if ($store.searchQuery) {
		const query = $store.searchQuery.toLowerCase();
		filtered = filtered.filter(
			note =>
				note.title.toLowerCase().includes(query) ||
				note.content.toLowerCase().includes(query) ||
				note.tags.some(tag => tag.name.toLowerCase().includes(query))
		);
	}

	// Filter by status
	if ($store.statusFilter !== 'all') {
		filtered = filtered.filter(note => note.status === $store.statusFilter);
	}

	// Filter by priority
	if ($store.priorityFilter !== 'all') {
		filtered = filtered.filter(note => note.priority === $store.priorityFilter);
	}

	// Filter by selected tags
	if ($store.selectedTags.length > 0) {
		filtered = filtered.filter(note =>
			$store.selectedTags.every(selectedTag =>
				note.tags.some(noteTag => noteTag.id === selectedTag.id)
			)
		);
	}

	return filtered;
});

export const noteStats = derived(noteManagementStore, $store => {
	const notes = $store.notes;
	return {
		total: notes.length,
		draft: notes.filter(n => n.status === 'draft').length,
		published: notes.filter(n => n.status === 'published').length,
		archived: notes.filter(n => n.status === 'archived').length,
		high: notes.filter(n => n.priority === 'high').length,
		medium: notes.filter(n => n.priority === 'medium').length,
		low: notes.filter(n => n.priority === 'low').length,
		urgent: notes.filter(n => n.priority === 'urgent').length
	};
});
