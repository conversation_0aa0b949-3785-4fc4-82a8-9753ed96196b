<script lang="ts">
	import { onMount } from 'svelte';
	import {
		Activity,
		TrendingUp,
		AlertTriangle,
		CheckCircle,
		Target,
		FileText,
		BarChart3,
		Settings,
		Download,
		RefreshCw,
		Lightbulb,
		Shield
	} from 'lucide-svelte';

	import Button from '$components/ui/Button.svelte';
	import Card from '$components/ui/Card.svelte';
	import DependencySmartSuggestions from './DependencySmartSuggestions.svelte';
	import { allNotes } from '$stores/notes';
	import { dependencyAnalysisReportService } from '$lib/services/dependencyAnalysisReportService';
	import { dependencyHealthService } from '$lib/services/dependencyHealthService';
	import { dependencyOptimizationService } from '$lib/services/dependencyOptimizationService';

	import type { DependencyAnalysisReport } from '$lib/services/dependencyAnalysisReportService';
	import type { DependencyHealthCheck } from '$lib/services/dependencyHealthService';
	import type { OptimizationPlan } from '$lib/services/dependencyOptimizationService';

	// 狀態
	let activeTab: 'overview' | 'health' | 'optimization' | 'report' = 'overview';
	let isAnalyzing = false;
	let analysisProgress = 0;

	// 分析結果
	let healthCheck: DependencyHealthCheck | null = null;
	let optimizationPlan: OptimizationPlan | null = null;
	let analysisReport: DependencyAnalysisReport | null = null;

	// 錯誤處理
	let error: string | null = null;

	onMount(() => {
		if ($allNotes && $allNotes.length > 0) {
			performInitialAnalysis();
		}
	});

	const performInitialAnalysis = async () => {
		if (!$allNotes || $allNotes.length === 0) return;

		isAnalyzing = true;
		analysisProgress = 0;
		error = null;

		try {
			// 健康檢查
			analysisProgress = 25;
			healthCheck = await dependencyHealthService.performHealthCheck($allNotes);

			// 優化計劃
			analysisProgress = 50;
			optimizationPlan = await dependencyOptimizationService.generateOptimizationPlan($allNotes);

			// 詳細報告
			analysisProgress = 75;
			analysisReport = await dependencyAnalysisReportService.generateReport($allNotes);

			analysisProgress = 100;
		} catch (err) {
			error = err instanceof Error ? err.message : '分析失敗';
			console.error('Analysis failed:', err);
		} finally {
			isAnalyzing = false;
		}
	};

	const refreshAnalysis = () => {
		performInitialAnalysis();
	};

	const exportReport = () => {
		if (!analysisReport) return;

		const reportData = {
			healthCheck,
			optimizationPlan,
			analysisReport,
			exportedAt: new Date().toISOString()
		};

		const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
		const url = URL.createObjectURL(blob);
		const a = document.createElement('a');
		a.href = url;
		a.download = `dependency-analysis-${new Date().toISOString().split('T')[0]}.json`;
		document.body.appendChild(a);
		a.click();
		document.body.removeChild(a);
		URL.revokeObjectURL(url);
	};

	const getHealthStatusColor = (status: string) => {
		switch (status) {
			case 'excellent':
				return 'text-green-600';
			case 'good':
				return 'text-blue-600';
			case 'fair':
				return 'text-yellow-600';
			case 'poor':
				return 'text-orange-600';
			case 'critical':
				return 'text-red-600';
			default:
				return 'text-gray-600';
		}
	};

	const getHealthStatusIcon = (status: string) => {
		switch (status) {
			case 'excellent':
			case 'good':
				return CheckCircle;
			case 'fair':
				return Activity;
			case 'poor':
			case 'critical':
				return AlertTriangle;
			default:
				return Activity;
		}
	};

	const getPriorityColor = (priority: string) => {
		switch (priority) {
			case 'critical':
				return 'text-red-600 bg-red-50';
			case 'high':
				return 'text-orange-600 bg-orange-50';
			case 'medium':
				return 'text-yellow-600 bg-yellow-50';
			case 'low':
				return 'text-blue-600 bg-blue-50';
			default:
				return 'text-gray-600 bg-gray-50';
		}
	};
</script>

<div class="advanced-dependency-analysis space-y-6">
	<!-- Header -->
	<div class="flex items-center justify-between">
		<div class="flex items-center space-x-3">
			<Activity class="h-6 w-6 text-primary" />
			<h2 class="text-2xl font-bold">高級依賴關係分析</h2>
		</div>

		<div class="flex items-center space-x-2">
			<Button variant="outline" size="sm" onclick={refreshAnalysis} disabled={isAnalyzing}>
				<RefreshCw class="h-4 w-4 mr-2 {isAnalyzing ? 'animate-spin' : ''}" />
				刷新分析
			</Button>

			<Button variant="outline" size="sm" onclick={exportReport} disabled={!analysisReport}>
				<Download class="h-4 w-4 mr-2" />
				導出報告
			</Button>
		</div>
	</div>

	<!-- 分析進度 -->
	{#if isAnalyzing}
		<Card class="p-6">
			<div class="flex items-center space-x-4">
				<div class="flex-shrink-0">
					<RefreshCw class="h-6 w-6 animate-spin text-primary" />
				</div>
				<div class="flex-1">
					<h3 class="font-medium mb-2">正在進行依賴關係分析...</h3>
					<div class="w-full bg-gray-200 rounded-full h-2">
						<div
							class="bg-primary h-2 rounded-full transition-all duration-300"
							style="width: {analysisProgress}%"
						></div>
					</div>
					<p class="text-sm text-muted-foreground mt-1">{analysisProgress}% 完成</p>
				</div>
			</div>
		</Card>
	{/if}

	<!-- 錯誤顯示 -->
	{#if error}
		<Card class="p-6 border-red-200 bg-red-50">
			<div class="flex items-center space-x-3">
				<AlertTriangle class="h-5 w-5 text-red-600" />
				<div>
					<h3 class="font-medium text-red-800">分析失敗</h3>
					<p class="text-red-600">{error}</p>
				</div>
			</div>
		</Card>
	{/if}

	<!-- 標籤頁導航 -->
	<div class="border-b border-border">
		<nav class="flex space-x-8">
			<button
				class="py-2 px-1 border-b-2 font-medium text-sm {activeTab === 'overview'
					? 'border-primary text-primary'
					: 'border-transparent text-muted-foreground hover:text-foreground'}"
				on:click={() => (activeTab = 'overview')}
			>
				<BarChart3 class="h-4 w-4 inline mr-2" />
				概覽
			</button>

			<button
				class="py-2 px-1 border-b-2 font-medium text-sm {activeTab === 'health'
					? 'border-primary text-primary'
					: 'border-transparent text-muted-foreground hover:text-foreground'}"
				on:click={() => (activeTab = 'health')}
			>
				<Shield class="h-4 w-4 inline mr-2" />
				健康檢查
			</button>

			<button
				class="py-2 px-1 border-b-2 font-medium text-sm {activeTab === 'optimization'
					? 'border-primary text-primary'
					: 'border-transparent text-muted-foreground hover:text-foreground'}"
				on:click={() => (activeTab = 'optimization')}
			>
				<Lightbulb class="h-4 w-4 inline mr-2" />
				優化建議
			</button>

			<button
				class="py-2 px-1 border-b-2 font-medium text-sm {activeTab === 'report'
					? 'border-primary text-primary'
					: 'border-transparent text-muted-foreground hover:text-foreground'}"
				on:click={() => (activeTab = 'report')}
			>
				<FileText class="h-4 w-4 inline mr-2" />
				詳細報告
			</button>
		</nav>
	</div>

	<!-- 標籤頁內容 -->
	<div class="tab-content">
		{#if activeTab === 'overview'}
			<!-- 概覽標籤頁 -->
			<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
				{#if healthCheck}
					<Card class="p-6">
						<div class="flex items-center justify-between">
							<div>
								<p class="text-sm font-medium text-muted-foreground">整體健康度</p>
								<div class="flex items-center space-x-2 mt-1">
									<span
										class="text-2xl font-bold {getHealthStatusColor(
											healthCheck.overallHealth.status
										)}"
									>
										{healthCheck.overallHealth.grade}
									</span>
									<svelte:component
										this={getHealthStatusIcon(healthCheck.overallHealth.status)}
										class="h-5 w-5 {getHealthStatusColor(healthCheck.overallHealth.status)}"
									/>
								</div>
							</div>
							<div class="text-right">
								<p class="text-sm text-muted-foreground">分數</p>
								<p class="text-lg font-semibold">
									{healthCheck.overallHealth.score.toFixed(0)}/100
								</p>
							</div>
						</div>
					</Card>
				{/if}

				{#if analysisReport}
					<Card class="p-6">
						<div class="flex items-center justify-between">
							<div>
								<p class="text-sm font-medium text-muted-foreground">總筆記數</p>
								<p class="text-2xl font-bold">{analysisReport.summary.totalNotes}</p>
							</div>
							<FileText class="h-8 w-8 text-blue-500" />
						</div>
					</Card>

					<Card class="p-6">
						<div class="flex items-center justify-between">
							<div>
								<p class="text-sm font-medium text-muted-foreground">依賴關係</p>
								<p class="text-2xl font-bold">{analysisReport.summary.totalDependencies}</p>
							</div>
							<Target class="h-8 w-8 text-green-500" />
						</div>
					</Card>

					<Card class="p-6">
						<div class="flex items-center justify-between">
							<div>
								<p class="text-sm font-medium text-muted-foreground">網絡密度</p>
								<p class="text-2xl font-bold">
									{(analysisReport.summary.networkDensity * 100).toFixed(1)}%
								</p>
							</div>
							<BarChart3 class="h-8 w-8 text-purple-500" />
						</div>
					</Card>
				{/if}
			</div>

			{#if optimizationPlan}
				<Card class="p-6">
					<h3 class="text-lg font-semibold mb-4">優化機會</h3>
					<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
						{#each ['critical', 'high', 'medium'] as priority}
							{@const suggestions = optimizationPlan.suggestions.filter(
								s => s.priority === priority
							)}
							{#if suggestions.length > 0}
								<div class="text-center p-4 rounded-lg {getPriorityColor(priority)}">
									<div class="text-2xl font-bold">{suggestions.length}</div>
									<div class="text-sm font-medium capitalize">{priority} 優先級</div>
								</div>
							{/if}
						{/each}
					</div>
				</Card>
			{/if}

			<!-- 智能建議 -->
			<DependencySmartSuggestions />
		{:else if activeTab === 'health'}
			<!-- 健康檢查標籤頁 -->
			{#if healthCheck}
				<div class="space-y-6">
					<!-- 健康指標 -->
					<Card class="p-6">
						<h3 class="text-lg font-semibold mb-4">健康指標</h3>
						<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
							<div class="text-center">
								<div class="text-2xl font-bold text-blue-600">
									{healthCheck.metrics.connectivity.score.toFixed(0)}
								</div>
								<div class="text-sm text-muted-foreground">連接性</div>
								<div class="text-xs text-muted-foreground mt-1">
									{healthCheck.metrics.connectivity.connectedRatio.toFixed(1)}% 已連接
								</div>
							</div>

							<div class="text-center">
								<div class="text-2xl font-bold text-green-600">
									{healthCheck.metrics.structure.score.toFixed(0)}
								</div>
								<div class="text-sm text-muted-foreground">結構</div>
								<div class="text-xs text-muted-foreground mt-1">
									{healthCheck.metrics.structure.circularDependencies} 循環依賴
								</div>
							</div>

							<div class="text-center">
								<div class="text-2xl font-bold text-purple-600">
									{healthCheck.metrics.consistency.score.toFixed(0)}
								</div>
								<div class="text-sm text-muted-foreground">一致性</div>
								<div class="text-xs text-muted-foreground mt-1">
									標籤一致性 {healthCheck.metrics.consistency.tagConsistency.toFixed(0)}%
								</div>
							</div>

							<div class="text-center">
								<div class="text-2xl font-bold text-orange-600">
									{healthCheck.metrics.maintainability.score.toFixed(0)}
								</div>
								<div class="text-sm text-muted-foreground">可維護性</div>
								<div class="text-xs text-muted-foreground mt-1">
									文檔化 {healthCheck.metrics.maintainability.documentationScore.toFixed(0)}%
								</div>
							</div>
						</div>
					</Card>

					<!-- 健康問題 -->
					{#if healthCheck.issues.length > 0}
						<Card class="p-6">
							<h3 class="text-lg font-semibold mb-4">檢測到的問題</h3>
							<div class="space-y-3">
								{#each healthCheck.issues as issue}
									<div class="flex items-start space-x-3 p-3 rounded-lg border">
										<AlertTriangle
											class="h-5 w-5 mt-0.5 {issue.severity === 'critical'
												? 'text-red-500'
												: issue.severity === 'high'
													? 'text-orange-500'
													: issue.severity === 'medium'
														? 'text-yellow-500'
														: 'text-blue-500'}"
										/>
										<div class="flex-1">
											<div class="flex items-center space-x-2">
												<h4 class="font-medium">{issue.title}</h4>
												<span
													class="px-2 py-1 text-xs rounded-full {getPriorityColor(issue.severity)}"
												>
													{issue.severity}
												</span>
											</div>
											<p class="text-sm text-muted-foreground mt-1">{issue.description}</p>
											<p class="text-sm text-blue-600 mt-2">{issue.recommendation}</p>
										</div>
									</div>
								{/each}
							</div>
						</Card>
					{:else}
						<Card class="p-6 text-center">
							<CheckCircle class="h-12 w-12 text-green-500 mx-auto mb-3" />
							<h3 class="text-lg font-semibold text-green-700">系統健康狀況良好</h3>
							<p class="text-muted-foreground">未檢測到需要關注的問題</p>
						</Card>
					{/if}
				</div>
			{/if}
		{:else if activeTab === 'optimization'}
			<!-- 優化建議標籤頁 -->
			{#if optimizationPlan}
				<div class="space-y-6">
					<!-- 優化階段 -->
					<Card class="p-6">
						<h3 class="text-lg font-semibold mb-4">優化階段</h3>
						<div class="space-y-4">
							{#each optimizationPlan.phases as phase}
								<div class="border rounded-lg p-4">
									<div class="flex items-center justify-between mb-2">
										<h4 class="font-medium">階段 {phase.phase}: {phase.title}</h4>
										<span class="text-sm text-muted-foreground">{phase.estimatedDuration}</span>
									</div>
									<p class="text-sm text-muted-foreground mb-3">{phase.description}</p>
									<div class="text-sm">
										<span class="font-medium">{phase.suggestions.length}</span> 個建議項目
									</div>
								</div>
							{/each}
						</div>
					</Card>

					<!-- 優化建議列表 -->
					<Card class="p-6">
						<h3 class="text-lg font-semibold mb-4">詳細建議</h3>
						<div class="space-y-4">
							{#each optimizationPlan.suggestions as suggestion}
								<div class="border rounded-lg p-4">
									<div class="flex items-start justify-between mb-2">
										<div class="flex items-center space-x-2">
											<h4 class="font-medium">{suggestion.title}</h4>
											<span
												class="px-2 py-1 text-xs rounded-full {getPriorityColor(
													suggestion.priority
												)}"
											>
												{suggestion.priority}
											</span>
										</div>
										<div class="text-sm text-muted-foreground">
											影響分數: {suggestion.impact.overallScore}
										</div>
									</div>
									<p class="text-sm text-muted-foreground mb-3">{suggestion.description}</p>
									<p class="text-sm mb-3">{suggestion.rationale}</p>

									<div class="flex items-center justify-between text-sm">
										<div class="flex items-center space-x-4">
											<span
												>工作量: <span class="font-medium">{suggestion.estimatedEffort}</span></span
											>
											<span
												>預期收益: <span class="font-medium">{suggestion.expectedBenefit}</span
												></span
											>
										</div>
										<span class="text-muted-foreground">
											影響 {suggestion.affectedNotes.length} 個筆記
										</span>
									</div>
								</div>
							{/each}
						</div>
					</Card>
				</div>
			{/if}
		{:else if activeTab === 'report'}
			<!-- 詳細報告標籤頁 -->
			{#if analysisReport}
				<div class="space-y-6">
					<!-- 網絡指標 -->
					<Card class="p-6">
						<h3 class="text-lg font-semibold mb-4">網絡指標</h3>
						<div class="grid grid-cols-1 md:grid-cols-3 gap-6">
							<div>
								<h4 class="font-medium mb-2">基本指標</h4>
								<div class="space-y-2 text-sm">
									<div class="flex justify-between">
										<span>網絡直徑:</span>
										<span class="font-medium">{analysisReport.networkMetrics.diameter}</span>
									</div>
									<div class="flex justify-between">
										<span>平均路徑長度:</span>
										<span class="font-medium"
											>{analysisReport.networkMetrics.averagePathLength.toFixed(2)}</span
										>
									</div>
									<div class="flex justify-between">
										<span>聚類係數:</span>
										<span class="font-medium"
											>{analysisReport.networkMetrics.clusteringCoefficient.toFixed(3)}</span
										>
									</div>
								</div>
							</div>

							<div>
								<h4 class="font-medium mb-2">中心性分佈</h4>
								<div class="space-y-2 text-sm">
									<div class="flex justify-between">
										<span>平均值:</span>
										<span class="font-medium"
											>{analysisReport.networkMetrics.centralityDistribution.mean.toFixed(3)}</span
										>
									</div>
									<div class="flex justify-between">
										<span>中位數:</span>
										<span class="font-medium"
											>{analysisReport.networkMetrics.centralityDistribution.median.toFixed(
												3
											)}</span
										>
									</div>
									<div class="flex justify-between">
										<span>標準差:</span>
										<span class="font-medium"
											>{analysisReport.networkMetrics.centralityDistribution.standardDeviation.toFixed(
												3
											)}</span
										>
									</div>
								</div>
							</div>

							<div>
								<h4 class="font-medium mb-2">風險評估</h4>
								<div class="space-y-2 text-sm">
									<div class="flex justify-between">
										<span>整體風險分數:</span>
										<span
											class="font-medium {analysisReport.riskAssessment.overallRiskScore > 0.7
												? 'text-red-600'
												: analysisReport.riskAssessment.overallRiskScore > 0.4
													? 'text-yellow-600'
													: 'text-green-600'}"
										>
											{(analysisReport.riskAssessment.overallRiskScore * 100).toFixed(0)}%
										</span>
									</div>
									<div class="flex justify-between">
										<span>關鍵節點:</span>
										<span class="font-medium"
											>{analysisReport.riskAssessment.criticalNodes.length}</span
										>
									</div>
									<div class="flex justify-between">
										<span>脆弱連接:</span>
										<span class="font-medium"
											>{analysisReport.riskAssessment.fragileConnections.length}</span
										>
									</div>
								</div>
							</div>
						</div>
					</Card>

					<!-- 聚類分析 -->
					{#if analysisReport.clusterAnalysis.length > 0}
						<Card class="p-6">
							<h3 class="text-lg font-semibold mb-4">聚類分析</h3>
							<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
								{#each analysisReport.clusterAnalysis as cluster}
									<div class="border rounded-lg p-4">
										<h4 class="font-medium mb-2">{cluster.clusterId}</h4>
										{#if cluster.mainTopic}
											<p class="text-sm text-muted-foreground mb-2">主題: {cluster.mainTopic}</p>
										{/if}
										<div class="text-sm space-y-1">
											<div class="flex justify-between">
												<span>節點數:</span>
												<span class="font-medium">{cluster.size}</span>
											</div>
											<div class="flex justify-between">
												<span>密度:</span>
												<span class="font-medium">{(cluster.density * 100).toFixed(1)}%</span>
											</div>
											<div class="flex justify-between">
												<span>凝聚力:</span>
												<span class="font-medium">{cluster.cohesion.toFixed(2)}</span>
											</div>
										</div>
										{#if cluster.keywords.length > 0}
											<div class="mt-3">
												<p class="text-xs text-muted-foreground mb-1">關鍵詞:</p>
												<div class="flex flex-wrap gap-1">
													{#each cluster.keywords as keyword}
														<span class="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded"
															>{keyword}</span
														>
													{/each}
												</div>
											</div>
										{/if}
									</div>
								{/each}
							</div>
						</Card>
					{/if}
				</div>
			{/if}
		{/if}
	</div>
</div>
