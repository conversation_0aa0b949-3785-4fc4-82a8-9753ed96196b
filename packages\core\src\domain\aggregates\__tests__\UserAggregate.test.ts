import { describe, it, expect, beforeEach } from "vitest";
import { UserAggregate, UserPreferences } from "../UserAggregate.js";
import { User, UserRole, UserStatus } from "../../entities/User.js";
import { UserId } from "../../value-objects/UserId.js";
import { NoteId } from "../../value-objects/NoteId.js";
import { BusinessRuleViolationError } from "../../../shared/Entity.js";

describe("UserAggregate", () => {
  let userAggregate: UserAggregate;
  let userId: UserId;

  beforeEach(() => {
    userId = UserId.generate();
    const user = User.create({
      username: "testuser",
      email: "<EMAIL>",
      displayName: "Test User",
    });
    userAggregate = new UserAggregate(user);
  });

  describe("Creation", () => {
    it("should create user aggregate with default preferences", () => {
      expect(userAggregate.user.username).toBe("testuser");
      expect(userAggregate.user.email).toBe("<EMAIL>");
      expect(userAggregate.preferences.theme).toBe("auto");
      expect(userAggregate.preferences.language).toBe("zh-TW");
    });

    it("should create user aggregate using static factory method", () => {
      const aggregate = UserAggregate.create(
        "newuser",
        "<EMAIL>",
        "password123",
        {
          displayName: "New User",
          role: UserRole.ADMIN,
        },
      );

      expect(aggregate.user.username).toBe("newuser");
      expect(aggregate.user.email).toBe("<EMAIL>");
      expect(aggregate.user.displayName).toBe("New User");
      expect(aggregate.user.role).toBe(UserRole.ADMIN);
    });
  });

  describe("Profile Management", () => {
    it("should update user profile", () => {
      userAggregate.updateProfile({
        displayName: "Updated Name",
        email: "<EMAIL>",
      });

      expect(userAggregate.user.displayName).toBe("Updated Name");
      expect(userAggregate.user.email).toBe("<EMAIL>");
    });

    it("should change username", () => {
      userAggregate.changeUsername("newusername");
      expect(userAggregate.user.username).toBe("newusername");
    });

    it("should reject invalid username", () => {
      expect(() => userAggregate.changeUsername("ab")).toThrow(
        BusinessRuleViolationError,
      );
    });

    it("should change email", () => {
      userAggregate.changeEmail("<EMAIL>");
      expect(userAggregate.user.email).toBe("<EMAIL>");
    });

    it("should reject invalid email", () => {
      expect(() => userAggregate.changeEmail("invalid-email")).toThrow(
        BusinessRuleViolationError,
      );
    });
  });

  describe("Password Management", () => {
    it("should reject same password change", () => {
      expect(() => userAggregate.changePassword("oldpass", "oldpass")).toThrow(
        BusinessRuleViolationError,
      );
    });

    it("should reject weak password", () => {
      expect(() => userAggregate.changePassword("oldpass", "123")).toThrow(
        BusinessRuleViolationError,
      );
    });
  });

  describe("User Status Management", () => {
    it("should activate user", () => {
      userAggregate.activate();
      expect(userAggregate.user.status).toBe(UserStatus.ACTIVE);
    });

    it("should deactivate user", () => {
      userAggregate.deactivate();
      expect(userAggregate.user.status).toBe(UserStatus.INACTIVE);
    });

    it("should suspend user", () => {
      userAggregate.suspend("Policy violation");
      expect(userAggregate.user.status).toBe(UserStatus.SUSPENDED);
    });

    it("should update user role", () => {
      userAggregate.updateRole(UserRole.ADMIN);
      expect(userAggregate.user.role).toBe(UserRole.ADMIN);
    });
  });

  describe("Note Management", () => {
    it("should add owned note", () => {
      const noteId = NoteId.generate();
      userAggregate.addOwnedNote(noteId);

      expect(userAggregate.ownedNotes).toContain(noteId);
      expect(userAggregate.statistics.totalNotes).toBe(1);
    });

    it("should remove owned note", () => {
      const noteId = NoteId.generate();
      userAggregate.addOwnedNote(noteId);
      userAggregate.removeOwnedNote(noteId);

      expect(userAggregate.ownedNotes).not.toContain(noteId);
      expect(userAggregate.statistics.totalNotes).toBe(0);
    });
  });

  describe("Preferences Management", () => {
    it("should update preferences", () => {
      const newPreferences: Partial<UserPreferences> = {
        theme: "dark",
        language: "en-US",
      };

      userAggregate.updatePreferences(newPreferences);

      expect(userAggregate.preferences.theme).toBe("dark");
      expect(userAggregate.preferences.language).toBe("en-US");
    });

    it("should update editor settings", () => {
      userAggregate.updateEditorSettings({
        fontSize: 16,
        enableVimMode: true,
      });

      expect(userAggregate.preferences.editorSettings.fontSize).toBe(16);
      expect(userAggregate.preferences.editorSettings.enableVimMode).toBe(true);
    });

    it("should update notification settings", () => {
      userAggregate.updateNotificationSettings({
        emailNotifications: false,
        weeklyDigest: true,
      });

      expect(
        userAggregate.preferences.notificationSettings.emailNotifications,
      ).toBe(false);
      expect(userAggregate.preferences.notificationSettings.weeklyDigest).toBe(
        true,
      );
    });

    it("should update privacy settings", () => {
      userAggregate.updatePrivacySettings({
        profileVisibility: "private",
        allowIndexing: false,
      });

      expect(userAggregate.preferences.privacySettings.profileVisibility).toBe(
        "private",
      );
      expect(userAggregate.preferences.privacySettings.allowIndexing).toBe(
        false,
      );
    });
  });

  describe("Session Management", () => {
    it("should create session", () => {
      const token = "session-token-123";
      const expiresAt = new Date(Date.now() + 3600000); // 1 hour
      const device = "Chrome on Windows";

      userAggregate.createSession(token, expiresAt, device);

      expect(userAggregate.isSessionValid(token)).toBe(true);
      expect(userAggregate.activeSessions).toContain(token);
    });

    it("should remove session", () => {
      const token = "session-token-123";
      const expiresAt = new Date(Date.now() + 3600000);
      const device = "Chrome on Windows";

      userAggregate.createSession(token, expiresAt, device);
      userAggregate.removeSession(token);

      expect(userAggregate.isSessionValid(token)).toBe(false);
      expect(userAggregate.activeSessions).not.toContain(token);
    });

    it("should clear all sessions", () => {
      const token1 = "session-token-1";
      const token2 = "session-token-2";
      const expiresAt = new Date(Date.now() + 3600000);
      const device = "Chrome on Windows";

      userAggregate.createSession(token1, expiresAt, device);
      userAggregate.createSession(token2, expiresAt, device);
      userAggregate.clearAllSessions();

      expect(userAggregate.activeSessions).toHaveLength(0);
    });

    it("should handle expired sessions", () => {
      const token = "session-token-123";
      const expiresAt = new Date(Date.now() - 1000); // Expired
      const device = "Chrome on Windows";

      userAggregate.createSession(token, expiresAt, device);

      expect(userAggregate.isSessionValid(token)).toBe(false);
    });
  });

  describe("Permissions", () => {
    it("should allow basic actions for active user", () => {
      userAggregate.activate();

      expect(userAggregate.canPerformAction("create_note")).toBe(true);
      expect(userAggregate.canPerformAction("edit_note")).toBe(true);
      expect(userAggregate.canPerformAction("delete_note")).toBe(true);
    });

    it("should deny actions for inactive user", () => {
      userAggregate.deactivate();

      expect(userAggregate.canPerformAction("create_note")).toBe(false);
      expect(userAggregate.canPerformAction("edit_note")).toBe(false);
    });

    it("should allow admin actions for admin user", () => {
      userAggregate.updateRole(UserRole.ADMIN);
      userAggregate.activate();

      expect(userAggregate.canPerformAction("admin_action")).toBe(true);
      expect(userAggregate.canPerformAction("moderate_content")).toBe(true);
    });

    it("should deny admin actions for regular user", () => {
      userAggregate.activate();

      expect(userAggregate.canPerformAction("admin_action")).toBe(false);
    });

    it("should allow moderation for moderator", () => {
      userAggregate.updateRole(UserRole.MODERATOR);
      userAggregate.activate();

      expect(userAggregate.canPerformAction("moderate_content")).toBe(true);
      expect(userAggregate.canPerformAction("admin_action")).toBe(false);
    });
  });

  describe("Statistics", () => {
    it("should update statistics", () => {
      userAggregate.updateStatistics({
        totalWords: 1000,
        currentStreak: 5,
      });

      expect(userAggregate.statistics.totalWords).toBe(1000);
      expect(userAggregate.statistics.currentStreak).toBe(5);
    });

    it("should record activity", () => {
      const beforeActivity = userAggregate.statistics.lastActiveDate;

      // Wait a bit to ensure different timestamp
      setTimeout(() => {
        userAggregate.recordActivity();
        expect(userAggregate.statistics.lastActiveDate).not.toEqual(
          beforeActivity,
        );
      }, 10);
    });
  });

  describe("Serialization", () => {
    it("should serialize to plain object", () => {
      const plainObject = userAggregate.toPlainObject();

      expect(plainObject).toHaveProperty("id");
      expect(plainObject).toHaveProperty("user");
      expect(plainObject).toHaveProperty("preferences");
      expect(plainObject).toHaveProperty("statistics");
      expect(plainObject).toHaveProperty("ownedNotes");
      expect(plainObject).toHaveProperty("activeSessionsCount");
    });
  });
});
