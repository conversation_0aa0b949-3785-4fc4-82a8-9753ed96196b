import { writable, derived } from 'svelte/store';
import type {
	Note,
	NoteVersion,
	CreateNoteData,
	UpdateNoteData,
	NoteStats,
	PaginatedResult,
	SearchParams,
	SearchOptions,
	NoteId
} from '$types';

export interface NotesState {
	notes: Note[];
	currentNote: Note | null;
	versions: Record<NoteId, NoteVersion[]>;
	stats: NoteStats | null;
	loading: boolean;
	error: string | null;
	lastUpdated: Date | null;
}

// Mock 數據
const mockNotes: Note[] = [
	{
		id: '1',
		title: '歡迎使用 Life Note',
		content: `# 歡迎使用 Life Note

Life Note 是一個功能強大的知識管理系統，專為現代知識工作者設計。

## 主要功能

### 📝 Markdown 編輯
- 支援完整的 Markdown 語法
- 實時預覽功能
- 語法高亮顯示
- 快捷鍵支援

### 🔗 依賴關係追蹤
- 自動檢測筆記間的關聯
- 視覺化依賴關係圖
- 智能推薦相關內容

### 🏷️ 標籤系統
- 靈活的標籤分類
- 標籤自動建議
- 多維度過濾

### 📊 版本控制
- 完整的版本歷史
- 版本比較功能
- 一鍵回滾

## 開始使用

1. 點擊「新增筆記」創建您的第一個筆記
2. 使用 Markdown 語法編寫內容
3. 添加標籤進行分類
4. 探索依賴關係功能

> 💡 **提示**：使用 \`Ctrl+S\` 快速保存筆記`,
		excerpt: '這是您的第一個筆記。Life Note 是一個功能強大的知識管理系統...',
		status: 'published',
		priority: 'high',
		authorId: 'user-1',
		categoryId: 'getting-started',
		tags: [
			{ id: 'tag-1', name: '歡迎', createdAt: new Date(), updatedAt: new Date() },
			{ id: 'tag-2', name: '開始', createdAt: new Date(), updatedAt: new Date() },
			{ id: 'tag-3', name: '指南', createdAt: new Date(), updatedAt: new Date() }
		],
		metadata: {},
		createdAt: new Date(Date.now() - 86400000 * 7),
		updatedAt: new Date(Date.now() - 86400000 * 2),
		publishedAt: new Date(Date.now() - 86400000 * 2),
		version: 1
	},
	{
		id: '2',
		title: 'Markdown 語法指南',
		content: `# Markdown 語法指南

學習如何使用 Markdown 語法來格式化您的筆記內容。

## 基本語法

### 標題
\`\`\`markdown
# 一級標題
## 二級標題
### 三級標題
\`\`\`

### 文字格式
- **粗體文字**
- *斜體文字*
- ~~刪除線~~
- \`行內代碼\`

### 列表
1. 有序列表項目 1
2. 有序列表項目 2

- 無序列表項目 1
- 無序列表項目 2

### 連結和圖片
[連結文字](https://example.com)
![圖片描述](image-url)

### 引用
> 這是一個引用塊
> 可以包含多行內容

### 代碼塊
\`\`\`javascript
function hello() {
    console.log("Hello, World!");
}
\`\`\`

### 表格
| 欄位 1 | 欄位 2 | 欄位 3 |
|--------|--------|--------|
| 內容 1 | 內容 2 | 內容 3 |
| 內容 4 | 內容 5 | 內容 6 |

## 進階功能

### 任務列表
- [x] 已完成的任務
- [ ] 未完成的任務
- [ ] 另一個任務

### 數學公式
使用 LaTeX 語法：$E = mc^2$

### 腳註
這是一個腳註的例子[^1]。

[^1]: 這是腳註的內容。`,
		excerpt: '學習如何使用 Markdown 語法來格式化您的筆記內容...',
		status: 'published',
		priority: 'medium',
		authorId: 'user-1',
		categoryId: 'tutorials',
		tags: [
			{ id: 'tag-4', name: 'Markdown', createdAt: new Date(), updatedAt: new Date() },
			{ id: 'tag-5', name: '教學', createdAt: new Date(), updatedAt: new Date() },
			{ id: 'tag-6', name: '語法', createdAt: new Date(), updatedAt: new Date() }
		],
		metadata: {},
		createdAt: new Date(Date.now() - 86400000 * 5),
		updatedAt: new Date(Date.now() - 86400000 * 1),
		publishedAt: new Date(Date.now() - 86400000 * 1),
		version: 2
	},
	{
		id: '3',
		title: '項目規劃筆記',
		content: `# 項目規劃筆記

記錄項目的重要里程碑和待辦事項。

## 項目概述
- **項目名稱**：Life Note 知識管理系統
- **開始日期**：2024年1月1日
- **預計完成**：2024年6月30日
- **項目經理**：張三

## 主要里程碑

### 第一階段：基礎架構 ✅
- [x] 項目初始化
- [x] 技術棧選擇
- [x] 開發環境搭建
- [x] 基礎 UI 組件

### 第二階段：核心功能 🚧
- [x] 筆記編輯器
- [x] 標籤系統
- [ ] 搜索功能
- [ ] 版本控制

### 第三階段：高級功能 📋
- [ ] 依賴關係分析
- [ ] AI 整合
- [ ] 協作功能
- [ ] 移動端適配

## 風險評估

### 高風險
- 性能優化挑戰
- 數據遷移複雜性

### 中風險
- 第三方服務整合
- 用戶體驗設計

### 低風險
- 基礎功能實現
- 測試覆蓋率

## 資源分配

| 角色 | 人員 | 工作量 |
|------|------|--------|
| 前端開發 | 李四 | 40% |
| 後端開發 | 王五 | 35% |
| UI/UX 設計 | 趙六 | 15% |
| 測試 | 錢七 | 10% |

## 下週計劃
1. 完成搜索功能開發
2. 進行性能測試
3. 用戶體驗優化
4. 文檔更新`,
		excerpt: '記錄項目的重要里程碑和待辦事項...',
		status: 'draft',
		priority: 'urgent',
		authorId: 'user-1',
		categoryId: 'projects',
		tags: [
			{ id: 'tag-7', name: '項目', createdAt: new Date(), updatedAt: new Date() },
			{ id: 'tag-8', name: '規劃', createdAt: new Date(), updatedAt: new Date() },
			{ id: 'tag-9', name: '管理', createdAt: new Date(), updatedAt: new Date() }
		],
		metadata: {},
		createdAt: new Date(Date.now() - 86400000 * 3),
		updatedAt: new Date(Date.now() - 3600000),
		version: 3
	}
];

const initialState: NotesState = {
	notes: [...mockNotes], // 初始化時載入 mock 數據
	currentNote: null,
	versions: {},
	stats: {
		totalNotes: mockNotes.length,
		totalTags: 9, // 根據 mock 數據計算
		notesThisWeek: 2,
		notesThisMonth: 3,
		averageWordsPerNote: 150,
		lastUpdated: new Date()
	},
	loading: false,
	error: null,
	lastUpdated: new Date()
};

function createNoteStore() {
	const { subscribe, set, update } = writable<NotesState>(initialState);

	return {
		subscribe,

		/**
		 * 設置筆記列表
		 */
		setNotes(notes: Note[]) {
			update(state => ({ ...state, notes }));
		},

		/**
		 * 添加筆記
		 */
		addNote(note: Note) {
			update(state => ({
				...state,
				notes: [...state.notes, note]
			}));
		},

		/**
		 * 更新筆記
		 */
		updateNote(noteId: string, updates: Partial<Note>) {
			update(state => ({
				...state,
				notes: state.notes.map(note => (note.id === noteId ? { ...note, ...updates } : note))
			}));
		},

		/**
		 * 刪除筆記
		 */
		deleteNote(noteId: string) {
			update(state => ({
				...state,
				notes: state.notes.filter(note => note.id !== noteId)
			}));
		},

		/**
		 * 設置加載狀態
		 */
		setLoading(loading: boolean) {
			update(state => ({ ...state, loading }));
		},

		/**
		 * 設置錯誤
		 */
		setError(error: string | null) {
			update(state => ({ ...state, error }));
		},

		/**
		 * 獲取單個筆記
		 */
		async getNote(id: NoteId): Promise<Note> {
			update(state => ({ ...state, loading: true, error: null }));

			try {
				// 模擬 API 調用
				await new Promise(resolve => setTimeout(resolve, 300));

				const note = mockNotes.find(n => n.id === id);
				if (!note) {
					throw new Error('筆記不存在');
				}

				update(state => ({
					...state,
					currentNote: note,
					loading: false
				}));

				return note;
			} catch (error) {
				const errorMessage = error instanceof Error ? error.message : '載入筆記失敗';
				update(state => ({
					...state,
					loading: false,
					error: errorMessage
				}));
				throw error;
			}
		},

		/**
		 * 創建筆記
		 */
		async createNote(data: CreateNoteData): Promise<Note> {
			update(state => ({ ...state, loading: true, error: null }));

			try {
				// 模擬 API 調用
				await new Promise(resolve => setTimeout(resolve, 500));

				const newNote: Note = {
					id: `note-${Date.now()}`,
					title: data.title,
					content: data.content,
					excerpt: data.content.substring(0, 200),
					status: data.status || 'draft',
					priority: data.priority || 'medium',
					authorId: 'user-1', // 在真實應用中從認證狀態獲取
					categoryId: data.categoryId,
					tags: data.tags || [],
					metadata: {},
					createdAt: new Date(),
					updatedAt: new Date(),
					version: 1
				};

				// 更新 mock 數據
				mockNotes.unshift(newNote);

				update(state => ({
					...state,
					notes: [newNote, ...state.notes],
					currentNote: newNote,
					loading: false,
					lastUpdated: new Date()
				}));

				return newNote;
			} catch (error) {
				const errorMessage = error instanceof Error ? error.message : '創建筆記失敗';
				update(state => ({
					...state,
					loading: false,
					error: errorMessage
				}));
				throw error;
			}
		},

		/**
		 * 載入所有筆記
		 */
		async loadNotes(): Promise<void> {
			update(state => ({ ...state, loading: true, error: null }));

			try {
				// 模擬 API 調用
				await new Promise(resolve => setTimeout(resolve, 500));

				update(state => ({
					...state,
					notes: [...mockNotes],
					loading: false,
					lastUpdated: new Date()
				}));
			} catch (error) {
				const errorMessage = error instanceof Error ? error.message : '載入筆記失敗';
				update(state => ({
					...state,
					loading: false,
					error: errorMessage
				}));
			}
		},

		/**
		 * 重置狀態
		 */
		reset() {
			set(initialState);
		}
	};
}

export const noteStore = createNoteStore();

// 派生存儲
export const currentNote = derived(noteStore, $state => $state.currentNote);
export const notesLoading = derived(noteStore, $state => $state.loading);
export const notesError = derived(noteStore, $state => $state.error);
export const allNotes = derived(noteStore, $state => $state.notes);
export const noteStats = derived(noteStore, $state => $state.stats);
