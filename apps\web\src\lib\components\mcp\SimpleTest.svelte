<script>
	import { onMount } from 'svelte';
	import Button from '$components/ui/Button.svelte';

	let count = 0;
	let message = '點擊按鈕測試';
	let debugInfo = [];

	function handleClick() {
		count++;
		message = `點擊了 ${count} 次`;
		debugInfo = [
			...debugInfo,
			`${new Date().toLocaleTimeString()}: handleClick called, count=${count}`
		];
		console.log('Button clicked:', count);
		alert('Button clicked! Count: ' + count);
	}

	function handleNativeClick() {
		count += 10;
		message = `原生按鈕點擊了，總計: ${count}`;
		debugInfo = [
			...debugInfo,
			`${new Date().toLocaleTimeString()}: handleNativeClick called, count=${count}`
		];
		console.log('Native button clicked:', count);
		alert('Native button clicked! Count: ' + count);
	}

	function handleUIButtonClick() {
		count += 5;
		message = `UI 按鈕點擊了，總計: ${count}`;
		debugInfo = [
			...debugInfo,
			`${new Date().toLocaleTimeString()}: handleUIButtonClick called, count=${count}`
		];
		console.log('UI Button clicked:', count);
		alert('UI Button clicked! Count: ' + count);
	}

	function resetTest() {
		count = 0;
		message = '測試已重置';
		debugInfo = [];
		console.log('Test reset');
		alert('Test reset!');
	}

	onMount(() => {
		debugInfo = [...debugInfo, `${new Date().toLocaleTimeString()}: Component mounted`];
		console.log('SimpleTest component mounted');
	});
</script>

<div class="p-6 space-y-4">
	<h2 class="text-xl font-bold">🔧 按鈕調試測試</h2>

	<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
		<strong>調試信息:</strong> 計數器: {count} | 狀態: {message}
	</div>

	<!-- 最基本的 HTML 測試 -->
	<div class="space-y-4 border-2 border-red-500 p-4 rounded bg-red-50">
		<h3 class="font-bold text-red-800">🚨 緊急調試 - 原生 HTML 按鈕</h3>

		<!-- 純 HTML 按鈕，使用 {@html} 繞過 Svelte 限制 -->
		<div>
			{@html `
				<button
					style="
						background: #ff6600;
						color: white;
						padding: 15px 30px;
						border: 3px solid #000;
						cursor: pointer;
						font-size: 18px;
						font-weight: bold;
						border-radius: 5px;
						position: relative;
						z-index: 9999;
						pointer-events: auto !important;
					"
					onclick="
						console.log('🧡 純 HTML onclick 觸發！');
						alert('🧡 純 HTML 按鈕點擊成功！');
						this.style.background = '#00ff00';
						this.innerText = '✅ HTML 成功！';
					"
				>
					🧡 純 HTML 按鈕測試
				</button>
			`}
		</div>

		<!-- 純 Svelte 按鈕測試 -->
		<button
			style="
				background: #ff0000;
				color: white;
				padding: 15px 30px;
				border: 3px solid #000;
				cursor: pointer;
				font-size: 18px;
				font-weight: bold;
				border-radius: 5px;
				position: relative;
				z-index: 9999;
				pointer-events: auto;
			"
			onmousedown={() => console.log('mousedown 事件觸發')}
			onmouseup={() => console.log('mouseup 事件觸發')}
			onmouseover={() => console.log('mouseover 事件觸發')}
			onclick={e => {
				console.log('🔴 Svelte onclick 觸發！');
				alert('🔴 按鈕點擊成功！這證明基本的 DOM 事件是工作的');
				e.target.style.background = '#00ff00';
				e.target.innerText = '✅ 點擊成功！';
			}}
		>
			🔴 點我測試 Svelte 事件
		</button>

		<!-- 測試 pointer-events -->
		<button
			style="
				background: #0066cc;
				color: white;
				padding: 15px 30px;
				border: 3px solid #000;
				cursor: pointer;
				font-size: 18px;
				font-weight: bold;
				border-radius: 5px;
				pointer-events: auto !important;
				z-index: 10000;
			"
			onclick={() => {
				console.log('🔵 pointer-events 測試成功！');
				alert('🔵 pointer-events 正常工作！');
			}}
		>
			🔵 測試 pointer-events
		</button>
	</div>

	<!-- Svelte 事件測試 -->
	<div class="space-y-4 border-2 border-blue-500 p-4 rounded bg-blue-50">
		<h3 class="font-bold text-blue-800">🔍 Svelte 事件測試</h3>

		<button
			style="background: purple; color: white; padding: 15px; border: none; cursor: pointer; font-size: 16px; pointer-events: auto !important;"
			onclick={() => {
				count++;
				message = `Svelte onclick 成功！點擊了 ${count} 次`;
				console.log('🟣 Svelte onclick 事件觸發:', count);
				alert(`🟣 Svelte onclick 成功！計數: ${count}`);
			}}
		>
			🟣 Svelte on:click 測試 (計數: {count})
		</button>

		<button
			style="background: orange; color: white; padding: 15px; border: none; cursor: pointer; font-size: 16px; pointer-events: auto !important;"
			onclick={() => {
				count += 10;
				message = `函數調用成功！總計: ${count}`;
				console.log('🟠 函數調用事件觸發:', count);
				alert(`🟠 函數調用成功！計數: ${count}`);
			}}
		>
			🟠 函數調用測試 (+10)
		</button>
	</div>

	<!-- 原生 HTML 按鈕 -->
	<div class="space-y-2">
		<h3 class="font-medium">原生 HTML 按鈕</h3>
		<button
			class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 cursor-pointer"
			onclick={handleNativeClick}
		>
			原生按鈕 (+10) - 總計: {count}
		</button>

		<button
			class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 cursor-pointer"
			onclick={handleClick}
		>
			原生按鈕 (+1)
		</button>
	</div>

	<!-- Svelte 事件處理 -->
	<div class="space-y-2">
		<h3 class="font-medium">Svelte 事件處理</h3>
		<button
			class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 cursor-pointer"
			onclick={handleClick}
		>
			Svelte 按鈕 (+1) - 總計: {count}
		</button>

		<button
			class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 cursor-pointer"
			onclick={resetTest}
		>
			重置測試
		</button>
	</div>

	<!-- UI 組件測試 -->
	<div class="space-y-2">
		<h3 class="font-medium">UI 組件按鈕</h3>
		<Button onclick={handleUIButtonClick}>UI 組件按鈕 (+5)</Button>

		<Button variant="outline" onclick={handleClick}>Outline 按鈕 (+1)</Button>

		<Button variant="destructive" onclick={resetTest}>重置測試</Button>

		<Button
			variant="secondary"
			onclick={() => {
				debugInfo = [];
				message = '調試信息已清除';
			}}
		>
			清除調試信息
		</Button>
	</div>

	<!-- 調試信息 -->
	<div class="mt-4 p-4 bg-gray-100 rounded">
		<h3 class="font-medium mb-2">調試信息</h3>
		<p class="text-sm">計數器: {count}</p>
		<p class="text-sm">消息: {message}</p>
		<p class="text-sm">時間: {new Date().toLocaleTimeString()}</p>

		<div class="mt-4">
			<h4 class="font-medium text-sm mb-2">事件日誌:</h4>
			<div class="max-h-32 overflow-y-auto bg-white p-2 rounded border text-xs">
				{#each debugInfo as info}
					<div class="mb-1">{info}</div>
				{:else}
					<div class="text-gray-500">沒有事件記錄</div>
				{/each}
			</div>
		</div>
	</div>
</div>
