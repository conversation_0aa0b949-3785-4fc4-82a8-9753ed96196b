<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import { <PERSON><PERSON><PERSON>, ZoomIn, ZoomOut, RotateCcw, Eye, EyeOff, Layers, Filter } from 'lucide-svelte';
	import { <PERSON><PERSON>, Card } from '$components/ui';
	import type { DependencyAnalysisOptions } from '$lib/services/dependencyService';

	export let options: DependencyAnalysisOptions;
	export let showLabels = true;
	export let showLegend = true;
	export let interactive = true;
	export let metadata: {
		totalNodes: number;
		totalLinks: number;
		clusters: number;
		density: number;
	} | null = null;

	const dispatch = createEventDispatcher<{
		optionsChange: { options: DependencyAnalysisOptions };
		toggleLabels: { show: boolean };
		toggleLegend: { show: boolean };
		toggleInteractive: { interactive: boolean };
		centerGraph: void;
		fitToView: void;
		refresh: void;
	}>();

	let showAdvanced = false;

	function updateOptions(updates: Partial<DependencyAnalysisOptions>) {
		const newOptions = { ...options, ...updates };
		dispatch('optionsChange', { options: newOptions });
	}

	function handleToggleLabels() {
		showLabels = !showLabels;
		dispatch('toggleLabels', { show: showLabels });
	}

	function handleToggleLegend() {
		showLegend = !showLegend;
		dispatch('toggleLegend', { show: showLegend });
	}

	function handleToggleInteractive() {
		interactive = !interactive;
		dispatch('toggleInteractive', { interactive });
	}

	function formatDensity(density: number): string {
		return (density * 100).toFixed(1) + '%';
	}
</script>

<div class="dependency-controls space-y-4">
	<!-- 主要控制 -->
	<Card class="p-4">
		<div class="flex items-center justify-between mb-4">
			<h3 class="text-lg font-semibold">依賴關係圖控制</h3>
			<Button variant="ghost" size="sm" on:click={() => (showAdvanced = !showAdvanced)}>
				<Settings class="h-4 w-4" />
			</Button>
		</div>

		<!-- 基本控制 -->
		<div class="grid grid-cols-2 md:grid-cols-4 gap-2 mb-4">
			<Button variant="outline" size="sm" on:click={() => dispatch('centerGraph')}>
				<RotateCcw class="h-4 w-4 mr-1" />
				重置視圖
			</Button>
			<Button variant="outline" size="sm" on:click={() => dispatch('fitToView')}>
				<ZoomIn class="h-4 w-4 mr-1" />
				適應視圖
			</Button>
			<Button variant="outline" size="sm" on:click={handleToggleLabels}>
				{#if showLabels}
					<EyeOff class="h-4 w-4 mr-1" />
					隱藏標籤
				{:else}
					<Eye class="h-4 w-4 mr-1" />
					顯示標籤
				{/if}
			</Button>
			<Button variant="outline" size="sm" on:click={handleToggleLegend}>
				<Layers class="h-4 w-4 mr-1" />
				{showLegend ? '隱藏' : '顯示'}圖例
			</Button>
		</div>

		<!-- 統計信息 -->
		{#if metadata}
			<div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
				<div class="text-center">
					<div class="text-2xl font-bold text-primary">{metadata.totalNodes}</div>
					<div class="text-muted-foreground">節點</div>
				</div>
				<div class="text-center">
					<div class="text-2xl font-bold text-primary">{metadata.totalLinks}</div>
					<div class="text-muted-foreground">連接</div>
				</div>
				<div class="text-center">
					<div class="text-2xl font-bold text-primary">{metadata.clusters}</div>
					<div class="text-muted-foreground">群組</div>
				</div>
				<div class="text-center">
					<div class="text-2xl font-bold text-primary">{formatDensity(metadata.density)}</div>
					<div class="text-muted-foreground">密度</div>
				</div>
			</div>
		{/if}
	</Card>

	<!-- 高級設置 -->
	{#if showAdvanced}
		<Card class="p-4">
			<div class="flex items-center gap-2 mb-4">
				<Filter class="h-4 w-4" />
				<h4 class="font-medium">分析選項</h4>
			</div>

			<div class="space-y-4">
				<!-- 連接類型 -->
				<div class="space-y-2">
					<h5 class="text-sm font-medium">連接類型</h5>
					<div class="space-y-2">
						<label class="flex items-center gap-2 text-sm">
							<input
								type="checkbox"
								checked={options.includeTagConnections}
								on:change={e => updateOptions({ includeTagConnections: e.currentTarget.checked })}
								class="rounded"
							/>
							<span>標籤連接</span>
							<span class="text-muted-foreground text-xs">基於共同標籤的連接</span>
						</label>
						<label class="flex items-center gap-2 text-sm">
							<input
								type="checkbox"
								checked={options.includeCategoryConnections}
								on:change={e =>
									updateOptions({ includeCategoryConnections: e.currentTarget.checked })}
								class="rounded"
							/>
							<span>分類連接</span>
							<span class="text-muted-foreground text-xs">基於相同分類的連接</span>
						</label>
						<label class="flex items-center gap-2 text-sm">
							<input
								type="checkbox"
								checked={options.includeContentSimilarity}
								on:change={e =>
									updateOptions({ includeContentSimilarity: e.currentTarget.checked })}
								class="rounded"
							/>
							<span>內容相似性</span>
							<span class="text-muted-foreground text-xs">基於內容相似度的連接</span>
						</label>
					</div>
				</div>

				<!-- 相似性閾值 -->
				{#if options.includeContentSimilarity}
					<div class="space-y-2">
						<label class="text-sm font-medium">
							相似性閾值: {(options.similarityThreshold * 100).toFixed(0)}%
						</label>
						<input
							type="range"
							min="0.1"
							max="0.9"
							step="0.1"
							value={options.similarityThreshold}
							on:input={e =>
								updateOptions({ similarityThreshold: parseFloat(e.currentTarget.value) })}
							class="w-full"
						/>
						<div class="flex justify-between text-xs text-muted-foreground">
							<span>寬鬆</span>
							<span>嚴格</span>
						</div>
					</div>
				{/if}

				<!-- 其他選項 -->
				<div class="space-y-2">
					<h5 class="text-sm font-medium">顯示選項</h5>
					<div class="space-y-2">
						<label class="flex items-center gap-2 text-sm">
							<input
								type="checkbox"
								checked={options.excludeIsolatedNodes}
								on:change={e => updateOptions({ excludeIsolatedNodes: e.currentTarget.checked })}
								class="rounded"
							/>
							<span>隱藏孤立節點</span>
							<span class="text-muted-foreground text-xs">不顯示沒有連接的節點</span>
						</label>
						<label class="flex items-center gap-2 text-sm">
							<input
								type="checkbox"
								checked={interactive}
								on:change={handleToggleInteractive}
								class="rounded"
							/>
							<span>交互模式</span>
							<span class="text-muted-foreground text-xs">啟用拖拽和縮放</span>
						</label>
					</div>
				</div>

				<!-- 最大距離 -->
				<div class="space-y-2">
					<label class="text-sm font-medium">
						最大跳躍距離: {options.maxDistance}
					</label>
					<input
						type="range"
						min="1"
						max="5"
						step="1"
						value={options.maxDistance}
						on:input={e => updateOptions({ maxDistance: parseInt(e.currentTarget.value) })}
						class="w-full"
					/>
					<div class="flex justify-between text-xs text-muted-foreground">
						<span>1</span>
						<span>2</span>
						<span>3</span>
						<span>4</span>
						<span>5</span>
					</div>
				</div>

				<!-- 應用按鈕 -->
				<div class="pt-2">
					<Button on:click={() => dispatch('refresh')} class="w-full">重新分析依賴關係</Button>
				</div>
			</div>
		</Card>
	{/if}

	<!-- 使用說明 -->
	<Card class="p-4 bg-muted/50">
		<h4 class="font-medium mb-2">使用說明</h4>
		<div class="text-sm text-muted-foreground space-y-1">
			<p>• <strong>拖拽節點</strong>：重新排列圖形布局</p>
			<p>• <strong>滾輪縮放</strong>：放大或縮小視圖</p>
			<p>• <strong>點擊節點</strong>：查看筆記詳情</p>
			<p>• <strong>懸停節點</strong>：高亮相關連接</p>
			<p>• <strong>點擊連接</strong>：查看關係詳情</p>
		</div>
	</Card>
</div>

<style>
	input[type='range'] {
		-webkit-appearance: none;
		appearance: none;
		height: 4px;
		background: #e5e7eb;
		border-radius: 2px;
		outline: none;
	}

	input[type='range']::-webkit-slider-thumb {
		-webkit-appearance: none;
		appearance: none;
		width: 16px;
		height: 16px;
		background: hsl(var(--primary));
		border-radius: 50%;
		cursor: pointer;
	}

	input[type='range']::-moz-range-thumb {
		width: 16px;
		height: 16px;
		background: hsl(var(--primary));
		border-radius: 50%;
		cursor: pointer;
		border: none;
	}
</style>
