#!/usr/bin/env node

/**
 * 簡化的 MCP 測試客戶端
 */

import { Client } from "@modelcontextprotocol/sdk/client/index.js";
import { StdioClientTransport } from "@modelcontextprotocol/sdk/client/stdio.js";
import { fileURLToPath } from "url";
import { dirname, join } from "path";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function testSimpleMCPServer() {
  console.log("🚀 Testing Simple MCP Server...\n");

  const client = new Client(
    {
      name: "simple-test-client",
      version: "1.0.0",
    },
    {
      capabilities: {},
    },
  );

  try {
    // 連接到服務器
    console.log("📡 Connecting to MCP server...");
    const serverPath = join(__dirname, "simple-server.js");
    const transport = new StdioClientTransport({
      command: "node",
      args: [serverPath],
    });

    await client.connect(transport);
    console.log("✅ Connected successfully!\n");

    // 測試工具列表
    console.log("🔧 Testing tools list...");
    const toolsResponse = await client.request({
      method: "tools/list",
      params: {},
    });

    console.log(`Found ${toolsResponse.tools.length} tools:`);
    toolsResponse.tools.forEach((tool) => {
      console.log(`  - ${tool.name}: ${tool.description}`);
    });
    console.log();

    // 測試資源列表
    console.log("📚 Testing resources list...");
    const resourcesResponse = await client.request({
      method: "resources/list",
      params: {},
    });

    console.log(`Found ${resourcesResponse.resources.length} resources:`);
    resourcesResponse.resources.forEach((resource) => {
      console.log(`  - ${resource.name} (${resource.uri})`);
    });
    console.log();

    // 測試創建筆記
    console.log("📝 Testing note creation...");
    const createResponse = await client.request({
      method: "tools/call",
      params: {
        name: "create_note",
        arguments: {
          title: "測試筆記",
          content: "這是一個測試筆記",
          tags: ["測試", "MCP"],
        },
      },
    });

    console.log("Create response:", createResponse.content[0].text);
    const createResult = JSON.parse(createResponse.content[0].text);
    const noteId = createResult.note.id;
    console.log();

    // 測試獲取筆記
    console.log("📖 Testing note retrieval...");
    const getResponse = await client.request({
      method: "tools/call",
      params: {
        name: "get_note",
        arguments: {
          id: noteId,
        },
      },
    });

    console.log("Get response:", getResponse.content[0].text);
    console.log();

    // 測試列出筆記
    console.log("📋 Testing list notes...");
    const listResponse = await client.request({
      method: "tools/call",
      params: {
        name: "list_notes",
        arguments: {
          limit: 10,
        },
      },
    });

    console.log("List response:", listResponse.content[0].text);
    console.log();

    // 測試讀取資源
    console.log("📊 Testing resource reading...");
    const resourceResponse = await client.request({
      method: "resources/read",
      params: {
        uri: "notes://all",
      },
    });

    console.log("Resource response:", resourceResponse.contents[0].text);
    console.log();

    console.log("✅ All tests passed!");
  } catch (error) {
    console.error("❌ Test failed:", error);
    process.exit(1);
  } finally {
    try {
      await client.close();
      console.log("🔌 Disconnected from server");
    } catch (error) {
      console.error("Error closing client:", error);
    }
  }
}

// 運行測試
testSimpleMCPServer().catch((error) => {
  console.error("Test execution failed:", error);
  process.exit(1);
});
