 Dependencies
node_modules/
.pnpm-store/
.npm/
.yarn/

# Build outputs
dist/
build/
.next/
.nuxt/
.output/
.vercel/
.netlify/
.svelte-kit/

# Generated files
*.tsbuildinfo
.turbo/
.cache/

# Logs
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov
.nyc_output/

# Instrumentation
.instrumentation.js

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Package manager
package-lock.json
yarn.lock

# Tauri
src-tauri/target/
src-tauri/Cargo.lock

# Database
*.db
*.sqlite
*.sqlite3
dev.db
test.db
prod.db

# Prisma
packages/storage/src/prisma/generated/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Temporary files
*.tmp
*.temp
.temp/
.tmp/

# Local development
.local/
.cache/

# Testing
.jest/
.vitest/

# Storybook
storybook-static/

# Capacitor
.capacitor/
android/
ios/

# Electron
electron-dist/

# Sentry
.sentryclirc
