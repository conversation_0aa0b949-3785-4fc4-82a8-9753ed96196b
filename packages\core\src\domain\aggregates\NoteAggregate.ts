/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  AggregateRoot,
  BusinessRuleViolationError,
  DomainEvent,
} from "../../shared/Entity.js";
import {
  Dependency,
  DependencyType,
  DependencyStrength,
} from "../entities/Dependency.js";
import { Note, NoteStatus, NotePriority } from "../entities/Note.js";
import { User } from "../entities/User.js";
import { NoteId } from "../value-objects/NoteId.js";
import { Tag } from "../value-objects/Tag.js";
import { UserId } from "../value-objects/UserId.js";
import { Version } from "../value-objects/Version.js";
import { FilePath } from "../value-objects/FilePath.js";
import { Checksum } from "../value-objects/Checksum.js";

/**
 * 筆記領域事件
 */
export class NoteCreatedEvent extends DomainEvent {
  constructor(
    public readonly noteId: NoteId,
    public readonly authorId: UserId,
    public readonly title: string,
  ) {
    super(noteId.value, "NoteCreated");
  }

  toPlainObject(): Record<string, unknown> {
    return {
      aggregateId: this.aggregateId,
      eventType: this.eventType,
      occurredOn: this.occurredOn.toISOString(),
      noteId: this.noteId.value,
      authorId: this.authorId.value,
      title: this.title,
    };
  }
}

export class NoteUpdatedEvent extends DomainEvent {
  constructor(
    public readonly noteId: NoteId,
    public readonly changes: Record<string, unknown>,
  ) {
    super(noteId.value, "NoteUpdated");
  }

  toPlainObject(): Record<string, unknown> {
    return {
      aggregateId: this.aggregateId,
      eventType: this.eventType,
      occurredOn: this.occurredOn.toISOString(),
      noteId: this.noteId.value,
      changes: this.changes,
    };
  }
}

export class NotePublishedEvent extends DomainEvent {
  constructor(
    public readonly noteId: NoteId,
    public readonly version: Version,
  ) {
    super(noteId.value, "NotePublished");
  }

  toPlainObject(): Record<string, unknown> {
    return {
      aggregateId: this.aggregateId,
      eventType: this.eventType,
      occurredOn: this.occurredOn.toISOString(),
      noteId: this.noteId.value,
      version: this.version.toPlainObject(),
    };
  }
}

export class NoteArchivedEvent extends DomainEvent {
  constructor(public readonly noteId: NoteId) {
    super(noteId.value, "NoteArchived");
  }

  toPlainObject(): Record<string, unknown> {
    return {
      aggregateId: this.aggregateId,
      eventType: this.eventType,
      occurredOn: this.occurredOn.toISOString(),
      noteId: this.noteId.value,
    };
  }
}

export class DependencyAddedEvent extends DomainEvent {
  constructor(
    public readonly sourceNoteId: NoteId,
    public readonly targetNoteId: NoteId,
    public readonly dependencyType: DependencyType,
  ) {
    super(sourceNoteId.value, "DependencyAdded");
  }

  toPlainObject(): Record<string, unknown> {
    return {
      aggregateId: this.aggregateId,
      eventType: this.eventType,
      occurredOn: this.occurredOn.toISOString(),
      sourceNoteId: this.sourceNoteId.value,
      targetNoteId: this.targetNoteId.value,
      dependencyType: this.dependencyType,
    };
  }
}

export class DependencyRemovedEvent extends DomainEvent {
  constructor(
    public readonly sourceNoteId: NoteId,
    public readonly targetNoteId: NoteId,
    public readonly dependencyType: DependencyType,
  ) {
    super(sourceNoteId.value, "DependencyRemoved");
  }

  toPlainObject(): Record<string, unknown> {
    return {
      aggregateId: this.aggregateId,
      eventType: this.eventType,
      occurredOn: this.occurredOn.toISOString(),
      sourceNoteId: this.sourceNoteId.value,
      targetNoteId: this.targetNoteId.value,
      dependencyType: this.dependencyType,
    };
  }
}

/**
 * 筆記聚合根
 * 管理筆記及其相關的依賴關係、版本歷史等
 */
export class NoteAggregate extends AggregateRoot<NoteId> {
  private _note: Note;
  private _dependencies: Dependency[];
  private _dependents: Dependency[]; // 依賴於此筆記的其他筆記
  private _versionHistory: Version[];
  private _authorId: UserId;

  constructor(
    note: Note,
    authorId: UserId,
    options: {
      dependencies?: Dependency[];
      dependents?: Dependency[];
      versionHistory?: Version[];
    } = {},
  ) {
    super(note.id, note.createdAt);

    this._note = note;
    this._authorId = authorId;
    this._dependencies = options.dependencies || [];
    this._dependents = options.dependents || [];
    this._versionHistory = options.versionHistory || [note.version];
  }

  /**
   * 創建新的筆記聚合
   */
  static create(
    title: string,
    content: string,
    authorId: UserId,
    options: {
      category?: string;
      tags?: Tag[];
      priority?: NotePriority;
      metadata?: Record<string, unknown>;
    } = {},
  ): NoteAggregate {
    const note = Note.create({
      title,
      content,
      category: options.category,
      tags: options.tags,
      priority: options.priority,
      metadata: options.metadata,
    });

    const aggregate = new NoteAggregate(note, authorId);
    aggregate.addDomainEvent(new NoteCreatedEvent(note.id, authorId, title));
    return aggregate;
  }

  /**
   * 更新筆記內容
   */
  updateNote(params: {
    title?: string;
    content?: string;
    category?: string;
    tags?: Tag[];
    priority?: NotePriority;
    metadata?: Record<string, unknown>;
  }): void {
    this._note.update(params);
    this.addDomainEvent(new NoteUpdatedEvent(this._note.id, params));
    this.markAsModified();
  }

  /**
   * 發布筆記
   */
  publishNote(): void {
    this._note.publish();
    this._versionHistory.push(this._note.version);
    this.addDomainEvent(
      new NotePublishedEvent(this._note.id, this._note.version),
    );
    this.markAsModified();
  }

  /**
   * 創建新的草稿版本
   */
  createDraftVersion(): void {
    this._note.createDraft();
    this.markAsModified();
  }

  /**
   * 歸檔筆記
   */
  archiveNote(): void {
    this._note.archive();
    this.addDomainEvent(new NoteArchivedEvent(this._note.id));
    this.markAsModified();
  }

  /**
   * 添加依賴關係
   */
  addDependency(
    targetNoteId: NoteId,
    type: DependencyType,
    options: {
      strength?: DependencyStrength;
      description?: string;
      metadata?: Record<string, unknown>;
    } = {},
  ): void {
    // 檢查是否已存在相同的依賴關係
    const existingDependency = this._dependencies.find(
      (dep) => dep.targetNoteId.equals(targetNoteId) && dep.type === type,
    );

    if (existingDependency) {
      throw new BusinessRuleViolationError("Dependency already exists");
    }

    const dependency = Dependency.create({
      sourceNoteId: this._note.id,
      targetNoteId,
      type,
      strength: options.strength,
      description: options.description,
      metadata: options.metadata,
    });

    // 檢查是否會創建循環依賴
    if (
      dependency.wouldCreateCycle([...this._dependencies, ...this._dependents])
    ) {
      throw new BusinessRuleViolationError(
        "Adding this dependency would create a cycle",
      );
    }

    this._dependencies.push(dependency);
    this.addDomainEvent(
      new DependencyAddedEvent(this._note.id, targetNoteId, type),
    );
    this.markAsModified();
  }

  /**
   * 移除依賴關係
   */
  removeDependency(targetNoteId: NoteId, type: DependencyType): void {
    const index = this._dependencies.findIndex(
      (dep) => dep.targetNoteId.equals(targetNoteId) && dep.type === type,
    );

    if (index === -1) {
      throw new BusinessRuleViolationError("Dependency not found");
    }

    this._dependencies.splice(index, 1);
    this.addDomainEvent(
      new DependencyRemovedEvent(this._note.id, targetNoteId, type),
    );
    this.markAsModified();
  }

  /**
   * 添加被依賴關係（其他筆記依賴於此筆記）
   */
  addDependent(dependency: Dependency): void {
    if (!dependency.targetNoteId.equals(this._note.id)) {
      throw new BusinessRuleViolationError(
        "Dependency target must be this note",
      );
    }

    this._dependents.push(dependency);
    this.markAsModified();
  }

  /**
   * 移除被依賴關係
   */
  removeDependent(sourceNoteId: NoteId, type: DependencyType): void {
    const index = this._dependents.findIndex(
      (dep) => dep.sourceNoteId.equals(sourceNoteId) && dep.type === type,
    );

    if (index !== -1) {
      this._dependents.splice(index, 1);
      this.markAsModified();
    }
  }

  /**
   * 獲取所有依賴的筆記 ID
   */
  getDependencyNoteIds(): NoteId[] {
    return this._dependencies
      .filter((dep) => dep.isActive)
      .map((dep) => dep.targetNoteId);
  }

  /**
   * 獲取所有依賴於此筆記的筆記 ID
   */
  getDependentNoteIds(): NoteId[] {
    return this._dependents
      .filter((dep) => dep.isActive)
      .map((dep) => dep.sourceNoteId);
  }

  /**
   * 獲取強依賴關係
   */
  getStrongDependencies(): Dependency[] {
    return this._dependencies.filter((dep) => dep.isStrong && dep.isActive);
  }

  /**
   * 獲取關鍵依賴關係
   */
  getCriticalDependencies(): Dependency[] {
    return this._dependencies.filter((dep) => dep.isCritical && dep.isActive);
  }

  /**
   * 檢查是否可以安全刪除
   */
  canSafelyDelete(): boolean {
    // 如果有其他筆記依賴於此筆記，則不能安全刪除
    const activeDependents = this._dependents.filter((dep) => dep.isActive);
    return activeDependents.length === 0;
  }

  /**
   * 獲取依賴關係統計
   */
  getDependencyStats(): {
    totalDependencies: number;
    activeDependencies: number;
    strongDependencies: number;
    criticalDependencies: number;
    totalDependents: number;
    activeDependents: number;
  } {
    const activeDependencies = this._dependencies.filter((dep) => dep.isActive);
    const activeDependents = this._dependents.filter((dep) => dep.isActive);

    return {
      totalDependencies: this._dependencies.length,
      activeDependencies: activeDependencies.length,
      strongDependencies: activeDependencies.filter((dep) => dep.isStrong)
        .length,
      criticalDependencies: activeDependencies.filter((dep) => dep.isCritical)
        .length,
      totalDependents: this._dependents.length,
      activeDependents: activeDependents.length,
    };
  }

  /**
   * 設置筆記文件路徑
   */
  setFilePath(filePath: string): void {
    const path = new FilePath(filePath);
    this._note.setFilePath(path.value);
    this.markAsModified();
  }

  /**
   * 設置筆記校驗和
   */
  setChecksum(checksum: string, algorithm: string = "sha256"): void {
    const checksumObj = new Checksum(checksum, algorithm as any);
    this._note.setChecksum(checksumObj.value);
    this.markAsModified();
  }

  /**
   * 驗證筆記完整性
   */
  verifyIntegrity(expectedChecksum: string): boolean {
    return this._note.checksum === expectedChecksum;
  }

  /**
   * 克隆筆記（創建副本）
   */
  clone(newTitle?: string): NoteAggregate {
    const clonedNote = Note.create({
      title: newTitle || `${this._note.title} (Copy)`,
      content: this._note.content,
      category: this._note.category,
      tags: this._note.tags,
      priority: this._note.priority,
      metadata: { ...this._note.metadata, originalNoteId: this._note.id.value },
    });

    return new NoteAggregate(clonedNote, this._authorId);
  }

  /**
   * 檢查筆記是否已過期（基於元數據中的過期時間）
   */
  isExpired(): boolean {
    const expiresAt = this._note.metadata.expiresAt;
    if (!expiresAt || typeof expiresAt !== "string") return false;

    try {
      const expirationDate = new Date(expiresAt);
      return expirationDate < new Date();
    } catch {
      return false;
    }
  }

  /**
   * 設置筆記過期時間
   */
  setExpirationDate(date: Date): void {
    this._note.update({
      metadata: { ...this._note.metadata, expiresAt: date.toISOString() },
    });
    this.markAsModified();
  }

  /**
   * 獲取筆記字數統計
   */
  getWordCount(): number {
    const content = this._note.content;
    // 簡單的字數統計（中英文混合）
    const words = content.match(/[\w\u4e00-\u9fff]+/g);
    return words ? words.length : 0;
  }

  /**
   * 獲取筆記字符統計
   */
  getCharacterCount(): {
    total: number;
    withoutSpaces: number;
    lines: number;
  } {
    const content = this._note.content;
    return {
      total: content.length,
      withoutSpaces: content.replace(/\s/g, "").length,
      lines: content.split("\n").length,
    };
  }

  /**
   * 檢查筆記是否包含特定標籤
   */
  hasTag(tagName: string): boolean {
    return this._note.tags.some((tag) => tag.name === tagName.toLowerCase());
  }

  /**
   * 添加標籤
   */
  addTag(tag: Tag): void {
    if (!this.hasTag(tag.name)) {
      this._note.addTag(tag);
      this.markAsModified();
    }
  }

  /**
   * 移除標籤
   */
  removeTag(tagName: string): void {
    this._note.removeTag(tagName);
    this.markAsModified();
  }

  // Getters
  get note(): Note {
    return this._note;
  }

  get authorId(): UserId {
    return this._authorId;
  }

  get dependencies(): Dependency[] {
    return [...this._dependencies];
  }

  get dependents(): Dependency[] {
    return [...this._dependents];
  }

  get versionHistory(): Version[] {
    return [...this._versionHistory];
  }

  get currentVersion(): Version {
    return this._note.version;
  }

  /**
   * 轉換為普通對象
   */
  toPlainObject(): Record<string, unknown> {
    return {
      id: this._id.value,
      note: this._note.toPlainObject(),
      authorId: this._authorId.value,
      dependencies: this._dependencies.map((dep) => dep.toPlainObject()),
      dependents: this._dependents.map((dep) => dep.toPlainObject()),
      versionHistory: this._versionHistory.map((version) => version.toString()),
      createdAt: this._createdAt.toISOString(),
      updatedAt: this._updatedAt.toISOString(),
    };
  }
}
