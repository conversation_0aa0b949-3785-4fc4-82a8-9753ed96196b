/**
 * AI Agent 相關類型定義
 */

/**
 * Agent 任務類型
 */
export type AgentTaskType =
	| 'note_analysis' // 筆記分析
	| 'dependency_check' // 依賴關係檢查
	| 'content_generation' // 內容生成
	| 'search_query' // 搜索查詢
	| 'summarization' // 摘要生成
	| 'translation' // 翻譯
	| 'code_analysis' // 代碼分析
	| 'text_processing' // 文本處理
	| 'question_answering' // 問答
	| 'classification' // 分類
	| 'extraction' // 信息提取
	| 'recommendation'; // 推薦

/**
 * Agent 能力級別
 */
export type AgentCapabilityLevel = 'basic' | 'intermediate' | 'advanced' | 'expert';

/**
 * Agent 執行模式
 */
export type AgentExecutionMode = 'local' | 'remote' | 'hybrid';

/**
 * Agent 狀態
 */
export type AgentStatus = 'idle' | 'busy' | 'error' | 'offline';

/**
 * Agent 任務優先級
 */
export type TaskPriority = 'low' | 'normal' | 'high' | 'urgent';

/**
 * Agent 任務狀態
 */
export type TaskStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';

/**
 * Agent 任務請求
 */
export interface AgentTaskRequest {
	id: string;
	type: AgentTaskType;
	priority: TaskPriority;
	input: any;
	context?: Record<string, any>;
	options?: AgentTaskOptions;
	createdAt: Date;
	timeout?: number; // 超時時間（毫秒）
}

/**
 * Agent 任務選項
 */
export interface AgentTaskOptions {
	maxTokens?: number;
	temperature?: number;
	model?: string;
	streaming?: boolean;
	includeContext?: boolean;
	language?: string;
	format?: 'text' | 'json' | 'markdown';
}

/**
 * Agent 任務結果
 */
export interface AgentTaskResult {
	id: string;
	taskId: string;
	status: TaskStatus;
	output?: any;
	error?: string;
	metadata?: Record<string, any>;
	executionTime: number;
	completedAt: Date;
	agentId: string;
}

/**
 * Agent 配置
 */
export interface AgentConfig {
	id: string;
	name: string;
	description: string;
	capabilities: AgentTaskType[];
	capabilityLevel: AgentCapabilityLevel;
	executionMode: AgentExecutionMode;
	maxConcurrentTasks: number;
	defaultTimeout: number;
	modelConfig?: ModelConfig;
	enabled: boolean;
}

/**
 * 模型配置
 */
export interface ModelConfig {
	provider: 'local' | 'openai' | 'anthropic' | 'google' | 'custom';
	model: string;
	apiKey?: string;
	baseUrl?: string;
	maxTokens?: number;
	temperature?: number;
	topP?: number;
	frequencyPenalty?: number;
	presencePenalty?: number;
}

/**
 * Agent 性能指標
 */
export interface AgentMetrics {
	agentId: string;
	totalTasks: number;
	completedTasks: number;
	failedTasks: number;
	averageExecutionTime: number;
	successRate: number;
	lastActiveAt: Date;
	uptime: number;
}

/**
 * Agent 基礎接口
 */
export interface IAgent {
	readonly id: string;
	readonly config: AgentConfig;
	readonly status: AgentStatus;
	readonly metrics: AgentMetrics;

	/**
	 * 初始化 Agent
	 */
	initialize(): Promise<void>;

	/**
	 * 執行任務
	 */
	executeTask(request: AgentTaskRequest): Promise<AgentTaskResult>;

	/**
	 * 檢查是否支援任務類型
	 */
	canHandle(taskType: AgentTaskType): boolean;

	/**
	 * 獲取當前負載
	 */
	getCurrentLoad(): number;

	/**
	 * 停止 Agent
	 */
	stop(): Promise<void>;

	/**
	 * 更新配置
	 */
	updateConfig(config: Partial<AgentConfig>): Promise<void>;
}

/**
 * Agent 管理器接口
 */
export interface IAgentManager {
	/**
	 * 註冊 Agent
	 */
	registerAgent(agent: IAgent): Promise<void>;

	/**
	 * 取消註冊 Agent
	 */
	unregisterAgent(agentId: string): Promise<void>;

	/**
	 * 獲取 Agent
	 */
	getAgent(agentId: string): IAgent | null;

	/**
	 * 獲取所有 Agent
	 */
	getAllAgents(): IAgent[];

	/**
	 * 根據任務類型獲取可用 Agent
	 */
	getAvailableAgents(taskType: AgentTaskType): IAgent[];

	/**
	 * 選擇最佳 Agent
	 */
	selectBestAgent(request: AgentTaskRequest): IAgent | null;

	/**
	 * 執行任務
	 */
	executeTask(request: AgentTaskRequest): Promise<AgentTaskResult>;

	/**
	 * 獲取系統狀態
	 */
	getSystemStatus(): AgentSystemStatus;
}

/**
 * Agent 系統狀態
 */
export interface AgentSystemStatus {
	totalAgents: number;
	activeAgents: number;
	totalTasks: number;
	runningTasks: number;
	queuedTasks: number;
	systemLoad: number;
	uptime: number;
}

/**
 * 智能路由配置
 */
export interface SmartRoutingConfig {
	localThreshold: number; // 本地處理閾值
	remoteThreshold: number; // 遠程處理閾值
	complexityWeights: {
		textLength: number;
		taskComplexity: number;
		contextSize: number;
		realTimeRequirement: number;
	};
	fallbackStrategy: 'local' | 'remote' | 'queue';
	maxRetries: number;
	retryDelay: number;
}

/**
 * 任務複雜度評估結果
 */
export interface TaskComplexityAssessment {
	score: number; // 複雜度分數 (0-1)
	factors: {
		textLength: number;
		semanticComplexity: number;
		contextRequirement: number;
		computationalLoad: number;
	};
	recommendedMode: AgentExecutionMode;
	confidence: number;
}

/**
 * Agent 事件類型
 */
export type AgentEventType =
	| 'agent_registered'
	| 'agent_unregistered'
	| 'task_started'
	| 'task_completed'
	| 'task_failed'
	| 'agent_status_changed'
	| 'system_overload'
	| 'routing_decision';

/**
 * Agent 事件
 */
export interface AgentEvent {
	type: AgentEventType;
	agentId?: string;
	taskId?: string;
	data?: any;
	timestamp: Date;
}

/**
 * Agent 事件監聽器
 */
export type AgentEventListener = (event: AgentEvent) => void;

/**
 * 本地 AI 模型配置
 */
export interface LocalModelConfig {
	modelPath: string;
	modelType: 'onnx' | 'transformers' | 'wasm';
	tokenizer?: string;
	maxSequenceLength: number;
	batchSize: number;
	numThreads?: number;
	useGPU?: boolean;
	quantization?: 'int8' | 'fp16' | 'fp32';
}

/**
 * 遠程 AI 服務配置
 */
export interface RemoteServiceConfig {
	provider: string;
	endpoint: string;
	apiKey: string;
	model: string;
	timeout: number;
	retryAttempts: number;
	rateLimitPerMinute: number;
}

/**
 * Agent 工廠接口
 */
export interface IAgentFactory {
	createLocalAgent(config: AgentConfig & { localModel: LocalModelConfig }): Promise<IAgent>;
	createRemoteAgent(config: AgentConfig & { remoteService: RemoteServiceConfig }): Promise<IAgent>;
	createHybridAgent(
		config: AgentConfig & {
			localModel: LocalModelConfig;
			remoteService: RemoteServiceConfig;
			routingConfig: SmartRoutingConfig;
		}
	): Promise<IAgent>;
}

/**
 * 任務隊列接口
 */
export interface ITaskQueue {
	enqueue(request: AgentTaskRequest): Promise<void>;
	dequeue(): Promise<AgentTaskRequest | null>;
	peek(): Promise<AgentTaskRequest | null>;
	size(): number;
	clear(): Promise<void>;
	getQueuedTasks(): AgentTaskRequest[];
}

/**
 * Agent 持久化接口
 */
export interface IAgentPersistence {
	saveAgentConfig(config: AgentConfig): Promise<void>;
	loadAgentConfig(agentId: string): Promise<AgentConfig | null>;
	saveTaskResult(result: AgentTaskResult): Promise<void>;
	loadTaskHistory(agentId: string, limit?: number): Promise<AgentTaskResult[]>;
	saveMetrics(metrics: AgentMetrics): Promise<void>;
	loadMetrics(agentId: string): Promise<AgentMetrics | null>;
}
