# MCP Server 實作規範

## MCP Server 實作規範

### 整體架構概覽

基於 Model Context Protocol (MCP) 標準，實現本地知識庫的 AI 整合服務：

- **STDIO 通訊協定**：標準輸入輸出通訊
- **HTTP 傳輸協定**：支援 Streamable HTTP 和 SSE
- **工具和資源註冊**：筆記管理、依賴分析、搜索功能
- **安全性和權限控制**：細粒度的訪問控制

## Context7 搜索的 MCP 最新實現

### 基於 TypeScript SDK 的 MCP Server

基於 `/modelcontextprotocol/typescript-sdk` 的最佳實踐：

```typescript
// src/mcp/server.ts
import {
  McpServer,
  ResourceTemplate,
} from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { StreamableHTTPServerTransport } from "@modelcontextprotocol/sdk/server/streamableHttp.js";
import { z } from "zod";

import { noteService } from "@/services/noteService";
import { dependencyService } from "@/services/dependencyService";
import { searchService } from "@/services/searchService";
import { aiService } from "@/services/aiService";
import type { Note, Dependency, SearchResult } from "@/types";

class LifeNoteMCPServer {
  private server: McpServer;
  private isInitialized = false;

  constructor() {
    this.server = new McpServer({
      name: "life-note-mcp-server",
      version: "1.0.0",
    });

    this.setupCapabilities();
    this.registerTools();
    this.registerResources();
    this.registerPrompts();
  }

  private setupCapabilities(): void {
    // 設置服務器能力
    this.server.setCapabilities({
      tools: {},
      resources: {
        subscribe: true,
        listChanged: true,
      },
      prompts: {},
      logging: {},
    });
  }

  private registerTools(): void {
    // 筆記創建工具
    this.server.registerTool(
      "create-note",
      {
        title: "創建筆記",
        description: "創建新的筆記文檔",
        inputSchema: {
          title: z.string().min(1, "標題不能為空").max(200, "標題過長"),
          content: z.string().max(50000, "內容過長"),
          tags: z.array(z.string()).optional().default([]),
          category: z.string().optional(),
          isPrivate: z.boolean().optional().default(false),
        },
      },
      async ({ title, content, tags, category, isPrivate }) => {
        try {
          const note = await noteService.create({
            title,
            content,
            tags,
            category,
            isPrivate,
            status: "draft",
          });

          // 觸發資源變更通知
          this.server.notification({
            method: "notifications/resources/list_changed",
            params: {},
          });

          return {
            content: [
              {
                type: "text",
                text: `✅ 筆記創建成功！\n\n**標題**: ${note.title}\n**ID**: ${note.id}\n**標籤**: ${tags.join(", ") || "無"}\n**狀態**: 草稿`,
              },
            ],
          };
        } catch (error) {
          return {
            content: [
              {
                type: "text",
                text: `❌ 創建筆記失敗: ${error instanceof Error ? error.message : "未知錯誤"}`,
              },
            ],
            isError: true,
          };
        }
      },
    );

    // 筆記搜索工具
    this.server.registerTool(
      "search-notes",
      {
        title: "搜索筆記",
        description: "在筆記庫中搜索相關內容",
        inputSchema: {
          query: z.string().min(1, "搜索查詢不能為空"),
          tags: z.array(z.string()).optional(),
          category: z.string().optional(),
          limit: z.number().min(1).max(50).optional().default(10),
          includeContent: z.boolean().optional().default(false),
        },
      },
      async ({ query, tags, category, limit, includeContent }) => {
        try {
          const results = await searchService.search({
            query,
            tags,
            category,
            limit,
          });

          if (results.length === 0) {
            return {
              content: [
                {
                  type: "text",
                  text: `🔍 未找到匹配 "${query}" 的筆記`,
                },
              ],
            };
          }

          const content = [
            {
              type: "text",
              text: `🔍 找到 ${results.length} 個匹配結果：\n\n`,
            },
          ];

          // 添加搜索結果
          results.forEach((result, index) => {
            content.push({
              type: "text",
              text: `**${index + 1}. ${result.title}**\n`,
            });

            if (includeContent && result.snippet) {
              content.push({
                type: "text",
                text: `${result.snippet}\n\n`,
              });
            }

            // 添加資源鏈接
            content.push({
              type: "resource",
              resource: {
                uri: `note://${result.id}`,
                text: `查看完整筆記: ${result.title}`,
              },
            });
          });

          return { content };
        } catch (error) {
          return {
            content: [
              {
                type: "text",
                text: `❌ 搜索失敗: ${error instanceof Error ? error.message : "未知錯誤"}`,
              },
            ],
            isError: true,
          };
        }
      },
    );

    // 依賴分析工具
    this.server.registerTool(
      "analyze-dependencies",
      {
        title: "分析依賴關係",
        description: "分析筆記之間的依賴關係",
        inputSchema: {
          noteId: z.string().min(1, "筆記ID不能為空"),
          depth: z.number().min(1).max(5).optional().default(2),
          includeIncoming: z.boolean().optional().default(true),
          includeOutgoing: z.boolean().optional().default(true),
        },
      },
      async ({ noteId, depth, includeIncoming, includeOutgoing }) => {
        try {
          const dependencies = await dependencyService.analyze(noteId, {
            depth,
            includeIncoming,
            includeOutgoing,
          });

          const content = [
            {
              type: "text",
              text: `📊 筆記依賴關係分析結果：\n\n`,
            },
          ];

          if (dependencies.incoming.length > 0) {
            content.push({
              type: "text",
              text: `**被依賴 (${dependencies.incoming.length}):**\n`,
            });
            dependencies.incoming.forEach((dep) => {
              content.push({
                type: "text",
                text: `- ${dep.sourceTitle} → 當前筆記\n`,
              });
            });
            content.push({ type: "text", text: "\n" });
          }

          if (dependencies.outgoing.length > 0) {
            content.push({
              type: "text",
              text: `**依賴他人 (${dependencies.outgoing.length}):**\n`,
            });
            dependencies.outgoing.forEach((dep) => {
              content.push({
                type: "text",
                text: `- 當前筆記 → ${dep.targetTitle}\n`,
              });
            });
          }

          if (dependencies.circular.length > 0) {
            content.push({
              type: "text",
              text: `\n⚠️ **循環依賴 (${dependencies.circular.length}):**\n`,
            });
            dependencies.circular.forEach((cycle) => {
              content.push({
                type: "text",
                text: `- ${cycle.path.join(" → ")}\n`,
              });
            });
          }

          return { content };
        } catch (error) {
          return {
            content: [
              {
                type: "text",
                text: `❌ 依賴分析失敗: ${error instanceof Error ? error.message : "未知錯誤"}`,
              },
            ],
            isError: true,
          };
        }
      },
    );

    // AI 輔助分析工具
    this.server.registerTool(
      "ai-analyze-note",
      {
        title: "AI 筆記分析",
        description: "使用 AI 分析筆記內容並提供建議",
        inputSchema: {
          noteId: z.string().min(1, "筆記ID不能為空"),
          analysisType: z
            .enum(["summary", "tags", "improvements", "connections"])
            .default("summary"),
          includeContext: z.boolean().optional().default(true),
        },
      },
      async ({ noteId, analysisType, includeContext }) => {
        try {
          const note = await noteService.findById(noteId);
          if (!note) {
            throw new Error("筆記不存在");
          }

          let context: string[] = [];
          if (includeContext) {
            const relatedNotes = await searchService.findRelated(noteId, 5);
            context = relatedNotes.map(
              (n) => `${n.title}: ${n.content.substring(0, 200)}`,
            );
          }

          const analysis = await aiService.analyzeNote(note.content, {
            type: analysisType,
            context: context.join("\n"),
          });

          return {
            content: [
              {
                type: "text",
                text: `🤖 AI 分析結果 (${analysisType}):\n\n${analysis.result}`,
              },
            ],
          };
        } catch (error) {
          return {
            content: [
              {
                type: "text",
                text: `❌ AI 分析失敗: ${error instanceof Error ? error.message : "未知錯誤"}`,
              },
            ],
            isError: true,
          };
        }
      },
    );
  }

  private registerResources(): void {
    // 筆記資源
    this.server.registerResource(
      "note",
      new ResourceTemplate("note://{noteId}", { list: undefined }),
      {
        title: "筆記資源",
        description: "獲取特定筆記的完整內容",
        mimeType: "text/markdown",
      },
      async (uri, { noteId }) => {
        try {
          const note = await noteService.findById(noteId);
          if (!note) {
            throw new Error(`筆記不存在: ${noteId}`);
          }

          const markdown = this.formatNoteAsMarkdown(note);

          return {
            contents: [
              {
                uri: uri.href,
                mimeType: "text/markdown",
                text: markdown,
              },
            ],
          };
        } catch (error) {
          throw new Error(
            `獲取筆記失敗: ${error instanceof Error ? error.message : "未知錯誤"}`,
          );
        }
      },
    );

    // 筆記列表資源
    this.server.registerResource(
      "notes-list",
      "notes://list",
      {
        title: "筆記列表",
        description: "獲取所有筆記的列表",
        mimeType: "application/json",
      },
      async (uri) => {
        try {
          const notes = await noteService.findAll({ limit: 100 });

          return {
            contents: [
              {
                uri: uri.href,
                mimeType: "application/json",
                text: JSON.stringify(
                  notes.map((note) => ({
                    id: note.id,
                    title: note.title,
                    tags: note.tags,
                    category: note.category,
                    status: note.status,
                    createdAt: note.createdAt,
                    updatedAt: note.updatedAt,
                  })),
                  null,
                  2,
                ),
              },
            ],
          };
        } catch (error) {
          throw new Error(
            `獲取筆記列表失敗: ${error instanceof Error ? error.message : "未知錯誤"}`,
          );
        }
      },
    );

    // 依賴圖資源
    this.server.registerResource(
      "dependency-graph",
      "dependencies://graph",
      {
        title: "依賴關係圖",
        description: "獲取完整的筆記依賴關係圖",
        mimeType: "application/json",
      },
      async (uri) => {
        try {
          const graph = await dependencyService.getFullGraph();

          return {
            contents: [
              {
                uri: uri.href,
                mimeType: "application/json",
                text: JSON.stringify(graph, null, 2),
              },
            ],
          };
        } catch (error) {
          throw new Error(
            `獲取依賴圖失敗: ${error instanceof Error ? error.message : "未知錯誤"}`,
          );
        }
      },
    );
  }

  private registerPrompts(): void {
    // 筆記創建提示
    this.server.registerPrompt(
      "create-note-prompt",
      {
        title: "筆記創建助手",
        description: "幫助用戶創建結構化的筆記",
        argsSchema: {
          topic: z.string().min(1, "主題不能為空"),
          type: z
            .enum(["meeting", "research", "idea", "tutorial"])
            .default("idea"),
        },
      },
      ({ topic, type }) => {
        const templates = {
          meeting: `# 會議記錄: ${topic}\n\n## 會議信息\n- 時間: \n- 參與者: \n- 地點: \n\n## 議程\n1. \n\n## 討論要點\n\n## 決議事項\n\n## 後續行動\n`,
          research: `# 研究筆記: ${topic}\n\n## 研究問題\n\n## 背景資料\n\n## 方法論\n\n## 發現\n\n## 結論\n\n## 參考資料\n`,
          idea: `# 想法: ${topic}\n\n## 核心概念\n\n## 相關思考\n\n## 可能應用\n\n## 下一步\n`,
          tutorial: `# 教程: ${topic}\n\n## 概述\n\n## 前置條件\n\n## 步驟\n\n### 步驟 1\n\n### 步驟 2\n\n## 總結\n\n## 延伸閱讀\n`,
        };

        return {
          messages: [
            {
              role: "user",
              content: {
                type: "text",
                text: `請幫我創建一個關於「${topic}」的${type}類型筆記。以下是建議的模板：\n\n${templates[type]}`,
              },
            },
          ],
        };
      },
    );

    // 筆記改進提示
    this.server.registerPrompt(
      "improve-note-prompt",
      {
        title: "筆記改進建議",
        description: "分析筆記並提供改進建議",
        argsSchema: {
          noteContent: z.string().min(1, "筆記內容不能為空"),
          focusArea: z
            .enum(["structure", "clarity", "completeness", "connections"])
            .default("structure"),
        },
      },
      ({ noteContent, focusArea }) => {
        const focusDescriptions = {
          structure: "結構和組織",
          clarity: "清晰度和可讀性",
          completeness: "完整性和深度",
          connections: "與其他概念的連接",
        };

        return {
          messages: [
            {
              role: "user",
              content: {
                type: "text",
                text: `請分析以下筆記，重點關注${focusDescriptions[focusArea]}，並提供具體的改進建議：\n\n${noteContent}`,
              },
            },
          ],
        };
      },
    );
  }

  private formatNoteAsMarkdown(note: Note): string {
    const frontmatter = [
      "---",
      `title: "${note.title}"`,
      `id: "${note.id}"`,
      `status: "${note.status}"`,
      `tags: [${note.tags.map((tag) => `"${tag}"`).join(", ")}]`,
      note.category ? `category: "${note.category}"` : "",
      `created: "${note.createdAt.toISOString()}"`,
      `updated: "${note.updatedAt.toISOString()}"`,
      "---",
      "",
    ]
      .filter(Boolean)
      .join("\n");

    return `${frontmatter}${note.content}`;
  }

  async startStdio(): Promise<void> {
    if (this.isInitialized) {
      throw new Error("Server already initialized");
    }

    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    this.isInitialized = true;

    console.error("Life Note MCP Server started (STDIO)");
  }

  async startHTTP(port: number = 3000): Promise<void> {
    if (this.isInitialized) {
      throw new Error("Server already initialized");
    }

    // 這裡需要實現 HTTP 服務器
    // 基於 Context7 搜索的 Streamable HTTP 實現
    const express = require("express");
    const app = express();

    app.use(express.json());

    // 實現 Streamable HTTP 端點
    app.post("/mcp", async (req: any, res: any) => {
      const transport = new StreamableHTTPServerTransport({
        sessionIdGenerator: () => Math.random().toString(36).substr(2, 9),
      });

      await this.server.connect(transport);
      await transport.handleRequest(req, res, req.body);
    });

    app.listen(port, () => {
      console.error(`Life Note MCP Server started (HTTP) on port ${port}`);
    });

    this.isInitialized = true;
  }
}

export { LifeNoteMCPServer };
```

## 實作要求

### STDIO 通訊協定配置

#### 啟動腳本

```typescript
// src/mcp/stdio-server.ts
import { LifeNoteMCPServer } from "./server";

async function main(): Promise<void> {
  try {
    const server = new LifeNoteMCPServer();
    await server.startStdio();

    // 保持進程運行
    process.on("SIGINT", () => {
      console.error("Received SIGINT, shutting down gracefully...");
      process.exit(0);
    });

    process.on("SIGTERM", () => {
      console.error("Received SIGTERM, shutting down gracefully...");
      process.exit(0);
    });
  } catch (error) {
    console.error("Failed to start MCP server:", error);
    process.exit(1);
  }
}

// 只在直接運行時啟動
if (require.main === module) {
  main().catch((error) => {
    console.error("Unhandled error:", error);
    process.exit(1);
  });
}
```

#### 客戶端配置範例

```json
{
  "mcpServers": {
    "life-note": {
      "command": "node",
      "args": ["dist/mcp/stdio-server.js"],
      "env": {
        "NODE_ENV": "production"
      }
    }
  }
}
```

### 編譯和執行規範

#### TypeScript 編譯配置

```json
// tsconfig.mcp.json
{
  "extends": "./tsconfig.json",
  "compilerOptions": {
    "target": "ES2020",
    "module": "CommonJS",
    "outDir": "./dist/mcp",
    "rootDir": "./src/mcp",
    "declaration": false,
    "sourceMap": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true
  },
  "include": ["src/mcp/**/*", "src/services/**/*", "src/types/**/*"],
  "exclude": ["node_modules", "dist", "**/*.test.ts"]
}
```

#### 構建腳本

```json
// package.json scripts
{
  "scripts": {
    "build:mcp": "tsc -p tsconfig.mcp.json",
    "start:mcp": "node dist/mcp/stdio-server.js",
    "dev:mcp": "tsx watch src/mcp/stdio-server.ts",
    "test:mcp": "vitest run src/mcp/**/*.test.ts"
  }
}
```

### 錯誤處理和日誌

#### 結構化日誌系統

```typescript
// src/mcp/logger.ts
import winston from "winston";

interface LogContext {
  requestId?: string;
  method?: string;
  params?: any;
  userId?: string;
  sessionId?: string;
}

class MCPLogger {
  private logger: winston.Logger;

  constructor() {
    this.logger = winston.createLogger({
      level: process.env.LOG_LEVEL || "info",
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json(),
      ),
      defaultMeta: {
        service: "life-note-mcp-server",
        version: "1.0.0",
      },
      transports: [
        // 錯誤日誌文件
        new winston.transports.File({
          filename: "logs/mcp-error.log",
          level: "error",
          maxsize: 5242880, // 5MB
          maxFiles: 5,
        }),
        // 所有日誌文件
        new winston.transports.File({
          filename: "logs/mcp-combined.log",
          maxsize: 5242880, // 5MB
          maxFiles: 10,
        }),
      ],
    });

    // 開發環境添加控制台輸出
    if (process.env.NODE_ENV !== "production") {
      this.logger.add(
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.colorize(),
            winston.format.simple(),
          ),
        }),
      );
    }
  }

  info(message: string, context?: LogContext): void {
    this.logger.info(message, context);
  }

  warn(message: string, context?: LogContext): void {
    this.logger.warn(message, context);
  }

  error(message: string, error?: Error, context?: LogContext): void {
    this.logger.error(message, {
      ...context,
      error: error
        ? {
            name: error.name,
            message: error.message,
            stack: error.stack,
          }
        : undefined,
    });
  }

  debug(message: string, context?: LogContext): void {
    this.logger.debug(message, context);
  }

  // 請求日誌
  logRequest(method: string, params: any, requestId: string): void {
    this.info("MCP request received", {
      requestId,
      method,
      params: this.sanitizeParams(params),
    });
  }

  logResponse(
    method: string,
    success: boolean,
    duration: number,
    requestId: string,
  ): void {
    this.info("MCP request completed", {
      requestId,
      method,
      success,
      duration,
    });
  }

  logError(method: string, error: Error, requestId: string): void {
    this.error("MCP request failed", error, {
      requestId,
      method,
    });
  }

  private sanitizeParams(params: any): any {
    // 移除敏感信息
    const sensitiveKeys = ["password", "token", "apiKey", "secret"];
    const sanitized = { ...params };

    for (const key of sensitiveKeys) {
      if (key in sanitized) {
        sanitized[key] = "[REDACTED]";
      }
    }

    return sanitized;
  }
}

export const mcpLogger = new MCPLogger();
```

#### 錯誤處理中間件

```typescript
// src/mcp/errorHandler.ts
import { mcpLogger } from "./logger";

export class MCPError extends Error {
  constructor(
    message: string,
    public code: number = -32603,
    public data?: any,
  ) {
    super(message);
    this.name = "MCPError";
  }
}

export class ValidationError extends MCPError {
  constructor(message: string, field?: string) {
    super(message, -32602, { field });
    this.name = "ValidationError";
  }
}

export class NotFoundError extends MCPError {
  constructor(resource: string, id: string) {
    super(`${resource} not found: ${id}`, -32601);
    this.name = "NotFoundError";
  }
}

export class PermissionError extends MCPError {
  constructor(action: string) {
    super(`Permission denied: ${action}`, -32000);
    this.name = "PermissionError";
  }
}

export function handleMCPError(
  error: unknown,
  requestId: string,
  method: string,
): {
  code: number;
  message: string;
  data?: any;
} {
  mcpLogger.logError(method, error as Error, requestId);

  if (error instanceof MCPError) {
    return {
      code: error.code,
      message: error.message,
      data: error.data,
    };
  }

  if (error instanceof Error) {
    // 根據錯誤類型映射到 JSON-RPC 錯誤碼
    if (error.name === "ValidationError") {
      return {
        code: -32602,
        message: "Invalid params",
        data: { details: error.message },
      };
    }

    if (error.name === "NotFoundError") {
      return {
        code: -32601,
        message: "Method not found",
        data: { details: error.message },
      };
    }

    return {
      code: -32603,
      message: "Internal error",
      data: { details: error.message },
    };
  }

  return {
    code: -32603,
    message: "Unknown error",
  };
}
```

#### 性能監控

```typescript
// src/mcp/metrics.ts
interface RequestMetrics {
  method: string;
  duration: number;
  success: boolean;
  timestamp: number;
}

class MetricsCollector {
  private metrics: RequestMetrics[] = [];
  private maxMetrics = 1000;

  recordRequest(method: string, duration: number, success: boolean): void {
    this.metrics.push({
      method,
      duration,
      success,
      timestamp: Date.now(),
    });

    // 保持最近的指標
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }
  }

  getStats(timeWindow: number = 3600000): {
    totalRequests: number;
    successRate: number;
    averageDuration: number;
    methodStats: Record<
      string,
      {
        count: number;
        successRate: number;
        averageDuration: number;
      }
    >;
  } {
    const cutoff = Date.now() - timeWindow;
    const recentMetrics = this.metrics.filter((m) => m.timestamp > cutoff);

    const totalRequests = recentMetrics.length;
    const successfulRequests = recentMetrics.filter((m) => m.success).length;
    const successRate =
      totalRequests > 0 ? successfulRequests / totalRequests : 0;
    const averageDuration =
      totalRequests > 0
        ? recentMetrics.reduce((sum, m) => sum + m.duration, 0) / totalRequests
        : 0;

    // 按方法統計
    const methodStats: Record<string, any> = {};
    for (const metric of recentMetrics) {
      if (!methodStats[metric.method]) {
        methodStats[metric.method] = {
          requests: [],
        };
      }
      methodStats[metric.method].requests.push(metric);
    }

    for (const method in methodStats) {
      const requests = methodStats[method].requests;
      const successful = requests.filter(
        (r: RequestMetrics) => r.success,
      ).length;

      methodStats[method] = {
        count: requests.length,
        successRate: requests.length > 0 ? successful / requests.length : 0,
        averageDuration:
          requests.length > 0
            ? requests.reduce(
                (sum: number, r: RequestMetrics) => sum + r.duration,
                0,
              ) / requests.length
            : 0,
      };
    }

    return {
      totalRequests,
      successRate,
      averageDuration,
      methodStats,
    };
  }
}

export const metricsCollector = new MetricsCollector();
```

這個 MCP Server 實作規範提供了完整的協議實現、錯誤處理和監控機制。
