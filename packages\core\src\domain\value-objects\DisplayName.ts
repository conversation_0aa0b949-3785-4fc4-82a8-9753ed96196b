import {
  ValueObject,
  BusinessRuleViolationError,
} from "../../shared/Entity.js";

/**
 * 顯示名稱值對象
 * 代表用戶的顯示名稱
 */
export class DisplayName extends ValueObject {
  private readonly _value: string;

  constructor(value: string) {
    super();
    this.validateDisplayName(value);
    this._value = value.trim();
  }

  /**
   * 創建顯示名稱值對象
   */
  static create(value: string): DisplayName {
    return new DisplayName(value);
  }

  /**
   * 從字符串創建顯示名稱
   */
  static fromString(value: string): DisplayName {
    return new DisplayName(value);
  }

  /**
   * 驗證顯示名稱格式
   */
  private validateDisplayName(value: string): void {
    if (!value || value.trim().length === 0) {
      throw new BusinessRuleViolationError("Display name cannot be empty");
    }

    const trimmedValue = value.trim();

    // 長度檢查
    if (trimmedValue.length < 1) {
      throw new BusinessRuleViolationError(
        "Display name must be at least 1 character long",
      );
    }

    if (trimmedValue.length > 50) {
      throw new BusinessRuleViolationError(
        "Display name cannot be longer than 50 characters",
      );
    }

    // 檢查是否只包含空白字符
    if (/^\s+$/.test(trimmedValue)) {
      throw new BusinessRuleViolationError(
        "Display name cannot contain only whitespace",
      );
    }

    // 檢查非法字符（控制字符）
    if (/[\x00-\x1F\x7F]/.test(trimmedValue)) {
      throw new BusinessRuleViolationError(
        "Display name cannot contain control characters",
      );
    }

    // 檢查是否包含過多的特殊字符
    const specialCharCount = (
      trimmedValue.match(/[^\w\s\u4e00-\u9fff\u3400-\u4dbf]/g) || []
    ).length;
    if (specialCharCount > trimmedValue.length * 0.3) {
      throw new BusinessRuleViolationError(
        "Display name contains too many special characters",
      );
    }

    // 檢查是否包含連續的空格
    if (/\s{3,}/.test(trimmedValue)) {
      throw new BusinessRuleViolationError(
        "Display name cannot contain more than 2 consecutive spaces",
      );
    }

    // 檢查不當內容的簡單過濾
    const inappropriatePatterns = [
      /admin/i,
      /administrator/i,
      /moderator/i,
      /support/i,
      /system/i,
      /service/i,
      /bot/i,
      /null/i,
      /undefined/i,
      /fuck/i,
      /shit/i,
      /damn/i,
      /bitch/i,
      /ass/i,
      /sex/i,
      /porn/i,
      /xxx/i,
      /nude/i,
      /naked/i,
      /kill/i,
      /die/i,
      /death/i,
      /murder/i,
      /suicide/i,
      /hate/i,
      /nazi/i,
      /hitler/i,
      /terrorist/i,
    ];

    for (const pattern of inappropriatePatterns) {
      if (pattern.test(trimmedValue)) {
        throw new BusinessRuleViolationError(
          "Display name contains inappropriate content",
        );
      }
    }
  }

  /**
   * 檢查是否包含中文字符
   */
  hasChineseCharacters(): boolean {
    return /[\u4e00-\u9fff\u3400-\u4dbf]/.test(this._value);
  }

  /**
   * 檢查是否包含英文字符
   */
  hasEnglishCharacters(): boolean {
    return /[a-zA-Z]/.test(this._value);
  }

  /**
   * 檢查是否包含數字
   */
  hasNumbers(): boolean {
    return /[0-9]/.test(this._value);
  }

  /**
   * 檢查是否包含特殊字符
   */
  hasSpecialCharacters(): boolean {
    return /[^\w\s\u4e00-\u9fff\u3400-\u4dbf]/.test(this._value);
  }

  /**
   * 檢查是否只包含字母和數字
   */
  isAlphanumeric(): boolean {
    return /^[a-zA-Z0-9\u4e00-\u9fff\u3400-\u4dbf\s]+$/.test(this._value);
  }

  /**
   * 獲取字符數（考慮中文字符）
   */
  getCharacterCount(): number {
    return Array.from(this._value).length;
  }

  /**
   * 獲取字節長度（UTF-8）
   */
  getByteLength(): number {
    return new TextEncoder().encode(this._value).length;
  }

  /**
   * 獲取首字母（用於頭像顯示）
   */
  getInitials(): string {
    const words = this._value.trim().split(/\s+/);

    if (words.length === 1) {
      // 單個詞，取前兩個字符
      return Array.from(words[0]).slice(0, 2).join("").toUpperCase();
    } else {
      // 多個詞，取每個詞的首字母
      return words
        .slice(0, 2)
        .map((word) => Array.from(word)[0])
        .join("")
        .toUpperCase();
    }
  }

  /**
   * 獲取縮短的顯示名稱
   */
  getShortened(maxLength: number = 20): string {
    if (this.getCharacterCount() <= maxLength) {
      return this._value;
    }

    const chars = Array.from(this._value);
    return chars.slice(0, maxLength - 1).join("") + "…";
  }

  /**
   * 轉換為標題格式（每個單詞首字母大寫）
   */
  toTitleCase(): string {
    return this._value.replace(
      /\w\S*/g,
      (txt) => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase(),
    );
  }

  /**
   * 轉換為小寫格式
   */
  toLowerCase(): string {
    return this._value.toLowerCase();
  }

  /**
   * 轉換為大寫格式
   */
  toUpperCase(): string {
    return this._value.toUpperCase();
  }

  /**
   * 清理顯示名稱（移除多餘空格）
   */
  normalize(): DisplayName {
    const normalized = this._value.replace(/\s+/g, " ").trim();
    return new DisplayName(normalized);
  }

  /**
   * 檢查是否為有效的真實姓名格式
   */
  looksLikeRealName(): boolean {
    // 簡單的真實姓名檢查
    const words = this._value.trim().split(/\s+/);

    // 應該有1-4個詞
    if (words.length < 1 || words.length > 4) {
      return false;
    }

    // 每個詞應該以字母開頭
    for (const word of words) {
      if (!/^[a-zA-Z\u4e00-\u9fff\u3400-\u4dbf]/.test(word)) {
        return false;
      }
    }

    // 不應該包含數字或特殊字符（除了連字符和撇號）
    if (
      /[0-9]/.test(this._value) ||
      /[^\w\s\u4e00-\u9fff\u3400-\u4dbf'-]/.test(this._value)
    ) {
      return false;
    }

    return true;
  }

  /**
   * 生成搜索友好的格式
   */
  toSearchableFormat(): string {
    return this._value
      .toLowerCase()
      .replace(/[^\w\s\u4e00-\u9fff\u3400-\u4dbf]/g, "")
      .replace(/\s+/g, " ")
      .trim();
  }

  /**
   * 生成 URL 友好的格式
   */
  toUrlFriendlyFormat(): string {
    return this._value
      .toLowerCase()
      .replace(/[^\w\u4e00-\u9fff\u3400-\u4dbf]/g, "-")
      .replace(/-+/g, "-")
      .replace(/^-|-$/g, "");
  }

  get value(): string {
    return this._value;
  }

  get length(): number {
    return this.getCharacterCount();
  }

  equals(other: ValueObject): boolean {
    if (!(other instanceof DisplayName)) {
      return false;
    }
    return this._value === other._value;
  }

  hashCode(): string {
    return this._value;
  }

  toString(): string {
    return this._value;
  }

  toPlainObject(): Record<string, unknown> {
    return {
      value: this._value,
      length: this.length,
      characterCount: this.getCharacterCount(),
      byteLength: this.getByteLength(),
      initials: this.getInitials(),
      hasChineseCharacters: this.hasChineseCharacters(),
      hasEnglishCharacters: this.hasEnglishCharacters(),
      hasNumbers: this.hasNumbers(),
      hasSpecialCharacters: this.hasSpecialCharacters(),
      isAlphanumeric: this.isAlphanumeric(),
      looksLikeRealName: this.looksLikeRealName(),
    };
  }

  static fromPlainObject(data: Record<string, unknown>): DisplayName {
    if (typeof data.value !== "string") {
      throw new BusinessRuleViolationError("Invalid DisplayName data");
    }

    return new DisplayName(data.value);
  }
}
