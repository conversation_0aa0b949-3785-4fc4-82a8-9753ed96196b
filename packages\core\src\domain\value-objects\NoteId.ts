import { generateRandomString } from "@life-note/utils";

import {
  ValueObject,
  BusinessRuleViolationError,
} from "../../shared/Entity.js";

/**
 * 筆記 ID 值對象
 * 代表筆記的唯一標識符
 */
export class NoteId extends ValueObject {
  private readonly _value: string;

  constructor(value: string) {
    super();
    this.validateId(value);
    this._value = value;
  }

  /**
   * 生成新的筆記 ID
   */
  static generate(): NoteId {
    return new NoteId(generateRandomString(16));
  }

  /**
   * 從字符串創建筆記 ID
   */
  static fromString(value: string): NoteId {
    return new NoteId(value);
  }

  /**
   * 驗證 ID 格式
   */
  private validateId(value: string): void {
    if (!value || value.trim().length === 0) {
      throw new BusinessRuleViolationError("Note ID cannot be empty");
    }

    if (value.length < 8 || value.length > 64) {
      throw new BusinessRuleViolationError(
        "Note ID must be between 8 and 64 characters",
      );
    }

    // 檢查是否只包含字母數字字符
    if (!/^[a-zA-Z0-9]+$/.test(value)) {
      throw new BusinessRuleViolationError(
        "Note ID can only contain alphanumeric characters",
      );
    }
  }

  get value(): string {
    return this._value;
  }

  equals(other: ValueObject): boolean {
    if (!(other instanceof NoteId)) {
      return false;
    }
    return this._value === other._value;
  }

  hashCode(): string {
    return this._value;
  }

  toString(): string {
    return this._value;
  }

  toPlainObject(): Record<string, unknown> {
    return {
      value: this._value,
    };
  }

  static fromPlainObject(data: Record<string, unknown>): NoteId {
    if (typeof data.value !== "string") {
      throw new BusinessRuleViolationError("Invalid NoteId data");
    }
    return new NoteId(data.value);
  }
}
