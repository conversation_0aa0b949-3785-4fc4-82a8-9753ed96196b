import { z } from "zod";

/**
 * 常用驗證 Schema
 */
export const CommonSchemas = {
  // 筆記 ID
  noteId: z.string().uuid("Invalid note ID format"),

  // 筆記標題
  noteTitle: z
    .string()
    .min(1, "Title cannot be empty")
    .max(200, "Title too long"),

  // 筆記內容
  noteContent: z.string().max(1000000, "Content too long"), // 1MB limit

  // 標籤
  tag: z
    .string()
    .min(1, "Tag cannot be empty")
    .max(50, "Tag too long")
    .regex(/^[a-zA-Z0-9\-_]+$/, "Tag contains invalid characters"),

  // 標籤數組
  tags: z.array(z.string()).max(20, "Too many tags"),

  // 文件路徑
  filePath: z
    .string()
    .min(1, "File path cannot be empty")
    .regex(/^[^<>:"|?*]+$/, "Invalid file path"),

  // URL
  url: z.string().url("Invalid URL format"),

  // 電子郵件
  email: z.string().email("Invalid email format"),

  // 版本號
  version: z.string().regex(/^\d+\.\d+\.\d+$/, "Invalid version format"),

  // 日期時間
  datetime: z.string().datetime("Invalid datetime format"),

  // 端口號
  port: z
    .number()
    .int("Port must be an integer")
    .min(1, "Port must be greater than 0")
    .max(65535, "Port must be less than 65536"),
};

/**
 * 文件驗證
 */
export class FileValidator {
  private static readonly ALLOWED_EXTENSIONS = [
    ".md",
    ".txt",
    ".json",
    ".yaml",
    ".yml",
  ];
  private static readonly MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

  static validateExtension(filename: string): boolean {
    const ext = filename.toLowerCase().substring(filename.lastIndexOf("."));
    return this.ALLOWED_EXTENSIONS.includes(ext);
  }

  static validateSize(size: number): boolean {
    return size <= this.MAX_FILE_SIZE;
  }

  static validateFilename(filename: string): boolean {
    // 檢查文件名是否包含非法字符
    const invalidChars = /[<>:"|?*]/;
    return (
      !invalidChars.test(filename) &&
      filename.length > 0 &&
      filename.length <= 255
    );
  }

  static validate(
    filename: string,
    size: number,
  ): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!this.validateFilename(filename)) {
      errors.push("Invalid filename");
    }

    if (!this.validateExtension(filename)) {
      errors.push("File extension not allowed");
    }

    if (!this.validateSize(size)) {
      errors.push("File size too large");
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }
}

/**
 * 筆記內容驗證
 */
export class NoteValidator {
  static validateMarkdown(content: string): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // 檢查內容長度
    if (content.length > 1000000) {
      errors.push("Content too long");
    }

    // 檢查是否包含惡意腳本
    if (this.containsMaliciousContent(content)) {
      errors.push("Content contains potentially malicious code");
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  private static containsMaliciousContent(content: string): boolean {
    const maliciousPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /<iframe\b[^>]*>/gi,
      /<object\b[^>]*>/gi,
      /<embed\b[^>]*>/gi,
    ];

    return maliciousPatterns.some((pattern) => pattern.test(content));
  }
}

/**
 * API 請求驗證
 */
export class RequestValidator {
  static validatePagination(
    page?: number,
    limit?: number,
  ): { page: number; limit: number } {
    const validatedPage = Math.max(1, page || 1);
    const validatedLimit = Math.min(100, Math.max(1, limit || 10));

    return { page: validatedPage, limit: validatedLimit };
  }

  static validateSortOrder(order?: string): "asc" | "desc" {
    return order === "desc" ? "desc" : "asc";
  }

  static validateSearchQuery(query?: string): string {
    if (!query || typeof query !== "string") {
      return "";
    }

    // 移除特殊字符，保留基本搜索功能
    return query
      .replace(/[^\w\s\-_.]/g, "")
      .trim()
      .substring(0, 100);
  }
}

/**
 * 安全驗證工具
 */
export class SecurityValidator {
  static validateApiKey(apiKey: string): boolean {
    // API 密鑰應該至少 32 個字符
    return typeof apiKey === "string" && apiKey.length >= 32;
  }

  static validateJWT(token: string): boolean {
    // 簡單的 JWT 格式檢查
    const parts = token.split(".");
    return parts.length === 3 && parts.every((part) => part.length > 0);
  }

  static validateOrigin(origin: string, allowedOrigins: string[]): boolean {
    return allowedOrigins.includes(origin) || allowedOrigins.includes("*");
  }

  static sanitizeInput(input: string): string {
    // 移除潛在的 XSS 攻擊向量
    return input
      .replace(/[<>]/g, "")
      .replace(/javascript:/gi, "")
      .replace(/on\w+=/gi, "")
      .trim();
  }
}

/**
 * 創建驗證中間件
 */
export function createValidator<T>(schema: z.ZodSchema<T>) {
  return (
    data: unknown,
  ): { success: true; data: T } | { success: false; errors: string[] } => {
    try {
      const validatedData = schema.parse(data);
      return { success: true, data: validatedData };
    } catch (error) {
      if (error instanceof z.ZodError) {
        return {
          success: false,
          errors: error.errors.map(
            (err) => `${err.path.join(".")}: ${err.message}`,
          ),
        };
      }
      return {
        success: false,
        errors: ["Validation failed"],
      };
    }
  };
}
