/* eslint-disable @typescript-eslint/no-unused-vars */
import { z } from "zod";

import { Entity, BusinessRuleViolationError } from "../../shared/Entity.js";
import { UserId } from "../value-objects/UserId.js";

/**
 * 用戶角色枚舉
 */
export enum UserRole {
  ADMIN = "admin",
  MODERATOR = "moderator",
  USER = "user",
  GUEST = "guest",
}

/**
 * 用戶狀態枚舉
 */
export enum UserStatus {
  ACTIVE = "active",
  INACTIVE = "inactive",
  SUSPENDED = "suspended",
}

/**
 * 用戶創建參數
 */
export interface CreateUserParams {
  username: string;
  email: string;
  displayName?: string;
  role?: UserRole;
  metadata?: Record<string, unknown>;
}

/**
 * 用戶更新參數
 */
export interface UpdateUserParams {
  username?: string;
  email?: string;
  displayName?: string;
  role?: UserRole;
  metadata?: Record<string, unknown>;
}

/**
 * 用戶實體
 * 代表系統中的一個用戶
 */
export class User extends Entity<UserId> {
  private _username: string;
  private _email: string;
  private _displayName?: string;
  private _role: UserRole;
  private _status: UserStatus;
  private _metadata: Record<string, unknown>;
  private _lastLoginAt?: Date;

  constructor(
    id: UserId,
    username: string,
    email: string,
    options: {
      displayName?: string;
      role?: UserRole;
      status?: UserStatus;
      metadata?: Record<string, unknown>;
      lastLoginAt?: Date;
      createdAt?: Date;
    } = {},
  ) {
    super(id, options.createdAt);

    this.validateUsername(username);
    this.validateEmail(email);

    this._username = username;
    this._email = email;
    this._displayName = options.displayName;
    this._role = options.role || UserRole.USER;
    this._status = options.status || UserStatus.ACTIVE;
    this._metadata = options.metadata || {};
    this._lastLoginAt = options.lastLoginAt;
  }

  /**
   * 創建新用戶
   */
  static create(params: CreateUserParams): User {
    const userId = UserId.generate();
    return new User(userId, params.username, params.email, {
      displayName: params.displayName,
      role: params.role,
      metadata: params.metadata,
    });
  }

  /**
   * 更新用戶信息
   */
  update(params: UpdateUserParams): void {
    let hasChanges = false;

    if (params.username !== undefined && params.username !== this._username) {
      this.validateUsername(params.username);
      this._username = params.username;
      hasChanges = true;
    }

    if (params.email !== undefined && params.email !== this._email) {
      this.validateEmail(params.email);
      this._email = params.email;
      hasChanges = true;
    }

    if (
      params.displayName !== undefined &&
      params.displayName !== this._displayName
    ) {
      this._displayName = params.displayName;
      hasChanges = true;
    }

    if (params.role !== undefined && params.role !== this._role) {
      this._role = params.role;
      hasChanges = true;
    }

    if (params.metadata !== undefined) {
      this._metadata = { ...this._metadata, ...params.metadata };
      hasChanges = true;
    }

    if (hasChanges) {
      this.touch();
    }
  }

  /**
   * 激活用戶
   */
  activate(): void {
    this._status = UserStatus.ACTIVE;
    this.touch();
  }

  /**
   * 停用用戶
   */
  deactivate(): void {
    this._status = UserStatus.INACTIVE;
    this.touch();
  }

  /**
   * 暫停用戶
   */
  suspend(): void {
    this._status = UserStatus.SUSPENDED;
    this.touch();
  }

  /**
   * 記錄登錄時間
   */
  recordLogin(): void {
    this._lastLoginAt = new Date();
    this.touch();
  }

  /**
   * 更新用戶資料
   */
  updateProfile(params: {
    displayName?: string;
    email?: string;
    metadata?: Record<string, unknown>;
  }): void {
    let hasChanges = false;

    if (
      params.displayName !== undefined &&
      params.displayName !== this._displayName
    ) {
      this._displayName = params.displayName;
      hasChanges = true;
    }

    if (params.email !== undefined && params.email !== this._email) {
      this.validateEmail(params.email);
      this._email = params.email;
      hasChanges = true;
    }

    if (params.metadata !== undefined) {
      this._metadata = { ...this._metadata, ...params.metadata };
      hasChanges = true;
    }

    if (hasChanges) {
      this.touch();
    }
  }

  /**
   * 更改用戶名
   */
  changeUsername(newUsername: string): void {
    this.validateUsername(newUsername);
    this._username = newUsername;
    this.touch();
  }

  /**
   * 更改電子郵件
   */
  changeEmail(newEmail: string): void {
    this.validateEmail(newEmail);
    this._email = newEmail;
    this.touch();
  }

  /**
   * 更新角色
   */
  updateRole(newRole: UserRole): void {
    this._role = newRole;
    this.touch();
  }

  /**
   * 更新元數據
   */
  updateMetadata(metadata: Record<string, unknown>): void {
    this._metadata = { ...this._metadata, ...metadata };
    this.touch();
  }

  /**
   * 驗證用戶名
   */
  private validateUsername(username: string): void {
    if (!username || username.trim().length === 0) {
      throw new BusinessRuleViolationError("Username cannot be empty");
    }

    if (username.length < 3 || username.length > 50) {
      throw new BusinessRuleViolationError(
        "Username must be between 3 and 50 characters",
      );
    }

    // 檢查是否只包含字母數字字符、連字符和下劃線
    if (!/^[a-zA-Z0-9\-_]+$/.test(username)) {
      throw new BusinessRuleViolationError(
        "Username can only contain alphanumeric characters, hyphens, and underscores",
      );
    }
  }

  /**
   * 驗證電子郵件
   */
  private validateEmail(email: string): void {
    if (!email || email.trim().length === 0) {
      throw new BusinessRuleViolationError("Email cannot be empty");
    }

    // 簡單的電子郵件格式驗證
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new BusinessRuleViolationError("Invalid email format");
    }
  }

  // Getters
  get username(): string {
    return this._username;
  }

  get email(): string {
    return this._email;
  }

  get displayName(): string | undefined {
    return this._displayName;
  }

  get role(): UserRole {
    return this._role;
  }

  get status(): UserStatus {
    return this._status;
  }

  get metadata(): Record<string, unknown> {
    return { ...this._metadata };
  }

  get lastLoginAt(): Date | undefined {
    return this._lastLoginAt;
  }

  /**
   * 檢查是否為管理員
   */
  get isAdmin(): boolean {
    return this._role === UserRole.ADMIN;
  }

  /**
   * 檢查是否為活躍用戶
   */
  get isActive(): boolean {
    return this._status === UserStatus.ACTIVE;
  }

  /**
   * 檢查是否被暫停
   */
  get isSuspended(): boolean {
    return this._status === UserStatus.SUSPENDED;
  }

  /**
   * 轉換為普通對象
   */
  toPlainObject(): Record<string, unknown> {
    return {
      id: this._id.value,
      username: this._username,
      email: this._email,
      displayName: this._displayName,
      role: this._role,
      status: this._status,
      metadata: this._metadata,
      lastLoginAt: this._lastLoginAt?.toISOString(),
      createdAt: this._createdAt.toISOString(),
      updatedAt: this._updatedAt.toISOString(),
    };
  }

  /**
   * 從普通對象創建用戶實體
   */
  static fromPlainObject(data: Record<string, unknown>): User {
    const schema = z.object({
      id: z.string(),
      username: z.string(),
      email: z.string(),
      displayName: z.string().optional(),
      role: z.nativeEnum(UserRole).default(UserRole.USER),
      status: z.nativeEnum(UserStatus).default(UserStatus.ACTIVE),
      metadata: z.record(z.unknown()).default({}),
      lastLoginAt: z.string().optional(),
      createdAt: z.string().optional(),
      updatedAt: z.string().optional(),
    });

    const parsed = schema.parse(data);

    return new User(
      UserId.fromString(parsed.id),
      parsed.username,
      parsed.email,
      {
        displayName: parsed.displayName,
        role: parsed.role,
        status: parsed.status,
        metadata: parsed.metadata,
        lastLoginAt: parsed.lastLoginAt
          ? new Date(parsed.lastLoginAt)
          : undefined,
        createdAt: parsed.createdAt ? new Date(parsed.createdAt) : undefined,
      },
    );
  }
}
