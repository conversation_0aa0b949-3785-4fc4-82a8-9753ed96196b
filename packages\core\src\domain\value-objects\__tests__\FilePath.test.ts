import { describe, it, expect } from "vitest";
import { FilePath } from "../FilePath.js";
import { BusinessRuleViolationError } from "../../../shared/Entity.js";

describe("FilePath Value Object", () => {
  describe("Valid FilePath Creation", () => {
    it("should create file path with valid format", () => {
      const filePath = new FilePath("documents/notes/test.md");
      expect(filePath.value).toBe("documents/notes/test.md");
    });

    it("should normalize backslashes to forward slashes", () => {
      const filePath = new FilePath("documents\\notes\\test.md");
      expect(filePath.value).toBe("documents/notes/test.md");
    });

    it("should trim whitespace", () => {
      const filePath = new FilePath("  documents/test.md  ");
      expect(filePath.value).toBe("documents/test.md");
    });

    it("should handle absolute paths", () => {
      const unixPath = new FilePath("/home/<USER>/documents/test.md");
      const windowsPath = new FilePath("C:/Users/<USER>/documents/test.md");

      expect(unixPath.isAbsolute).toBe(true);
      expect(windowsPath.isAbsolute).toBe(true);
    });

    it("should handle relative paths", () => {
      const relativePath = new FilePath("documents/test.md");
      expect(relativePath.isRelative).toBe(true);
    });
  });

  describe("Invalid FilePath Validation", () => {
    it("should reject empty path", () => {
      expect(() => new FilePath("")).toThrow(BusinessRuleViolationError);
      expect(() => new FilePath("   ")).toThrow(BusinessRuleViolationError);
    });

    it("should reject paths with illegal characters", () => {
      const illegalChars = ["<", ">", ":", '"', "|", "?", "*"];

      illegalChars.forEach((char) => {
        expect(() => new FilePath(`test${char}file.txt`)).toThrow(
          BusinessRuleViolationError,
        );
      });
    });

    it("should reject reserved names", () => {
      const reservedNames = ["CON", "PRN", "AUX", "NUL", "COM1", "LPT1"];

      reservedNames.forEach((name) => {
        expect(() => new FilePath(name)).toThrow(BusinessRuleViolationError);
        expect(() => new FilePath(`${name}.txt`)).toThrow(
          BusinessRuleViolationError,
        );
      });
    });

    it("should reject paths ending with dot", () => {
      expect(() => new FilePath("test.")).toThrow(BusinessRuleViolationError);
    });

    it("should reject paths with consecutive separators", () => {
      expect(() => new FilePath("documents//test.md")).toThrow(
        BusinessRuleViolationError,
      );
      expect(() => new FilePath("documents\\\\test.md")).toThrow(
        BusinessRuleViolationError,
      );
    });

    it("should reject paths with parent directory references", () => {
      expect(() => new FilePath("../test.md")).toThrow(
        BusinessRuleViolationError,
      );
      expect(() => new FilePath("documents/../test.md")).toThrow(
        BusinessRuleViolationError,
      );
    });

    it("should reject too long paths", () => {
      const longPath = "a".repeat(4097);
      expect(() => new FilePath(longPath)).toThrow(BusinessRuleViolationError);
    });
  });

  describe("Path Properties", () => {
    const filePath = new FilePath("documents/notes/test-file.md");

    it("should extract file name correctly", () => {
      expect(filePath.fileName).toBe("test-file.md");
    });

    it("should extract base name correctly", () => {
      expect(filePath.baseName).toBe("test-file");
    });

    it("should extract extension correctly", () => {
      expect(filePath.extension).toBe("md");
    });

    it("should extract directory correctly", () => {
      expect(filePath.directory).toBe("documents/notes");
    });

    it("should calculate depth correctly", () => {
      expect(filePath.depth).toBe(2);
    });

    it("should handle root directory", () => {
      const rootPath = new FilePath("/");
      expect(rootPath.directory).toBe("/");
      expect(rootPath.depth).toBe(0);
    });

    it("should handle current directory", () => {
      const currentPath = new FilePath("test.md");
      expect(currentPath.directory).toBe(".");
    });
  });

  describe("File Type Detection", () => {
    it("should detect files vs directories", () => {
      const file = new FilePath("test.md");
      const directory = new FilePath("documents");

      expect(file.isFile()).toBe(true);
      expect(file.isDirectory()).toBe(false);
      expect(directory.isFile()).toBe(false);
      expect(directory.isDirectory()).toBe(true);
    });

    it("should check specific extensions", () => {
      const mdFile = new FilePath("test.md");
      const txtFile = new FilePath("test.txt");

      expect(mdFile.hasExtension("md")).toBe(true);
      expect(mdFile.hasExtension("txt")).toBe(false);
      expect(txtFile.hasExtension("MD")).toBe(false); // case sensitive
    });

    it("should detect image files", () => {
      const imageFiles = ["photo.jpg", "image.png", "icon.svg", "picture.gif"];

      imageFiles.forEach((fileName) => {
        const filePath = new FilePath(fileName);
        expect(filePath.isImageFile()).toBe(true);
      });

      const nonImageFile = new FilePath("document.pdf");
      expect(nonImageFile.isImageFile()).toBe(false);
    });

    it("should detect document files", () => {
      const docFiles = [
        "readme.txt",
        "notes.md",
        "document.pdf",
        "report.docx",
      ];

      docFiles.forEach((fileName) => {
        const filePath = new FilePath(fileName);
        expect(filePath.isDocumentFile()).toBe(true);
      });

      const nonDocFile = new FilePath("script.js");
      expect(nonDocFile.isDocumentFile()).toBe(false);
    });

    it("should detect code files", () => {
      const codeFiles = [
        "script.js",
        "component.tsx",
        "style.css",
        "config.json",
        "main.py",
      ];

      codeFiles.forEach((fileName) => {
        const filePath = new FilePath(fileName);
        expect(filePath.isCodeFile()).toBe(true);
      });

      const nonCodeFile = new FilePath("image.jpg");
      expect(nonCodeFile.isCodeFile()).toBe(false);
    });
  });

  describe("Path Operations", () => {
    it("should join paths correctly", () => {
      const basePath = new FilePath("documents");
      const joinedPath = basePath.join("notes/test.md");

      expect(joinedPath.value).toBe("documents/notes/test.md");
    });

    it("should handle joining with trailing slash", () => {
      const basePath = new FilePath("documents/");
      const joinedPath = basePath.join("test.md");

      expect(joinedPath.value).toBe("documents/test.md");
    });

    it("should handle joining with leading slash", () => {
      const basePath = new FilePath("documents");
      const joinedPath = basePath.join("/test.md");

      expect(joinedPath.value).toBe("documents/test.md");
    });

    it("should change extension correctly", () => {
      const filePath = new FilePath("document.md");
      const newPath = filePath.changeExtension("txt");

      expect(newPath.value).toBe("document.txt");
    });

    it("should change extension with dot prefix", () => {
      const filePath = new FilePath("document.md");
      const newPath = filePath.changeExtension(".html");

      expect(newPath.value).toBe("document.html");
    });
  });

  describe("Relative Path Operations", () => {
    it("should create relative path correctly", () => {
      const targetPath = new FilePath("documents/notes/test.md");
      const basePath = "documents";
      const relativePath = targetPath.relativeTo(basePath);

      expect(relativePath.value).toBe("notes/test.md");
    });

    it("should handle same directory", () => {
      const targetPath = new FilePath("documents/test.md");
      const basePath = "documents";
      const relativePath = targetPath.relativeTo(basePath);

      expect(relativePath.value).toBe("test.md");
    });

    it("should handle parent directory navigation", () => {
      const targetPath = new FilePath("documents/test.md");
      const basePath = "documents/notes";
      const relativePath = targetPath.relativeTo(basePath);

      expect(relativePath.value).toBe("../test.md");
    });

    it("should throw error for mixed absolute/relative paths", () => {
      const absolutePath = new FilePath("/home/<USER>/test.md");
      const relativePath = "documents";

      expect(() => absolutePath.relativeTo(relativePath)).toThrow(
        BusinessRuleViolationError,
      );
    });
  });

  describe("Value Object Behavior", () => {
    it("should be equal to another path with same value", () => {
      const path1 = new FilePath("documents/test.md");
      const path2 = new FilePath("documents/test.md");
      expect(path1.equals(path2)).toBe(true);
    });

    it("should not be equal to different path", () => {
      const path1 = new FilePath("documents/test1.md");
      const path2 = new FilePath("documents/test2.md");
      expect(path1.equals(path2)).toBe(false);
    });

    it("should not be equal to non-path object", () => {
      const path = new FilePath("documents/test.md");
      expect(path.equals({} as any)).toBe(false);
    });

    it("should have consistent hash code", () => {
      const path1 = new FilePath("documents/test.md");
      const path2 = new FilePath("documents/test.md");
      expect(path1.hashCode()).toBe(path2.hashCode());
    });

    it("should convert to string correctly", () => {
      const path = new FilePath("documents/test.md");
      expect(path.toString()).toBe("documents/test.md");
    });
  });

  describe("Serialization", () => {
    it("should serialize to plain object", () => {
      const filePath = new FilePath("documents/notes/test.md");
      const plainObject = filePath.toPlainObject();

      expect(plainObject).toEqual({
        value: "documents/notes/test.md",
        fileName: "test.md",
        baseName: "test",
        extension: "md",
        directory: "documents/notes",
        depth: 2,
        isAbsolute: false,
        isFile: true,
        isDirectory: false,
      });
    });

    it("should deserialize from plain object", () => {
      const plainObject = {
        value: "documents/test.md",
      };

      const filePath = FilePath.fromPlainObject(plainObject);
      expect(filePath.value).toBe("documents/test.md");
    });

    it("should throw error for invalid plain object", () => {
      const invalidObject = { value: 123 };
      expect(() => FilePath.fromPlainObject(invalidObject)).toThrow(
        BusinessRuleViolationError,
      );
    });
  });

  describe("Static Factory Methods", () => {
    it("should create path using create method", () => {
      const path = FilePath.create("documents/test.md");
      expect(path.value).toBe("documents/test.md");
    });

    it("should create path using fromString method", () => {
      const path = FilePath.fromString("documents/test.md");
      expect(path.value).toBe("documents/test.md");
    });
  });
});
