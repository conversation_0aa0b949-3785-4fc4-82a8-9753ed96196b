<script lang="ts">
	import { page } from '$app/stores';
	import { goto } from '$app/navigation';
	import { onMount } from 'svelte';
	import {
		ArrowLeft,
		Save,
		Eye,
		EyeOff,
		Settings,
		Tag,
		Calendar,
		AlertCircle,
		CheckCircle
	} from 'lucide-svelte';
	import { <PERSON><PERSON>, Card } from '$components/ui';
	import MarkdownEditor from '$lib/components/editor/MarkdownEditor.svelte';
	import MarkdownRenderer from '$lib/components/editor/MarkdownRenderer.svelte';
	import TagInput from '$lib/components/notes/TagInput.svelte';
	import { noteStore } from '$stores/notes';
	import type { Note } from '$types';

	// 路由參數
	$: noteId = $page.params.id;

	// 狀態變數
	let note: Note | null = null;
	let isLoading = true;
	let isSaving = false;
	let error: string | null = null;
	let hasUnsavedChanges = false;
	let isPreviewMode = false;
	let showSettings = false;

	// 表單數據
	let formData = {
		title: '',
		content: '',
		tags: [] as Array<{ name: string }>,
		status: 'draft' as 'draft' | 'published' | 'archived',
		priority: 'medium' as 'low' | 'medium' | 'high' | 'urgent',
		categoryId: ''
	};

	// 自動保存
	let autoSaveTimeout: NodeJS.Timeout;
	let lastSaved: Date | null = null;

	// Mock 數據
	const mockNote: Note = {
		id: '1',
		title: '歡迎使用 Life Note',
		content: `# 歡迎使用 Life Note

Life Note 是一個功能強大的知識管理系統，專為現代知識工作者設計。

## 主要功能

### 📝 Markdown 編輯
- 支援完整的 Markdown 語法
- 實時預覽功能
- 語法高亮顯示
- 快捷鍵支援

### 🔗 依賴關係追蹤
- 自動檢測筆記間的關聯
- 視覺化依賴關係圖
- 智能推薦相關內容

### 🏷️ 標籤系統
- 靈活的標籤分類
- 標籤自動建議
- 多維度過濾

### 📊 版本控制
- 完整的版本歷史
- 版本比較功能
- 一鍵回滾

## 開始使用

1. 點擊「新增筆記」創建您的第一個筆記
2. 使用 Markdown 語法編寫內容
3. 添加標籤進行分類
4. 探索依賴關係功能

> 💡 **提示**：使用 \`Ctrl+S\` 快速保存筆記`,
		excerpt: '這是您的第一個筆記。Life Note 是一個功能強大的知識管理系統...',
		status: 'published',
		priority: 'high',
		tags: [{ name: '歡迎' }, { name: '開始' }, { name: '指南' }],
		authorId: 'user-1',
		categoryId: 'getting-started',
		createdAt: new Date(Date.now() - 86400000 * 7),
		updatedAt: new Date(Date.now() - 86400000 * 2)
	};

	onMount(async () => {
		try {
			isLoading = true;
			// 從存儲中載入筆記
			note = await noteStore.getNote(noteId);

			// 初始化表單數據
			if (note) {
				formData = {
					title: note.title,
					content: note.content,
					tags: [...note.tags],
					status: note.status,
					priority: note.priority,
					categoryId: note.categoryId || ''
				};
			}
		} catch (err) {
			error = err instanceof Error ? err.message : '載入筆記失敗';
		} finally {
			isLoading = false;
		}

		// 監聽鍵盤快捷鍵
		const handleKeyDown = (e: KeyboardEvent) => {
			if (e.ctrlKey && e.key === 's') {
				e.preventDefault();
				handleSave();
			}
		};

		window.addEventListener('keydown', handleKeyDown);

		return () => {
			window.removeEventListener('keydown', handleKeyDown);
			if (autoSaveTimeout) {
				clearTimeout(autoSaveTimeout);
			}
		};
	});

	// 監聽表單變化
	$: {
		if (
			note &&
			(formData.title !== note.title ||
				formData.content !== note.content ||
				JSON.stringify(formData.tags) !== JSON.stringify(note.tags) ||
				formData.status !== note.status ||
				formData.priority !== note.priority ||
				formData.categoryId !== (note.categoryId || ''))
		) {
			hasUnsavedChanges = true;
			scheduleAutoSave();
		}
	}

	function scheduleAutoSave() {
		if (autoSaveTimeout) {
			clearTimeout(autoSaveTimeout);
		}

		autoSaveTimeout = setTimeout(() => {
			handleAutoSave();
		}, 3000); // 3秒後自動保存
	}

	async function handleAutoSave() {
		if (!hasUnsavedChanges) return;

		try {
			await saveNote(false); // 靜默保存
			lastSaved = new Date();
		} catch (err) {
			console.error('Auto save failed:', err);
		}
	}

	async function handleSave() {
		try {
			await saveNote(true);
			hasUnsavedChanges = false;
			lastSaved = new Date();
		} catch (err) {
			error = err instanceof Error ? err.message : '保存失敗';
		}
	}

	async function saveNote(showFeedback = true) {
		if (!note) return;

		isSaving = true;

		try {
			const updateData = {
				title: formData.title,
				content: formData.content,
				excerpt: formData.content.substring(0, 200),
				tags: formData.tags,
				status: formData.status,
				priority: formData.priority,
				categoryId: formData.categoryId || undefined
			};

			// await noteStore.updateNote(note.id, updateData);

			// 更新本地數據
			note = { ...note, ...updateData, updatedAt: new Date() };

			if (showFeedback) {
				// 顯示保存成功提示
			}
		} finally {
			isSaving = false;
		}
	}

	function handleBack() {
		if (hasUnsavedChanges) {
			const confirmed = confirm('您有未保存的更改，確定要離開嗎？');
			if (!confirmed) return;
		}
		goto(`/notes/${noteId}`);
	}

	function handlePreview() {
		goto(`/notes/${noteId}`);
	}

	function togglePreviewMode() {
		isPreviewMode = !isPreviewMode;
	}

	function toggleSettings() {
		showSettings = !showSettings;
	}

	function handleTagsChange(event: CustomEvent<Array<{ name: string }>>) {
		formData.tags = event.detail;
	}

	function formatLastSaved(): string {
		if (!lastSaved) return '';

		const now = new Date();
		const diff = now.getTime() - lastSaved.getTime();

		if (diff < 60000) {
			// 小於1分鐘
			return '剛剛保存';
		} else if (diff < 3600000) {
			// 小於1小時
			const minutes = Math.floor(diff / 60000);
			return `${minutes}分鐘前保存`;
		} else {
			return lastSaved.toLocaleTimeString('zh-TW', {
				hour: '2-digit',
				minute: '2-digit'
			});
		}
	}
</script>

<svelte:head>
	<title>編輯 - {formData.title || '無標題'} - Life Note</title>
</svelte:head>

<div class="container mx-auto px-4 py-8">
	{#if isLoading}
		<div class="flex items-center justify-center min-h-96">
			<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
		</div>
	{:else if error}
		<Card class="p-8 text-center">
			<h2 class="text-xl font-semibold text-destructive mb-2">載入失敗</h2>
			<p class="text-muted-foreground mb-4">{error}</p>
			<Button on:click={handleBack}>返回</Button>
		</Card>
	{:else}
		<!-- 標題欄 -->
		<div class="flex items-center justify-between mb-6">
			<div class="flex items-center gap-4">
				<Button variant="ghost" on:click={handleBack}>
					<ArrowLeft class="h-4 w-4 mr-2" />
					返回
				</Button>

				<div class="flex items-center gap-2 text-sm text-muted-foreground">
					{#if hasUnsavedChanges}
						<AlertCircle class="h-4 w-4 text-yellow-500" />
						<span>有未保存的更改</span>
					{:else if lastSaved}
						<CheckCircle class="h-4 w-4 text-green-500" />
						<span>{formatLastSaved()}</span>
					{/if}
				</div>
			</div>

			<div class="flex items-center gap-2">
				<Button variant="outline" size="sm" on:click={togglePreviewMode}>
					{#if isPreviewMode}
						<EyeOff class="h-4 w-4 mr-1" />
						編輯
					{:else}
						<Eye class="h-4 w-4 mr-1" />
						預覽
					{/if}
				</Button>

				<Button variant="outline" size="sm" on:click={toggleSettings}>
					<Settings class="h-4 w-4 mr-1" />
					設定
				</Button>

				<Button variant="outline" size="sm" on:click={handlePreview}>
					<Eye class="h-4 w-4 mr-1" />
					查看
				</Button>

				<Button size="sm" on:click={handleSave} disabled={isSaving}>
					<Save class="h-4 w-4 mr-1" />
					{isSaving ? '保存中...' : '保存'}
				</Button>
			</div>
		</div>

		<div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
			<!-- 主要編輯區域 -->
			<div class="lg:col-span-3">
				<Card class="p-6">
					<!-- 標題輸入 -->
					<div class="mb-6">
						<input
							type="text"
							bind:value={formData.title}
							placeholder="輸入筆記標題..."
							class="w-full text-3xl font-bold bg-transparent border-none outline-none placeholder-muted-foreground"
						/>
					</div>

					<!-- 內容編輯器 -->
					<div class="min-h-96">
						{#if isPreviewMode}
							<MarkdownRenderer content={formData.content} />
						{:else}
							<MarkdownEditor
								bind:value={formData.content}
								placeholder="開始編寫您的筆記..."
								class="min-h-96"
							/>
						{/if}
					</div>
				</Card>
			</div>

			<!-- 側邊欄設定 -->
			<div class="lg:col-span-1 space-y-4">
				<!-- 基本設定 -->
				<Card class="p-4">
					<h3 class="font-semibold mb-3">筆記設定</h3>

					<div class="space-y-4">
						<div>
							<label class="text-sm font-medium">狀態</label>
							<select bind:value={formData.status} class="w-full mt-1 p-2 border rounded">
								<option value="draft">草稿</option>
								<option value="published">已發布</option>
								<option value="archived">已歸檔</option>
							</select>
						</div>

						<div>
							<label class="text-sm font-medium">優先級</label>
							<select bind:value={formData.priority} class="w-full mt-1 p-2 border rounded">
								<option value="low">低</option>
								<option value="medium">中</option>
								<option value="high">高</option>
								<option value="urgent">緊急</option>
							</select>
						</div>
					</div>
				</Card>

				<!-- 標籤管理 -->
				<Card class="p-4">
					<h3 class="font-semibold mb-3 flex items-center gap-2">
						<Tag class="h-4 w-4" />
						標籤
					</h3>
					<TagInput tags={formData.tags} on:change={handleTagsChange} />
				</Card>

				<!-- 統計信息 -->
				<Card class="p-4">
					<h3 class="font-semibold mb-3">統計</h3>
					<div class="space-y-2 text-sm">
						<div class="flex justify-between">
							<span class="text-muted-foreground">字符數</span>
							<span>{formData.content.length}</span>
						</div>
						<div class="flex justify-between">
							<span class="text-muted-foreground">單詞數</span>
							<span>{formData.content.split(/\s+/).filter(word => word.length > 0).length}</span>
						</div>
						<div class="flex justify-between">
							<span class="text-muted-foreground">行數</span>
							<span>{formData.content.split('\n').length}</span>
						</div>
					</div>
				</Card>

				<!-- 最後更新 -->
				{#if note}
					<Card class="p-4">
						<h3 class="font-semibold mb-3 flex items-center gap-2">
							<Calendar class="h-4 w-4" />
							時間信息
						</h3>
						<div class="space-y-2 text-sm">
							<div>
								<span class="text-muted-foreground">創建時間</span>
								<div class="mt-1">
									{new Date(note.createdAt).toLocaleString('zh-TW')}
								</div>
							</div>
							<div>
								<span class="text-muted-foreground">最後更新</span>
								<div class="mt-1">
									{new Date(note.updatedAt).toLocaleString('zh-TW')}
								</div>
							</div>
						</div>
					</Card>
				{/if}
			</div>
		</div>
	{/if}
</div>
