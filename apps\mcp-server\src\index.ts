#!/usr/bin/env node

import { Command } from "commander";
import { config } from "dotenv";
import { LifeNoteMCPServer } from "./server/LifeNoteMCPServer.js";
import { logger } from "./utils/logger.js";
import { validateConfig } from "./utils/config.js";

// 載入環境變數
config();

const program = new Command();

program
  .name("life-note-mcp")
  .description("Life Note MCP Server for AI Agent Integration")
  .version("0.1.0");

program
  .command("start")
  .description("Start the Life Note MCP Server")
  .option("-p, --port <port>", "Server port", "3001")
  .option("-h, --host <host>", "Server host", "localhost")
  .option("--notes-dir <dir>", "Notes directory path", "./notes")
  .option("--config <file>", "Configuration file path")
  .option("--log-level <level>", "Log level (debug, info, warn, error)", "info")
  .option("--enable-cors", "Enable CORS for web clients", false)
  .option("--enable-auth", "Enable authentication", false)
  .action(async (options) => {
    try {
      // 驗證配置
      const config = await validateConfig(options);

      // 創建並啟動 MCP Server
      const server = new LifeNoteMCPServer(config);

      // 處理優雅關閉
      const gracefulShutdown = async (signal: string) => {
        logger.info(`Received ${signal}, shutting down gracefully...`);
        try {
          await server.stop();
          process.exit(0);
        } catch (error) {
          logger.error("Error during shutdown:", error);
          process.exit(1);
        }
      };

      process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
      process.on("SIGINT", () => gracefulShutdown("SIGINT"));
      process.on("SIGUSR2", () => gracefulShutdown("SIGUSR2")); // nodemon restart

      // 啟動服務器
      await server.start();

      logger.info(`Life Note MCP Server started successfully`);
      logger.info(`Server running on ${config.host}:${config.port}`);
      logger.info(`Notes directory: ${config.notesDir}`);
      logger.info(`Log level: ${config.logLevel}`);
    } catch (error) {
      logger.error("Failed to start server:", error);
      process.exit(1);
    }
  });

program
  .command("validate")
  .description("Validate configuration and notes directory")
  .option("--notes-dir <dir>", "Notes directory path", "./notes")
  .option("--config <file>", "Configuration file path")
  .action(async (options) => {
    try {
      const config = await validateConfig(options);
      logger.info("Configuration validation successful");
      logger.info("Configuration:", config);
    } catch (error) {
      logger.error("Configuration validation failed:", error);
      process.exit(1);
    }
  });

program
  .command("info")
  .description("Display server information")
  .action(() => {
    console.log(`
Life Note MCP Server v0.1.0

A Model Context Protocol (MCP) server that provides AI agents with access to Life Note
knowledge management system. This server enables AI agents to:

• Read and search through notes
• Analyze note dependencies and relationships
• Access note metadata and tags
• Perform semantic search across the knowledge base
• Monitor note changes and updates

Features:
• RESTful API for note operations
• Real-time note change monitoring
• Semantic search capabilities
• Dependency analysis
• Tag-based organization
• Markdown support
• File system integration

For more information, visit: https://github.com/life-note/life-note-client-vibe
    `);
  });

program
  .command("test-connection")
  .description("Test connection to notes directory")
  .option("--notes-dir <dir>", "Notes directory path", "./notes")
  .action(async (options) => {
    try {
      const { NotesService } = await import("./services/NotesService.js");
      const notesService = new NotesService(options.notesDir);

      const notes = await notesService.getAllNotes();
      logger.info(`Successfully connected to notes directory`);
      logger.info(`Found ${notes.length} notes`);

      if (notes.length > 0) {
        logger.info("Sample notes:");
        notes.slice(0, 3).forEach((note) => {
          logger.info(`  - ${note.title} (${note.id})`);
        });
      }
    } catch (error) {
      logger.error("Failed to connect to notes directory:", error);
      process.exit(1);
    }
  });

// 錯誤處理
process.on("uncaughtException", (error) => {
  logger.error("Uncaught Exception:", error);
  process.exit(1);
});

process.on("unhandledRejection", (reason, promise) => {
  logger.error("Unhandled Rejection at:", promise, "reason:", reason);
  process.exit(1);
});

// 解析命令行參數
program.parse();

// 如果沒有提供命令，顯示幫助
if (!process.argv.slice(2).length) {
  program.outputHelp();
}
