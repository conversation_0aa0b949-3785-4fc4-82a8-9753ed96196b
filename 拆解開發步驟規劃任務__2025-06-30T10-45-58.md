[ ] NAME:Current Task List DESCRIPTION:Root task for conversation **NEW_AGENT** -[x] NAME:項目初始化與基礎架構搭建 DESCRIPTION:建立 monorepo 結構、配置開發環境、設置基礎工具鏈
--[x] NAME:建立 Monorepo 結構 DESCRIPTION:創建 apps/、packages/、services/ 目錄結構，配置 pnpm workspaces
--[x] NAME:配置開發工具鏈 DESCRIPTION:設置 ESLint、Prettier、TypeScript、Husky 等開發工具
--[x] NAME:環境變數與配置管理 DESCRIPTION:建立 .env 模板和配置管理系統 -[x] NAME:核心領域模型設計與實現 DESCRIPTION:實現 Note、Dependency、Version 等核心實體和值對象
--[x] NAME:設計領域實體 DESCRIPTION:定義 Note、Dependency、Version、User 等核心實體
--[x] NAME:實現值對象 DESCRIPTION:創建 NoteId、Tag、Version 等值對象
--[x] NAME:建立聚合根 DESCRIPTION:實現 NoteAggregate、DependencyGraph 等聚合根 -[x] NAME:本地存儲層實現 DESCRIPTION:配置 SQLite + Prisma ORM，實現基礎 CRUD 操作
--[x] NAME:配置 Prisma ORM DESCRIPTION:設置 SQLite 資料庫和 Prisma schema
--[x] NAME:實現 Repository 模式 DESCRIPTION:創建 NoteRepository、DependencyRepository 等
--[x] NAME:資料庫遷移與種子 DESCRIPTION:建立初始資料庫結構和測試資料 -[x] NAME:前端基礎架構搭建 DESCRIPTION:配置 Svelte + SvelteKit + TypeScript 基礎環境，建立 UI 組件庫和狀態管理
--[x] NAME:配置 Svelte + SvelteKit 基礎環境 DESCRIPTION:建立 Web 應用的基礎架構和構建配置，使用 Svelte + SvelteKit + TypeScript
--[x] NAME:建立 UI 組件庫 DESCRIPTION:創建通用組件（Button、Modal、Form 等）
--[x] NAME:配置狀態管理 DESCRIPTION:設置 Svelte Stores 狀態管理方案，包含應用狀態、主題管理和通知系統 -[x] NAME:筆記管理核心功能 DESCRIPTION:實現筆記的創建、編輯、版本控制和 Markdown 處理
--[x] NAME:實現筆記編輯器 DESCRIPTION:創建 Markdown 編輯器組件，支援實時預覽
--[x] NAME:版本控制系統 DESCRIPTION:實現 draft-v1 → v1 → draft-v2 的版本流程
--[x] NAME:筆記檔案管理 DESCRIPTION:實現筆記的創建、儲存、刪除功能 -[/] NAME:依賴關係檢測系統 DESCRIPTION:實現文件間依賴關係的自動檢測和可視化
--[x] NAME:文件依賴分析引擎 DESCRIPTION:開發自動檢測文件間依賴關係的演算法
--[x] NAME:依賴關係視覺化 DESCRIPTION:實現依賴關係圖和樹狀結構顯示
--[x] NAME:依賴更新通知 DESCRIPTION:當依賴文件變更時的自動通知機制 -[x] NAME:MCP Server 實現 DESCRIPTION:基於 Model Context Protocol 實現 AI 整合服務
--[x] NAME:實現 MCP TypeScript SDK 整合 DESCRIPTION:基於 @modelcontextprotocol/sdk 建立 MCP 服務器 - 已完成技術規範和實現指南
--[ ] NAME:筆記管理 MCP 工具 DESCRIPTION:實現筆記 CRUD 操作的 MCP 工具和資源 - 已有詳細實現範例
--[ ] NAME:依賴分析 MCP 服務 DESCRIPTION:將依賴關係檢測功能包裝為 MCP 工具 - 已有詳細實現範例 -[x] NAME:本地 AI Agent 服務 DESCRIPTION:實現本地輕量級 AI 助理和智慧路由機制
--[x] NAME:本地 Agent 核心架構 DESCRIPTION:實現 Agent 基礎類和 AgentManager
--[x] NAME:智慧路由機制 DESCRIPTION:實現根據任務複雜度選擇本地/遠端處理
--[ ] NAME:本地 AI 能力整合 DESCRIPTION:整合 Transformers.js 和 ONNX Runtime 本地推理 -[ ] NAME:Tauri 桌面應用整合 DESCRIPTION:配置 Tauri 框架，實現跨平台桌面應用打包
--[ ] NAME:配置 Tauri 基礎環境 DESCRIPTION:初始化 Tauri 專案和基礎配置
--[ ] NAME:實現原生功能整合 DESCRIPTION:整合檔案系統、系統通知等原生功能
--[ ] NAME:跨平台打包配置 DESCRIPTION:配置 Windows、macOS、Linux 的打包設定 -[ ] NAME:測試框架與 CI/CD DESCRIPTION:建立完整的測試體系和自動化部署流程
--[ ] NAME:建立單元測試框架 DESCRIPTION:配置 Vitest + React Testing Library
--[ ] NAME:實現整合測試 DESCRIPTION:建立 API 和 Agent 間通訊的整合測試
--[ ] NAME:配置 CI/CD 流程 DESCRIPTION:設置 GitHub Actions 自動化測試和部署 -[x] NAME:實現 Markdown 筆記編輯器 DESCRIPTION:創建功能完整的 Markdown 編輯器，支援實時預覽、語法高亮、工具欄和快捷鍵 -[x] NAME:實現搜索和過濾系統 DESCRIPTION:創建全文搜索、標籤過濾、分類篩選和高級搜索功能 -[x] NAME:實現依賴關係可視化 DESCRIPTION:使用圖形庫創建互動式的筆記依賴關係圖表和網絡視圖 -[x] NAME:完善筆記管理功能 DESCRIPTION:實現筆記的 CRUD 操作、版本歷史、標籤管理和批量操作 -[ ] NAME:添加用戶體驗增強功能 DESCRIPTION:實現快捷鍵支援、拖拽操作、自動保存和離線提示
