<script lang="ts">
	import { createEventDispatcher, onMount } from 'svelte';
	import { tv, type VariantProps } from 'tailwind-variants';
	import { X } from 'lucide-svelte';
	import Button from './Button.svelte';

	const modalVariants = tv({
		base: 'relative bg-background rounded-lg shadow-lg border border-border',
		variants: {
			size: {
				sm: 'max-w-sm',
				default: 'max-w-md',
				lg: 'max-w-lg',
				xl: 'max-w-xl',
				'2xl': 'max-w-2xl',
				'3xl': 'max-w-3xl',
				'4xl': 'max-w-4xl',
				full: 'max-w-full mx-4'
			}
		},
		defaultVariants: {
			size: 'default'
		}
	});

	type Size = VariantProps<typeof modalVariants>['size'];

	interface $$Props {
		open?: boolean;
		size?: Size;
		title?: string;
		description?: string;
		showCloseButton?: boolean;
		closeOnEscape?: boolean;
		closeOnBackdrop?: boolean;
		class?: string;
	}

	export let open: boolean = false;
	export let size: Size = 'default';
	export let title: string = '';
	export let description: string = '';
	export let showCloseButton: boolean = true;
	export let closeOnEscape: boolean = true;
	export let closeOnBackdrop: boolean = true;
	let className: string = '';
	export { className as class };

	const dispatch = createEventDispatcher<{
		close: void;
		open: void;
	}>();

	let dialogElement: HTMLDialogElement;
	let previousActiveElement: Element | null = null;

	$: computedClass = modalVariants({ size, class: className });

	// Handle open/close state
	$: if (open) {
		openModal();
	} else {
		closeModal();
	}

	const openModal = () => {
		if (dialogElement && !dialogElement.open) {
			previousActiveElement = document.activeElement;
			dialogElement.showModal();
			dispatch('open');
		}
	};

	const closeModal = () => {
		if (dialogElement && dialogElement.open) {
			dialogElement.close();
			if (previousActiveElement instanceof HTMLElement) {
				previousActiveElement.focus();
			}
			dispatch('close');
		}
	};

	const handleKeydown = (event: KeyboardEvent) => {
		if (event.key === 'Escape' && closeOnEscape) {
			event.preventDefault();
			open = false;
		}
	};

	const handleBackdropClick = (event: MouseEvent) => {
		if (closeOnBackdrop && event.target === dialogElement) {
			open = false;
		}
	};

	const handleCloseClick = () => {
		open = false;
	};

	onMount(() => {
		return () => {
			if (dialogElement && dialogElement.open) {
				dialogElement.close();
			}
		};
	});
</script>

<!-- svelte-ignore a11y-click-events-have-key-events a11y-no-noninteractive-element-interactions -->
<dialog
	bind:this={dialogElement}
	class="backdrop:bg-black/50 backdrop:backdrop-blur-sm"
	onkeydown={handleKeydown}
	onclick={handleBackdropClick}
>
	<div class={computedClass}>
		<!-- Header -->
		{#if title || showCloseButton || $$slots.header}
			<div class="flex items-center justify-between p-6 border-b border-border">
				<div class="flex-1">
					<slot name="header">
						{#if title}
							<h2 class="text-lg font-semibold leading-none tracking-tight">
								{title}
							</h2>
						{/if}
						{#if description}
							<p class="text-sm text-muted-foreground mt-1">
								{description}
							</p>
						{/if}
					</slot>
				</div>

				{#if showCloseButton}
					<Button
						variant="ghost"
						size="icon"
						class="h-6 w-6 rounded-full"
						onclick={handleCloseClick}
						aria-label="Close modal"
					>
						<X class="h-4 w-4" />
					</Button>
				{/if}
			</div>
		{/if}

		<!-- Content -->
		<div class="p-6">
			<slot />
		</div>

		<!-- Footer -->
		{#if $$slots.footer}
			<div class="flex items-center justify-end space-x-2 p-6 border-t border-border">
				<slot name="footer" />
			</div>
		{/if}
	</div>
</dialog>

<style>
	dialog {
		padding: 0;
		border: none;
		background: transparent;
		max-width: none;
		max-height: none;
	}

	dialog::backdrop {
		background: rgba(0, 0, 0, 0.5);
		backdrop-filter: blur(4px);
	}

	dialog[open] {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100vw;
		height: 100vh;
	}
</style>
