<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import { <PERSON><PERSON>, <PERSON>, Badge, Modal } from '$components/ui';
	import MarkdownPreview from '$lib/components/editor/MarkdownPreview.svelte';
	import VersionHistory from './VersionHistory.svelte';
	import {
		Edit,
		Archive,
		Trash2,
		Copy,
		Share,
		Calendar,
		Tag as TagIcon,
		User,
		Clock,
		FileText,
		History,
		X
	} from 'lucide-svelte';
	import type { Note } from '$types';

	// Props
	export let note: Note | null = null;
	export let open: boolean = false;

	// Events
	const dispatch = createEventDispatcher<{
		close: void;
		edit: { note: Note };
		archive: { note: Note };
		delete: { note: Note };
		duplicate: { note: Note };
		versionRestore: { note: Note };
	}>();

	// State
	let showVersionHistory = false;

	// Reactive statements
	$: if (!note && open) {
		open = false;
	}

	const handleClose = () => {
		dispatch('close');
	};

	const handleEdit = () => {
		if (note) {
			dispatch('edit', { note });
		}
	};

	const handleArchive = () => {
		if (note) {
			dispatch('archive', { note });
		}
	};

	const handleDelete = () => {
		if (note && confirm(`確定要刪除筆記 "${note.title}" 嗎？此操作無法撤銷。`)) {
			dispatch('delete', { note });
		}
	};

	const handleDuplicate = () => {
		if (note) {
			dispatch('duplicate', { note });
		}
	};

	const handleShowVersionHistory = () => {
		showVersionHistory = true;
	};

	const handleVersionRestore = (event: CustomEvent<{ note: Note }>) => {
		showVersionHistory = false;
		dispatch('versionRestore', { note: event.detail.note });
	};

	const getStatusColor = (status: string) => {
		switch (status) {
			case 'draft':
				return 'secondary';
			case 'published':
				return 'success';
			case 'archived':
				return 'outline';
			default:
				return 'secondary';
		}
	};

	const getPriorityColor = (priority: string) => {
		switch (priority) {
			case 'urgent':
				return 'destructive';
			case 'high':
				return 'warning';
			case 'medium':
				return 'default';
			case 'low':
				return 'secondary';
			default:
				return 'secondary';
		}
	};

	const formatDate = (date: Date) => {
		return new Intl.DateTimeFormat('zh-TW', {
			year: 'numeric',
			month: 'long',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit'
		}).format(new Date(date));
	};

	const getStatusText = (status: string) => {
		switch (status) {
			case 'draft':
				return '草稿';
			case 'published':
				return '已發布';
			case 'archived':
				return '已歸檔';
			default:
				return status;
		}
	};

	const getPriorityText = (priority: string) => {
		switch (priority) {
			case 'urgent':
				return '緊急';
			case 'high':
				return '高';
			case 'medium':
				return '中';
			case 'low':
				return '低';
			default:
				return priority;
		}
	};
</script>

{#if note}
	<Modal bind:open title="" size="4xl" showCloseButton={false} on:close={handleClose}>
		<div class="space-y-6">
			<!-- Header -->
			<div class="flex items-start justify-between">
				<div class="flex-1 min-w-0">
					<h1 class="text-2xl font-bold mb-2">{note.title}</h1>

					<!-- Meta info -->
					<div class="flex flex-wrap items-center gap-4 text-sm text-muted-foreground mb-4">
						<div class="flex items-center gap-1">
							<Calendar class="h-4 w-4" />
							創建於 {formatDate(note.createdAt)}
						</div>

						{#if note.updatedAt.getTime() !== note.createdAt.getTime()}
							<div class="flex items-center gap-1">
								<Clock class="h-4 w-4" />
								更新於 {formatDate(note.updatedAt)}
							</div>
						{/if}

						<div class="flex items-center gap-1">
							<User class="h-4 w-4" />
							版本 {note.version}
						</div>

						<div class="flex items-center gap-1">
							<FileText class="h-4 w-4" />
							{note.content.length} 字符
						</div>
					</div>

					<!-- Status and Priority -->
					<div class="flex items-center gap-2 mb-4">
						<Badge variant={getStatusColor(note.status)}>
							{getStatusText(note.status)}
						</Badge>
						<Badge variant={getPriorityColor(note.priority)}>
							{getPriorityText(note.priority)}
						</Badge>
					</div>

					<!-- Tags -->
					{#if note.tags.length > 0}
						<div class="flex flex-wrap gap-2 mb-4">
							{#each note.tags as tag}
								<Badge variant="outline" size="sm">
									<TagIcon class="mr-1 h-3 w-3" />
									{tag.name}
								</Badge>
							{/each}
						</div>
					{/if}
				</div>

				<!-- Actions -->
				<div class="flex items-center gap-2 ml-4">
					<Button variant="outline" size="sm" onclick={handleEdit}>
						<Edit class="mr-2 h-4 w-4" />
						編輯
					</Button>

					<Button variant="outline" size="sm" onclick={handleDuplicate}>
						<Copy class="mr-2 h-4 w-4" />
						複製
					</Button>

					<Button variant="outline" size="sm" onclick={handleShowVersionHistory}>
						<History class="mr-2 h-4 w-4" />
						版本歷史
					</Button>

					{#if note.status !== 'archived'}
						<Button variant="outline" size="sm" onclick={handleArchive}>
							<Archive class="mr-2 h-4 w-4" />
							歸檔
						</Button>
					{/if}

					<Button variant="outline" size="sm" onclick={handleDelete}>
						<Trash2 class="mr-2 h-4 w-4" />
						刪除
					</Button>

					<Button variant="ghost" size="sm" onclick={handleClose}>
						<X class="h-4 w-4" />
					</Button>
				</div>
			</div>

			<!-- Content -->
			<Card class="p-6">
				{#if note.content.trim()}
					<MarkdownPreview content={note.content} />
				{:else}
					<div class="text-center text-muted-foreground py-8">
						<FileText class="h-12 w-12 mx-auto mb-4 opacity-50" />
						<p>此筆記沒有內容</p>
					</div>
				{/if}
			</Card>

			<!-- Metadata -->
			{#if note.metadata && Object.keys(note.metadata).length > 0}
				<Card class="p-4">
					<h3 class="text-sm font-medium mb-2">元數據</h3>
					<div class="text-xs text-muted-foreground space-y-1">
						{#each Object.entries(note.metadata) as [key, value]}
							<div class="flex justify-between">
								<span class="font-medium">{key}:</span>
								<span>{JSON.stringify(value)}</span>
							</div>
						{/each}
					</div>
				</Card>
			{/if}
		</div>
	</Modal>

	<!-- Version History Modal -->
	<VersionHistory {note} bind:open={showVersionHistory} on:restore={handleVersionRestore} />
{/if}
