import type {
  PrismaClient,
  Dependency as PrismaDependency,
} from "../prisma/generated/index.js";
import type {
  PaginatedResult,
  PaginationParams,
} from "../interfaces/IRepository.js";
import { BaseRepository } from "./BaseRepository.js";

/**
 * 依賴關係實體接口（簡化版本）
 */
export interface Dependency {
  id: string;
  sourceNoteId: string;
  targetNoteId: string;
  type:
    | "reference"
    | "include"
    | "extends"
    | "implements"
    | "uses"
    | "depends_on";
  strength: "weak" | "medium" | "strong" | "critical";
  description?: string;
  metadata?: Record<string, unknown>;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 依賴關係查詢條件
 */
export interface DependencyQueryCondition {
  sourceNoteId?: string;
  targetNoteId?: string;
  type?: Dependency["type"];
  strength?: Dependency["strength"];
  isActive?: boolean;
  createdAfter?: Date;
  createdBefore?: Date;
}

/**
 * 依賴關係統計信息
 */
export interface DependencyStatistics {
  total: number;
  active: number;
  inactive: number;
  byType: Record<Dependency["type"], number>;
  byStrength: Record<Dependency["strength"], number>;
  averageDependenciesPerNote: number;
  mostConnectedNotes: Array<{
    noteId: string;
    connectionCount: number;
  }>;
  circularDependencies: number;
}

/**
 * 依賴路徑信息
 */
export interface DependencyPath {
  path: string[];
  length: number;
  isCircular: boolean;
  totalStrength: number;
  weakestLink: {
    sourceId: string;
    targetId: string;
    strength: Dependency["strength"];
  };
}

/**
 * 依賴圖節點信息
 */
export interface DependencyNode {
  noteId: string;
  title: string;
  incomingCount: number;
  outgoingCount: number;
  totalConnections: number;
  dependencies: Array<{
    targetId: string;
    type: Dependency["type"];
    strength: Dependency["strength"];
  }>;
  dependents: Array<{
    sourceId: string;
    type: Dependency["type"];
    strength: Dependency["strength"];
  }>;
}

/**
 * 依賴關係存儲庫實現
 */
export class DependencyRepository extends BaseRepository<
  Dependency,
  string,
  PrismaDependency
> {
  constructor(prisma: PrismaClient) {
    super(prisma);
  }

  protected getModel() {
    return this.prisma.dependency;
  }

  protected toDomainEntity(model: PrismaDependency): Dependency {
    return {
      id: model.id,
      sourceNoteId: model.sourceNoteId,
      targetNoteId: model.targetNoteId,
      type: model.type as Dependency["type"],
      strength: model.strength as Dependency["strength"],
      description: model.description || undefined,
      metadata: this.parseJson<Record<string, unknown>>(model.metadata),
      isActive: model.isActive,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    };
  }

  protected toPrismaModel(
    entity: Dependency,
  ): Omit<PrismaDependency, "id" | "createdAt" | "updatedAt"> {
    return {
      sourceNoteId: entity.sourceNoteId,
      targetNoteId: entity.targetNoteId,
      type: entity.type,
      strength: entity.strength,
      description: entity.description || null,
      metadata: this.stringifyJson(entity.metadata),
      isActive: entity.isActive,
    };
  }

  protected extractId(entity: Dependency): string {
    return entity.id;
  }

  /**
   * 根據源筆記 ID 查找依賴關係
   */
  async findBySourceNoteId(
    sourceNoteId: string,
    params?: PaginationParams,
  ): Promise<PaginatedResult<Dependency>> {
    const condition = { sourceNoteId };
    return params
      ? await this.findByConditionPaginated(condition, params)
      : await this.findByConditionPaginated(condition, { page: 1, limit: 50 });
  }

  /**
   * 根據目標筆記 ID 查找依賴關係
   */
  async findByTargetNoteId(
    targetNoteId: string,
    params?: PaginationParams,
  ): Promise<PaginatedResult<Dependency>> {
    const condition = { targetNoteId };
    return params
      ? await this.findByConditionPaginated(condition, params)
      : await this.findByConditionPaginated(condition, { page: 1, limit: 50 });
  }

  /**
   * 根據筆記 ID 查找所有相關依賴關係
   */
  async findByNoteId(
    noteId: string,
    params?: PaginationParams,
  ): Promise<PaginatedResult<Dependency>> {
    try {
      const {
        page = 1,
        limit = 50,
        sortBy = "createdAt",
        sortOrder = "desc",
      } = params || {};
      const skip = (page - 1) * limit;

      const whereClause = {
        OR: [{ sourceNoteId: noteId }, { targetNoteId: noteId }],
      };

      const [results, total] = await Promise.all([
        this.prisma.dependency.findMany({
          where: whereClause,
          skip,
          take: limit,
          orderBy: { [sortBy]: sortOrder },
        }),
        this.prisma.dependency.count({
          where: whereClause,
        }),
      ]);

      const entities = results.map((result) => this.toDomainEntity(result));
      return this.createPaginatedResult(entities, total, page, limit);
    } catch (error) {
      console.error("Error finding dependencies by note ID:", error);
      throw error;
    }
  }

  /**
   * 根據依賴類型查找依賴關係
   */
  async findByType(
    type: Dependency["type"],
    params?: PaginationParams,
  ): Promise<PaginatedResult<Dependency>> {
    const condition = { type };
    return params
      ? await this.findByConditionPaginated(condition, params)
      : await this.findByConditionPaginated(condition, { page: 1, limit: 50 });
  }

  /**
   * 根據依賴強度查找依賴關係
   */
  async findByStrength(
    strength: Dependency["strength"],
    params?: PaginationParams,
  ): Promise<PaginatedResult<Dependency>> {
    const condition = { strength };
    return params
      ? await this.findByConditionPaginated(condition, params)
      : await this.findByConditionPaginated(condition, { page: 1, limit: 50 });
  }

  /**
   * 根據複合條件查找依賴關係
   */
  async findByCondition(
    condition: DependencyQueryCondition,
    params?: PaginationParams,
  ): Promise<PaginatedResult<Dependency>> {
    try {
      const {
        page = 1,
        limit = 50,
        sortBy = "createdAt",
        sortOrder = "desc",
      } = params || {};
      const skip = (page - 1) * limit;

      // 構建查詢條件
      const whereClause: any = {};

      if (condition.sourceNoteId) {
        whereClause.sourceNoteId = condition.sourceNoteId;
      }

      if (condition.targetNoteId) {
        whereClause.targetNoteId = condition.targetNoteId;
      }

      if (condition.type) {
        whereClause.type = condition.type;
      }

      if (condition.strength) {
        whereClause.strength = condition.strength;
      }

      if (condition.isActive !== undefined) {
        whereClause.isActive = condition.isActive;
      }

      if (condition.createdAfter || condition.createdBefore) {
        whereClause.createdAt = {};
        if (condition.createdAfter) {
          whereClause.createdAt.gte = condition.createdAfter;
        }
        if (condition.createdBefore) {
          whereClause.createdAt.lte = condition.createdBefore;
        }
      }

      const [results, total] = await Promise.all([
        this.prisma.dependency.findMany({
          where: whereClause,
          skip,
          take: limit,
          orderBy: { [sortBy]: sortOrder },
        }),
        this.prisma.dependency.count({
          where: whereClause,
        }),
      ]);

      const entities = results.map((result) => this.toDomainEntity(result));
      return this.createPaginatedResult(entities, total, page, limit);
    } catch (error) {
      console.error("Error finding dependencies by condition:", error);
      throw error;
    }
  }

  /**
   * 查找活躍的依賴關係
   */
  async findActive(
    params?: PaginationParams,
  ): Promise<PaginatedResult<Dependency>> {
    const condition = { isActive: true };
    return params
      ? await this.findByConditionPaginated(condition, params)
      : await this.findByConditionPaginated(condition, { page: 1, limit: 50 });
  }

  /**
   * 查找非活躍的依賴關係
   */
  async findInactive(
    params?: PaginationParams,
  ): Promise<PaginatedResult<Dependency>> {
    const condition = { isActive: false };
    return params
      ? await this.findByConditionPaginated(condition, params)
      : await this.findByConditionPaginated(condition, { page: 1, limit: 50 });
  }

  /**
   * 檢查兩個筆記之間是否存在依賴關係
   */
  async existsBetween(
    sourceNoteId: string,
    targetNoteId: string,
    type?: Dependency["type"],
  ): Promise<boolean> {
    try {
      const whereClause: any = {
        sourceNoteId,
        targetNoteId,
      };

      if (type) {
        whereClause.type = type;
      }

      const count = await this.prisma.dependency.count({
        where: whereClause,
      });

      return count > 0;
    } catch (error) {
      console.error("Error checking dependency existence:", error);
      throw error;
    }
  }

  /**
   * 獲取兩個筆記之間的依賴關係
   */
  async findBetween(
    sourceNoteId: string,
    targetNoteId: string,
    type?: Dependency["type"],
  ): Promise<Dependency | null> {
    try {
      const whereClause: any = {
        sourceNoteId,
        targetNoteId,
      };

      if (type) {
        whereClause.type = type;
      }

      const result = await this.prisma.dependency.findFirst({
        where: whereClause,
      });

      return result ? this.toDomainEntity(result) : null;
    } catch (error) {
      console.error("Error finding dependency between notes:", error);
      throw error;
    }
  }

  /**
   * 創建依賴關係
   */
  async createDependency(
    sourceNoteId: string,
    targetNoteId: string,
    type: Dependency["type"],
    strength: Dependency["strength"] = "medium",
    description?: string,
  ): Promise<Dependency> {
    try {
      const result = await this.prisma.dependency.create({
        data: {
          sourceNoteId,
          targetNoteId,
          type,
          strength,
          description: description || null,
          isActive: true,
        },
      });

      return this.toDomainEntity(result);
    } catch (error) {
      console.error("Error creating dependency:", error);
      throw error;
    }
  }

  /**
   * 批量創建依賴關係
   */
  async batchCreateDependencies(
    dependencies: Array<{
      sourceNoteId: string;
      targetNoteId: string;
      type: Dependency["type"];
      strength?: Dependency["strength"];
      description?: string;
    }>,
  ): Promise<Dependency[]> {
    try {
      const results: Dependency[] = [];

      await this.prisma.$transaction(async (tx) => {
        for (const dep of dependencies) {
          const result = await tx.dependency.create({
            data: {
              sourceNoteId: dep.sourceNoteId,
              targetNoteId: dep.targetNoteId,
              type: dep.type,
              strength: dep.strength || "medium",
              description: dep.description || null,
              isActive: true,
            },
          });
          results.push(this.toDomainEntity(result));
        }
      });

      return results;
    } catch (error) {
      console.error("Error batch creating dependencies:", error);
      throw error;
    }
  }

  /**
   * 更新依賴關係強度
   */
  async updateStrength(
    dependencyId: string,
    strength: Dependency["strength"],
  ): Promise<void> {
    try {
      await this.prisma.dependency.update({
        where: { id: dependencyId },
        data: { strength },
      });
    } catch (error) {
      console.error("Error updating dependency strength:", error);
      throw error;
    }
  }

  /**
   * 激活依賴關係
   */
  async activate(dependencyId: string): Promise<void> {
    try {
      await this.prisma.dependency.update({
        where: { id: dependencyId },
        data: { isActive: true },
      });
    } catch (error) {
      console.error("Error activating dependency:", error);
      throw error;
    }
  }

  /**
   * 停用依賴關係
   */
  async deactivate(dependencyId: string): Promise<void> {
    try {
      await this.prisma.dependency.update({
        where: { id: dependencyId },
        data: { isActive: false },
      });
    } catch (error) {
      console.error("Error deactivating dependency:", error);
      throw error;
    }
  }

  /**
   * 刪除筆記的所有依賴關係
   */
  async deleteByNoteId(noteId: string): Promise<void> {
    try {
      await this.prisma.dependency.deleteMany({
        where: {
          OR: [{ sourceNoteId: noteId }, { targetNoteId: noteId }],
        },
      });
    } catch (error) {
      console.error("Error deleting dependencies by note ID:", error);
      throw error;
    }
  }

  /**
   * 獲取依賴關係統計信息
   */
  async getStatistics(): Promise<DependencyStatistics> {
    try {
      const [total, active, typeCounts, strengthCounts] = await Promise.all([
        this.prisma.dependency.count(),
        this.prisma.dependency.count({ where: { isActive: true } }),
        this.prisma.dependency.groupBy({
          by: ["type"],
          _count: { type: true },
        }),
        this.prisma.dependency.groupBy({
          by: ["strength"],
          _count: { strength: true },
        }),
      ]);

      const inactive = total - active;

      // 計算最連接的筆記（簡化版本）
      const mostConnectedNotes: Array<{
        noteId: string;
        connectionCount: number;
      }> = [];

      return {
        total,
        active,
        inactive,
        byType: typeCounts.reduce(
          (acc, item) => {
            acc[item.type as Dependency["type"]] = item._count.type;
            return acc;
          },
          {} as Record<Dependency["type"], number>,
        ),
        byStrength: strengthCounts.reduce(
          (acc, item) => {
            acc[item.strength as Dependency["strength"]] = item._count.strength;
            return acc;
          },
          {} as Record<Dependency["strength"], number>,
        ),
        averageDependenciesPerNote: 0, // 需要計算
        mostConnectedNotes,
        circularDependencies: 0, // 需要實現循環檢測
      };
    } catch (error) {
      console.error("Error getting dependency statistics:", error);
      throw error;
    }
  }

  /**
   * 獲取筆記的依賴統計信息
   */
  async getNoteStatistics(noteId: string): Promise<{
    incomingCount: number;
    outgoingCount: number;
    totalConnections: number;
    byType: Record<Dependency["type"], number>;
    byStrength: Record<Dependency["strength"], number>;
  }> {
    try {
      const [incomingCount, outgoingCount, dependencies] = await Promise.all([
        this.prisma.dependency.count({
          where: { targetNoteId: noteId, isActive: true },
        }),
        this.prisma.dependency.count({
          where: { sourceNoteId: noteId, isActive: true },
        }),
        this.prisma.dependency.findMany({
          where: {
            OR: [{ sourceNoteId: noteId }, { targetNoteId: noteId }],
            isActive: true,
          },
        }),
      ]);

      const totalConnections = incomingCount + outgoingCount;

      const byType = dependencies.reduce(
        (acc, dep) => {
          const type = dep.type as Dependency["type"];
          acc[type] = (acc[type] || 0) + 1;
          return acc;
        },
        {} as Record<Dependency["type"], number>,
      );

      const byStrength = dependencies.reduce(
        (acc, dep) => {
          const strength = dep.strength as Dependency["strength"];
          acc[strength] = (acc[strength] || 0) + 1;
          return acc;
        },
        {} as Record<Dependency["strength"], number>,
      );

      return {
        incomingCount,
        outgoingCount,
        totalConnections,
        byType,
        byStrength,
      };
    } catch (error) {
      console.error("Error getting note dependency statistics:", error);
      throw error;
    }
  }
}
