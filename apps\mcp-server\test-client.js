#!/usr/bin/env node

/**
 * Life Note MCP Server 測試客戶端
 *
 * 這個腳本用於測試 MCP 服務器的功能
 */

import { Client } from "@modelcontextprotocol/sdk/client/index.js";
import { StdioClientTransport } from "@modelcontextprotocol/sdk/client/stdio.js";
import { spawn } from "child_process";
import { fileURLToPath } from "url";
import { dirname, join } from "path";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function testMCPServer() {
  console.log("🚀 Starting Life Note MCP Server test...\n");

  const client = new Client(
    {
      name: "life-note-test-client",
      version: "1.0.0",
    },
    {
      capabilities: {},
    },
  );

  try {
    // 啟動 MCP 服務器進程
    console.log("📡 Connecting to MCP server...");
    const serverPath = join(__dirname, "index.js");
    const transport = new StdioClientTransport({
      command: "node",
      args: [serverPath],
    });

    await client.connect(transport);
    console.log("✅ Connected to MCP server\n");

    // 測試 1: 列出可用工具
    console.log("🔧 Testing tools list...");
    const toolsResponse = await client.request({
      method: "tools/list",
      params: {},
    });

    console.log(`Found ${toolsResponse.tools.length} tools:`);
    toolsResponse.tools.forEach((tool) => {
      console.log(`  - ${tool.name}: ${tool.description}`);
    });
    console.log();

    // 測試 2: 列出可用資源
    console.log("📚 Testing resources list...");
    const resourcesResponse = await client.request({
      method: "resources/list",
      params: {},
    });

    console.log(`Found ${resourcesResponse.resources.length} resources:`);
    resourcesResponse.resources.forEach((resource) => {
      console.log(
        `  - ${resource.name} (${resource.uri}): ${resource.description || "No description"}`,
      );
    });
    console.log();

    // 測試 3: 創建筆記
    console.log("📝 Testing note creation...");
    const createResponse = await client.request({
      method: "tools/call",
      params: {
        name: "create_note",
        arguments: {
          title: "測試筆記",
          content:
            "這是一個通過 MCP 服務器創建的測試筆記。\n\n## 功能測試\n\n- [x] 創建筆記\n- [ ] 更新筆記\n- [ ] 搜索筆記",
          tags: ["測試", "MCP", "自動化"],
          priority: "high",
          status: "published",
        },
      },
    });

    console.log("Create note response:");
    console.log(createResponse.content[0].text);
    console.log();

    // 從回應中提取筆記 ID
    const createResult = JSON.parse(createResponse.content[0].text);
    const noteId = createResult.note?.id;

    if (noteId) {
      // 測試 4: 獲取筆記
      console.log("📖 Testing note retrieval...");
      const getResponse = await client.request({
        method: "tools/call",
        params: {
          name: "get_note",
          arguments: {
            id: noteId,
          },
        },
      });

      console.log("Get note response:");
      const getResult = JSON.parse(getResponse.content[0].text);
      console.log(`Retrieved note: "${getResult.note.title}"`);
      console.log();

      // 測試 5: 更新筆記
      console.log("✏️ Testing note update...");
      const updateResponse = await client.request({
        method: "tools/call",
        params: {
          name: "update_note",
          arguments: {
            id: noteId,
            content:
              getResult.note.content +
              "\n\n## 更新測試\n\n這個內容是通過 MCP 服務器更新的。",
            tags: [...getResult.note.tags, "已更新"],
          },
        },
      });

      console.log("Update note response:");
      const updateResult = JSON.parse(updateResponse.content[0].text);
      console.log(`Updated note: "${updateResult.note.title}"`);
      console.log();
    }

    // 測試 6: 搜索筆記
    console.log("🔍 Testing note search...");
    const searchResponse = await client.request({
      method: "tools/call",
      params: {
        name: "search_notes",
        arguments: {
          query: "測試",
          tags: ["MCP"],
          limit: 5,
        },
      },
    });

    console.log("Search notes response:");
    const searchResult = JSON.parse(searchResponse.content[0].text);
    console.log(`Found ${searchResult.count} notes matching search criteria`);
    searchResult.results.forEach((note) => {
      console.log(`  - ${note.title} (ID: ${note.id})`);
    });
    console.log();

    // 測試 7: 列出所有筆記
    console.log("📋 Testing list all notes...");
    const listResponse = await client.request({
      method: "tools/call",
      params: {
        name: "list_all_notes",
        arguments: {
          limit: 10,
        },
      },
    });

    console.log("List all notes response:");
    const listResult = JSON.parse(listResponse.content[0].text);
    console.log(`Total notes: ${listResult.total}`);
    console.log(`Showing ${listResult.notes.length} notes:`);
    listResult.notes.forEach((note) => {
      console.log(`  - ${note.title} (${note.status}, ${note.priority})`);
    });
    console.log();

    // 測試 8: 讀取資源
    console.log("📊 Testing resource reading...");
    const resourceResponse = await client.request({
      method: "resources/read",
      params: {
        uri: "notes://statistics",
      },
    });

    console.log("Resource read response:");
    const resourceData = JSON.parse(resourceResponse.contents[0].text);
    console.log(`Statistics: ${resourceData.total} total notes`);
    console.log("Status distribution:", resourceData.byStatus);
    console.log("Priority distribution:", resourceData.byPriority);
    console.log();

    // 清理：刪除測試筆記
    if (noteId) {
      console.log("🗑️ Cleaning up test note...");
      const deleteResponse = await client.request({
        method: "tools/call",
        params: {
          name: "delete_note",
          arguments: {
            id: noteId,
          },
        },
      });

      console.log("Delete note response:");
      const deleteResult = JSON.parse(deleteResponse.content[0].text);
      console.log(deleteResult.message);
      console.log();
    }

    console.log("✅ All tests completed successfully!");
  } catch (error) {
    console.error("❌ Test failed:", error);
    process.exit(1);
  } finally {
    try {
      await client.close();
      console.log("🔌 Disconnected from MCP server");
    } catch (error) {
      console.error("Error closing client:", error);
    }
  }
}

// 錯誤處理
process.on("uncaughtException", (error) => {
  console.error("Uncaught Exception:", error);
  process.exit(1);
});

process.on("unhandledRejection", (reason, promise) => {
  console.error("Unhandled Rejection at:", promise, "reason:", reason);
  process.exit(1);
});

// 運行測試
testMCPServer().catch((error) => {
  console.error("Test execution failed:", error);
  process.exit(1);
});
