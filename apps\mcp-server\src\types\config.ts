/**
 * 服務器配置接口
 */
export interface ServerConfig {
  // 服務器基本配置
  host: string;
  port: number;
  logLevel: "debug" | "info" | "warn" | "error";

  // 筆記相關配置
  notesDir: string;

  // 功能開關
  enableCors: boolean;
  enableAuth: boolean;
  enableHttpServer: boolean;
  enableMCPServer: boolean;
  enableFileWatcher: boolean;

  // CORS 配置
  corsOrigins?: string | string[];

  // 認證配置
  authSecret?: string;
  authTokenExpiry?: string;

  // 搜索配置
  searchConfig?: {
    maxResults: number;
    fuzzyThreshold: number;
    enableSemanticSearch: boolean;
  };

  // 依賴分析配置
  dependencyConfig?: {
    enableAutoAnalysis: boolean;
    analysisInterval: number;
    similarityThreshold: number;
  };

  // 文件監控配置
  fileWatcherConfig?: {
    debounceMs: number;
    ignorePatterns: string[];
    watchSubdirectories: boolean;
  };

  // 緩存配置
  cacheConfig?: {
    enableCache: boolean;
    cacheTTL: number;
    maxCacheSize: number;
  };

  // 性能配置
  performanceConfig?: {
    maxConcurrentRequests: number;
    requestTimeout: number;
    enableRateLimit: boolean;
    rateLimitWindow: number;
    rateLimitMax: number;
  };
}

/**
 * 默認配置
 */
export const defaultConfig: ServerConfig = {
  host: "localhost",
  port: 3001,
  logLevel: "info",
  notesDir: "./notes",
  enableCors: true,
  enableAuth: false,
  enableHttpServer: true,
  enableMCPServer: true,
  enableFileWatcher: true,
  corsOrigins: "*",
  searchConfig: {
    maxResults: 50,
    fuzzyThreshold: 0.6,
    enableSemanticSearch: true,
  },
  dependencyConfig: {
    enableAutoAnalysis: true,
    analysisInterval: 300000, // 5 minutes
    similarityThreshold: 0.3,
  },
  fileWatcherConfig: {
    debounceMs: 1000,
    ignorePatterns: [
      "**/.git/**",
      "**/node_modules/**",
      "**/.DS_Store",
      "**/Thumbs.db",
      "**/*.tmp",
      "**/*.temp",
    ],
    watchSubdirectories: true,
  },
  cacheConfig: {
    enableCache: true,
    cacheTTL: 300000, // 5 minutes
    maxCacheSize: 1000,
  },
  performanceConfig: {
    maxConcurrentRequests: 100,
    requestTimeout: 30000, // 30 seconds
    enableRateLimit: true,
    rateLimitWindow: 60000, // 1 minute
    rateLimitMax: 100,
  },
};

/**
 * 環境變數映射
 */
export const envMapping: Record<string, keyof ServerConfig> = {
  HOST: "host",
  PORT: "port",
  LOG_LEVEL: "logLevel",
  NOTES_DIR: "notesDir",
  ENABLE_CORS: "enableCors",
  ENABLE_AUTH: "enableAuth",
  ENABLE_HTTP_SERVER: "enableHttpServer",
  ENABLE_MCP_SERVER: "enableMCPServer",
  ENABLE_FILE_WATCHER: "enableFileWatcher",
  CORS_ORIGINS: "corsOrigins",
  AUTH_SECRET: "authSecret",
  AUTH_TOKEN_EXPIRY: "authTokenExpiry",
};

/**
 * 配置驗證規則
 */
export interface ConfigValidationRule {
  field: keyof ServerConfig;
  required?: boolean;
  type?: "string" | "number" | "boolean" | "array" | "object";
  min?: number;
  max?: number;
  pattern?: RegExp;
  validator?: (value: any) => boolean | string;
}

export const configValidationRules: ConfigValidationRule[] = [
  {
    field: "host",
    required: true,
    type: "string",
    pattern: /^[a-zA-Z0-9.-]+$/,
  },
  {
    field: "port",
    required: true,
    type: "number",
    min: 1,
    max: 65535,
  },
  {
    field: "logLevel",
    required: true,
    type: "string",
    validator: (value) => ["debug", "info", "warn", "error"].includes(value),
  },
  {
    field: "notesDir",
    required: true,
    type: "string",
    validator: (value) => typeof value === "string" && value.length > 0,
  },
  {
    field: "enableCors",
    type: "boolean",
  },
  {
    field: "enableAuth",
    type: "boolean",
  },
  {
    field: "enableHttpServer",
    type: "boolean",
  },
  {
    field: "enableMCPServer",
    type: "boolean",
  },
  {
    field: "enableFileWatcher",
    type: "boolean",
  },
];

/**
 * 運行時配置類型
 */
export interface RuntimeConfig extends ServerConfig {
  // 運行時添加的配置
  startTime: Date;
  processId: number;
  nodeVersion: string;
  platform: string;
  architecture: string;
}

/**
 * 配置更新事件
 */
export interface ConfigUpdateEvent {
  timestamp: Date;
  oldConfig: Partial<ServerConfig>;
  newConfig: Partial<ServerConfig>;
  source: "file" | "api" | "env" | "cli";
}

/**
 * 配置管理器接口
 */
export interface ConfigManager {
  load(): Promise<ServerConfig>;
  save(config: ServerConfig): Promise<void>;
  validate(config: Partial<ServerConfig>): Promise<ValidationResult>;
  merge(base: ServerConfig, override: Partial<ServerConfig>): ServerConfig;
  watch(callback: (event: ConfigUpdateEvent) => void): void;
  unwatch(): void;
}

/**
 * 配置驗證結果
 */
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

export interface ValidationWarning {
  field: string;
  message: string;
  value?: any;
}
