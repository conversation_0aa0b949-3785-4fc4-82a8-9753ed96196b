import { dependencyNotificationService } from './dependencyNotificationService';
import { allNotes } from '$stores/notes';
import { get } from 'svelte/store';

/**
 * 依賴關係監控初始化器
 * 負責在應用啟動時初始化依賴關係監控系統
 */
class DependencyMonitoringInitializer {
	private isInitialized = false;
	private autoStartEnabled = true;
	private defaultInterval = 30000; // 30 秒

	/**
	 * 初始化依賴關係監控系統
	 */
	async initialize(): Promise<void> {
		if (this.isInitialized) {
			console.log('Dependency monitoring already initialized');
			return;
		}

		try {
			// 載入設置
			await this.loadSettings();

			// 設置默認訂閱
			await this.setupDefaultSubscriptions();

			// 如果啟用自動開始，則開始監控
			if (this.autoStartEnabled) {
				await this.startMonitoring();
			}

			this.isInitialized = true;
			console.log('Dependency monitoring initialized successfully');
		} catch (error) {
			console.error('Failed to initialize dependency monitoring:', error);
		}
	}

	/**
	 * 載入監控設置
	 */
	private async loadSettings(): Promise<void> {
		try {
			const savedSettings = localStorage.getItem('dependency-monitoring-settings');
			if (savedSettings) {
				const settings = JSON.parse(savedSettings);
				this.autoStartEnabled = settings.autoStartEnabled !== false; // 默認為 true
				this.defaultInterval = settings.monitoringInterval * 1000 || this.defaultInterval;
			}
		} catch (error) {
			console.error('Failed to load monitoring settings:', error);
		}
	}

	/**
	 * 設置默認訂閱
	 */
	private async setupDefaultSubscriptions(): Promise<void> {
		try {
			const notes = get(allNotes);
			if (!notes || notes.length === 0) {
				console.log('No notes available for subscription setup');
				return;
			}

			// 為所有筆記設置默認訂閱
			for (const note of notes) {
				// 檢查是否已經有訂閱
				const existingSubscriptions = dependencyNotificationService.getSubscriptions();
				const hasSubscription = existingSubscriptions.some(sub => sub.noteId === note.id);

				if (!hasSubscription) {
					// 為每個筆記創建訂閱，監控其依賴關係
					dependencyNotificationService.subscribeToDependencyNotifications(note.id, []);
				}
			}

			console.log(`Set up default subscriptions for ${notes.length} notes`);
		} catch (error) {
			console.error('Failed to setup default subscriptions:', error);
		}
	}

	/**
	 * 開始監控
	 */
	private async startMonitoring(): Promise<void> {
		try {
			const notes = get(allNotes);
			if (!notes || notes.length === 0) {
				console.log('No notes available for monitoring');
				return;
			}

			// 檢查是否已經在監控
			if (dependencyNotificationService.isMonitoringActive()) {
				console.log('Dependency monitoring is already active');
				return;
			}

			// 開始監控
			dependencyNotificationService.startMonitoring(notes, this.defaultInterval);
			console.log(`Started dependency monitoring with ${this.defaultInterval}ms interval`);
		} catch (error) {
			console.error('Failed to start monitoring:', error);
		}
	}

	/**
	 * 停止監控
	 */
	async stopMonitoring(): Promise<void> {
		try {
			dependencyNotificationService.stopMonitoring();
			console.log('Dependency monitoring stopped');
		} catch (error) {
			console.error('Failed to stop monitoring:', error);
		}
	}

	/**
	 * 重新啟動監控
	 */
	async restartMonitoring(): Promise<void> {
		try {
			await this.stopMonitoring();
			await this.startMonitoring();
			console.log('Dependency monitoring restarted');
		} catch (error) {
			console.error('Failed to restart monitoring:', error);
		}
	}

	/**
	 * 更新監控設置
	 */
	async updateSettings(settings: {
		autoStartEnabled?: boolean;
		monitoringInterval?: number;
	}): Promise<void> {
		try {
			// 更新本地設置
			if (settings.autoStartEnabled !== undefined) {
				this.autoStartEnabled = settings.autoStartEnabled;
			}
			if (settings.monitoringInterval !== undefined) {
				this.defaultInterval = settings.monitoringInterval * 1000;
			}

			// 保存到 localStorage
			const currentSettings = JSON.parse(
				localStorage.getItem('dependency-monitoring-settings') || '{}'
			);
			const newSettings = {
				...currentSettings,
				...settings
			};
			localStorage.setItem('dependency-monitoring-settings', JSON.stringify(newSettings));

			// 如果監控正在運行，重新啟動以應用新設置
			if (dependencyNotificationService.isMonitoringActive()) {
				await this.restartMonitoring();
			}

			console.log('Monitoring settings updated:', settings);
		} catch (error) {
			console.error('Failed to update monitoring settings:', error);
		}
	}

	/**
	 * 獲取監控狀態
	 */
	getStatus(): {
		isInitialized: boolean;
		isMonitoring: boolean;
		autoStartEnabled: boolean;
		interval: number;
		subscriptionCount: number;
		notificationCount: number;
	} {
		return {
			isInitialized: this.isInitialized,
			isMonitoring: dependencyNotificationService.isMonitoringActive(),
			autoStartEnabled: this.autoStartEnabled,
			interval: this.defaultInterval,
			subscriptionCount: dependencyNotificationService.getSubscriptions().length,
			notificationCount: dependencyNotificationService.getNotifications().length
		};
	}

	/**
	 * 手動觸發依賴關係檢查
	 */
	async triggerManualCheck(): Promise<void> {
		try {
			const notes = get(allNotes);
			if (!notes || notes.length === 0) {
				console.log('No notes available for manual check');
				return;
			}

			// 暫時停止自動監控
			const wasMonitoring = dependencyNotificationService.isMonitoringActive();
			if (wasMonitoring) {
				dependencyNotificationService.stopMonitoring();
			}

			// 執行一次檢查
			// 這裡我們需要訪問 dependencyNotificationService 的私有方法
			// 所以我們重新啟動監控，讓它執行一次檢查，然後根據之前的狀態決定是否繼續
			dependencyNotificationService.startMonitoring(notes, 1000); // 1秒後檢查

			// 等待檢查完成
			setTimeout(() => {
				dependencyNotificationService.stopMonitoring();
				if (wasMonitoring) {
					dependencyNotificationService.startMonitoring(notes, this.defaultInterval);
				}
			}, 2000);

			console.log('Manual dependency check triggered');
		} catch (error) {
			console.error('Failed to trigger manual check:', error);
		}
	}

	/**
	 * 清理資源
	 */
	async cleanup(): Promise<void> {
		try {
			await this.stopMonitoring();
			this.isInitialized = false;
			console.log('Dependency monitoring cleanup completed');
		} catch (error) {
			console.error('Failed to cleanup monitoring:', error);
		}
	}
}

// 導出單例實例
export const dependencyMonitoringInitializer = new DependencyMonitoringInitializer();
