import { writable } from 'svelte/store';

export interface Notification {
	id: string;
	title: string;
	message: string;
	type: 'info' | 'success' | 'warning' | 'error';
	read: boolean;
	createdAt: Date;
	actionUrl?: string;
	actionLabel?: string;
}

export interface NotificationState {
	notifications: Notification[];
	unreadCount: number;
	loading: boolean;
	error: string | null;
}

const initialState: NotificationState = {
	notifications: [],
	unreadCount: 0,
	loading: false,
	error: null
};

function createNotificationStore() {
	const { subscribe, set, update } = writable<NotificationState>(initialState);

	return {
		subscribe,

		// Load notifications
		async loadNotifications() {
			update(state => ({ ...state, loading: true, error: null }));

			try {
				// TODO: Implement actual API call
				// const notifications = await notificationService.getAllNotifications();
				const notifications: Notification[] = []; // Placeholder
				const unreadCount = notifications.filter(n => !n.read).length;

				update(state => ({
					...state,
					notifications,
					unreadCount,
					loading: false
				}));
			} catch (error) {
				update(state => ({
					...state,
					loading: false,
					error: error instanceof Error ? error.message : 'Failed to load notifications'
				}));
			}
		},

		// Add notification
		addNotification(notification: Omit<Notification, 'id' | 'createdAt' | 'read'>) {
			const newNotification: Notification = {
				...notification,
				id: crypto.randomUUID(),
				createdAt: new Date(),
				read: false
			};

			update(state => ({
				...state,
				notifications: [newNotification, ...state.notifications],
				unreadCount: state.unreadCount + 1
			}));

			return newNotification.id;
		},

		// Mark as read
		markAsRead(id: string) {
			update(state => {
				const notifications = state.notifications.map(n =>
					n.id === id ? { ...n, read: true } : n
				);
				const unreadCount = notifications.filter(n => !n.read).length;

				return {
					...state,
					notifications,
					unreadCount
				};
			});
		},

		// Mark all as read
		markAllAsRead() {
			update(state => ({
				...state,
				notifications: state.notifications.map(n => ({ ...n, read: true })),
				unreadCount: 0
			}));
		},

		// Remove notification
		removeNotification(id: string) {
			update(state => {
				const notification = state.notifications.find(n => n.id === id);
				const notifications = state.notifications.filter(n => n.id !== id);
				const unreadCount =
					notification && !notification.read ? state.unreadCount - 1 : state.unreadCount;

				return {
					...state,
					notifications,
					unreadCount
				};
			});
		},

		// Clear all notifications
		clearAll() {
			update(state => ({
				...state,
				notifications: [],
				unreadCount: 0
			}));
		},

		// Reset store
		reset() {
			set(initialState);
		}
	};
}

export const notificationStore = createNotificationStore();
