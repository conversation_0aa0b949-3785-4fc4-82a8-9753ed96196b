# 程式碼組織結構

## 整體項目結構

```
life-note-client-vibe/
├── apps/                           # 應用程式
│   ├── desktop/                    # Tauri 桌面應用
│   ├── mobile/                     # Capacitor 移動應用
│   └── web/                        # Web 應用
├── packages/                       # 共享套件
│   ├── core/                       # 核心業務邏輯
│   ├── ui/                         # UI 組件庫
│   ├── agents/                     # AI Agent 實現
│   ├── storage/                    # 存儲抽象層
│   └── utils/                      # 工具函數
├── services/                       # 後端服務
│   ├── local-agent/                # 本地 Agent 服務
│   ├── sync-service/               # 同步服務
│   └── ai-gateway/                 # AI 網關服務
├── docs/                           # 文檔
├── tools/                          # 開發工具
├── scripts/                        # 構建腳本
└── tests/                          # 測試文件
```

## 前端應用結構

### Desktop App (Tauri)

```
apps/desktop/
├── src/
│   ├── main.rs                     # Tauri 主程序
│   ├── commands/                   # Tauri 命令
│   │   ├── mod.rs
│   │   ├── file_operations.rs
│   │   ├── system_integration.rs
│   │   └── security.rs
│   └── lib.rs
├── src-tauri/
│   ├── Cargo.toml
│   ├── tauri.conf.json
│   └── build.rs
├── src-web/                        # Web 前端部分
│   ├── src/
│   │   ├── main.tsx                # 應用入口
│   │   ├── App.tsx                 # 根組件
│   │   ├── components/             # 組件
│   │   ├── pages/                  # 頁面
│   │   ├── hooks/                  # 自定義 Hooks
│   │   ├── stores/                 # 狀態管理
│   │   ├── services/               # 服務層
│   │   └── types/                  # 類型定義
│   ├── public/
│   ├── package.json
│   └── vite.config.ts
└── icons/                          # 應用圖標
```

### Web App

```
apps/web/
├── src/
│   ├── main.tsx                    # 應用入口
│   ├── App.tsx                     # 根組件
│   ├── components/                 # 組件目錄
│   │   ├── common/                 # 通用組件
│   │   │   ├── Button/
│   │   │   │   ├── index.tsx
│   │   │   │   ├── Button.module.css
│   │   │   │   └── Button.test.tsx
│   │   │   └── Modal/
│   │   ├── note/                   # 筆記相關組件
│   │   │   ├── NoteEditor/
│   │   │   ├── NoteViewer/
│   │   │   └── NoteList/
│   │   └── dependency/             # 依賴關係組件
│   │       ├── DependencyGraph/
│   │       └── DependencyTree/
│   ├── pages/                      # 頁面組件
│   │   ├── HomePage/
│   │   ├── NotePage/
│   │   ├── SettingsPage/
│   │   └── AnalyticsPage/
│   ├── hooks/                      # 自定義 Hooks
│   │   ├── useNote.ts
│   │   ├── useDependency.ts
│   │   ├── useAgent.ts
│   │   └── useSync.ts
│   ├── stores/                     # 狀態管理
│   │   ├── noteStore.ts
│   │   ├── dependencyStore.ts
│   │   ├── agentStore.ts
│   │   └── userStore.ts
│   ├── services/                   # 服務層
│   │   ├── api/                    # API 服務
│   │   │   ├── noteApi.ts
│   │   │   ├── dependencyApi.ts
│   │   │   └── agentApi.ts
│   │   ├── storage/                # 存儲服務
│   │   │   ├── localStorage.ts
│   │   │   └── indexedDB.ts
│   │   └── sync/                   # 同步服務
│   │       ├── syncManager.ts
│   │       └── conflictResolver.ts
│   ├── types/                      # 類型定義
│   │   ├── note.ts
│   │   ├── dependency.ts
│   │   ├── agent.ts
│   │   └── api.ts
│   ├── utils/                      # 工具函數
│   │   ├── markdown.ts
│   │   ├── validation.ts
│   │   └── formatting.ts
│   └── styles/                     # 樣式文件
│       ├── globals.css
│       ├── variables.css
│       └── components/
├── public/
│   ├── index.html
│   ├── manifest.json
│   └── icons/
├── package.json
├── vite.config.ts
├── tailwind.config.js
└── tsconfig.json
```

## 共享套件結構

### Core Package

```
packages/core/
├── src/
│   ├── domain/                     # 領域模型
│   │   ├── entities/               # 實體
│   │   │   ├── Note.ts
│   │   │   ├── Dependency.ts
│   │   │   ├── Version.ts
│   │   │   └── User.ts
│   │   ├── value-objects/          # 值對象
│   │   │   ├── NoteId.ts
│   │   │   ├── Version.ts
│   │   │   └── Tag.ts
│   │   ├── aggregates/             # 聚合根
│   │   │   ├── NoteAggregate.ts
│   │   │   └── DependencyGraph.ts
│   │   └── events/                 # 領域事件
│   │       ├── NoteCreated.ts
│   │       ├── NotePublished.ts
│   │       └── DependencyDetected.ts
│   ├── application/                # 應用服務
│   │   ├── services/               # 應用服務
│   │   │   ├── NoteService.ts
│   │   │   ├── DependencyService.ts
│   │   │   └── VersionService.ts
│   │   ├── commands/               # 命令
│   │   │   ├── CreateNote.ts
│   │   │   ├── PublishNote.ts
│   │   │   └── AnalyzeDependencies.ts
│   │   ├── queries/                # 查詢
│   │   │   ├── GetNote.ts
│   │   │   ├── SearchNotes.ts
│   │   │   └── GetDependencyGraph.ts
│   │   └── handlers/               # 處理器
│   │       ├── CommandHandler.ts
│   │       ├── QueryHandler.ts
│   │       └── EventHandler.ts
│   ├── infrastructure/             # 基礎設施
│   │   ├── repositories/           # 倉庫實現
│   │   │   ├── NoteRepository.ts
│   │   │   ├── DependencyRepository.ts
│   │   │   └── VersionRepository.ts
│   │   ├── external/               # 外部服務
│   │   │   ├── AIService.ts
│   │   │   ├── SearchService.ts
│   │   │   └── SyncService.ts
│   │   └── adapters/               # 適配器
│   │       ├── DatabaseAdapter.ts
│   │       ├── FileSystemAdapter.ts
│   │       └── NetworkAdapter.ts
│   └── shared/                     # 共享代碼
│       ├── interfaces/             # 接口定義
│       ├── types/                  # 類型定義
│       ├── constants/              # 常量
│       ├── errors/                 # 錯誤定義
│       └── utils/                  # 工具函數
├── tests/
│   ├── unit/
│   ├── integration/
│   └── fixtures/
├── package.json
└── tsconfig.json
```

### UI Package

```
packages/ui/
├── src/
│   ├── components/                 # 組件
│   │   ├── atoms/                  # 原子組件
│   │   │   ├── Button/
│   │   │   ├── Input/
│   │   │   ├── Icon/
│   │   │   └── Badge/
│   │   ├── molecules/              # 分子組件
│   │   │   ├── SearchBox/
│   │   │   ├── TagInput/
│   │   │   └── StatusIndicator/
│   │   ├── organisms/              # 有機體組件
│   │   │   ├── NoteEditor/
│   │   │   ├── DependencyGraph/
│   │   │   └── NavigationSidebar/
│   │   └── templates/              # 模板組件
│   │       ├── NoteLayout/
│   │       └── DashboardLayout/
│   ├── hooks/                      # UI Hooks
│   │   ├── useTheme.ts
│   │   ├── useKeyboard.ts
│   │   └── useResizable.ts
│   ├── styles/                     # 樣式系統
│   │   ├── tokens/                 # 設計令牌
│   │   │   ├── colors.ts
│   │   │   ├── typography.ts
│   │   │   ├── spacing.ts
│   │   │   └── shadows.ts
│   │   ├── themes/                 # 主題
│   │   │   ├── light.ts
│   │   │   ├── dark.ts
│   │   │   └── system.ts
│   │   └── utilities/              # 工具樣式
│   │       ├── layout.css
│   │       └── animations.css
│   ├── icons/                      # 圖標組件
│   │   ├── Note.tsx
│   │   ├── Dependency.tsx
│   │   └── Agent.tsx
│   └── utils/                      # UI 工具
│       ├── classNames.ts
│       ├── responsive.ts
│       └── accessibility.ts
├── stories/                        # Storybook 故事
├── tests/
├── package.json
└── tsconfig.json
```

### Agents Package

```
packages/agents/
├── src/
│   ├── core/                       # Agent 核心
│   │   ├── Agent.ts                # 基礎 Agent 類
│   │   ├── AgentManager.ts         # Agent 管理器
│   │   ├── MessageBus.ts           # 消息總線
│   │   └── Context.ts              # 上下文管理
│   ├── local/                      # 本地 Agent
│   │   ├── NoteProcessorAgent.ts
│   │   ├── DependencyAnalyzerAgent.ts
│   │   ├── FileManagerAgent.ts
│   │   └── SearchAgent.ts
│   ├── remote/                     # 遠端 Agent
│   │   ├── AIAnalysisAgent.ts
│   │   ├── CollaborationAgent.ts
│   │   └── SyncAgent.ts
│   ├── mcp/                        # MCP 實現
│   │   ├── MCPServer.ts
│   │   ├── MCPClient.ts
│   │   ├── protocols/
│   │   │   ├── NoteProtocol.ts
│   │   │   └── DependencyProtocol.ts
│   │   └── tools/
│   │       ├── NoteTool.ts
│   │       └── DependencyTool.ts
│   ├── workflows/                  # 工作流
│   │   ├── NoteCreationWorkflow.ts
│   │   ├── DependencyAnalysisWorkflow.ts
│   │   └── SyncWorkflow.ts
│   └── utils/                      # Agent 工具
│       ├── routing.ts
│       ├── security.ts
│       └── performance.ts
├── tests/
├── package.json
└── tsconfig.json
```

## 後端服務結構

### Local Agent Service

```
services/local-agent/
├── src/
│   ├── main.ts                     # 服務入口
│   ├── app.ts                      # 應用配置
│   ├── controllers/                # 控制器
│   │   ├── NoteController.ts
│   │   ├── DependencyController.ts
│   │   └── AgentController.ts
│   ├── services/                   # 業務服務
│   │   ├── NoteService.ts
│   │   ├── DependencyService.ts
│   │   └── AgentOrchestrator.ts
│   ├── repositories/               # 數據倉庫
│   │   ├── NoteRepository.ts
│   │   ├── DependencyRepository.ts
│   │   └── VersionRepository.ts
│   ├── middleware/                 # 中間件
│   │   ├── auth.ts
│   │   ├── validation.ts
│   │   ├── logging.ts
│   │   └── errorHandler.ts
│   ├── routes/                     # 路由定義
│   │   ├── notes.ts
│   │   ├── dependencies.ts
│   │   └── agents.ts
│   ├── database/                   # 數據庫
│   │   ├── connection.ts
│   │   ├── migrations/
│   │   └── seeds/
│   ├── config/                     # 配置
│   │   ├── database.ts
│   │   ├── agents.ts
│   │   └── security.ts
│   └── utils/                      # 工具函數
│       ├── logger.ts
│       ├── crypto.ts
│       └── validation.ts
├── tests/
├── package.json
├── tsconfig.json
└── Dockerfile
```

## 測試結構

```
tests/
├── unit/                           # 單元測試
│   ├── domain/
│   ├── application/
│   └── infrastructure/
├── integration/                    # 整合測試
│   ├── api/
│   ├── database/
│   └── agents/
├── e2e/                           # 端對端測試
│   ├── user-workflows/
│   ├── agent-interactions/
│   └── sync-scenarios/
├── performance/                    # 性能測試
│   ├── load-tests/
│   └── stress-tests/
├── fixtures/                      # 測試數據
│   ├── notes/
│   ├── dependencies/
│   └── users/
└── utils/                         # 測試工具
    ├── testHelpers.ts
    ├── mockData.ts
    └── testSetup.ts
```

## 配置文件結構

```
├── .env.example                    # 環境變量模板
├── .env.local                      # 本地環境變量
├── .gitignore                      # Git 忽略文件
├── .eslintrc.js                    # ESLint 配置
├── .prettierrc                     # Prettier 配置
├── package.json                    # 根包配置
├── pnpm-workspace.yaml             # pnpm 工作空間
├── tsconfig.json                   # TypeScript 根配置
├── vitest.config.ts                # 測試配置
├── docker-compose.yml              # Docker 編排
├── Dockerfile                      # Docker 鏡像
└── README.md                       # 項目說明
```

## 命名約定

### 文件命名

- **組件文件**：PascalCase (e.g., `NoteEditor.tsx`)
- **Hook 文件**：camelCase with use prefix (e.g., `useNote.ts`)
- **服務文件**：camelCase with Service suffix (e.g., `noteService.ts`)
- **類型文件**：camelCase (e.g., `note.ts`)
- **常量文件**：UPPER_SNAKE_CASE (e.g., `API_ENDPOINTS.ts`)

### 目錄命名

- **組件目錄**：PascalCase (e.g., `NoteEditor/`)
- **功能目錄**：kebab-case (e.g., `note-management/`)
- **服務目錄**：kebab-case (e.g., `local-agent/`)

### 代碼命名

- **類名**：PascalCase (e.g., `NoteService`)
- **接口名**：PascalCase with I prefix (e.g., `INoteRepository`)
- **變量名**：camelCase (e.g., `noteContent`)
- **常量名**：UPPER_SNAKE_CASE (e.g., `MAX_NOTE_SIZE`)
- **函數名**：camelCase (e.g., `createNote`)

這個程式碼組織結構確保了項目的可維護性、可擴展性和團隊協作效率。
