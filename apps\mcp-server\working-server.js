#!/usr/bin/env node

/**
 * Working Life Note MCP Server
 * 基於官方 MCP SDK 示例的實現
 */

import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import {
  CallToolRequestSchema,
  ListResourcesRequestSchema,
  ListToolsRequestSchema,
  ReadResourceRequestSchema,
} from "@modelcontextprotocol/sdk/types.js";

// 內存數據存儲
const notes = new Map();
let nextId = 1;

// 初始化示例數據
function initializeData() {
  notes.set("1", {
    id: "1",
    title: "歡迎使用 Life Note MCP Server",
    content:
      "這是一個示例筆記，展示 MCP 服務器的功能。\n\n## 功能特性\n\n- 創建筆記\n- 讀取筆記\n- 列出所有筆記\n- 搜索筆記",
    tags: ["歡迎", "MCP", "示例"],
    status: "published",
    priority: "medium",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  });

  notes.set("2", {
    id: "2",
    title: "MCP 協議說明",
    content:
      "Model Context Protocol (MCP) 是一個標準化的協議，用於 AI 模型與外部工具和數據源的交互。",
    tags: ["MCP", "協議", "技術"],
    status: "published",
    priority: "high",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  });

  nextId = 3;
}

async function main() {
  // 初始化數據
  initializeData();

  const server = new Server(
    {
      name: "life-note-mcp-server",
      version: "1.0.0",
    },
    {
      capabilities: {
        tools: {},
        resources: {},
      },
    },
  );

  // 列出工具
  server.setRequestHandler(ListToolsRequestSchema, async () => {
    return {
      tools: [
        {
          name: "create_note",
          description: "Create a new note with title and content",
          inputSchema: {
            type: "object",
            properties: {
              title: {
                type: "string",
                description: "The title of the note",
              },
              content: {
                type: "string",
                description: "The content of the note",
              },
              tags: {
                type: "array",
                items: { type: "string" },
                description: "Tags for the note",
              },
            },
            required: ["title"],
          },
        },
        {
          name: "get_note",
          description: "Get a note by its ID",
          inputSchema: {
            type: "object",
            properties: {
              id: {
                type: "string",
                description: "The ID of the note to retrieve",
              },
            },
            required: ["id"],
          },
        },
        {
          name: "list_notes",
          description: "List all notes",
          inputSchema: {
            type: "object",
            properties: {
              limit: {
                type: "number",
                description: "Maximum number of notes to return",
              },
            },
          },
        },
        {
          name: "search_notes",
          description: "Search notes by title or content",
          inputSchema: {
            type: "object",
            properties: {
              query: {
                type: "string",
                description: "Search query",
              },
            },
            required: ["query"],
          },
        },
      ],
    };
  });

  // 調用工具
  server.setRequestHandler(CallToolRequestSchema, async (request) => {
    const { name, arguments: args } = request.params;

    try {
      switch (name) {
        case "create_note": {
          const note = {
            id: String(nextId++),
            title: args.title,
            content: args.content || "",
            tags: args.tags || [],
            status: "published",
            priority: "medium",
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          };
          notes.set(note.id, note);

          return {
            content: [
              {
                type: "text",
                text: JSON.stringify(
                  {
                    success: true,
                    message: `Note "${note.title}" created successfully`,
                    note: note,
                  },
                  null,
                  2,
                ),
              },
            ],
          };
        }

        case "get_note": {
          const note = notes.get(args.id);
          if (!note) {
            return {
              content: [
                {
                  type: "text",
                  text: JSON.stringify(
                    {
                      success: false,
                      error: `Note with ID ${args.id} not found`,
                    },
                    null,
                    2,
                  ),
                },
              ],
            };
          }

          return {
            content: [
              {
                type: "text",
                text: JSON.stringify(
                  {
                    success: true,
                    note: note,
                  },
                  null,
                  2,
                ),
              },
            ],
          };
        }

        case "list_notes": {
          const allNotes = Array.from(notes.values());
          const limitedNotes = args.limit
            ? allNotes.slice(0, args.limit)
            : allNotes;

          return {
            content: [
              {
                type: "text",
                text: JSON.stringify(
                  {
                    success: true,
                    notes: limitedNotes,
                    total: allNotes.length,
                  },
                  null,
                  2,
                ),
              },
            ],
          };
        }

        case "search_notes": {
          const query = args.query.toLowerCase();
          const results = Array.from(notes.values()).filter(
            (note) =>
              note.title.toLowerCase().includes(query) ||
              note.content.toLowerCase().includes(query) ||
              note.tags.some((tag) => tag.toLowerCase().includes(query)),
          );

          return {
            content: [
              {
                type: "text",
                text: JSON.stringify(
                  {
                    success: true,
                    results: results,
                    count: results.length,
                    query: args.query,
                  },
                  null,
                  2,
                ),
              },
            ],
          };
        }

        default:
          return {
            content: [
              {
                type: "text",
                text: JSON.stringify(
                  {
                    success: false,
                    error: `Unknown tool: ${name}`,
                  },
                  null,
                  2,
                ),
              },
            ],
          };
      }
    } catch (error) {
      return {
        content: [
          {
            type: "text",
            text: JSON.stringify(
              {
                success: false,
                error: error.message,
                tool: name,
              },
              null,
              2,
            ),
          },
        ],
      };
    }
  });

  // 列出資源
  server.setRequestHandler(ListResourcesRequestSchema, async () => {
    return {
      resources: [
        {
          uri: "notes://all",
          name: "All Notes",
          description: "Complete list of all notes",
          mimeType: "application/json",
        },
        {
          uri: "notes://stats",
          name: "Notes Statistics",
          description: "Statistical information about notes",
          mimeType: "application/json",
        },
      ],
    };
  });

  // 讀取資源
  server.setRequestHandler(ReadResourceRequestSchema, async (request) => {
    const { uri } = request.params;

    try {
      switch (uri) {
        case "notes://all":
          return {
            contents: [
              {
                uri,
                mimeType: "application/json",
                text: JSON.stringify(
                  {
                    notes: Array.from(notes.values()),
                    total: notes.size,
                    timestamp: new Date().toISOString(),
                  },
                  null,
                  2,
                ),
              },
            ],
          };

        case "notes://stats":
          const allNotes = Array.from(notes.values());
          const stats = {
            total: allNotes.length,
            byStatus: {},
            byPriority: {},
            totalTags: new Set(allNotes.flatMap((note) => note.tags)).size,
            timestamp: new Date().toISOString(),
          };

          allNotes.forEach((note) => {
            stats.byStatus[note.status] =
              (stats.byStatus[note.status] || 0) + 1;
            stats.byPriority[note.priority] =
              (stats.byPriority[note.priority] || 0) + 1;
          });

          return {
            contents: [
              {
                uri,
                mimeType: "application/json",
                text: JSON.stringify(stats, null, 2),
              },
            ],
          };

        default:
          return {
            contents: [
              {
                uri,
                mimeType: "application/json",
                text: JSON.stringify(
                  {
                    error: `Unknown resource: ${uri}`,
                  },
                  null,
                  2,
                ),
              },
            ],
          };
      }
    } catch (error) {
      return {
        contents: [
          {
            uri,
            mimeType: "application/json",
            text: JSON.stringify(
              {
                error: error.message,
              },
              null,
              2,
            ),
          },
        ],
      };
    }
  });

  // 錯誤處理
  server.onerror = (error) => {
    console.error("[MCP Server] Error:", error);
  };

  // 連接到 stdio 傳輸
  const transport = new StdioServerTransport();
  await server.connect(transport);

  console.error("[MCP Server] Life Note MCP Server started successfully");
  console.error(`[MCP Server] Initialized with ${notes.size} notes`);
}

// 優雅關閉
process.on("SIGINT", async () => {
  console.error("[MCP Server] Shutting down...");
  process.exit(0);
});

process.on("SIGTERM", async () => {
  console.error("[MCP Server] Shutting down...");
  process.exit(0);
});

// 啟動服務器
main().catch((error) => {
  console.error("[MCP Server] Failed to start:", error);
  process.exit(1);
});
