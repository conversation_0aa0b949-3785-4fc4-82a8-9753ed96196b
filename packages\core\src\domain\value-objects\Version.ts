import {
  ValueObject,
  BusinessRuleViolationError,
} from "../../shared/Entity.js";

/**
 * 版本值對象
 * 實現 Git-like 的版本控制：draft-v1 → v1 → draft-v2 → v2
 */
export class Version extends ValueObject {
  private readonly _major: number;
  private readonly _minor: number;
  private readonly _patch: number;
  private readonly _isDraft: boolean;

  constructor(
    major: number,
    minor: number = 0,
    patch: number = 0,
    isDraft: boolean = true,
  ) {
    super();
    this.validateVersionNumbers(major, minor, patch);

    this._major = major;
    this._minor = minor;
    this._patch = patch;
    this._isDraft = isDraft;
  }

  /**
   * 創建初始版本（draft-v1.0.0）
   */
  static initial(): Version {
    return new Version(1, 0, 0, true);
  }

  /**
   * 從字符串解析版本
   * 支持格式：
   * - "1.0.0" (已發布版本)
   * - "draft-1.0.0" (草稿版本)
   * - "v1.0.0" (帶 v 前綴的已發布版本)
   * - "draft-v1.0.0" (帶 v 前綴的草稿版本)
   */
  static fromString(versionString: string): Version {
    const cleanVersion = versionString.trim();

    // 檢查是否為草稿版本
    const isDraft = cleanVersion.startsWith("draft-");
    const versionPart = isDraft ? cleanVersion.substring(6) : cleanVersion;

    // 移除可能的 'v' 前綴
    const numberPart = versionPart.startsWith("v")
      ? versionPart.substring(1)
      : versionPart;

    // 解析版本號
    const parts = numberPart.split(".");
    if (parts.length !== 3) {
      throw new BusinessRuleViolationError(
        `Invalid version format: ${versionString}`,
      );
    }

    const major = parseInt(parts[0], 10);
    const minor = parseInt(parts[1], 10);
    const patch = parseInt(parts[2], 10);

    if (isNaN(major) || isNaN(minor) || isNaN(patch)) {
      throw new BusinessRuleViolationError(
        `Invalid version numbers: ${versionString}`,
      );
    }

    return new Version(major, minor, patch, isDraft);
  }

  /**
   * 驗證版本號
   */
  private validateVersionNumbers(
    major: number,
    minor: number,
    patch: number,
  ): void {
    if (!Number.isInteger(major) || major < 1) {
      throw new BusinessRuleViolationError(
        "Major version must be a positive integer",
      );
    }

    if (!Number.isInteger(minor) || minor < 0) {
      throw new BusinessRuleViolationError(
        "Minor version must be a non-negative integer",
      );
    }

    if (!Number.isInteger(patch) || patch < 0) {
      throw new BusinessRuleViolationError(
        "Patch version must be a non-negative integer",
      );
    }
  }

  /**
   * 發布當前草稿版本（移除 draft 標記）
   */
  publish(): Version {
    if (!this._isDraft) {
      throw new BusinessRuleViolationError(
        "Cannot publish a non-draft version",
      );
    }

    return new Version(this._major, this._minor, this._patch, false);
  }

  /**
   * 增加主版本號並創建新草稿
   */
  increment(): Version {
    return new Version(this._major + 1, 0, 0, true);
  }

  /**
   * 增加次版本號並創建新草稿
   */
  incrementMinor(): Version {
    return new Version(this._major, this._minor + 1, 0, true);
  }

  /**
   * 增加補丁版本號並創建新草稿
   */
  incrementPatch(): Version {
    return new Version(this._major, this._minor, this._patch + 1, true);
  }

  /**
   * 創建下一個草稿版本
   */
  incrementDraft(): Version {
    if (this._isDraft) {
      throw new BusinessRuleViolationError(
        "Cannot increment draft version of a draft",
      );
    }

    return new Version(this._major + 1, 0, 0, true);
  }

  /**
   * 比較版本大小
   * 返回值：
   * - 負數：當前版本小於其他版本
   * - 0：版本相等
   * - 正數：當前版本大於其他版本
   */
  compareTo(other: Version): number {
    // 比較主版本號
    if (this._major !== other._major) {
      return this._major - other._major;
    }

    // 比較次版本號
    if (this._minor !== other._minor) {
      return this._minor - other._minor;
    }

    // 比較補丁版本號
    if (this._patch !== other._patch) {
      return this._patch - other._patch;
    }

    // 如果版本號相同，草稿版本小於發布版本
    if (this._isDraft !== other._isDraft) {
      return this._isDraft ? -1 : 1;
    }

    return 0;
  }

  /**
   * 檢查是否為更新版本
   */
  isNewerThan(other: Version): boolean {
    return this.compareTo(other) > 0;
  }

  /**
   * 檢查是否為相同版本
   */
  isSameAs(other: Version): boolean {
    return this.compareTo(other) === 0;
  }

  /**
   * 檢查是否為舊版本
   */
  isOlderThan(other: Version): boolean {
    return this.compareTo(other) < 0;
  }

  // Getters
  get major(): number {
    return this._major;
  }

  get minor(): number {
    return this._minor;
  }

  get patch(): number {
    return this._patch;
  }

  get isDraft(): boolean {
    return this._isDraft;
  }

  get isPublished(): boolean {
    return !this._isDraft;
  }

  equals(other: ValueObject): boolean {
    if (!(other instanceof Version)) {
      return false;
    }

    return (
      this._major === other._major &&
      this._minor === other._minor &&
      this._patch === other._patch &&
      this._isDraft === other._isDraft
    );
  }

  hashCode(): string {
    return `${this._major}.${this._minor}.${this._patch}-${this._isDraft ? "draft" : "published"}`;
  }

  toString(): string {
    const versionNumber = `${this._major}.${this._minor}.${this._patch}`;
    return this._isDraft ? `draft-${versionNumber}` : versionNumber;
  }

  /**
   * 轉換為帶 v 前綴的字符串
   */
  toVersionString(): string {
    const versionNumber = `v${this._major}.${this._minor}.${this._patch}`;
    return this._isDraft ? `draft-${versionNumber}` : versionNumber;
  }

  toPlainObject(): Record<string, unknown> {
    return {
      major: this._major,
      minor: this._minor,
      patch: this._patch,
      isDraft: this._isDraft,
    };
  }

  static fromPlainObject(data: Record<string, unknown>): Version {
    if (
      typeof data.major !== "number" ||
      typeof data.minor !== "number" ||
      typeof data.patch !== "number" ||
      typeof data.isDraft !== "boolean"
    ) {
      throw new BusinessRuleViolationError("Invalid Version data");
    }

    return new Version(data.major, data.minor, data.patch, data.isDraft);
  }
}
