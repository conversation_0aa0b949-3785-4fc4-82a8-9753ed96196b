import type { WorkspaceAggregate, WorkspaceMemberRole } from "@life-note/core";
import type { UserId } from "@life-note/core";
import type {
  ISearchableRepository,
  PaginatedResult,
  PaginationParams,
} from "./IRepository.js";

/**
 * 工作空間 ID 類型
 */
export type WorkspaceId = string;

/**
 * 工作空間查詢條件
 */
export interface WorkspaceQueryCondition {
  ownerId?: string;
  isPublic?: boolean;
  createdAfter?: Date;
  createdBefore?: Date;
  memberCount?: {
    min?: number;
    max?: number;
  };
  noteCount?: {
    min?: number;
    max?: number;
  };
}

/**
 * 工作空間成員信息
 */
export interface WorkspaceMemberInfo {
  id: string;
  workspaceId: string;
  userId: string;
  role: WorkspaceMemberRole;
  permissions: string[];
  joinedAt: Date;
  invitedBy?: string;
  user?: {
    id: string;
    username: string;
    email: string;
    displayName?: string;
  };
}

/**
 * 工作空間統計信息
 */
export interface WorkspaceStatistics {
  total: number;
  publicWorkspaces: number;
  privateWorkspaces: number;
  totalMembers: number;
  averageMembersPerWorkspace: number;
  totalNotes: number;
  averageNotesPerWorkspace: number;
  createdThisWeek: number;
  createdThisMonth: number;
  activeWorkspaces: number;
}

/**
 * 工作空間設置
 */
export interface WorkspaceSettings {
  name: string;
  description?: string;
  isPublic: boolean;
  allowGuestAccess: boolean;
  defaultNoteVisibility: "public" | "private" | "members";
  enableVersionControl: boolean;
  enableDependencyTracking: boolean;
  enableCollaboration: boolean;
  maxMembers: number;
  retentionDays: number;
  customDomain?: string;
  branding: {
    logo?: string;
    primaryColor?: string;
    secondaryColor?: string;
  };
}

/**
 * 工作空間存儲庫接口
 */
export interface IWorkspaceRepository
  extends ISearchableRepository<WorkspaceAggregate, WorkspaceId> {
  /**
   * 根據擁有者 ID 查找工作空間
   */
  findByOwnerId(
    ownerId: UserId,
    params?: PaginationParams,
  ): Promise<PaginatedResult<WorkspaceAggregate>>;

  /**
   * 查找用戶參與的工作空間
   */
  findByMemberId(
    userId: UserId,
    params?: PaginationParams,
  ): Promise<PaginatedResult<WorkspaceAggregate>>;

  /**
   * 查找公開的工作空間
   */
  findPublicWorkspaces(
    params?: PaginationParams,
  ): Promise<PaginatedResult<WorkspaceAggregate>>;

  /**
   * 根據複合條件查找工作空間
   */
  findByCondition(
    condition: WorkspaceQueryCondition,
    params?: PaginationParams,
  ): Promise<PaginatedResult<WorkspaceAggregate>>;

  /**
   * 查找最近創建的工作空間
   */
  findRecentlyCreated(limit?: number): Promise<WorkspaceAggregate[]>;

  /**
   * 查找最活躍的工作空間
   */
  findMostActive(limit?: number): Promise<WorkspaceAggregate[]>;

  /**
   * 檢查工作空間名稱是否可用
   */
  isNameAvailable(name: string, excludeId?: WorkspaceId): Promise<boolean>;

  /**
   * 獲取工作空間統計信息
   */
  getStatistics(): Promise<WorkspaceStatistics>;

  /**
   * 獲取用戶的工作空間統計信息
   */
  getUserWorkspaceStatistics(userId: UserId): Promise<WorkspaceStatistics>;

  // 成員管理
  /**
   * 添加工作空間成員
   */
  addMember(
    workspaceId: WorkspaceId,
    userId: UserId,
    role: WorkspaceMemberRole,
    invitedBy?: UserId,
  ): Promise<void>;

  /**
   * 移除工作空間成員
   */
  removeMember(workspaceId: WorkspaceId, userId: UserId): Promise<void>;

  /**
   * 更新成員角色
   */
  updateMemberRole(
    workspaceId: WorkspaceId,
    userId: UserId,
    role: WorkspaceMemberRole,
  ): Promise<void>;

  /**
   * 更新成員權限
   */
  updateMemberPermissions(
    workspaceId: WorkspaceId,
    userId: UserId,
    permissions: string[],
  ): Promise<void>;

  /**
   * 獲取工作空間成員列表
   */
  getMembers(
    workspaceId: WorkspaceId,
    params?: PaginationParams,
  ): Promise<PaginatedResult<WorkspaceMemberInfo>>;

  /**
   * 獲取成員信息
   */
  getMember(
    workspaceId: WorkspaceId,
    userId: UserId,
  ): Promise<WorkspaceMemberInfo | null>;

  /**
   * 檢查用戶是否為工作空間成員
   */
  isMember(workspaceId: WorkspaceId, userId: UserId): Promise<boolean>;

  /**
   * 檢查用戶在工作空間中的角色
   */
  getMemberRole(
    workspaceId: WorkspaceId,
    userId: UserId,
  ): Promise<WorkspaceMemberRole | null>;

  /**
   * 檢查用戶是否有特定權限
   */
  hasPermission(
    workspaceId: WorkspaceId,
    userId: UserId,
    permission: string,
  ): Promise<boolean>;

  /**
   * 批量添加成員
   */
  batchAddMembers(
    workspaceId: WorkspaceId,
    members: Array<{
      userId: UserId;
      role: WorkspaceMemberRole;
    }>,
    invitedBy?: UserId,
  ): Promise<void>;

  /**
   * 批量移除成員
   */
  batchRemoveMembers(
    workspaceId: WorkspaceId,
    userIds: UserId[],
  ): Promise<void>;

  // 設置管理
  /**
   * 獲取工作空間設置
   */
  getSettings(workspaceId: WorkspaceId): Promise<WorkspaceSettings | null>;

  /**
   * 更新工作空間設置
   */
  updateSettings(
    workspaceId: WorkspaceId,
    settings: Partial<WorkspaceSettings>,
  ): Promise<void>;

  // 統計信息管理
  /**
   * 更新工作空間統計信息
   */
  updateWorkspaceStatistics(
    workspaceId: WorkspaceId,
    statistics: Record<string, unknown>,
  ): Promise<void>;

  /**
   * 增加工作空間筆記計數
   */
  incrementNoteCount(
    workspaceId: WorkspaceId,
    increment?: number,
  ): Promise<void>;

  /**
   * 減少工作空間筆記計數
   */
  decrementNoteCount(
    workspaceId: WorkspaceId,
    decrement?: number,
  ): Promise<void>;

  /**
   * 更新工作空間活動時間
   */
  updateLastActivity(workspaceId: WorkspaceId): Promise<void>;

  /**
   * 獲取工作空間活動統計
   */
  getActivityStatistics(
    workspaceId: WorkspaceId,
  ): Promise<Record<string, unknown>>;
}
