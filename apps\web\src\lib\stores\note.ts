import { writable, derived } from 'svelte/store';
import type { Note, NoteStatus, NotePriority, Tag } from '$types';

export interface NoteStatistics {
	totalNotes: number;
	weeklyNotes: number;
	totalTags: number;
	totalDependencies: number;
	notesByStatus: Record<NoteStatus, number>;
	notesByPriority: Record<NotePriority, number>;
}

export interface NoteState {
	notes: Note[];
	recentNotes: Note[];
	currentNote: Note | null;
	loading: boolean;
	error: string | null;
	statistics: NoteStatistics | null;
	searchQuery: string;
	selectedTags: Tag[];
	selectedStatus: NoteStatus | null;
	selectedPriority: NotePriority | null;
}

const initialState: NoteState = {
	notes: [],
	recentNotes: [],
	currentNote: null,
	loading: false,
	error: null,
	statistics: null,
	searchQuery: '',
	selectedTags: [],
	selectedStatus: null,
	selectedPriority: null
};

function createNoteStore() {
	const { subscribe, set, update } = writable<NoteState>(initialState);

	return {
		subscribe,

		// Loading state
		setLoading(loading: boolean) {
			update(state => ({ ...state, loading }));
		},

		// Error handling
		setError(error: string | null) {
			update(state => ({ ...state, error }));
		},

		clearError() {
			update(state => ({ ...state, error: null }));
		},

		// Load all notes
		async loadNotes() {
			update(state => ({ ...state, loading: true, error: null }));

			try {
				// TODO: Implement actual API call
				// const notes = await noteService.getAllNotes();
				const notes: Note[] = []; // Placeholder

				update(state => ({
					...state,
					notes,
					loading: false
				}));
			} catch (error) {
				update(state => ({
					...state,
					loading: false,
					error: error instanceof Error ? error.message : 'Failed to load notes'
				}));
			}
		},

		// Load recent notes
		async loadRecentNotes(limit = 10) {
			try {
				// TODO: Implement actual API call
				// const recentNotes = await noteService.getRecentNotes(limit);
				const recentNotes: Note[] = []; // Placeholder

				update(state => ({ ...state, recentNotes }));
			} catch (error) {
				console.error('Failed to load recent notes:', error);
			}
		},

		// Load note by ID
		async loadNote(id: string) {
			update(state => ({ ...state, loading: true, error: null }));

			try {
				// TODO: Implement actual API call
				// const note = await noteService.getNoteById(id);
				const note: Note | null = null; // Placeholder

				update(state => ({
					...state,
					currentNote: note,
					loading: false
				}));

				return note;
			} catch (error) {
				update(state => ({
					...state,
					loading: false,
					error: error instanceof Error ? error.message : 'Failed to load note'
				}));
				return null;
			}
		},

		// Create new note
		async createNote(noteData: {
			title: string;
			content: string;
			category?: string;
			tags?: Tag[];
			priority?: NotePriority;
		}) {
			update(state => ({ ...state, loading: true, error: null }));

			try {
				// TODO: Implement actual API call
				// const newNote = await noteService.createNote(noteData);
				const newNote: Note = {} as Note; // Placeholder

				update(state => ({
					...state,
					notes: [newNote, ...state.notes],
					recentNotes: [newNote, ...state.recentNotes.slice(0, 9)],
					currentNote: newNote,
					loading: false
				}));

				return newNote;
			} catch (error) {
				update(state => ({
					...state,
					loading: false,
					error: error instanceof Error ? error.message : 'Failed to create note'
				}));
				throw error;
			}
		},

		// Update note
		async updateNote(id: string, updates: Partial<Note>) {
			update(state => ({ ...state, loading: true, error: null }));

			try {
				// TODO: Implement actual API call
				// const updatedNote = await noteService.updateNote(id, updates);
				const updatedNote: Note = {} as Note; // Placeholder

				update(state => ({
					...state,
					notes: state.notes.map(note => (note.id.value === id ? updatedNote : note)),
					recentNotes: state.recentNotes.map(note => (note.id.value === id ? updatedNote : note)),
					currentNote: state.currentNote?.id.value === id ? updatedNote : state.currentNote,
					loading: false
				}));

				return updatedNote;
			} catch (error) {
				update(state => ({
					...state,
					loading: false,
					error: error instanceof Error ? error.message : 'Failed to update note'
				}));
				throw error;
			}
		},

		// Delete note
		async deleteNote(id: string) {
			update(state => ({ ...state, loading: true, error: null }));

			try {
				// TODO: Implement actual API call
				// await noteService.deleteNote(id);

				update(state => ({
					...state,
					notes: state.notes.filter(note => note.id.value !== id),
					recentNotes: state.recentNotes.filter(note => note.id.value !== id),
					currentNote: state.currentNote?.id.value === id ? null : state.currentNote,
					loading: false
				}));
			} catch (error) {
				update(state => ({
					...state,
					loading: false,
					error: error instanceof Error ? error.message : 'Failed to delete note'
				}));
				throw error;
			}
		},

		// Load statistics
		async loadStatistics() {
			try {
				// TODO: Implement actual API call
				// const statistics = await noteService.getStatistics();
				const statistics: NoteStatistics = {
					totalNotes: 0,
					weeklyNotes: 0,
					totalTags: 0,
					totalDependencies: 0,
					notesByStatus: {
						draft: 0,
						published: 0,
						archived: 0
					},
					notesByPriority: {
						low: 0,
						medium: 0,
						high: 0,
						urgent: 0
					}
				}; // Placeholder

				update(state => ({ ...state, statistics }));
			} catch (error) {
				console.error('Failed to load statistics:', error);
			}
		},

		// Search and filter
		setSearchQuery(query: string) {
			update(state => ({ ...state, searchQuery: query }));
		},

		setSelectedTags(tags: Tag[]) {
			update(state => ({ ...state, selectedTags: tags }));
		},

		setSelectedStatus(status: NoteStatus | null) {
			update(state => ({ ...state, selectedStatus: status }));
		},

		setSelectedPriority(priority: NotePriority | null) {
			update(state => ({ ...state, selectedPriority: priority }));
		},

		// Clear filters
		clearFilters() {
			update(state => ({
				...state,
				searchQuery: '',
				selectedTags: [],
				selectedStatus: null,
				selectedPriority: null
			}));
		},

		// Reset store
		reset() {
			set(initialState);
		}
	};
}

export const noteStore = createNoteStore();

// Derived stores for filtered data
export const filteredNotes = derived(noteStore, $noteStore => {
	let filtered = $noteStore.notes;

	// Apply search query filter
	if ($noteStore.searchQuery) {
		const query = $noteStore.searchQuery.toLowerCase();
		filtered = filtered.filter(
			note => note.title.toLowerCase().includes(query) || note.content.toLowerCase().includes(query)
		);
	}

	// Apply status filter
	if ($noteStore.selectedStatus) {
		filtered = filtered.filter(note => note.status === $noteStore.selectedStatus);
	}

	// Apply priority filter
	if ($noteStore.selectedPriority) {
		filtered = filtered.filter(note => note.priority === $noteStore.selectedPriority);
	}

	// Apply tags filter
	if ($noteStore.selectedTags.length > 0) {
		filtered = filtered.filter(note =>
			$noteStore.selectedTags.every(selectedTag =>
				note.tags.some(noteTag => noteTag.name === selectedTag.name)
			)
		);
	}

	return filtered;
});
