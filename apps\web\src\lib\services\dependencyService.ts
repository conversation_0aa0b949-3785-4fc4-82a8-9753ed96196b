import type { Note } from '$types';

export interface DependencyNode {
	id: string;
	title: string;
	type: 'note' | 'tag' | 'category';
	status: string;
	priority: string;
	size: number; // 節點大小，基於內容長度或重要性
	group: number; // 用於分組著色
	x?: number;
	y?: number;
	fx?: number; // 固定 x 座標
	fy?: number; // 固定 y 座標
}

export interface DependencyLink {
	source: string | DependencyNode;
	target: string | DependencyNode;
	type: 'reference' | 'tag' | 'category' | 'similar';
	strength: number; // 連接強度 0-1
	distance: number; // 理想距離
}

export interface DependencyGraph {
	nodes: DependencyNode[];
	links: DependencyLink[];
	metadata: {
		totalNodes: number;
		totalLinks: number;
		clusters: number;
		density: number; // 圖的密度
	};
}

export interface DependencyAnalysisOptions {
	includeTagConnections: boolean;
	includeCategoryConnections: boolean;
	includeContentSimilarity: boolean;
	similarityThreshold: number; // 0-1
	maxDistance: number; // 最大跳躍距離
	excludeIsolatedNodes: boolean;
}

export class DependencyService {
	private linkPatterns = [
		// Markdown 連結
		/\[([^\]]+)\]\(([^)]+)\)/g,
		// Wiki 風格連結
		/\[\[([^\]]+)\]\]/g,
		// 標籤引用
		/#([a-zA-Z0-9\u4e00-\u9fff_-]+)/g,
		// 檔案引用
		/\@([a-zA-Z0-9\u4e00-\u9fff_.-]+)/g,
		// 筆記 ID 引用
		/note:([a-zA-Z0-9-]+)/g,
		// 章節引用
		/§([^\s]+)/g,
		// 引用語法
		/\^([a-zA-Z0-9-]+)/g
	];

	// 語義相似度關鍵詞
	private semanticKeywords = {
		technology: ['技術', '開發', '程式', '代碼', '框架', '工具', 'tech', 'dev', 'code'],
		design: ['設計', '介面', 'UI', 'UX', '用戶', '體驗', 'design', 'interface'],
		business: ['商業', '業務', '市場', '策略', '管理', 'business', 'strategy'],
		learning: ['學習', '教學', '知識', '筆記', '總結', 'learning', 'study'],
		project: ['項目', '專案', '計劃', '任務', 'project', 'task', 'plan']
	};

	/**
	 * 分析筆記間的依賴關係
	 */
	async analyzeDependencies(
		notes: Note[],
		options: DependencyAnalysisOptions = this.getDefaultOptions()
	): Promise<DependencyGraph> {
		const nodes = this.createNodes(notes);
		const links = this.createLinks(notes, nodes, options);

		// 過濾孤立節點
		const filteredNodes = options.excludeIsolatedNodes
			? this.filterIsolatedNodes(nodes, links)
			: nodes;

		const metadata = this.calculateMetadata(filteredNodes, links);

		return {
			nodes: filteredNodes,
			links,
			metadata
		};
	}

	/**
	 * 創建節點
	 */
	private createNodes(notes: Note[]): DependencyNode[] {
		return notes.map((note, index) => ({
			id: note.id,
			title: note.title,
			type: 'note',
			status: note.status,
			priority: note.priority,
			size: this.calculateNodeSize(note),
			group: this.calculateGroup(note, index),
			x: undefined,
			y: undefined
		}));
	}

	/**
	 * 創建連接
	 */
	private createLinks(
		notes: Note[],
		nodes: DependencyNode[],
		options: DependencyAnalysisOptions
	): DependencyLink[] {
		const links: DependencyLink[] = [];
		const noteMap = new Map(notes.map(note => [note.id, note]));

		// 1. 內容引用連接
		for (const note of notes) {
			const references = this.extractReferences(note.content);
			for (const ref of references) {
				const targetNote = this.findNoteByReference(ref, notes);
				if (targetNote && targetNote.id !== note.id) {
					links.push({
						source: note.id,
						target: targetNote.id,
						type: 'reference',
						strength: 0.8,
						distance: 50
					});
				}
			}
		}

		// 2. 標籤連接
		if (options.includeTagConnections) {
			const tagConnections = this.createTagConnections(notes);
			links.push(...tagConnections);
		}

		// 3. 分類連接
		if (options.includeCategoryConnections) {
			const categoryConnections = this.createCategoryConnections(notes);
			links.push(...categoryConnections);
		}

		// 4. 內容相似性連接
		if (options.includeContentSimilarity) {
			const similarityConnections = this.createSimilarityConnections(
				notes,
				options.similarityThreshold
			);
			links.push(...similarityConnections);
		}

		return this.deduplicateLinks(links);
	}

	/**
	 * 提取內容中的引用
	 */
	private extractReferences(content: string): string[] {
		const references: string[] = [];

		for (const pattern of this.linkPatterns) {
			let match;
			while ((match = pattern.exec(content)) !== null) {
				if (match[1]) {
					references.push(match[1]);
				}
				if (match[2]) {
					references.push(match[2]);
				}
			}
		}

		return [...new Set(references)]; // 去重
	}

	/**
	 * 根據引用查找筆記
	 */
	private findNoteByReference(reference: string, notes: Note[]): Note | null {
		// 嘗試不同的匹配策略
		const strategies = [
			// 精確標題匹配
			(ref: string) => notes.find(note => note.title === ref),
			// 部分標題匹配
			(ref: string) => notes.find(note => note.title.toLowerCase().includes(ref.toLowerCase())),
			// ID 匹配
			(ref: string) => notes.find(note => note.id === ref),
			// 標籤匹配
			(ref: string) => notes.find(note => note.tags.some(tag => tag.name === ref))
		];

		for (const strategy of strategies) {
			const result = strategy(reference);
			if (result) return result;
		}

		return null;
	}

	/**
	 * 創建標籤連接
	 */
	private createTagConnections(notes: Note[]): DependencyLink[] {
		const links: DependencyLink[] = [];
		const tagMap = new Map<string, Note[]>();

		// 建立標籤到筆記的映射
		for (const note of notes) {
			for (const tag of note.tags) {
				if (!tagMap.has(tag.name)) {
					tagMap.set(tag.name, []);
				}
				tagMap.get(tag.name)!.push(note);
			}
		}

		// 為共享標籤的筆記創建連接
		for (const [tagName, taggedNotes] of tagMap) {
			if (taggedNotes.length > 1) {
				for (let i = 0; i < taggedNotes.length; i++) {
					for (let j = i + 1; j < taggedNotes.length; j++) {
						links.push({
							source: taggedNotes[i].id,
							target: taggedNotes[j].id,
							type: 'tag',
							strength: 0.4,
							distance: 80
						});
					}
				}
			}
		}

		return links;
	}

	/**
	 * 創建分類連接
	 */
	private createCategoryConnections(notes: Note[]): DependencyLink[] {
		const links: DependencyLink[] = [];
		const categoryMap = new Map<string, Note[]>();

		// 建立分類到筆記的映射
		for (const note of notes) {
			if (note.categoryId) {
				if (!categoryMap.has(note.categoryId)) {
					categoryMap.set(note.categoryId, []);
				}
				categoryMap.get(note.categoryId)!.push(note);
			}
		}

		// 為同分類的筆記創建連接
		for (const [categoryId, categoryNotes] of categoryMap) {
			if (categoryNotes.length > 1) {
				for (let i = 0; i < categoryNotes.length; i++) {
					for (let j = i + 1; j < categoryNotes.length; j++) {
						links.push({
							source: categoryNotes[i].id,
							target: categoryNotes[j].id,
							type: 'category',
							strength: 0.3,
							distance: 100
						});
					}
				}
			}
		}

		return links;
	}

	/**
	 * 創建內容相似性連接
	 */
	private createSimilarityConnections(notes: Note[], threshold: number): DependencyLink[] {
		const links: DependencyLink[] = [];

		for (let i = 0; i < notes.length; i++) {
			for (let j = i + 1; j < notes.length; j++) {
				const similarity = this.calculateContentSimilarity(notes[i], notes[j]);
				if (similarity >= threshold) {
					links.push({
						source: notes[i].id,
						target: notes[j].id,
						type: 'similar',
						strength: similarity * 0.6,
						distance: 120
					});
				}
			}
		}

		return links;
	}

	/**
	 * 計算內容相似性
	 */
	private calculateContentSimilarity(note1: Note, note2: Note): number {
		// 1. 詞彙相似性 (Jaccard)
		const words1 = this.extractWords(note1.content);
		const words2 = this.extractWords(note2.content);

		const intersection = new Set([...words1].filter(word => words2.has(word)));
		const union = new Set([...words1, ...words2]);
		const jaccardSimilarity = intersection.size / union.size;

		// 2. 語義相似性
		const semantic1 = this.extractSemanticFeatures(note1);
		const semantic2 = this.extractSemanticFeatures(note2);
		const semanticSimilarity = this.calculateSemanticSimilarity(semantic1, semantic2);

		// 3. 標籤相似性
		const tagSimilarity = this.calculateTagSimilarity(note1.tags, note2.tags);

		// 4. 標題相似性
		const titleSimilarity = this.calculateTitleSimilarity(note1.title, note2.title);

		// 加權平均
		return (
			jaccardSimilarity * 0.4 +
			semanticSimilarity * 0.3 +
			tagSimilarity * 0.2 +
			titleSimilarity * 0.1
		);
	}

	/**
	 * 提取語義特徵
	 */
	private extractSemanticFeatures(note: Note): Map<string, number> {
		const features = new Map<string, number>();
		const content = note.content.toLowerCase();

		// 分析語義關鍵詞
		for (const [category, keywords] of Object.entries(this.semanticKeywords)) {
			let score = 0;
			for (const keyword of keywords) {
				const regex = new RegExp(`\\b${keyword}\\b`, 'gi');
				const matches = content.match(regex);
				if (matches) {
					score += matches.length;
				}
			}
			if (score > 0) {
				features.set(category, score);
			}
		}

		return features;
	}

	/**
	 * 計算語義相似性
	 */
	private calculateSemanticSimilarity(
		features1: Map<string, number>,
		features2: Map<string, number>
	): number {
		if (features1.size === 0 || features2.size === 0) return 0;

		const allCategories = new Set([...features1.keys(), ...features2.keys()]);
		let dotProduct = 0;
		let norm1 = 0;
		let norm2 = 0;

		for (const category of allCategories) {
			const score1 = features1.get(category) || 0;
			const score2 = features2.get(category) || 0;

			dotProduct += score1 * score2;
			norm1 += score1 * score1;
			norm2 += score2 * score2;
		}

		if (norm1 === 0 || norm2 === 0) return 0;
		return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
	}

	/**
	 * 計算標籤相似性
	 */
	private calculateTagSimilarity(tags1: any[], tags2: any[]): number {
		if (tags1.length === 0 || tags2.length === 0) return 0;

		const tagNames1 = new Set(tags1.map(tag => tag.name.toLowerCase()));
		const tagNames2 = new Set(tags2.map(tag => tag.name.toLowerCase()));

		const intersection = new Set([...tagNames1].filter(tag => tagNames2.has(tag)));
		const union = new Set([...tagNames1, ...tagNames2]);

		return intersection.size / union.size;
	}

	/**
	 * 計算標題相似性
	 */
	private calculateTitleSimilarity(title1: string, title2: string): number {
		const words1 = new Set(title1.toLowerCase().split(/\s+/));
		const words2 = new Set(title2.toLowerCase().split(/\s+/));

		const intersection = new Set([...words1].filter(word => words2.has(word)));
		const union = new Set([...words1, ...words2]);

		return intersection.size / union.size;
	}

	/**
	 * 提取詞彙
	 */
	private extractWords(text: string): Set<string> {
		return new Set(
			text
				.toLowerCase()
				.replace(/[^\w\s\u4e00-\u9fff]/g, ' ')
				.split(/\s+/)
				.filter(word => word.length > 2)
		);
	}

	/**
	 * 計算節點大小
	 */
	private calculateNodeSize(note: Note): number {
		const baseSize = 10;
		const contentLength = note.content.length;
		const tagCount = note.tags.length;

		// 基於內容長度和標籤數量計算大小
		return baseSize + Math.log(contentLength + 1) * 2 + tagCount * 2;
	}

	/**
	 * 計算節點分組
	 */
	private calculateGroup(note: Note, index: number): number {
		// 基於優先級和狀態分組
		if (note.priority === 'urgent') return 1;
		if (note.priority === 'high') return 2;
		if (note.status === 'draft') return 3;
		if (note.status === 'published') return 4;
		return 5;
	}

	/**
	 * 過濾孤立節點
	 */
	private filterIsolatedNodes(nodes: DependencyNode[], links: DependencyLink[]): DependencyNode[] {
		const connectedNodeIds = new Set<string>();

		for (const link of links) {
			const sourceId = typeof link.source === 'string' ? link.source : link.source.id;
			const targetId = typeof link.target === 'string' ? link.target : link.target.id;
			connectedNodeIds.add(sourceId);
			connectedNodeIds.add(targetId);
		}

		return nodes.filter(node => connectedNodeIds.has(node.id));
	}

	/**
	 * 去重連接
	 */
	private deduplicateLinks(links: DependencyLink[]): DependencyLink[] {
		const linkMap = new Map<string, DependencyLink>();

		for (const link of links) {
			const sourceId = typeof link.source === 'string' ? link.source : link.source.id;
			const targetId = typeof link.target === 'string' ? link.target : link.target.id;

			// 確保連接的方向一致性
			const key = sourceId < targetId ? `${sourceId}-${targetId}` : `${targetId}-${sourceId}`;

			if (!linkMap.has(key) || linkMap.get(key)!.strength < link.strength) {
				linkMap.set(key, link);
			}
		}

		return Array.from(linkMap.values());
	}

	/**
	 * 計算圖的元數據
	 */
	private calculateMetadata(
		nodes: DependencyNode[],
		links: DependencyLink[]
	): DependencyGraph['metadata'] {
		const totalNodes = nodes.length;
		const totalLinks = links.length;
		const maxPossibleLinks = (totalNodes * (totalNodes - 1)) / 2;
		const density = maxPossibleLinks > 0 ? totalLinks / maxPossibleLinks : 0;

		// 簡單的聚類計算（基於分組）
		const groups = new Set(nodes.map(node => node.group));
		const clusters = groups.size;

		return {
			totalNodes,
			totalLinks,
			clusters,
			density
		};
	}

	/**
	 * 獲取默認選項
	 */
	private getDefaultOptions(): DependencyAnalysisOptions {
		return {
			includeTagConnections: true,
			includeCategoryConnections: true,
			includeContentSimilarity: true,
			similarityThreshold: 0.3,
			maxDistance: 3,
			excludeIsolatedNodes: false
		};
	}

	/**
	 * 查找節點的鄰居
	 */
	findNeighbors(nodeId: string, graph: DependencyGraph, maxDistance: number = 1): string[] {
		const neighbors = new Set<string>();
		const visited = new Set<string>();
		const queue: Array<{ id: string; distance: number }> = [{ id: nodeId, distance: 0 }];

		while (queue.length > 0) {
			const { id, distance } = queue.shift()!;

			if (visited.has(id) || distance > maxDistance) continue;
			visited.add(id);

			if (distance > 0) {
				neighbors.add(id);
			}

			// 查找直接連接的節點
			for (const link of graph.links) {
				const sourceId = typeof link.source === 'string' ? link.source : link.source.id;
				const targetId = typeof link.target === 'string' ? link.target : link.target.id;

				if (sourceId === id && !visited.has(targetId)) {
					queue.push({ id: targetId, distance: distance + 1 });
				} else if (targetId === id && !visited.has(sourceId)) {
					queue.push({ id: sourceId, distance: distance + 1 });
				}
			}
		}

		return Array.from(neighbors);
	}

	/**
	 * 計算節點的中心性
	 */
	calculateCentrality(graph: DependencyGraph): Map<string, number> {
		const centrality = new Map<string, number>();

		// 度中心性（連接數量）
		for (const node of graph.nodes) {
			let degree = 0;
			for (const link of graph.links) {
				const sourceId = typeof link.source === 'string' ? link.source : link.source.id;
				const targetId = typeof link.target === 'string' ? link.target : link.target.id;

				if (sourceId === node.id || targetId === node.id) {
					degree++;
				}
			}
			centrality.set(node.id, degree);
		}

		return centrality;
	}

	/**
	 * 分析依賴路徑
	 */
	async analyzeDependencyPaths(
		graph: DependencyGraph,
		sourceId: string,
		targetId: string,
		maxDepth: number = 3
	): Promise<string[][]> {
		const paths: string[][] = [];
		const visited = new Set<string>();

		const findPaths = (
			currentId: string,
			targetId: string,
			currentPath: string[],
			depth: number
		) => {
			if (depth > maxDepth) return;
			if (currentId === targetId) {
				paths.push([...currentPath, currentId]);
				return;
			}
			if (visited.has(currentId)) return;

			visited.add(currentId);
			currentPath.push(currentId);

			// 查找所有連接的節點
			const connectedNodes = graph.links
				.filter(link => {
					const sourceNodeId = typeof link.source === 'string' ? link.source : link.source.id;
					const targetNodeId = typeof link.target === 'string' ? link.target : link.target.id;
					return sourceNodeId === currentId || targetNodeId === currentId;
				})
				.map(link => {
					const sourceNodeId = typeof link.source === 'string' ? link.source : link.source.id;
					const targetNodeId = typeof link.target === 'string' ? link.target : link.target.id;
					return sourceNodeId === currentId ? targetNodeId : sourceNodeId;
				});

			for (const nextId of connectedNodes) {
				findPaths(nextId, targetId, [...currentPath], depth + 1);
			}

			visited.delete(currentId);
		};

		findPaths(sourceId, targetId, [], 0);
		return paths;
	}

	/**
	 * 檢測社群聚類
	 */
	async detectCommunities(graph: DependencyGraph): Promise<Map<string, string[]>> {
		const communities = new Map<string, string[]>();
		const visited = new Set<string>();
		let communityId = 0;

		// 使用深度優先搜索檢測連通分量
		const dfs = (nodeId: string, currentCommunity: string[]) => {
			if (visited.has(nodeId)) return;

			visited.add(nodeId);
			currentCommunity.push(nodeId);

			// 查找所有連接的節點
			const connectedNodes = graph.links
				.filter(link => {
					const sourceNodeId = typeof link.source === 'string' ? link.source : link.source.id;
					const targetNodeId = typeof link.target === 'string' ? link.target : link.target.id;
					return (sourceNodeId === nodeId || targetNodeId === nodeId) && link.strength > 0.3;
				})
				.map(link => {
					const sourceNodeId = typeof link.source === 'string' ? link.source : link.source.id;
					const targetNodeId = typeof link.target === 'string' ? link.target : link.target.id;
					return sourceNodeId === nodeId ? targetNodeId : sourceNodeId;
				});

			for (const connectedNodeId of connectedNodes) {
				dfs(connectedNodeId, currentCommunity);
			}
		};

		for (const node of graph.nodes) {
			if (!visited.has(node.id)) {
				const community: string[] = [];
				dfs(node.id, community);
				if (community.length > 1) {
					communities.set(`community-${communityId++}`, community);
				}
			}
		}

		return communities;
	}

	/**
	 * 計算節點重要性 (PageRank 算法)
	 */
	async calculateNodeImportance(graph: DependencyGraph): Promise<Map<string, number>> {
		const importance = new Map<string, number>();
		const dampingFactor = 0.85;
		const iterations = 50;
		const tolerance = 1e-6;

		// 初始化
		for (const node of graph.nodes) {
			importance.set(node.id, 1.0);
		}

		// 建立鄰接表
		const adjacencyList = new Map<string, string[]>();
		for (const node of graph.nodes) {
			adjacencyList.set(node.id, []);
		}

		for (const link of graph.links) {
			const sourceId = typeof link.source === 'string' ? link.source : link.source.id;
			const targetId = typeof link.target === 'string' ? link.target : link.target.id;

			adjacencyList.get(sourceId)?.push(targetId);
			adjacencyList.get(targetId)?.push(sourceId);
		}

		// PageRank 迭代
		for (let iter = 0; iter < iterations; iter++) {
			const newImportance = new Map<string, number>();
			let maxChange = 0;

			for (const node of graph.nodes) {
				let rank = (1 - dampingFactor) / graph.nodes.length;

				const incomingNodes = adjacencyList.get(node.id) || [];
				for (const incomingNodeId of incomingNodes) {
					const incomingRank = importance.get(incomingNodeId) || 0;
					const outgoingCount = adjacencyList.get(incomingNodeId)?.length || 1;
					rank += dampingFactor * (incomingRank / outgoingCount);
				}

				newImportance.set(node.id, rank);
				maxChange = Math.max(maxChange, Math.abs(rank - (importance.get(node.id) || 0)));
			}

			importance.clear();
			for (const [nodeId, rank] of newImportance) {
				importance.set(nodeId, rank);
			}

			if (maxChange < tolerance) break;
		}

		return importance;
	}
}

// 導出單例實例
export const dependencyService = new DependencyService();
