import { describe, it, expect, beforeAll, afterAll } from "vitest";
import { prismaClient } from "../prisma/client.js";

describe("Database Connection", () => {
  beforeAll(async () => {
    await prismaClient.connect();
  });

  afterAll(async () => {
    await prismaClient.disconnect();
  });

  it("should connect to database successfully", async () => {
    const isConnected = prismaClient.isConnected();
    expect(isConnected).toBe(true);
  });

  it("should perform health check", async () => {
    const isHealthy = await prismaClient.healthCheck();
    expect(isHealthy).toBe(true);
  });

  it("should get database statistics", async () => {
    const stats = await prismaClient.getDatabaseStats();
    expect(stats).toHaveProperty("users");
    expect(stats).toHaveProperty("notes");
    expect(stats).toHaveProperty("workspaces");
    expect(stats).toHaveProperty("dependencies");
    expect(typeof stats.users).toBe("number");
    expect(typeof stats.notes).toBe("number");
    expect(typeof stats.workspaces).toBe("number");
    expect(typeof stats.dependencies).toBe("number");
  });

  it("should create and retrieve a test user", async () => {
    // 創建測試用戶
    const testUser = await prismaClient.client.user.create({
      data: {
        id: "test-user-1",
        username: "testuser1",
        email: "<EMAIL>",
        displayName: "測試用戶1",
        role: "user",
        status: "active",
      },
    });

    expect(testUser).toBeDefined();
    expect(testUser.username).toBe("testuser1");
    expect(testUser.email).toBe("<EMAIL>");

    // 檢索用戶
    const retrievedUser = await prismaClient.client.user.findUnique({
      where: { id: "test-user-1" },
    });

    expect(retrievedUser).toBeDefined();
    expect(retrievedUser?.username).toBe("testuser1");

    // 清理測試數據
    await prismaClient.client.user.delete({
      where: { id: "test-user-1" },
    });
  });

  it("should create and retrieve a test note", async () => {
    // 首先創建一個用戶
    const testUser = await prismaClient.client.user.create({
      data: {
        id: "test-user-2",
        username: "testuser2",
        email: "<EMAIL>",
        displayName: "測試用戶2",
        role: "user",
        status: "active",
      },
    });

    // 創建測試筆記
    const testNote = await prismaClient.client.note.create({
      data: {
        id: "test-note-1",
        title: "測試筆記",
        content: "這是一個測試筆記的內容",
        category: "測試",
        tags: JSON.stringify(["測試", "筆記"]),
        status: "draft",
        priority: "medium",
        version: "1.0.0",
        authorId: testUser.id,
        metadata: JSON.stringify({ test: true }),
      },
    });

    expect(testNote).toBeDefined();
    expect(testNote.title).toBe("測試筆記");
    expect(testNote.authorId).toBe(testUser.id);

    // 檢索筆記
    const retrievedNote = await prismaClient.client.note.findUnique({
      where: { id: "test-note-1" },
    });

    expect(retrievedNote).toBeDefined();
    expect(retrievedNote?.title).toBe("測試筆記");

    // 檢查 JSON 字段
    const tags = JSON.parse(retrievedNote?.tags || "[]");
    expect(tags).toEqual(["測試", "筆記"]);

    const metadata = JSON.parse(retrievedNote?.metadata || "{}");
    expect(metadata.test).toBe(true);

    // 清理測試數據
    await prismaClient.client.note.delete({
      where: { id: "test-note-1" },
    });
    await prismaClient.client.user.delete({
      where: { id: "test-user-2" },
    });
  });

  it("should create and retrieve dependencies", async () => {
    // 創建測試用戶
    const testUser = await prismaClient.client.user.create({
      data: {
        id: "test-user-3",
        username: "testuser3",
        email: "<EMAIL>",
        displayName: "測試用戶3",
        role: "user",
        status: "active",
      },
    });

    // 創建兩個測試筆記
    const sourceNote = await prismaClient.client.note.create({
      data: {
        id: "test-note-source",
        title: "源筆記",
        content: "這是源筆記",
        status: "draft",
        priority: "medium",
        version: "1.0.0",
        authorId: testUser.id,
      },
    });

    const targetNote = await prismaClient.client.note.create({
      data: {
        id: "test-note-target",
        title: "目標筆記",
        content: "這是目標筆記",
        status: "draft",
        priority: "medium",
        version: "1.0.0",
        authorId: testUser.id,
      },
    });

    // 創建依賴關係
    const dependency = await prismaClient.client.dependency.create({
      data: {
        id: "test-dependency-1",
        sourceNoteId: sourceNote.id,
        targetNoteId: targetNote.id,
        type: "reference",
        strength: "medium",
        description: "測試依賴關係",
        metadata: JSON.stringify({ test: true }),
      },
    });

    expect(dependency).toBeDefined();
    expect(dependency.sourceNoteId).toBe(sourceNote.id);
    expect(dependency.targetNoteId).toBe(targetNote.id);

    // 檢索依賴關係
    const retrievedDependency = await prismaClient.client.dependency.findUnique(
      {
        where: { id: "test-dependency-1" },
        include: {
          sourceNote: true,
          targetNote: true,
        },
      },
    );

    expect(retrievedDependency).toBeDefined();
    expect(retrievedDependency?.sourceNote.title).toBe("源筆記");
    expect(retrievedDependency?.targetNote.title).toBe("目標筆記");

    // 清理測試數據
    await prismaClient.client.dependency.delete({
      where: { id: "test-dependency-1" },
    });
    await prismaClient.client.note.delete({
      where: { id: "test-note-source" },
    });
    await prismaClient.client.note.delete({
      where: { id: "test-note-target" },
    });
    await prismaClient.client.user.delete({
      where: { id: "test-user-3" },
    });
  });

  it("should handle workspace operations", async () => {
    // 創建測試用戶
    const testUser = await prismaClient.client.user.create({
      data: {
        id: "test-user-4",
        username: "testuser4",
        email: "<EMAIL>",
        displayName: "測試用戶4",
        role: "user",
        status: "active",
      },
    });

    // 創建工作空間
    const workspace = await prismaClient.client.workspace.create({
      data: {
        id: "test-workspace-1",
        name: "測試工作空間",
        description: "這是一個測試工作空間",
        isPublic: false,
        ownerId: testUser.id,
        settings: JSON.stringify({
          allowGuestAccess: false,
          defaultNoteVisibility: "private",
        }),
      },
    });

    expect(workspace).toBeDefined();
    expect(workspace.name).toBe("測試工作空間");
    expect(workspace.ownerId).toBe(testUser.id);

    // 添加工作空間成員
    const member = await prismaClient.client.workspaceMember.create({
      data: {
        workspaceId: workspace.id,
        userId: testUser.id,
        role: "owner",
        permissions: JSON.stringify(["read", "write", "admin"]),
      },
    });

    expect(member).toBeDefined();
    expect(member.role).toBe("owner");

    // 檢索工作空間及其成員
    const retrievedWorkspace = await prismaClient.client.workspace.findUnique({
      where: { id: "test-workspace-1" },
      include: {
        members: {
          include: {
            user: true,
          },
        },
      },
    });

    expect(retrievedWorkspace).toBeDefined();
    expect(retrievedWorkspace?.members).toHaveLength(1);
    expect(retrievedWorkspace?.members[0].user.username).toBe("testuser4");

    // 清理測試數據
    await prismaClient.client.workspaceMember.delete({
      where: { id: member.id },
    });
    await prismaClient.client.workspace.delete({
      where: { id: "test-workspace-1" },
    });
    await prismaClient.client.user.delete({
      where: { id: "test-user-4" },
    });
  });
});
