<!doctype html>
<html lang="en" class="%sveltekit.theme%">
	<head>
		<meta charset="utf-8" />
		<link rel="icon" href="%sveltekit.assets%/favicon.svg" type="image/svg+xml" />
		<link rel="icon" href="data:image/x-icon;base64,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" />
		<link rel="apple-touch-icon" href="%sveltekit.assets%/favicon.svg" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />

		<!-- Primary Meta Tags -->
		<title>Life Note - Your Personal Knowledge Management System</title>
		<meta name="title" content="Life Note - Your Personal Knowledge Management System" />
		<meta
			name="description"
			content="A powerful, local-first note-taking application with markdown support, dependency tracking, and intelligent organization."
		/>

		<!-- Open Graph / Facebook -->
		<meta property="og:type" content="website" />
		<meta property="og:url" content="https://life-note.app/" />
		<meta property="og:title" content="Life Note - Your Personal Knowledge Management System" />
		<meta
			property="og:description"
			content="A powerful, local-first note-taking application with markdown support, dependency tracking, and intelligent organization."
		/>
		<meta property="og:image" content="%sveltekit.assets%/og-image.png" />

		<!-- Twitter -->
		<meta property="twitter:card" content="summary_large_image" />
		<meta property="twitter:url" content="https://life-note.app/" />
		<meta
			property="twitter:title"
			content="Life Note - Your Personal Knowledge Management System"
		/>
		<meta
			property="twitter:description"
			content="A powerful, local-first note-taking application with markdown support, dependency tracking, and intelligent organization."
		/>
		<meta property="twitter:image" content="%sveltekit.assets%/twitter-image.png" />

		<!-- Fonts -->
		<link rel="preconnect" href="https://fonts.googleapis.com" />
		<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
		<link
			href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&family=Merriweather:wght@300;400;700&display=swap"
			rel="stylesheet"
		/>

		<!-- Theme and CSS Variables -->
		<style>
			:root {
				/* Light theme colors */
				--color-bg: 255 255 255;
				--color-bg-secondary: 248 250 252;
				--color-bg-tertiary: 241 245 249;
				--color-text: 15 23 42;
				--color-text-muted: 100 116 139;
				--color-text-subtle: 148 163 184;
				--color-border: 226 232 240;
				--color-border-hover: 203 213 225;
				--color-primary: 14 165 233;
				--color-primary-hover: 2 132 199;
				--color-accent: 217 70 239;
				--color-success: 34 197 94;
				--color-warning: 245 158 11;
				--color-error: 239 68 68;
			}

			.dark {
				/* Dark theme colors */
				--color-bg: 2 6 23;
				--color-bg-secondary: 15 23 42;
				--color-bg-tertiary: 30 41 59;
				--color-text: 248 250 252;
				--color-text-muted: 148 163 184;
				--color-text-subtle: 100 116 139;
				--color-border: 51 65 85;
				--color-border-hover: 71 85 105;
				--color-primary: 56 189 248;
				--color-primary-hover: 14 165 233;
				--color-accent: 232 121 249;
				--color-success: 34 197 94;
				--color-warning: 251 191 36;
				--color-error: 248 113 113;
			}

			/* Base styles */
			* {
				box-sizing: border-box;
			}

			html {
				scroll-behavior: smooth;
			}

			body {
				margin: 0;
				padding: 0;
				font-family:
					'Inter',
					system-ui,
					-apple-system,
					sans-serif;
				background-color: rgb(var(--color-bg));
				color: rgb(var(--color-text));
				line-height: 1.6;
				-webkit-font-smoothing: antialiased;
				-moz-osx-font-smoothing: grayscale;
			}

			/* Loading state */
			.loading {
				display: flex;
				align-items: center;
				justify-content: center;
				min-height: 100vh;
				background-color: rgb(var(--color-bg));
			}

			.loading-spinner {
				width: 40px;
				height: 40px;
				border: 3px solid rgb(var(--color-border));
				border-top: 3px solid rgb(var(--color-primary));
				border-radius: 50%;
				animation: spin 1s linear infinite;
			}

			@keyframes spin {
				0% {
					transform: rotate(0deg);
				}
				100% {
					transform: rotate(360deg);
				}
			}
		</style>

		%sveltekit.head%
	</head>
	<body data-sveltekit-preload-data="hover" class="antialiased">
		<div style="display: contents" class="app">
			<!-- Loading fallback -->
			<noscript>
				<div class="loading">
					<div>
						<h1>Life Note</h1>
						<p>Please enable JavaScript to use this application.</p>
					</div>
				</div>
			</noscript>

			<!-- App content -->
			%sveltekit.body%
		</div>
	</body>
</html>
