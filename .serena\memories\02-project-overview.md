# 專案技術概覽

## 專案概述

### 專案名稱

本地端智慧知識庫筆記系統

### 專案類型

混合架構桌面應用程式（本地優先 + 雲端協作）

### 核心功能

- **版本控制知識管理**：基於Git概念的Tag-based版本控制，支援draft-v1 → v1 → draft-v2流程
- **智慧依賴關係檢測**：自動識別和標記專案內文件間的依賴關係
- **混合AI助理**：本地輕量級Agent + 遠端協作Agent的分層處理架構
- **多格式筆記支援**：Markdown檔案存儲 + YAML frontmatter + SQLite索引
- **跨平台部署**：支援iOS、Android、Windows等多平台應用

## 技術架構

### 應用程式類型

混合架構單頁應用（SPA）+ 本地代理服務

### 部署模式

**混合部署策略**：

- 本地部署：核心筆記功能、敏感資料處理、基本AI助理
- 雲端部署：複雜AI協作、跨領域分析、多Agent協調
- 漸進式降級：遠端服務故障時本地仍可正常運作

### 資料存儲策略

**多層存儲架構**：

- 筆記內容：Markdown檔案 + YAML frontmatter
- 系統索引：SQLite資料庫（關聯、標籤、元資料）
- 版本控制：Git-like本地儲存庫
- 快取機制：本地快取 + 漸進式同步

## 開發約束

### 技術偏好

- **前端框架**：React + AG-UI核心套件
- **後端語言**：Node.js/Python（本地Agent服務）
- **AI整合**：Gemini-2.5-flash + MCP（Model Context Protocol）
- **跨平台打包**：Tauri框架
- **通訊協議**：JSON-RPC 2.0 over HTTPS + Server-Sent Events

### 效能要求

- 本地操作回應時間： 80%

### 可擴展性

- 插件式Agent架構，支援自定義MCP模組
- 可配置的脫敏處理策略
- 支援自定義筆記模板和工作流程
- 預留第三方整合介面

### 安全性

- **分層脱敏處理**：基礎遮罩 → 資料置換 → 資料洗牌
- **本地優先原則**：敏感資料不離開本地環境
- **OAuth 2.0認證**：統一身份認證機制
- **加密存儲**：本地敏感資料加密保護

### 測試策略

- **單元測試**：核心業務邏輯覆蓋
- **整合測試**：AI Agent間通訊驗證
- **端對端測試**：完整使用者工作流程
- **效能測試**：大量筆記情境下的回應時間

## 架構創新點

### 智慧路由機制

根據任務複雜度和敏感性自動選擇本地或遠端處理：

- **本地處理優先**：檔案操作、日曆管理、基本問答
- **遠端協作場景**：跨領域分析、複雜工作流程、多Agent協作

### 漸進式降級設計

確保在網路中斷或遠端服務故障時，本地功能完全可用，提供一致的用戶體驗。

### 版本控制創新

借鑑Git概念但簡化為知識工作者的直觀模式，降低版本管理的學習成本。

_此技術概覽將作為後續詳細設計文檔的基礎，包括技術棧配置、架構模式設計、業務邏輯實現和程式碼結構規劃。_
