import { BaseAgent } from './BaseAgent';
import type { AgentConfig, AgentTaskRequest, AgentTaskResult } from '$lib/types/agent';

/**
 * Gemini 2.5 Flash 本地 Agent 實現
 * 統一使用 Google Gemini API，支援 MCP 通信
 */
export class GeminiAgent extends BaseAgent {
	private apiKey: string = '';
	private baseUrl = 'https://generativelanguage.googleapis.com/v1beta';
	private model = 'gemini-1.5-flash';
	private mcpClient: any = null; // MCP 客戶端

	constructor(config: AgentConfig, apiKey?: string) {
		super(config);
		if (apiKey) {
			this.apiKey = apiKey;
		}
	}

	/**
	 * 設置 API Key
	 */
	setApiKey(apiKey: string): void {
		this.apiKey = apiKey;
	}

	/**
	 * 設置 MCP 客戶端
	 */
	setMcpClient(mcpClient: any): void {
		this.mcpClient = mcpClient;
	}

	/**
	 * 初始化 Agent
	 */
	protected async onInitialize(): Promise<void> {
		if (!this.apiKey) {
			throw new Error('Gemini API key is required');
		}

		try {
			// 測試 API 連接
			await this.testConnection();
			console.log(`Gemini Agent ${this.id} initialized successfully`);
		} catch (error) {
			console.error(`Failed to initialize Gemini Agent ${this.id}:`, error);
			throw error;
		}
	}

	/**
	 * 執行任務實現
	 */
	protected async onExecuteTask(request: AgentTaskRequest): Promise<any> {
		try {
			// 檢查是否需要使用 MCP 工具
			const mcpResult = await this.tryMcpExecution(request);
			if (mcpResult) {
				return mcpResult;
			}

			// 使用 Gemini API 執行任務
			switch (request.type) {
				case 'note_analysis':
					return await this.analyzeNote(request);
				case 'content_generation':
					return await this.generateContent(request);
				case 'summarization':
					return await this.summarizeText(request);
				case 'translation':
					return await this.translateText(request);
				case 'question_answering':
					return await this.answerQuestion(request);
				case 'classification':
					return await this.classifyText(request);
				case 'extraction':
					return await this.extractInformation(request);
				case 'dependency_check':
					return await this.checkDependencies(request);
				case 'search_query':
					return await this.processSearchQuery(request);
				case 'recommendation':
					return await this.generateRecommendations(request);
				default:
					return await this.handleGenericTask(request);
			}
		} catch (error) {
			console.error(`Task execution failed for Gemini Agent ${this.id}:`, error);
			throw error;
		}
	}

	/**
	 * 停止實現
	 */
	protected async onStop(): Promise<void> {
		console.log(`Stopping Gemini Agent ${this.id}`);
		// 清理資源
		this.mcpClient = null;
	}

	/**
	 * 配置更新實現
	 */
	protected async onConfigUpdate(oldConfig: AgentConfig, newConfig: AgentConfig): Promise<void> {
		console.log(`Configuration updated for Gemini Agent ${this.id}`);
	}

	/**
	 * 取消任務實現
	 */
	protected async onCancelTask(taskId: string): Promise<void> {
		console.log(`Task ${taskId} cancelled for Gemini Agent ${this.id}`);
	}

	/**
	 * 測試 API 連接
	 */
	private async testConnection(): Promise<void> {
		try {
			const response = await this.callGeminiAPI([{ role: 'user', parts: [{ text: 'Hello' }] }], {
				maxOutputTokens: 10
			});

			if (!response.candidates || response.candidates.length === 0) {
				throw new Error('Invalid API response');
			}

			console.log('Gemini API connection test successful');
		} catch (error) {
			console.error('Gemini API connection test failed:', error);
			throw error;
		}
	}

	/**
	 * 嘗試使用 MCP 執行任務
	 */
	private async tryMcpExecution(request: AgentTaskRequest): Promise<any> {
		if (!this.mcpClient) {
			return null;
		}

		try {
			// 根據任務類型選擇 MCP 工具
			switch (request.type) {
				case 'note_analysis':
				case 'dependency_check':
				case 'search_query':
					// 這些任務可以通過 MCP 工具執行
					return await this.executeMcpTool(request);
				default:
					return null;
			}
		} catch (error) {
			console.warn(`MCP execution failed, falling back to Gemini API:`, error);
			return null;
		}
	}

	/**
	 * 執行 MCP 工具
	 */
	private async executeMcpTool(request: AgentTaskRequest): Promise<any> {
		// 這裡實現 MCP 工具調用邏輯
		// 根據任務類型調用相應的 MCP 工具
		const toolName = this.getToolNameForTask(request.type);

		if (toolName && this.mcpClient.hasTool(toolName)) {
			const result = await this.mcpClient.callTool(toolName, request.input);
			return {
				output: result,
				executionMode: 'mcp',
				toolUsed: toolName
			};
		}

		return null;
	}

	/**
	 * 獲取任務對應的 MCP 工具名稱
	 */
	private getToolNameForTask(taskType: string): string | null {
		const toolMap: Record<string, string> = {
			note_analysis: 'analyze_note',
			dependency_check: 'check_dependencies',
			search_query: 'search_notes'
		};
		return toolMap[taskType] || null;
	}

	/**
	 * 調用 Gemini API
	 */
	private async callGeminiAPI(messages: any[], options: any = {}): Promise<any> {
		if (!this.apiKey) {
			throw new Error('API Key is required');
		}

		const url = `${this.baseUrl}/models/${this.model}:generateContent?key=${this.apiKey}`;

		const payload = {
			contents: messages,
			generationConfig: {
				maxOutputTokens: options.maxOutputTokens || 2048,
				temperature: options.temperature || 0.7,
				topP: options.topP || 0.8,
				topK: options.topK || 40
			}
		};

		console.log('Calling Gemini API:', { url: url.replace(this.apiKey, '***'), payload });

		try {
			const response = await fetch(url, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(payload)
			});

			if (!response.ok) {
				const errorText = await response.text();
				console.error('Gemini API error response:', errorText);

				let errorMessage = `HTTP ${response.status}`;
				try {
					const errorJson = JSON.parse(errorText);
					errorMessage = errorJson.error?.message || errorText;
				} catch {
					errorMessage = errorText;
				}

				throw new Error(`Gemini API error: ${errorMessage}`);
			}

			const result = await response.json();
			console.log('Gemini API response:', result);
			return result;
		} catch (error) {
			console.error('Gemini API call failed:', error);
			throw error;
		}
	}

	/**
	 * 分析筆記
	 */
	private async analyzeNote(request: AgentTaskRequest): Promise<any> {
		const { content, title } = request.input;

		const messages = [
			{
				role: 'user',
				parts: [
					{
						text: `請分析以下筆記，提供詳細的分析結果：

標題：${title}
內容：${content}

請以 JSON 格式回應，包含以下字段：
- sentiment: 情感分析 (positive/negative/neutral)
- topics: 主要主題列表
- complexity: 複雜度 (low/medium/high)
- readability: 可讀性分數 (0-1)
- keyPoints: 關鍵要點列表
- suggestions: 改進建議列表
- tags: 建議的標籤列表`
					}
				]
			}
		];

		const response = await this.callGeminiAPI(messages);
		const content_text = response.candidates[0].content.parts[0].text;

		try {
			return JSON.parse(content_text);
		} catch {
			// 如果無法解析 JSON，返回結構化的默認結果
			return {
				sentiment: 'neutral',
				topics: ['general'],
				complexity: 'medium',
				readability: 0.7,
				keyPoints: [content_text.substring(0, 100) + '...'],
				suggestions: ['Consider adding more structure'],
				tags: ['analysis'],
				rawResponse: content_text
			};
		}
	}

	/**
	 * 生成內容
	 */
	private async generateContent(request: AgentTaskRequest): Promise<any> {
		const { prompt, maxLength = 500, style = 'professional' } = request.input;

		const messages = [
			{
				role: 'user',
				parts: [
					{
						text: `請根據以下提示生成內容：

提示：${prompt}
風格：${style}
最大長度：${maxLength} 字符

請生成高質量、相關且有用的內容。`
					}
				]
			}
		];

		const response = await this.callGeminiAPI(messages, {
			maxOutputTokens: Math.ceil(maxLength / 2),
			temperature: request.options?.temperature || 0.7
		});

		const generatedContent = response.candidates[0].content.parts[0].text;

		return {
			content: generatedContent,
			length: generatedContent.length,
			style,
			usage: response.usageMetadata
		};
	}

	/**
	 * 摘要文本
	 */
	private async summarizeText(request: AgentTaskRequest): Promise<any> {
		const { text, maxLength = 200, language = 'zh-TW' } = request.input;

		const messages = [
			{
				role: 'user',
				parts: [
					{
						text: `請為以下文本生成摘要：

原文：${text}

要求：
- 摘要長度不超過 ${maxLength} 字符
- 語言：${language}
- 保留關鍵信息和主要觀點
- 簡潔明了`
					}
				]
			}
		];

		const response = await this.callGeminiAPI(messages, {
			maxOutputTokens: Math.ceil(maxLength / 2)
		});

		const summary = response.candidates[0].content.parts[0].text;

		return {
			summary,
			originalLength: text.length,
			summaryLength: summary.length,
			compressionRatio: summary.length / text.length,
			language,
			usage: response.usageMetadata
		};
	}

	/**
	 * 翻譯文本
	 */
	private async translateText(request: AgentTaskRequest): Promise<any> {
		const { text, targetLanguage, sourceLanguage = 'auto' } = request.input;

		const messages = [
			{
				role: 'user',
				parts: [
					{
						text: `請將以下文本翻譯：

原文：${text}
源語言：${sourceLanguage}
目標語言：${targetLanguage}

請提供準確、自然的翻譯。`
					}
				]
			}
		];

		const response = await this.callGeminiAPI(messages);
		const translatedText = response.candidates[0].content.parts[0].text;

		return {
			translatedText,
			sourceLanguage,
			targetLanguage,
			confidence: 0.95,
			usage: response.usageMetadata
		};
	}

	/**
	 * 回答問題
	 */
	private async answerQuestion(request: AgentTaskRequest): Promise<any> {
		const { question, context, language = 'zh-TW' } = request.input;

		let prompt = `請回答以下問題：

問題：${question}`;

		if (context) {
			prompt += `\n\n參考資料：${context}`;
		}

		prompt += `\n\n請提供準確、詳細的回答，使用 ${language} 語言。`;

		const messages = [
			{
				role: 'user',
				parts: [{ text: prompt }]
			}
		];

		const response = await this.callGeminiAPI(messages);
		const answer = response.candidates[0].content.parts[0].text;

		return {
			answer,
			question,
			hasContext: !!context,
			language,
			confidence: 0.9,
			usage: response.usageMetadata
		};
	}

	/**
	 * 分類文本
	 */
	private async classifyText(request: AgentTaskRequest): Promise<any> {
		const { text, categories, language = 'zh-TW' } = request.input;

		const categoryList = categories?.join('、') || '一般分類';

		const messages = [
			{
				role: 'user',
				parts: [
					{
						text: `請將以下文本分類到最適合的類別中：

文本：${text}
可選類別：${categoryList}

請以 JSON 格式回應，包含：
- category: 選擇的類別
- confidence: 信心度 (0-1)
- reasoning: 分類理由`
					}
				]
			}
		];

		const response = await this.callGeminiAPI(messages);
		const content_text = response.candidates[0].content.parts[0].text;

		try {
			return JSON.parse(content_text);
		} catch {
			return {
				category: categories?.[0] || 'general',
				confidence: 0.7,
				reasoning: '自動分類',
				rawResponse: content_text
			};
		}
	}

	/**
	 * 提取信息
	 */
	private async extractInformation(request: AgentTaskRequest): Promise<any> {
		const { text, extractionType, language = 'zh-TW' } = request.input;

		const messages = [
			{
				role: 'user',
				parts: [
					{
						text: `請從以下文本中提取 ${extractionType}：

文本：${text}

請以列表形式提供提取的信息。`
					}
				]
			}
		];

		const response = await this.callGeminiAPI(messages);
		const extractedText = response.candidates[0].content.parts[0].text;

		// 簡單解析提取的信息
		const extracted = extractedText
			.split('\n')
			.filter(line => line.trim().length > 0)
			.map(line => line.replace(/^[-*•]\s*/, '').trim());

		return {
			extracted,
			extractionType,
			confidence: 0.85,
			language,
			usage: response.usageMetadata
		};
	}

	/**
	 * 檢查依賴關係
	 */
	private async checkDependencies(request: AgentTaskRequest): Promise<any> {
		// 這個方法主要通過 MCP 工具執行，這裡提供備用實現
		const { noteId, content } = request.input;

		const messages = [
			{
				role: 'user',
				parts: [
					{
						text: `請分析以下筆記的依賴關係：

筆記內容：${content}

請識別：
1. 引用的其他筆記或文檔
2. 相關的主題和概念
3. 可能的依賴關係

以 JSON 格式回應。`
					}
				]
			}
		];

		const response = await this.callGeminiAPI(messages);
		const content_text = response.candidates[0].content.parts[0].text;

		try {
			return JSON.parse(content_text);
		} catch {
			return {
				dependencies: [],
				references: [],
				topics: [],
				rawResponse: content_text
			};
		}
	}

	/**
	 * 處理搜索查詢
	 */
	private async processSearchQuery(request: AgentTaskRequest): Promise<any> {
		const { query, context } = request.input;

		const messages = [
			{
				role: 'user',
				parts: [
					{
						text: `請幫助優化以下搜索查詢：

原始查詢：${query}
${context ? `上下文：${context}` : ''}

請提供：
1. 優化後的查詢詞
2. 相關的搜索建議
3. 可能的同義詞

以 JSON 格式回應。`
					}
				]
			}
		];

		const response = await this.callGeminiAPI(messages);
		const content_text = response.candidates[0].content.parts[0].text;

		try {
			return JSON.parse(content_text);
		} catch {
			return {
				optimizedQuery: query,
				suggestions: [query],
				synonyms: [],
				rawResponse: content_text
			};
		}
	}

	/**
	 * 生成推薦
	 */
	private async generateRecommendations(request: AgentTaskRequest): Promise<any> {
		const { context, type = 'general', limit = 5 } = request.input;

		const messages = [
			{
				role: 'user',
				parts: [
					{
						text: `基於以下上下文，請生成 ${limit} 個 ${type} 推薦：

上下文：${context}

請以 JSON 格式回應，包含推薦列表，每個推薦包含：
- title: 推薦標題
- description: 推薦描述
- relevance: 相關性分數 (0-1)
- reason: 推薦理由`
					}
				]
			}
		];

		const response = await this.callGeminiAPI(messages);
		const content_text = response.candidates[0].content.parts[0].text;

		try {
			return JSON.parse(content_text);
		} catch {
			return {
				recommendations: [],
				type,
				rawResponse: content_text
			};
		}
	}

	/**
	 * 處理通用任務
	 */
	private async handleGenericTask(request: AgentTaskRequest): Promise<any> {
		const messages = [
			{
				role: 'user',
				parts: [
					{
						text: `請處理以下任務：

任務類型：${request.type}
輸入：${JSON.stringify(request.input, null, 2)}

請提供適當的回應。`
					}
				]
			}
		];

		const response = await this.callGeminiAPI(messages);
		const content_text = response.candidates[0].content.parts[0].text;

		return {
			result: content_text,
			taskType: request.type,
			usage: response.usageMetadata
		};
	}
}
