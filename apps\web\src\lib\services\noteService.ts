import type {
	Note,
	NoteId,
	NoteStatus,
	NotePriority,
	Tag,
	PaginatedResult,
	SearchParams
} from '$types';
import { repositoryFactory } from '@life-note/storage';

export interface CreateNoteData {
	title: string;
	content: string;
	categoryId?: string;
	tags?: string[];
	priority?: NotePriority;
	metadata?: Record<string, any>;
}

export interface UpdateNoteData {
	title?: string;
	content?: string;
	categoryId?: string;
	tags?: string[];
	priority?: NotePriority;
	status?: NoteStatus;
	metadata?: Record<string, any>;
}

export class NoteService {
	/**
	 * 創建新筆記
	 */
	async createNote(authorId: string, data: CreateNoteData): Promise<Note> {
		try {
			const noteData = {
				id: this.generateNoteId(),
				title: data.title,
				content: data.content,
				excerpt: this.generateExcerpt(data.content),
				status: 'draft' as NoteStatus,
				priority: data.priority || ('medium' as NotePriority),
				authorId,
				categoryId: data.categoryId,
				tags:
					data.tags?.map(tagName => ({
						id: this.generateTagId(),
						name: tagName,
						createdAt: new Date(),
						updatedAt: new Date()
					})) || [],
				metadata: data.metadata || {},
				createdAt: new Date(),
				updatedAt: new Date(),
				version: 1
			};

			const noteRepo = repositoryFactory.noteRepository;
			return await noteRepo.save(noteData);
		} catch (error) {
			console.error('Failed to create note:', error);
			throw new Error('創建筆記失敗');
		}
	}

	/**
	 * 更新筆記
	 */
	async updateNote(noteId: NoteId, data: UpdateNoteData): Promise<Note> {
		try {
			const noteRepo = repositoryFactory.noteRepository;
			const existingNote = await noteRepo.findById(noteId);

			if (!existingNote) {
				throw new Error('筆記不存在');
			}

			const updatedNote = {
				...existingNote,
				...data,
				excerpt: data.content ? this.generateExcerpt(data.content) : existingNote.excerpt,
				updatedAt: new Date(),
				version: existingNote.version + 1
			};

			// 如果有標籤更新，處理標籤
			if (data.tags) {
				updatedNote.tags = data.tags.map(tagName => ({
					id: this.generateTagId(),
					name: tagName,
					createdAt: new Date(),
					updatedAt: new Date()
				}));
			}

			return await noteRepo.save(updatedNote);
		} catch (error) {
			console.error('Failed to update note:', error);
			throw new Error('更新筆記失敗');
		}
	}

	/**
	 * 刪除筆記
	 */
	async deleteNote(noteId: NoteId): Promise<void> {
		try {
			const noteRepo = repositoryFactory.noteRepository;
			await noteRepo.delete(noteId);
		} catch (error) {
			console.error('Failed to delete note:', error);
			throw new Error('刪除筆記失敗');
		}
	}

	/**
	 * 獲取筆記詳情
	 */
	async getNote(noteId: NoteId): Promise<Note | null> {
		try {
			const noteRepo = repositoryFactory.noteRepository;
			return await noteRepo.findById(noteId);
		} catch (error) {
			console.error('Failed to get note:', error);
			throw new Error('獲取筆記失敗');
		}
	}

	/**
	 * 獲取用戶的筆記列表
	 */
	async getUserNotes(
		authorId: string,
		params: Partial<SearchParams> = {}
	): Promise<PaginatedResult<Note>> {
		try {
			const noteRepo = repositoryFactory.noteRepository;
			const searchParams: SearchParams = {
				page: params.page || 1,
				limit: params.limit || 20,
				sortBy: params.sortBy || 'updatedAt',
				sortOrder: params.sortOrder || 'desc',
				authorId,
				...params
			};

			return await noteRepo.search(searchParams);
		} catch (error) {
			console.error('Failed to get user notes:', error);
			throw new Error('獲取筆記列表失敗');
		}
	}

	/**
	 * 搜索筆記
	 */
	async searchNotes(params: SearchParams): Promise<PaginatedResult<Note>> {
		try {
			const noteRepo = repositoryFactory.noteRepository;
			return await noteRepo.search(params);
		} catch (error) {
			console.error('Failed to search notes:', error);
			throw new Error('搜索筆記失敗');
		}
	}

	/**
	 * 發布筆記
	 */
	async publishNote(noteId: NoteId): Promise<Note> {
		try {
			return await this.updateNote(noteId, {
				status: 'published',
				metadata: {
					publishedAt: new Date().toISOString()
				}
			});
		} catch (error) {
			console.error('Failed to publish note:', error);
			throw new Error('發布筆記失敗');
		}
	}

	/**
	 * 歸檔筆記
	 */
	async archiveNote(noteId: NoteId): Promise<Note> {
		try {
			return await this.updateNote(noteId, {
				status: 'archived',
				metadata: {
					archivedAt: new Date().toISOString()
				}
			});
		} catch (error) {
			console.error('Failed to archive note:', error);
			throw new Error('歸檔筆記失敗');
		}
	}

	/**
	 * 複製筆記
	 */
	async duplicateNote(noteId: NoteId, authorId: string): Promise<Note> {
		try {
			const originalNote = await this.getNote(noteId);
			if (!originalNote) {
				throw new Error('原筆記不存在');
			}

			return await this.createNote(authorId, {
				title: `${originalNote.title} (副本)`,
				content: originalNote.content,
				categoryId: originalNote.categoryId,
				tags: originalNote.tags.map(tag => tag.name),
				priority: originalNote.priority,
				metadata: {
					...originalNote.metadata,
					originalNoteId: noteId,
					duplicatedAt: new Date().toISOString()
				}
			});
		} catch (error) {
			console.error('Failed to duplicate note:', error);
			throw new Error('複製筆記失敗');
		}
	}

	/**
	 * 生成筆記摘要
	 */
	private generateExcerpt(content: string, maxLength: number = 200): string {
		// 移除 Markdown 語法
		const plainText = content
			.replace(/#{1,6}\s+/g, '') // 標題
			.replace(/\*\*(.*?)\*\*/g, '$1') // 粗體
			.replace(/\*(.*?)\*/g, '$1') // 斜體
			.replace(/`(.*?)`/g, '$1') // 行內代碼
			.replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // 鏈接
			.replace(/!\[([^\]]*)\]\([^)]+\)/g, '$1') // 圖片
			.replace(/\n+/g, ' ') // 換行
			.trim();

		return plainText.length > maxLength ? plainText.substring(0, maxLength) + '...' : plainText;
	}

	/**
	 * 生成筆記 ID
	 */
	private generateNoteId(): string {
		return `note_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
	}

	/**
	 * 生成標籤 ID
	 */
	private generateTagId(): string {
		return `tag_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
	}
}

// 導出單例實例
export const noteService = new NoteService();
