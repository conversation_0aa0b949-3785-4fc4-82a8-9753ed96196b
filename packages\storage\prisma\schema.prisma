// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../src/prisma/generated"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// 用戶表
model User {
  id          String   @id @default(cuid())
  username    String   @unique
  email       String   @unique
  displayName String?
  role        String   @default("user") // admin, moderator, user, guest
  status      String   @default("active") // active, inactive, suspended
  metadata    String?  // JSON string
  lastLoginAt DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 關聯
  notes         Note[]
  workspaces    WorkspaceMember[]
  ownedWorkspaces Workspace[]

  @@map("users")
}

// 工作空間表
model Workspace {
  id          String   @id @default(cuid())
  name        String
  description String?
  isPublic    Boolean  @default(false)
  settings    String?  // JSON string
  statistics  String?  // JSON string
  ownerId     String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 關聯
  owner   User              @relation(fields: [ownerId], references: [id], onDelete: Cascade)
  members WorkspaceMember[]
  notes   Note[]

  @@map("workspaces")
}

// 工作空間成員表
model WorkspaceMember {
  id          String   @id @default(cuid())
  workspaceId String
  userId      String
  role        String   // owner, admin, editor, viewer
  permissions String?  // JSON string
  joinedAt    DateTime @default(now())
  invitedBy   String?

  // 關聯
  workspace Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([workspaceId, userId])
  @@map("workspace_members")
}

// 筆記表
model Note {
  id          String   @id @default(cuid())
  title       String
  content     String   @default("")
  category    String?
  tags        String?  // JSON string for tags array
  status      String   @default("draft") // draft, published, archived
  priority    String   @default("medium") // low, medium, high, urgent
  version     String   @default("1.0.0")
  filePath    String?
  checksum    String?
  metadata    String?  // JSON string
  authorId    String
  workspaceId String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 關聯
  author    User       @relation(fields: [authorId], references: [id], onDelete: Cascade)
  workspace Workspace? @relation(fields: [workspaceId], references: [id], onDelete: SetNull)

  // 依賴關係
  dependencies Dependency[] @relation("SourceNote")
  dependents   Dependency[] @relation("TargetNote")

  // 版本歷史
  versions NoteVersion[]

  @@map("notes")
}

// 筆記版本表
model NoteVersion {
  id        String   @id @default(cuid())
  noteId    String
  version   String
  title     String
  content   String
  metadata  String?  // JSON string
  createdAt DateTime @default(now())

  // 關聯
  note Note @relation(fields: [noteId], references: [id], onDelete: Cascade)

  @@unique([noteId, version])
  @@map("note_versions")
}

// 依賴關係表
model Dependency {
  id           String   @id @default(cuid())
  sourceNoteId String
  targetNoteId String
  type         String   // reference, include, extends, implements, uses, depends_on
  strength     String   @default("medium") // weak, medium, strong, critical
  description  String?
  metadata     String?  // JSON string
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // 關聯
  sourceNote Note @relation("SourceNote", fields: [sourceNoteId], references: [id], onDelete: Cascade)
  targetNote Note @relation("TargetNote", fields: [targetNoteId], references: [id], onDelete: Cascade)

  @@unique([sourceNoteId, targetNoteId, type])
  @@map("dependencies")
}

// 標籤表（可選，如果需要標籤的額外信息）
model Tag {
  id          String   @id @default(cuid())
  name        String   @unique
  color       String?
  description String?
  category    String?
  usage_count Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("tags")
}

// 分類表
model Category {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  color       String?
  icon        String?
  parentId    String?
  order       Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 自關聯（樹狀結構）
  parent   Category?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children Category[] @relation("CategoryHierarchy")

  @@map("categories")
}

// 搜索索引表（用於全文搜索）
model SearchIndex {
  id       String @id @default(cuid())
  noteId   String @unique
  title    String
  content  String
  tags     String // 標籤的文本表示，用於搜索
  category String?

  @@map("search_index")
}

// 用戶偏好設置表
model UserPreference {
  id     String @id @default(cuid())
  userId String @unique
  theme  String @default("auto") // light, dark, auto
  language String @default("zh-TW")
  timezone String @default("Asia/Taipei")
  editorSettings String?        // JSON string
  notificationSettings String?  // JSON string
  privacySettings String?       // JSON string
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("user_preferences")
}

// 用戶統計表
model UserStatistics {
  id                    String   @id @default(cuid())
  userId                String   @unique
  totalNotes            Int      @default(0)
  publishedNotes        Int      @default(0)
  draftNotes            Int      @default(0)
  archivedNotes         Int      @default(0)
  totalWords            Int      @default(0)
  averageWordsPerNote   Float    @default(0)
  notesCreatedThisWeek  Int      @default(0)
  notesCreatedThisMonth Int      @default(0)
  longestStreak         Int      @default(0)
  currentStreak         Int      @default(0)
  lastActiveDate        DateTime @default(now())
  favoriteCategories    String?  // JSON string for favorite categories
  mostUsedTags          String?  // JSON string for most used tags
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  @@map("user_statistics")
}

// 會話表
model UserSession {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  device    String?
  ipAddress String?
  userAgent String?
  expiresAt DateTime
  createdAt DateTime @default(now())

  @@map("user_sessions")
}
