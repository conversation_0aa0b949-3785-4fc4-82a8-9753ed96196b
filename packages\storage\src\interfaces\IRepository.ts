/**
 * 基礎存儲庫接口
 * 定義所有存儲庫的通用操作
 */
export interface IRepository<TEntity, TId> {
  /**
   * 根據 ID 查找實體
   */
  findById(id: TId): Promise<TEntity | null>;

  /**
   * 查找所有實體
   */
  findAll(): Promise<TEntity[]>;

  /**
   * 保存實體（創建或更新）
   */
  save(entity: TEntity): Promise<TEntity>;

  /**
   * 根據 ID 刪除實體
   */
  deleteById(id: TId): Promise<void>;

  /**
   * 檢查實體是否存在
   */
  exists(id: TId): Promise<boolean>;

  /**
   * 計算實體總數
   */
  count(): Promise<number>;
}

/**
 * 分頁查詢參數
 */
export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

/**
 * 分頁查詢結果
 */
export interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

/**
 * 查詢條件接口
 */
export interface QueryOptions {
  where?: Record<string, unknown>;
  include?: Record<string, boolean>;
  orderBy?: Record<string, "asc" | "desc">;
  skip?: number;
  take?: number;
}

/**
 * 支援分頁的存儲庫接口
 */
export interface IPaginatedRepository<TEntity, TId>
  extends IRepository<TEntity, TId> {
  /**
   * 分頁查詢
   */
  findPaginated(params: PaginationParams): Promise<PaginatedResult<TEntity>>;

  /**
   * 根據條件分頁查詢
   */
  findByConditionPaginated(
    condition: Record<string, unknown>,
    params: PaginationParams,
  ): Promise<PaginatedResult<TEntity>>;
}

/**
 * 支援搜索的存儲庫接口
 */
export interface ISearchableRepository<TEntity, TId>
  extends IPaginatedRepository<TEntity, TId> {
  /**
   * 全文搜索
   */
  search(
    query: string,
    params?: PaginationParams,
  ): Promise<PaginatedResult<TEntity>>;

  /**
   * 根據條件搜索
   */
  searchByCondition(
    query: string,
    condition: Record<string, unknown>,
    params?: PaginationParams,
  ): Promise<PaginatedResult<TEntity>>;
}

/**
 * 工作單元接口
 * 用於管理事務和批量操作
 */
export interface IUnitOfWork {
  /**
   * 開始事務
   */
  begin(): Promise<void>;

  /**
   * 提交事務
   */
  commit(): Promise<void>;

  /**
   * 回滾事務
   */
  rollback(): Promise<void>;

  /**
   * 在事務中執行操作
   */
  transaction<T>(operation: () => Promise<T>): Promise<T>;

  /**
   * 保存所有變更
   */
  saveChanges(): Promise<void>;
}

/**
 * 數據庫連接接口
 */
export interface IDatabaseConnection {
  /**
   * 連接到數據庫
   */
  connect(): Promise<void>;

  /**
   * 斷開數據庫連接
   */
  disconnect(): Promise<void>;

  /**
   * 檢查連接狀態
   */
  isConnected(): boolean;

  /**
   * 執行原始 SQL 查詢
   */
  executeRaw(sql: string, params?: unknown[]): Promise<unknown>;

  /**
   * 執行原始 SQL 查詢並返回結果
   */
  queryRaw<T = unknown>(sql: string, params?: unknown[]): Promise<T[]>;
}
