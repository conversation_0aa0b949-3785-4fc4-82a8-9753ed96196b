<script lang="ts">
	import { createEventDispatcher, onMount } from 'svelte';
	import { Search, X, Clock, TrendingUp } from 'lucide-svelte';
	import { Button } from '$components/ui';
	import { searchStore } from '$stores/search';
	import { clickOutside } from '$lib/actions/clickOutside';

	export let placeholder = '搜索筆記...';
	export let value = '';
	export let showSuggestions = true;
	export let showRecentSearches = true;
	export let autofocus = false;

	const dispatch = createEventDispatcher<{
		search: { query: string };
		clear: void;
		select: { query: string };
	}>();

	let inputElement: HTMLInputElement;
	let showDropdown = false;
	let highlightedIndex = -1;

	$: suggestions = $searchStore.suggestions;
	$: recentSearches = $searchStore.recentSearches;
	$: popularTags = $searchStore.popularTags;

	// 組合建議列表
	$: combinedSuggestions = [
		...suggestions.map(s => ({ type: 'suggestion', value: s, label: s })),
		...(showRecentSearches && value.length === 0
			? recentSearches.map(s => ({ type: 'recent', value: s, label: s }))
			: []),
		...(value.length === 0
			? popularTags
					.slice(0, 5)
					.map(t => ({ type: 'tag', value: t.name, label: `${t.name} (${t.count})` }))
			: [])
	];

	onMount(() => {
		if (autofocus && inputElement) {
			inputElement.focus();
		}
	});

	function handleInput(event: Event) {
		const target = event.target as HTMLInputElement;
		value = target.value;
		highlightedIndex = -1;

		if (value.length >= 2) {
			searchStore.getSuggestions(value);
		}

		showDropdown = true;
	}

	function handleKeydown(event: KeyboardEvent) {
		if (!showDropdown || combinedSuggestions.length === 0) {
			if (event.key === 'Enter') {
				handleSearch();
			}
			return;
		}

		switch (event.key) {
			case 'ArrowDown':
				event.preventDefault();
				highlightedIndex = Math.min(highlightedIndex + 1, combinedSuggestions.length - 1);
				break;
			case 'ArrowUp':
				event.preventDefault();
				highlightedIndex = Math.max(highlightedIndex - 1, -1);
				break;
			case 'Enter':
				event.preventDefault();
				if (highlightedIndex >= 0) {
					selectSuggestion(combinedSuggestions[highlightedIndex]);
				} else {
					handleSearch();
				}
				break;
			case 'Escape':
				showDropdown = false;
				highlightedIndex = -1;
				inputElement.blur();
				break;
		}
	}

	function handleSearch() {
		if (value.trim()) {
			dispatch('search', { query: value.trim() });
			showDropdown = false;
		}
	}

	function handleClear() {
		value = '';
		showDropdown = false;
		highlightedIndex = -1;
		dispatch('clear');
		inputElement.focus();
	}

	function selectSuggestion(suggestion: { type: string; value: string; label: string }) {
		value = suggestion.value;
		showDropdown = false;
		highlightedIndex = -1;
		dispatch('select', { query: suggestion.value });
		dispatch('search', { query: suggestion.value });
	}

	function handleFocus() {
		showDropdown = true;
	}

	function handleClickOutside() {
		showDropdown = false;
		highlightedIndex = -1;
	}

	function getIconForType(type: string) {
		switch (type) {
			case 'recent':
				return Clock;
			case 'tag':
				return TrendingUp;
			default:
				return Search;
		}
	}

	function getTypeLabel(type: string) {
		switch (type) {
			case 'recent':
				return '最近搜索';
			case 'tag':
				return '熱門標籤';
			case 'suggestion':
				return '建議';
			default:
				return '';
		}
	}
</script>

<div class="relative w-full" use:clickOutside on:clickOutside={handleClickOutside}>
	<!-- 搜索輸入框 -->
	<div class="relative">
		<Search
			class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"
		/>
		<input
			bind:this={inputElement}
			bind:value
			type="text"
			{placeholder}
			class="w-full pl-10 pr-10 py-2 border border-input rounded-lg focus:outline-none focus:ring-2 focus:ring-ring bg-background"
			on:input={handleInput}
			on:keydown={handleKeydown}
			on:focus={handleFocus}
		/>
		{#if value}
			<button
				type="button"
				class="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
				on:click={handleClear}
			>
				<X class="h-4 w-4" />
			</button>
		{/if}
	</div>

	<!-- 建議下拉框 -->
	{#if showDropdown && combinedSuggestions.length > 0}
		<div
			class="absolute top-full left-0 right-0 mt-1 bg-popover border border-border rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto"
		>
			{#each combinedSuggestions as suggestion, index}
				{@const Icon = getIconForType(suggestion.type)}
				<button
					type="button"
					class="w-full px-3 py-2 text-left hover:bg-accent hover:text-accent-foreground flex items-center gap-2 {highlightedIndex ===
					index
						? 'bg-accent text-accent-foreground'
						: ''}"
					on:click={() => selectSuggestion(suggestion)}
				>
					<Icon class="h-4 w-4 text-muted-foreground" />
					<div class="flex-1 min-w-0">
						<div class="text-sm">{suggestion.label}</div>
						{#if suggestion.type !== 'suggestion'}
							<div class="text-xs text-muted-foreground">{getTypeLabel(suggestion.type)}</div>
						{/if}
					</div>
				</button>
			{/each}

			<!-- 清除最近搜索 -->
			{#if recentSearches.length > 0 && value.length === 0}
				<div class="border-t border-border">
					<button
						type="button"
						class="w-full px-3 py-2 text-left text-sm text-muted-foreground hover:bg-accent hover:text-accent-foreground"
						on:click={() => searchStore.clearRecentSearches()}
					>
						清除搜索歷史
					</button>
				</div>
			{/if}
		</div>
	{/if}
</div>

<style>
	/* 確保下拉框在其他元素之上 */
	:global(.search-dropdown) {
		z-index: 1000;
	}
</style>
