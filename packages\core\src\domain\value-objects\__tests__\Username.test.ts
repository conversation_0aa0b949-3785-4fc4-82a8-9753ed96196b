import { describe, it, expect } from "vitest";
import { Username } from "../Username.js";
import { BusinessRuleViolationError } from "../../../shared/Entity.js";

describe("Username Value Object", () => {
  describe("Valid Username Creation", () => {
    it("should create username with valid format", () => {
      const username = new Username("testuser");
      expect(username.value).toBe("testuser");
    });

    it("should trim whitespace", () => {
      const username = new Username("  testuser  ");
      expect(username.value).toBe("testuser");
    });

    it("should handle valid usernames with numbers and special chars", () => {
      const validUsernames = [
        "user123",
        "test_user",
        "user-name",
        "user_123",
        "test-123",
        "username_with_underscores",
      ];

      validUsernames.forEach((usernameStr) => {
        expect(() => new Username(usernameStr)).not.toThrow();
      });
    });
  });

  describe("Invalid Username Validation", () => {
    it("should reject empty username", () => {
      expect(() => new Username("")).toThrow(BusinessRuleViolationError);
      expect(() => new Username("   ")).toThrow(BusinessRuleViolationError);
    });

    it("should reject too short username", () => {
      expect(() => new Username("ab")).toThrow(BusinessRuleViolationError);
    });

    it("should reject too long username", () => {
      const longUsername = "a".repeat(31);
      expect(() => new Username(longUsername)).toThrow(
        BusinessRuleViolationError,
      );
    });

    it("should reject username starting with number", () => {
      expect(() => new Username("123user")).toThrow(BusinessRuleViolationError);
    });

    it("should reject username starting with special characters", () => {
      expect(() => new Username("_user")).toThrow(BusinessRuleViolationError);
      expect(() => new Username("-user")).toThrow(BusinessRuleViolationError);
    });

    it("should reject username ending with special characters", () => {
      expect(() => new Username("user_")).toThrow(BusinessRuleViolationError);
      expect(() => new Username("user-")).toThrow(BusinessRuleViolationError);
    });

    it("should reject username with consecutive special characters", () => {
      expect(() => new Username("user__name")).toThrow(
        BusinessRuleViolationError,
      );
      expect(() => new Username("user--name")).toThrow(
        BusinessRuleViolationError,
      );
      expect(() => new Username("user_-name")).toThrow(
        BusinessRuleViolationError,
      );
    });

    it("should reject username with invalid characters", () => {
      expect(() => new Username("user@name")).toThrow(
        BusinessRuleViolationError,
      );
      expect(() => new Username("user.name")).toThrow(
        BusinessRuleViolationError,
      );
      expect(() => new Username("user name")).toThrow(
        BusinessRuleViolationError,
      );
      expect(() => new Username("user#name")).toThrow(
        BusinessRuleViolationError,
      );
    });

    it("should reject reserved usernames", () => {
      const reservedUsernames = [
        "admin",
        "administrator",
        "root",
        "system",
        "support",
        "api",
        "null",
        "undefined",
      ];

      reservedUsernames.forEach((username) => {
        expect(() => new Username(username)).toThrow(
          BusinessRuleViolationError,
        );
        expect(() => new Username(username.toUpperCase())).toThrow(
          BusinessRuleViolationError,
        );
      });
    });

    it("should reject inappropriate usernames", () => {
      const inappropriateUsernames = [
        "fuckuser",
        "shitname",
        "killme",
        "hateyou",
      ];

      inappropriateUsernames.forEach((username) => {
        expect(() => new Username(username)).toThrow(
          BusinessRuleViolationError,
        );
      });
    });
  });

  describe("Username Properties", () => {
    it("should detect numbers in username", () => {
      const username1 = new Username("user123");
      const username2 = new Username("testuser");

      expect(username1.hasNumbers()).toBe(true);
      expect(username2.hasNumbers()).toBe(false);
    });

    it("should detect special characters in username", () => {
      const username1 = new Username("user_name");
      const username2 = new Username("username");

      expect(username1.hasSpecialCharacters()).toBe(true);
      expect(username2.hasSpecialCharacters()).toBe(false);
    });

    it("should detect alpha-only usernames", () => {
      const username1 = new Username("username");
      const username2 = new Username("user123");

      expect(username1.isAlphaOnly()).toBe(true);
      expect(username2.isAlphaOnly()).toBe(false);
    });

    it("should detect alphanumeric usernames", () => {
      const username1 = new Username("user123");
      const username2 = new Username("user_123");

      expect(username1.isAlphanumeric()).toBe(true);
      expect(username2.isAlphanumeric()).toBe(false);
    });

    it("should return correct length", () => {
      const username = new Username("testuser");
      expect(username.length).toBe(8);
    });
  });

  describe("Username Formatting", () => {
    it("should convert to display format", () => {
      const username = new Username("testuser");
      expect(username.toDisplayFormat()).toBe("Testuser");
    });

    it("should convert to lowercase", () => {
      const username = new Username("TestUser");
      expect(username.toLowerCase()).toBe("testuser");
    });

    it("should convert to uppercase", () => {
      const username = new Username("testuser");
      expect(username.toUpperCase()).toBe("TESTUSER");
    });
  });

  describe("Username Variants Generation", () => {
    it("should generate valid variants", () => {
      const username = new Username("testuser");
      const variants = username.generateVariants();

      expect(variants.length).toBeGreaterThan(0);
      expect(variants.length).toBeLessThanOrEqual(10);

      // Check that all variants are valid
      variants.forEach((variant) => {
        expect(() => new Username(variant)).not.toThrow();
      });
    });

    it("should include numbered variants", () => {
      const username = new Username("testuser");
      const variants = username.generateVariants();

      expect(variants).toContain("testuser1");
      expect(variants).toContain("testuser2");
    });

    it("should include underscore variants", () => {
      const username = new Username("testuser");
      const variants = username.generateVariants();

      // 檢查是否包含下劃線變體（可能在前10個中）
      const hasUnderscoreVariants = variants.some((v) => v.includes("_"));
      expect(hasUnderscoreVariants).toBe(true);
    });
  });

  describe("Value Object Behavior", () => {
    it("should be equal to another username with same value (case insensitive)", () => {
      const username1 = new Username("testuser");
      const username2 = new Username("TestUser");
      expect(username1.equals(username2)).toBe(true);
    });

    it("should not be equal to different username", () => {
      const username1 = new Username("testuser1");
      const username2 = new Username("testuser2");
      expect(username1.equals(username2)).toBe(false);
    });

    it("should not be equal to non-username object", () => {
      const username = new Username("testuser");
      expect(username.equals({} as any)).toBe(false);
    });

    it("should have consistent hash code (case insensitive)", () => {
      const username1 = new Username("testuser");
      const username2 = new Username("TestUser");
      expect(username1.hashCode()).toBe(username2.hashCode());
    });

    it("should convert to string correctly", () => {
      const username = new Username("testuser");
      expect(username.toString()).toBe("testuser");
    });
  });

  describe("Serialization", () => {
    it("should serialize to plain object", () => {
      const username = new Username("test_user123");
      const plainObject = username.toPlainObject();

      expect(plainObject).toEqual({
        value: "test_user123",
        length: 12,
        hasNumbers: true,
        hasSpecialCharacters: true,
        isAlphaOnly: false,
        isAlphanumeric: false,
      });
    });

    it("should deserialize from plain object", () => {
      const plainObject = {
        value: "testuser",
        length: 8,
        hasNumbers: false,
        hasSpecialCharacters: false,
        isAlphaOnly: true,
        isAlphanumeric: true,
      };

      const username = Username.fromPlainObject(plainObject);
      expect(username.value).toBe("testuser");
    });

    it("should throw error for invalid plain object", () => {
      const invalidObject = { value: 123 };
      expect(() => Username.fromPlainObject(invalidObject)).toThrow(
        BusinessRuleViolationError,
      );
    });
  });

  describe("Static Factory Methods", () => {
    it("should create username using create method", () => {
      const username = Username.create("testuser");
      expect(username.value).toBe("testuser");
    });

    it("should create username using fromString method", () => {
      const username = Username.fromString("testuser");
      expect(username.value).toBe("testuser");
    });
  });
});
