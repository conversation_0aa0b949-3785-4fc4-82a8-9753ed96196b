import { writable } from 'svelte/store';

export interface Category {
	name: string;
	count: number;
	color?: string;
}

export interface CategoryState {
	categories: Category[];
	loading: boolean;
	error: string | null;
}

const initialState: CategoryState = {
	categories: [],
	loading: false,
	error: null
};

function createCategoryStore() {
	const { subscribe, set, update } = writable<CategoryState>(initialState);

	return {
		subscribe,

		// Load categories
		async loadCategories() {
			update(state => ({ ...state, loading: true, error: null }));

			try {
				// TODO: Implement actual API call
				// const categories = await categoryService.getAllCategories();
				const categories: Category[] = []; // Placeholder

				update(state => ({
					...state,
					categories,
					loading: false
				}));
			} catch (error) {
				update(state => ({
					...state,
					loading: false,
					error: error instanceof Error ? error.message : 'Failed to load categories'
				}));
			}
		},

		// Add category
		addCategory(category: Category) {
			update(state => ({
				...state,
				categories: [...state.categories, category]
			}));
		},

		// Update category
		updateCategory(name: string, updates: Partial<Category>) {
			update(state => ({
				...state,
				categories: state.categories.map(cat => (cat.name === name ? { ...cat, ...updates } : cat))
			}));
		},

		// Remove category
		removeCategory(name: string) {
			update(state => ({
				...state,
				categories: state.categories.filter(cat => cat.name !== name)
			}));
		},

		// Reset store
		reset() {
			set(initialState);
		}
	};
}

export const categoryStore = createCategoryStore();
