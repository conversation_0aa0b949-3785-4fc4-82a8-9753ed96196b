import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
	CallToolRequestSchema,
	ListResourcesRequestSchema,
	ListToolsRequestSchema,
	ReadResourceRequestSchema
} from '@modelcontextprotocol/sdk/types.js';
import { MCPToolRouter } from './tools.js';
import { MCPResourceHandler } from './resources.js';
import { MCP_SERVER_CONFIG, NOTE_TOOLS, DEPENDENCY_TOOLS } from './types.js';

/**
 * Life Note MCP Server
 *
 * 這個 MCP 服務器為 AI 助手提供了與 Life Note 應用交互的能力，
 * 包括筆記管理和依賴關係分析功能。
 */
export class LifeNoteMCPServer {
	private server: Server;
	private isRunning = false;

	constructor() {
		this.server = new Server(
			{
				name: MCP_SERVER_CONFIG.name,
				version: MCP_SERVER_CONFIG.version
			},
			{
				capabilities: {
					tools: {},
					resources: {}
				}
			}
		);

		this.setupHandlers();
	}

	/**
	 * 設置 MCP 服務器的處理器
	 */
	private setupHandlers(): void {
		// 列出可用工具
		this.server.setRequestHandler(ListToolsRequestSchema, async () => {
			return {
				tools: [...NOTE_TOOLS, ...DEPENDENCY_TOOLS]
			};
		});

		// 執行工具
		this.server.setRequestHandler(CallToolRequestSchema, async request => {
			const { name, arguments: args } = request.params;

			try {
				const result = await MCPToolRouter.executeTool(name, args);

				if (result.isError) {
					throw new Error(result.content[0]?.text || 'Tool execution failed');
				}

				return {
					content: result.content
				};
			} catch (error) {
				const errorMessage = error instanceof Error ? error.message : 'Unknown error';
				return {
					content: [
						{
							type: 'text' as const,
							text: JSON.stringify(
								{
									error: errorMessage,
									tool: name,
									timestamp: new Date().toISOString()
								},
								null,
								2
							)
						}
					],
					isError: true
				};
			}
		});

		// 列出可用資源
		this.server.setRequestHandler(ListResourcesRequestSchema, async () => {
			return {
				resources: MCPResourceHandler.getAvailableResources()
			};
		});

		// 讀取資源
		this.server.setRequestHandler(ReadResourceRequestSchema, async request => {
			const { uri } = request.params;

			try {
				const result = await MCPResourceHandler.handleResource(uri);

				if (result.isError) {
					throw new Error(result.error || 'Resource access failed');
				}

				return {
					contents: result.contents
				};
			} catch (error) {
				const errorMessage = error instanceof Error ? error.message : 'Unknown error';
				return {
					contents: [
						{
							uri,
							mimeType: 'application/json',
							text: JSON.stringify(
								{
									error: errorMessage,
									uri,
									timestamp: new Date().toISOString()
								},
								null,
								2
							)
						}
					]
				};
			}
		});

		// 錯誤處理
		this.server.onerror = error => {
			console.error('[MCP Server Error]', error);
		};

		// 服務器關閉處理
		process.on('SIGINT', async () => {
			await this.stop();
			process.exit(0);
		});

		process.on('SIGTERM', async () => {
			await this.stop();
			process.exit(0);
		});
	}

	/**
	 * 啟動 MCP 服務器
	 */
	async start(): Promise<void> {
		if (this.isRunning) {
			console.warn('[MCP Server] Server is already running');
			return;
		}

		try {
			const transport = new StdioServerTransport();
			await this.server.connect(transport);

			this.isRunning = true;
			console.log(`[MCP Server] ${MCP_SERVER_CONFIG.name} v${MCP_SERVER_CONFIG.version} started`);
			console.log(`[MCP Server] Available tools: ${NOTE_TOOLS.length + DEPENDENCY_TOOLS.length}`);
			console.log(
				`[MCP Server] Available resources: ${MCPResourceHandler.getAvailableResources().length}`
			);
		} catch (error) {
			console.error('[MCP Server] Failed to start:', error);
			throw error;
		}
	}

	/**
	 * 停止 MCP 服務器
	 */
	async stop(): Promise<void> {
		if (!this.isRunning) {
			return;
		}

		try {
			await this.server.close();
			this.isRunning = false;
			console.log('[MCP Server] Server stopped');
		} catch (error) {
			console.error('[MCP Server] Error stopping server:', error);
			throw error;
		}
	}

	/**
	 * 檢查服務器是否正在運行
	 */
	isServerRunning(): boolean {
		return this.isRunning;
	}

	/**
	 * 獲取服務器配置信息
	 */
	getServerInfo() {
		return {
			name: MCP_SERVER_CONFIG.name,
			version: MCP_SERVER_CONFIG.version,
			description: MCP_SERVER_CONFIG.description,
			isRunning: this.isRunning,
			tools: {
				note: NOTE_TOOLS.length,
				dependency: DEPENDENCY_TOOLS.length,
				total: NOTE_TOOLS.length + DEPENDENCY_TOOLS.length
			},
			resources: MCPResourceHandler.getAvailableResources().length
		};
	}

	/**
	 * 獲取工具使用統計
	 */
	getToolStats() {
		return {
			availableTools: MCPToolRouter.getAvailableTools(),
			toolHelp: MCPToolRouter.getAvailableTools().reduce(
				(acc, toolName) => {
					acc[toolName] = MCPToolRouter.getToolHelp(toolName);
					return acc;
				},
				{} as Record<string, string | null>
			)
		};
	}
}

// 創建並導出服務器實例
export const mcpServer = new LifeNoteMCPServer();

// 如果直接運行此文件，啟動服務器
if (import.meta.url === `file://${process.argv[1]}`) {
	mcpServer.start().catch(error => {
		console.error('Failed to start MCP server:', error);
		process.exit(1);
	});
}

// 導出便利函數
export async function startMCPServer(): Promise<void> {
	await mcpServer.start();
}

export async function stopMCPServer(): Promise<void> {
	await mcpServer.stop();
}

export function getMCPServerInfo() {
	return mcpServer.getServerInfo();
}

export function getMCPToolStats() {
	return mcpServer.getToolStats();
}

export function isMCPServerRunning(): boolean {
	return mcpServer.isServerRunning();
}

/**
 * 健康檢查函數
 */
export async function healthCheck(): Promise<{
	status: 'healthy' | 'unhealthy';
	timestamp: string;
	server: any;
	errors?: string[];
}> {
	const timestamp = new Date().toISOString();
	const errors: string[] = [];

	try {
		const serverInfo = getMCPServerInfo();

		// 檢查服務器狀態
		if (!serverInfo.isRunning) {
			errors.push('MCP server is not running');
		}

		// 檢查工具可用性
		const toolStats = getMCPToolStats();
		if (toolStats.availableTools.length === 0) {
			errors.push('No tools available');
		}

		return {
			status: errors.length === 0 ? 'healthy' : 'unhealthy',
			timestamp,
			server: serverInfo,
			...(errors.length > 0 && { errors })
		};
	} catch (error) {
		errors.push(`Health check failed: ${error}`);
		return {
			status: 'unhealthy',
			timestamp,
			server: null,
			errors
		};
	}
}
