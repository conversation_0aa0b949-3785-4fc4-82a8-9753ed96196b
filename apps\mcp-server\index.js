#!/usr/bin/env node

/**
 * Life Note MCP Server
 *
 * 這是一個獨立的 MCP (Model Context Protocol) 服務器，
 * 為 AI 助手提供與 Life Note 應用交互的能力。
 *
 * 使用方法：
 * node index.js
 *
 * 或者作為 MCP 服務器：
 * npx @life-note/mcp-server
 */

import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import {
  CallToolRequestSchema,
  ListResourcesRequestSchema,
  ListToolsRequestSchema,
  ReadResourceRequestSchema,
} from "@modelcontextprotocol/sdk/types.js";

// 模擬的筆記數據存儲（在實際應用中，這會連接到真實的數據庫）
let notesDatabase = new Map();
let nextNoteId = 1;

// 初始化一些示例數據
function initializeSampleData() {
  const sampleNotes = [
    {
      id: "1",
      title: "歡迎使用 Life Note",
      content: "這是您的第一個筆記。Life Note 是一個功能強大的筆記管理應用。",
      tags: ["歡迎", "開始"],
      status: "published",
      priority: "medium",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: "2",
      title: "MCP 服務器功能",
      content:
        "這個 MCP 服務器提供了完整的筆記管理功能，包括創建、讀取、更新和刪除筆記。",
      tags: ["MCP", "功能", "技術"],
      status: "published",
      priority: "high",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
  ];

  sampleNotes.forEach((note) => {
    notesDatabase.set(note.id, note);
  });
  nextNoteId = 3;
}

// 工具定義
const TOOLS = [
  {
    name: "create_note",
    description: "Create a new note with title, content, tags, and metadata",
    inputSchema: {
      type: "object",
      properties: {
        title: {
          type: "string",
          description: "The title of the note",
        },
        content: {
          type: "string",
          description: "The content of the note in Markdown format",
          default: "",
        },
        tags: {
          type: "array",
          items: { type: "string" },
          description: "Array of tags for the note",
          default: [],
        },
        priority: {
          type: "string",
          enum: ["low", "medium", "high", "urgent"],
          description: "Priority level of the note",
          default: "medium",
        },
        status: {
          type: "string",
          enum: ["draft", "published", "archived"],
          description: "Status of the note",
          default: "draft",
        },
      },
      required: ["title"],
    },
  },
  {
    name: "get_note",
    description: "Get a note by ID",
    inputSchema: {
      type: "object",
      properties: {
        id: {
          type: "string",
          description: "The ID of the note to retrieve",
        },
      },
      required: ["id"],
    },
  },
  {
    name: "update_note",
    description: "Update an existing note by ID",
    inputSchema: {
      type: "object",
      properties: {
        id: {
          type: "string",
          description: "The ID of the note to update",
        },
        title: {
          type: "string",
          description: "New title for the note",
        },
        content: {
          type: "string",
          description: "New content for the note",
        },
        tags: {
          type: "array",
          items: { type: "string" },
          description: "New tags for the note",
        },
        priority: {
          type: "string",
          enum: ["low", "medium", "high", "urgent"],
          description: "New priority level",
        },
        status: {
          type: "string",
          enum: ["draft", "published", "archived"],
          description: "New status",
        },
      },
      required: ["id"],
    },
  },
  {
    name: "delete_note",
    description: "Delete a note by ID",
    inputSchema: {
      type: "object",
      properties: {
        id: {
          type: "string",
          description: "The ID of the note to delete",
        },
      },
      required: ["id"],
    },
  },
  {
    name: "search_notes",
    description: "Search notes with various filters",
    inputSchema: {
      type: "object",
      properties: {
        query: {
          type: "string",
          description: "Search query to match against title and content",
        },
        tags: {
          type: "array",
          items: { type: "string" },
          description: "Filter by tags",
        },
        status: {
          type: "string",
          enum: ["draft", "published", "archived"],
          description: "Filter by status",
        },
        priority: {
          type: "string",
          enum: ["low", "medium", "high", "urgent"],
          description: "Filter by priority",
        },
        limit: {
          type: "number",
          description: "Maximum number of results to return",
          default: 20,
        },
      },
    },
  },
  {
    name: "list_all_notes",
    description: "List all notes with optional pagination",
    inputSchema: {
      type: "object",
      properties: {
        limit: {
          type: "number",
          description: "Maximum number of notes to return",
          default: 50,
        },
        offset: {
          type: "number",
          description: "Number of notes to skip",
          default: 0,
        },
      },
    },
  },
];

// 資源定義
const RESOURCES = [
  {
    uri: "notes://all",
    name: "All Notes",
    description: "Complete list of all notes in the system",
    mimeType: "application/json",
  },
  {
    uri: "notes://statistics",
    name: "Notes Statistics",
    description: "Statistical information about notes",
    mimeType: "application/json",
  },
];

// 工具執行函數
async function executeTool(name, args) {
  console.error(
    `[MCP Server] Executing tool: ${name} with args:`,
    JSON.stringify(args, null, 2),
  );

  try {
    switch (name) {
      case "create_note":
        return await createNote(args);
      case "get_note":
        return await getNote(args);
      case "update_note":
        return await updateNote(args);
      case "delete_note":
        return await deleteNote(args);
      case "search_notes":
        return await searchNotes(args);
      case "list_all_notes":
        return await listAllNotes(args);
      default:
        throw new Error(`Unknown tool: ${name}`);
    }
  } catch (error) {
    console.error(`[MCP Server] Tool execution error:`, error);
    throw error;
  }
}

// 筆記操作函數
async function createNote(args) {
  const {
    title,
    content = "",
    tags = [],
    priority = "medium",
    status = "draft",
  } = args;

  if (!title || title.trim() === "") {
    throw new Error("Title is required");
  }

  const note = {
    id: String(nextNoteId++),
    title: title.trim(),
    content,
    tags,
    priority,
    status,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  notesDatabase.set(note.id, note);

  return [
    {
      type: "text",
      text: JSON.stringify(
        {
          success: true,
          note,
          message: `Note "${title}" created successfully with ID: ${note.id}`,
        },
        null,
        2,
      ),
    },
  ];
}

async function getNote(args) {
  const { id } = args;

  if (!id) {
    throw new Error("Note ID is required");
  }

  const note = notesDatabase.get(id);
  if (!note) {
    throw new Error(`Note with ID ${id} not found`);
  }

  return [
    {
      type: "text",
      text: JSON.stringify(
        {
          success: true,
          note,
        },
        null,
        2,
      ),
    },
  ];
}

async function updateNote(args) {
  const { id, ...updates } = args;

  if (!id) {
    throw new Error("Note ID is required");
  }

  const note = notesDatabase.get(id);
  if (!note) {
    throw new Error(`Note with ID ${id} not found`);
  }

  // 更新筆記
  const updatedNote = {
    ...note,
    ...updates,
    updatedAt: new Date().toISOString(),
  };

  notesDatabase.set(id, updatedNote);

  return [
    {
      type: "text",
      text: JSON.stringify(
        {
          success: true,
          note: updatedNote,
          message: `Note ${id} updated successfully`,
        },
        null,
        2,
      ),
    },
  ];
}

async function deleteNote(args) {
  const { id } = args;

  if (!id) {
    throw new Error("Note ID is required");
  }

  const note = notesDatabase.get(id);
  if (!note) {
    throw new Error(`Note with ID ${id} not found`);
  }

  notesDatabase.delete(id);

  return [
    {
      type: "text",
      text: JSON.stringify(
        {
          success: true,
          message: `Note "${note.title}" (ID: ${id}) deleted successfully`,
        },
        null,
        2,
      ),
    },
  ];
}

async function searchNotes(args) {
  const { query, tags, status, priority, limit = 20 } = args;

  let results = Array.from(notesDatabase.values());

  // 應用過濾器
  if (query) {
    const searchTerm = query.toLowerCase();
    results = results.filter(
      (note) =>
        note.title.toLowerCase().includes(searchTerm) ||
        note.content.toLowerCase().includes(searchTerm),
    );
  }

  if (tags && tags.length > 0) {
    results = results.filter((note) =>
      tags.some((tag) => note.tags.includes(tag)),
    );
  }

  if (status) {
    results = results.filter((note) => note.status === status);
  }

  if (priority) {
    results = results.filter((note) => note.priority === priority);
  }

  // 限制結果數量
  results = results.slice(0, limit);

  return [
    {
      type: "text",
      text: JSON.stringify(
        {
          success: true,
          results,
          count: results.length,
          query: args,
        },
        null,
        2,
      ),
    },
  ];
}

async function listAllNotes(args) {
  const { limit = 50, offset = 0 } = args;

  const allNotes = Array.from(notesDatabase.values());
  const paginatedNotes = allNotes.slice(offset, offset + limit);

  return [
    {
      type: "text",
      text: JSON.stringify(
        {
          success: true,
          notes: paginatedNotes,
          total: allNotes.length,
          limit,
          offset,
        },
        null,
        2,
      ),
    },
  ];
}

// 資源處理函數
async function handleResource(uri) {
  console.error(`[MCP Server] Handling resource: ${uri}`);

  switch (uri) {
    case "notes://all":
      return {
        uri,
        mimeType: "application/json",
        text: JSON.stringify(
          {
            notes: Array.from(notesDatabase.values()),
            total: notesDatabase.size,
            timestamp: new Date().toISOString(),
          },
          null,
          2,
        ),
      };

    case "notes://statistics":
      const notes = Array.from(notesDatabase.values());
      const stats = {
        total: notes.length,
        byStatus: {},
        byPriority: {},
        totalTags: new Set(notes.flatMap((note) => note.tags)).size,
        timestamp: new Date().toISOString(),
      };

      // 統計狀態分布
      notes.forEach((note) => {
        stats.byStatus[note.status] = (stats.byStatus[note.status] || 0) + 1;
        stats.byPriority[note.priority] =
          (stats.byPriority[note.priority] || 0) + 1;
      });

      return {
        uri,
        mimeType: "application/json",
        text: JSON.stringify(stats, null, 2),
      };

    default:
      throw new Error(`Unknown resource: ${uri}`);
  }
}

// 創建並啟動 MCP 服務器
async function main() {
  console.error("[MCP Server] Starting Life Note MCP Server...");

  // 初始化示例數據
  initializeSampleData();
  console.error(
    `[MCP Server] Initialized with ${notesDatabase.size} sample notes`,
  );

  const server = new Server(
    {
      name: "life-note-mcp-server",
      version: "1.0.0",
    },
    {
      capabilities: {
        tools: {},
        resources: {},
      },
    },
  );

  // 設置錯誤處理
  server.onerror = (error) => {
    console.error("[MCP Server] Server error:", error);
  };

  // 設置工具處理器
  server.setRequestHandler(ListToolsRequestSchema, async () => {
    console.error("[MCP Server] Listing tools");
    return { tools: TOOLS };
  });

  server.setRequestHandler(CallToolRequestSchema, async (request) => {
    const { name, arguments: args } = request.params;
    console.error(`[MCP Server] Tool call: ${name}`);

    try {
      const content = await executeTool(name, args);
      return { content };
    } catch (error) {
      console.error(`[MCP Server] Tool error:`, error);
      return {
        content: [
          {
            type: "text",
            text: JSON.stringify(
              {
                error: error.message,
                tool: name,
                timestamp: new Date().toISOString(),
              },
              null,
              2,
            ),
          },
        ],
        isError: true,
      };
    }
  });

  // 設置資源處理器
  server.setRequestHandler(ListResourcesRequestSchema, async () => {
    console.error("[MCP Server] Listing resources");
    return { resources: RESOURCES };
  });

  server.setRequestHandler(ReadResourceRequestSchema, async (request) => {
    const { uri } = request.params;
    console.error(`[MCP Server] Reading resource: ${uri}`);

    try {
      const content = await handleResource(uri);
      return { contents: [content] };
    } catch (error) {
      console.error(`[MCP Server] Resource error:`, error);
      return {
        contents: [
          {
            uri,
            mimeType: "application/json",
            text: JSON.stringify(
              {
                error: error.message,
                uri,
                timestamp: new Date().toISOString(),
              },
              null,
              2,
            ),
          },
        ],
      };
    }
  });

  // 優雅關閉
  process.on("SIGINT", async () => {
    console.error("[MCP Server] Shutting down...");
    try {
      await server.close();
    } catch (error) {
      console.error("[MCP Server] Error during shutdown:", error);
    }
    process.exit(0);
  });

  process.on("SIGTERM", async () => {
    console.error("[MCP Server] Shutting down...");
    try {
      await server.close();
    } catch (error) {
      console.error("[MCP Server] Error during shutdown:", error);
    }
    process.exit(0);
  });

  // 連接到 stdio 傳輸
  try {
    const transport = new StdioServerTransport();
    await server.connect(transport);

    console.error("[MCP Server] Life Note MCP Server started successfully");
    console.error(`[MCP Server] Available tools: ${TOOLS.length}`);
    console.error(`[MCP Server] Available resources: ${RESOURCES.length}`);
    console.error("[MCP Server] Ready to accept requests...");
  } catch (error) {
    console.error("[MCP Server] Failed to connect transport:", error);
    throw error;
  }
}

// 啟動服務器
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch((error) => {
    console.error("[MCP Server] Failed to start:", error);
    process.exit(1);
  });
}
