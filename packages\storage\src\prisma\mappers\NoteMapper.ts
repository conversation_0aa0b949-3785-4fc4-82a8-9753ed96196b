/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  Note,
  NoteId,
  NoteStatus,
  NotePriority,
  Tag,
  Version,
  UserId,
} from "@life-note/core";

import type { Note as PrismaNote } from "../generated/index.js";

/**
 * 筆記數據映射器
 * 負責在領域對象和數據庫對象之間進行轉換
 */
export class NoteMapper {
  /**
   * 將 Prisma 筆記對象轉換為領域筆記對象
   */
  static toDomain(prismaNote: PrismaNote): Note {
    // 解析標籤 JSON
    let tags: Tag[] = [];
    try {
      const tagsData = JSON.parse(prismaNote.tags);
      tags = Array.isArray(tagsData)
        ? tagsData.map((tagData) => Tag.fromPlainObject(tagData))
        : [];
    } catch (error) {
      tags = [];
    }

    // 解析元數據 JSON
    let metadata: Record<string, unknown> = {};
    try {
      metadata = JSON.parse(prismaNote.metadata);
    } catch (error) {
      metadata = {};
    }

    return new Note(
      NoteId.fromString(prismaNote.id),
      prismaNote.title,
      prismaNote.content,
      {
        category: prismaNote.category || undefined,
        tags,
        status: prismaNote.status as NoteStatus,
        priority: prismaNote.priority as NotePriority,
        version: Version.fromString(prismaNote.version),
        metadata,
        filePath: prismaNote.filePath || undefined,
        checksum: prismaNote.checksum || undefined,
        createdAt: prismaNote.createdAt,
      },
    );
  }

  /**
   * 將領域筆記對象轉換為 Prisma 創建數據
   */
  static toPrismaCreate(
    note: Note,
    authorId: UserId,
  ): Omit<PrismaNote, "createdAt" | "updatedAt"> {
    return {
      id: note.id.value,
      title: note.title,
      content: note.content,
      category: note.category || null,
      tags: JSON.stringify(note.tags.map((tag) => tag.toPlainObject())),
      status: note.status,
      priority: note.priority,
      version: note.version.toString(),
      metadata: JSON.stringify(note.metadata),
      filePath: note.filePath || null,
      checksum: note.checksum || null,
      authorId: authorId.value,
    };
  }

  /**
   * 將領域筆記對象轉換為 Prisma 更新數據
   */
  static toPrismaUpdate(
    note: Note,
  ): Partial<Omit<PrismaNote, "id" | "authorId" | "createdAt" | "updatedAt">> {
    return {
      title: note.title,
      content: note.content,
      category: note.category || null,
      tags: JSON.stringify(note.tags.map((tag) => tag.toPlainObject())),
      status: note.status,
      priority: note.priority,
      version: note.version.toString(),
      metadata: JSON.stringify(note.metadata),
      filePath: note.filePath || null,
      checksum: note.checksum || null,
    };
  }

  /**
   * 批量轉換 Prisma 筆記對象為領域對象
   */
  static toDomainList(prismaNotes: PrismaNote[]): Note[] {
    return prismaNotes.map((prismaNote) => this.toDomain(prismaNote));
  }

  /**
   * 將搜索參數轉換為 Prisma where 條件
   */
  static toWhereClause(params: {
    authorId?: UserId;
    status?: NoteStatus;
    priority?: NotePriority;
    category?: string;
    title?: string;
    content?: string;
    createdAfter?: Date;
    createdBefore?: Date;
    updatedAfter?: Date;
    updatedBefore?: Date;
  }): Record<string, unknown> {
    const where: Record<string, unknown> = {};

    if (params.authorId) {
      where.authorId = params.authorId.value;
    }

    if (params.status) {
      where.status = params.status;
    }

    if (params.priority) {
      where.priority = params.priority;
    }

    if (params.category) {
      where.category = params.category;
    }

    if (params.title) {
      where.title = {
        contains: params.title,
        mode: "insensitive",
      };
    }

    if (params.content) {
      where.content = {
        contains: params.content,
        mode: "insensitive",
      };
    }

    if (params.createdAfter || params.createdBefore) {
      where.createdAt = {};
      if (params.createdAfter) {
        (where.createdAt as Record<string, unknown>).gte = params.createdAfter;
      }
      if (params.createdBefore) {
        (where.createdAt as Record<string, unknown>).lte = params.createdBefore;
      }
    }

    if (params.updatedAfter || params.updatedBefore) {
      where.updatedAt = {};
      if (params.updatedAfter) {
        (where.updatedAt as Record<string, unknown>).gte = params.updatedAfter;
      }
      if (params.updatedBefore) {
        (where.updatedAt as Record<string, unknown>).lte = params.updatedBefore;
      }
    }

    return where;
  }

  /**
   * 將排序參數轉換為 Prisma orderBy 條件
   */
  static toOrderByClause(
    sortBy?: string,
    sortOrder?: "asc" | "desc",
  ): Record<string, string> | undefined {
    if (!sortBy) {
      return { createdAt: "desc" }; // 默認按創建時間降序
    }

    const validSortFields = [
      "title",
      "createdAt",
      "updatedAt",
      "status",
      "priority",
      "category",
    ];
    if (!validSortFields.includes(sortBy)) {
      return { createdAt: "desc" };
    }

    return { [sortBy]: sortOrder || "asc" };
  }

  /**
   * 驗證筆記數據完整性
   */
  static validatePrismaNote(prismaNote: PrismaNote): boolean {
    try {
      // 驗證必需字段
      if (!prismaNote.id || !prismaNote.title || !prismaNote.authorId) {
        return false;
      }

      // 驗證 JSON 字段
      JSON.parse(prismaNote.tags);
      JSON.parse(prismaNote.metadata);

      // 驗證枚舉值
      const validStatuses = Object.values(NoteStatus);
      const validPriorities = Object.values(NotePriority);

      if (!validStatuses.includes(prismaNote.status as NoteStatus)) {
        return false;
      }

      if (!validPriorities.includes(prismaNote.priority as NotePriority)) {
        return false;
      }

      // 驗證版本格式
      Version.fromString(prismaNote.version);

      return true;
    } catch (error) {
      return false;
    }
  }
}
