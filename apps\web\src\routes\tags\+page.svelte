<script>
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { 
    Tag, 
    Search, 
    Plus, 
    Edit3, 
    Trash2, 
    Hash,
    TrendingUp,
    Calendar,
    FileText
  } from 'lucide-svelte';
  
  import Button from '$components/ui/Button.svelte';
  import Card from '$components/ui/Card.svelte';
  import { allNotes } from '$stores/notes';

  // 標籤數據
  let tags = [];
  let searchQuery = '';
  let selectedTag = null;
  let showCreateModal = false;
  let showEditModal = false;
  let newTagName = '';
  let newTagColor = '#3b82f6';
  let editingTag = null;

  // 計算標籤統計
  $: tagStats = tags.map(tag => {
    const relatedNotes = $allNotes ? $allNotes.filter(note => 
      note.tags.some(noteTag => noteTag.name === tag.name)
    ) : [];
    
    return {
      ...tag,
      noteCount: relatedNotes.length,
      recentNotes: relatedNotes.slice(0, 3),
      lastUsed: relatedNotes.length > 0 ? 
        new Date(Math.max(...relatedNotes.map(note => new Date(note.updatedAt)))) : 
        null
    };
  });

  // 過濾標籤
  $: filteredTags = tagStats.filter(tag => 
    tag.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // 排序標籤（按使用頻率）
  $: sortedTags = filteredTags.sort((a, b) => b.noteCount - a.noteCount);

  // 初始化標籤數據
  onMount(() => {
    loadTags();
  });

  const loadTags = () => {
    // 從筆記中提取所有標籤
    if ($allNotes) {
      const tagMap = new Map();
      
      $allNotes.forEach(note => {
        note.tags.forEach(tag => {
          if (!tagMap.has(tag.name)) {
            tagMap.set(tag.name, {
              id: tag.id || Math.random().toString(36).substr(2, 9),
              name: tag.name,
              color: tag.color || '#3b82f6',
              description: tag.description || '',
              createdAt: new Date().toISOString()
            });
          }
        });
      });
      
      tags = Array.from(tagMap.values());
    }
  };

  const createTag = () => {
    if (newTagName.trim()) {
      const newTag = {
        id: Math.random().toString(36).substr(2, 9),
        name: newTagName.trim(),
        color: newTagColor,
        description: '',
        createdAt: new Date().toISOString()
      };
      
      tags = [...tags, newTag];
      newTagName = '';
      newTagColor = '#3b82f6';
      showCreateModal = false;
    }
  };

  const editTag = (tag) => {
    editingTag = { ...tag };
    showEditModal = true;
  };

  const updateTag = () => {
    if (editingTag && editingTag.name.trim()) {
      tags = tags.map(tag => 
        tag.id === editingTag.id ? editingTag : tag
      );
      showEditModal = false;
      editingTag = null;
    }
  };

  const deleteTag = (tagToDelete) => {
    if (confirm(`確定要刪除標籤「${tagToDelete.name}」嗎？`)) {
      tags = tags.filter(tag => tag.id !== tagToDelete.id);
    }
  };

  const viewTagNotes = (tag) => {
    goto(`/search?tag=${encodeURIComponent(tag.name)}`);
  };

  const getTagColor = (color) => {
    return {
      backgroundColor: color + '20',
      borderColor: color,
      color: color
    };
  };
</script>

<svelte:head>
  <title>標籤管理 - Life Note</title>
</svelte:head>

<div class="tags-page h-full flex flex-col">
  <!-- Header -->
  <header class="flex-shrink-0 border-b border-border bg-background/95 backdrop-blur">
    <div class="container mx-auto px-4 py-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold flex items-center">
            <Hash class="h-6 w-6 mr-2" />
            標籤管理
          </h1>
          <p class="text-muted-foreground mt-1">
            管理和組織您的筆記標籤
          </p>
        </div>
        
        <Button on:click={() => showCreateModal = true}>
          <Plus class="h-4 w-4 mr-2" />
          新增標籤
        </Button>
      </div>
      
      <!-- Search -->
      <div class="mt-6 relative max-w-md">
        <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <input
          type="text"
          placeholder="搜尋標籤..."
          bind:value={searchQuery}
          class="w-full pl-10 pr-4 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
        />
      </div>
    </div>
  </header>

  <!-- Content -->
  <main class="flex-1 overflow-auto">
    <div class="container mx-auto px-4 py-6">
      <!-- Statistics -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        <Card class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
              <Tag class="h-5 w-5 text-blue-600" />
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-muted-foreground">總標籤數</p>
              <p class="text-2xl font-bold">{tags.length}</p>
            </div>
          </div>
        </Card>
        
        <Card class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
              <TrendingUp class="h-5 w-5 text-green-600" />
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-muted-foreground">使用中標籤</p>
              <p class="text-2xl font-bold">{tagStats.filter(tag => tag.noteCount > 0).length}</p>
            </div>
          </div>
        </Card>
        
        <Card class="p-4">
          <div class="flex items-center">
            <div class="p-2 bg-purple-100 rounded-lg">
              <FileText class="h-5 w-5 text-purple-600" />
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-muted-foreground">平均每標籤筆記數</p>
              <p class="text-2xl font-bold">
                {tags.length > 0 ? Math.round(tagStats.reduce((sum, tag) => sum + tag.noteCount, 0) / tags.length) : 0}
              </p>
            </div>
          </div>
        </Card>
      </div>

      <!-- Tags Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {#each sortedTags as tag (tag.id)}
          <Card class="p-4 hover:shadow-md transition-shadow">
            <div class="flex items-start justify-between mb-3">
              <div class="flex items-center">
                <span 
                  class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border"
                  style={getTagColor(tag.color)}
                >
                  <Tag class="h-3 w-3 mr-1" />
                  {tag.name}
                </span>
              </div>
              
              <div class="flex items-center space-x-1">
                <Button variant="ghost" size="sm" onclick={() => editTag(tag)}>
                  <Edit3 class="h-3 w-3" />
                </Button>
                <Button variant="ghost" size="sm" onclick={() => deleteTag(tag)}>
                  <Trash2 class="h-3 w-3" />
                </Button>
              </div>
            </div>
            
            <div class="space-y-2">
              <div class="flex items-center justify-between text-sm">
                <span class="text-muted-foreground">筆記數量</span>
                <span class="font-medium">{tag.noteCount}</span>
              </div>
              
              {#if tag.lastUsed}
                <div class="flex items-center justify-between text-sm">
                  <span class="text-muted-foreground">最後使用</span>
                  <span class="font-medium">{tag.lastUsed.toLocaleDateString()}</span>
                </div>
              {/if}
              
              {#if tag.noteCount > 0}
                <Button 
                  variant="outline" 
                  size="sm" 
                  class="w-full mt-3"
                  on:click={() => viewTagNotes(tag)}
                >
                  查看相關筆記
                </Button>
              {/if}
            </div>
          </Card>
        {/each}
      </div>
      
      {#if sortedTags.length === 0}
        <div class="text-center py-12">
          <Tag class="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 class="text-lg font-medium text-muted-foreground mb-2">
            {searchQuery ? '沒有找到匹配的標籤' : '還沒有任何標籤'}
          </h3>
          <p class="text-muted-foreground mb-4">
            {searchQuery ? '嘗試使用不同的關鍵字搜尋' : '開始創建您的第一個標籤來組織筆記'}
          </p>
          {#if !searchQuery}
            <Button on:click={() => showCreateModal = true}>
              <Plus class="h-4 w-4 mr-2" />
              新增標籤
            </Button>
          {/if}
        </div>
      {/if}
    </div>
  </main>
</div>

<!-- Create Tag Modal -->
{#if showCreateModal}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <Card class="w-full max-w-md mx-4 p-6">
      <h3 class="text-lg font-semibold mb-4">新增標籤</h3>
      
      <div class="space-y-4">
        <div>
          <label for="tagName" class="block text-sm font-medium mb-1">標籤名稱</label>
          <input
            id="tagName"
            type="text"
            bind:value={newTagName}
            placeholder="輸入標籤名稱..."
            class="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
          />
        </div>
        
        <div>
          <label for="tagColor" class="block text-sm font-medium mb-1">標籤顏色</label>
          <input
            id="tagColor"
            type="color"
            bind:value={newTagColor}
            class="w-full h-10 border border-input rounded-md"
          />
        </div>
      </div>
      
      <div class="flex justify-end space-x-2 mt-6">
        <Button variant="outline" on:click={() => showCreateModal = false}>
          取消
        </Button>
        <Button on:click={createTag} disabled={!newTagName.trim()}>
          創建
        </Button>
      </div>
    </Card>
  </div>
{/if}

<!-- Edit Tag Modal -->
{#if showEditModal && editingTag}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <Card class="w-full max-w-md mx-4 p-6">
      <h3 class="text-lg font-semibold mb-4">編輯標籤</h3>
      
      <div class="space-y-4">
        <div>
          <label for="editTagName" class="block text-sm font-medium mb-1">標籤名稱</label>
          <input
            id="editTagName"
            type="text"
            bind:value={editingTag.name}
            class="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
          />
        </div>
        
        <div>
          <label for="editTagColor" class="block text-sm font-medium mb-1">標籤顏色</label>
          <input
            id="editTagColor"
            type="color"
            bind:value={editingTag.color}
            class="w-full h-10 border border-input rounded-md"
          />
        </div>
      </div>
      
      <div class="flex justify-end space-x-2 mt-6">
        <Button variant="outline" on:click={() => showEditModal = false}>
          取消
        </Button>
        <Button on:click={updateTag} disabled={!editingTag.name.trim()}>
          更新
        </Button>
      </div>
    </Card>
  </div>
{/if}
