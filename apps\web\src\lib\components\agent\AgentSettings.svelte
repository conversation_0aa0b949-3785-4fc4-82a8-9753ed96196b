<script lang="ts">
	import { onMount } from 'svelte';
	import { 
		<PERSON><PERSON><PERSON>, 
		<PERSON>, 
		Bo<PERSON>, 
		CheckCircle, 
		AlertCircle, 
		Loader2,
		Eye,
		EyeOff,
		Save,
		TestTube
	} from 'lucide-svelte';
	
	import Button from '$components/ui/Button.svelte';
	import Card from '$components/ui/Card.svelte';
	import { simpleAgentManager } from '$lib/services/agent/SimpleAgentManager';

	// 狀態
	let apiKey = '';
	let showApiKey = false;
	let isConnecting = false;
	let isConnected = false;
	let connectionError = '';
	let agentStatus = 'offline';
	let systemStatus: any = null;

	// 從本地存儲載入 API Key
	onMount(() => {
		const savedApiKey = localStorage.getItem('gemini_api_key');
		if (savedApiKey) {
			apiKey = savedApiKey;
			initializeAgent();
		}
		
		// 監聽 Agent 事件
		simpleAgentManager.addEventListener('agent_registered', handleAgentEvent);
		simpleAgentManager.addEventListener('agent_status_changed', handleAgentEvent);
		
		// 定期更新系統狀態
		const statusInterval = setInterval(updateSystemStatus, 5000);
		
		return () => {
			clearInterval(statusInterval);
			simpleAgentManager.removeEventListener('agent_registered', handleAgentEvent);
			simpleAgentManager.removeEventListener('agent_status_changed', handleAgentEvent);
		};
	});

	const handleAgentEvent = (event: any) => {
		console.log('Agent event:', event);
		updateSystemStatus();
	};

	const updateSystemStatus = () => {
		systemStatus = simpleAgentManager.getSystemStatus();
		isConnected = simpleAgentManager.isReady();
		agentStatus = isConnected ? 'connected' : 'offline';
	};

	const initializeAgent = async () => {
		console.log('initializeAgent called with apiKey:', apiKey ? '***' : 'empty');

		if (!apiKey.trim()) {
			connectionError = '請輸入 Gemini API Key';
			return;
		}

		isConnecting = true;
		connectionError = '';
		console.log('Starting agent initialization...');

		try {
			await simpleAgentManager.initializePrimaryAgent(apiKey);

			// 保存 API Key 到本地存儲
			localStorage.setItem('gemini_api_key', apiKey);

			isConnected = true;
			agentStatus = 'connected';
			updateSystemStatus();

			console.log('Gemini Agent initialized successfully');
		} catch (error) {
			console.error('Failed to initialize agent:', error);
			connectionError = error instanceof Error ? error.message : '連接失敗';
			isConnected = false;
			agentStatus = 'error';
		} finally {
			isConnecting = false;
		}
	};

	const testConnection = async () => {
		console.log('testConnection called');

		if (!apiKey.trim()) {
			connectionError = '請輸入 API Key';
			return;
		}

		isConnecting = true;
		connectionError = '';

		try {
			// 先初始化 Agent（如果還沒有）
			if (!simpleAgentManager.isReady()) {
				console.log('Agent not ready, initializing...');
				await simpleAgentManager.initializePrimaryAgent(apiKey);
			}

			// 創建測試任務
			const testRequest = {
				id: `test-${Date.now()}`,
				type: 'question_answering' as const,
				priority: 'normal' as const,
				input: {
					question: 'Hello, are you working?',
					language: 'en'
				},
				createdAt: new Date()
			};

			console.log('Executing test task...');
			const result = await simpleAgentManager.executeTask(testRequest);

			if (result.status === 'completed') {
				connectionError = '';
				console.log('Connection test successful:', result);

				// 保存 API Key
				localStorage.setItem('gemini_api_key', apiKey);
				isConnected = true;
				agentStatus = 'connected';
				updateSystemStatus();
			} else {
				throw new Error('Test failed');
			}
		} catch (error) {
			console.error('Connection test failed:', error);
			connectionError = error instanceof Error ? error.message : '測試失敗';
			isConnected = false;
			agentStatus = 'error';
		} finally {
			isConnecting = false;
		}
	};

	const saveSettings = async () => {
		await initializeAgent();
	};

	const clearApiKey = () => {
		apiKey = '';
		localStorage.removeItem('gemini_api_key');
		isConnected = false;
		agentStatus = 'offline';
		connectionError = '';
		simpleAgentManager.shutdown();
	};

	const toggleApiKeyVisibility = () => {
		showApiKey = !showApiKey;
	};

	const getStatusColor = (status: string) => {
		switch (status) {
			case 'connected': return 'text-green-600';
			case 'connecting': return 'text-yellow-600';
			case 'error': return 'text-red-600';
			default: return 'text-gray-600';
		}
	};

	const getStatusIcon = (status: string) => {
		switch (status) {
			case 'connected': return CheckCircle;
			case 'connecting': return Loader2;
			case 'error': return AlertCircle;
			default: return Bot;
		}
	};

	const formatUptime = (uptime: number) => {
		const seconds = Math.floor(uptime / 1000);
		const minutes = Math.floor(seconds / 60);
		const hours = Math.floor(minutes / 60);
		
		if (hours > 0) {
			return `${hours}h ${minutes % 60}m`;
		} else if (minutes > 0) {
			return `${minutes}m ${seconds % 60}s`;
		} else {
			return `${seconds}s`;
		}
	};
</script>

<Card class="p-6">
	<div class="flex items-center space-x-3 mb-6">
		<Settings class="h-6 w-6 text-primary" />
		<h3 class="text-lg font-semibold">AI Agent 設置</h3>
	</div>

	<!-- API Key 設置 -->
	<div class="space-y-4 mb-6">
		<div>
			<label for="api-key" class="block text-sm font-medium mb-2">
				<Key class="h-4 w-4 inline mr-1" />
				Gemini API Key
			</label>
			<div class="flex space-x-2">
				<div class="relative flex-1">
					<input
						id="api-key"
						type={showApiKey ? 'text' : 'password'}
						bind:value={apiKey}
						placeholder="輸入您的 Gemini API Key"
						class="w-full px-3 py-2 pr-10 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring bg-background text-foreground placeholder:text-muted-foreground"
						disabled={isConnecting}
					/>
					<button
						type="button"
						onclick={toggleApiKeyVisibility}
						class="absolute right-2 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
					>
						{#if showApiKey}
							<EyeOff class="h-4 w-4" />
						{:else}
							<Eye class="h-4 w-4" />
						{/if}
					</button>
				</div>
				
				<Button
					variant="outline"
					size="sm"
					onclick={testConnection}
					disabled={isConnecting || !apiKey.trim()}
				>
					{#if isConnecting}
						<Loader2 class="h-4 w-4 mr-2 animate-spin" />
					{:else}
						<TestTube class="h-4 w-4 mr-2" />
					{/if}
					測試
				</Button>

				<Button
					variant="default"
					size="sm"
					onclick={saveSettings}
					disabled={isConnecting || !apiKey.trim()}
				>
					{#if isConnecting}
						<Loader2 class="h-4 w-4 mr-2 animate-spin" />
					{:else}
						<Save class="h-4 w-4 mr-2" />
					{/if}
					保存
				</Button>
			</div>
			
			{#if connectionError}
				<p class="text-sm text-red-600 mt-2 flex items-center">
					<AlertCircle class="h-4 w-4 mr-1" />
					{connectionError}
				</p>
			{/if}
		</div>

		<!-- 連接狀態 -->
		<div class="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
			<div class="flex items-center space-x-2">
				<svelte:component 
					this={getStatusIcon(agentStatus)} 
					class="h-5 w-5 {getStatusColor(agentStatus)} {agentStatus === 'connecting' ? 'animate-spin' : ''}"
				/>
				<span class="font-medium">Agent 狀態</span>
			</div>
			<span class="text-sm {getStatusColor(agentStatus)} capitalize">
				{agentStatus === 'connected' ? '已連接' : 
				 agentStatus === 'connecting' ? '連接中' : 
				 agentStatus === 'error' ? '錯誤' : '離線'}
			</span>
		</div>
	</div>

	<!-- 系統狀態 -->
	{#if systemStatus && isConnected}
		<div class="border-t pt-6">
			<h4 class="font-medium mb-4">系統狀態</h4>
			<div class="grid grid-cols-2 md:grid-cols-4 gap-4">
				<div class="text-center p-3 bg-blue-50 rounded-lg">
					<div class="text-2xl font-bold text-blue-600">{systemStatus.totalAgents}</div>
					<div class="text-sm text-blue-600">總 Agent 數</div>
				</div>
				
				<div class="text-center p-3 bg-green-50 rounded-lg">
					<div class="text-2xl font-bold text-green-600">{systemStatus.activeAgents}</div>
					<div class="text-sm text-green-600">活躍 Agent</div>
				</div>
				
				<div class="text-center p-3 bg-purple-50 rounded-lg">
					<div class="text-2xl font-bold text-purple-600">{systemStatus.totalTasks}</div>
					<div class="text-sm text-purple-600">總任務數</div>
				</div>
				
				<div class="text-center p-3 bg-orange-50 rounded-lg">
					<div class="text-2xl font-bold text-orange-600">{systemStatus.runningTasks}</div>
					<div class="text-sm text-orange-600">運行中任務</div>
				</div>
			</div>
			
			<div class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
				<div class="flex justify-between items-center p-3 bg-muted/30 rounded-lg">
					<span class="text-sm font-medium">系統負載</span>
					<div class="flex items-center space-x-2">
						<div class="w-20 bg-gray-200 rounded-full h-2">
							<div 
								class="bg-primary h-2 rounded-full transition-all duration-300"
								style="width: {(systemStatus.systemLoad * 100).toFixed(0)}%"
							></div>
						</div>
						<span class="text-sm">{(systemStatus.systemLoad * 100).toFixed(0)}%</span>
					</div>
				</div>
				
				<div class="flex justify-between items-center p-3 bg-muted/30 rounded-lg">
					<span class="text-sm font-medium">運行時間</span>
					<span class="text-sm font-mono">{formatUptime(systemStatus.uptime)}</span>
				</div>
			</div>
		</div>
	{/if}

	<!-- 操作按鈕 -->
	{#if isConnected}
		<div class="border-t pt-6">
			<div class="flex justify-between items-center">
				<div class="text-sm text-muted-foreground">
					Agent 已就緒，可以開始使用 AI 功能
				</div>
				<Button variant="outline" size="sm" on:click={clearApiKey}>
					清除設置
				</Button>
			</div>
		</div>
	{/if}

	<!-- 使用說明 -->
	<div class="border-t pt-6 mt-6">
		<h4 class="font-medium mb-2">使用說明</h4>
		<div class="text-sm text-muted-foreground space-y-1">
			<p>• 需要 Google Gemini API Key 才能使用 AI 功能</p>
			<p>• API Key 會安全地存儲在本地瀏覽器中</p>
			<p>• 支援筆記分析、內容生成、摘要、翻譯等功能</p>
			<p>• 可與 MCP 服務器進行通信以增強功能</p>
		</div>
	</div>
</Card>
