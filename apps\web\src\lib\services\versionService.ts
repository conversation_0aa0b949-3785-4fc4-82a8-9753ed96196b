import type { Note, NoteVersion, NoteId } from '$types';
import { repositoryFactory } from '@life-note/storage';

export interface VersionDiff {
	type: 'added' | 'removed' | 'modified';
	field: string;
	oldValue?: any;
	newValue?: any;
	description: string;
}

export interface CreateVersionData {
	noteId: NoteId;
	title: string;
	content: string;
	changes: string;
	authorId: string;
}

export class VersionService {
	/**
	 * 創建新版本
	 */
	async createVersion(data: CreateVersionData): Promise<NoteVersion> {
		try {
			const noteRepo = repositoryFactory.noteRepository;
			const note = await noteRepo.findById(data.noteId);

			if (!note) {
				throw new Error('筆記不存在');
			}

			const versionData: NoteVersion = {
				id: this.generateVersionId(),
				noteId: data.noteId,
				version: note.version + 1,
				title: data.title,
				content: data.content,
				changes: data.changes,
				authorId: data.authorId,
				createdAt: new Date(),
				metadata: {
					previousVersion: note.version,
					wordCount: data.content.length,
					changeType: this.detectChangeType(note, data)
				}
			};

			// 保存版本記錄
			const versionRepo = repositoryFactory.versionRepository;
			const savedVersion = await versionRepo.save(versionData);

			// 更新筆記的版本號
			const updatedNote = {
				...note,
				version: versionData.version,
				title: data.title,
				content: data.content,
				updatedAt: new Date()
			};
			await noteRepo.save(updatedNote);

			return savedVersion;
		} catch (error) {
			console.error('Failed to create version:', error);
			throw new Error('創建版本失敗');
		}
	}

	/**
	 * 獲取筆記的版本歷史
	 */
	async getVersionHistory(noteId: NoteId): Promise<NoteVersion[]> {
		try {
			const versionRepo = repositoryFactory.versionRepository;
			return await versionRepo.findByNoteId(noteId);
		} catch (error) {
			console.error('Failed to get version history:', error);
			throw new Error('獲取版本歷史失敗');
		}
	}

	/**
	 * 獲取特定版本
	 */
	async getVersion(versionId: string): Promise<NoteVersion | null> {
		try {
			const versionRepo = repositoryFactory.versionRepository;
			return await versionRepo.findById(versionId);
		} catch (error) {
			console.error('Failed to get version:', error);
			throw new Error('獲取版本失敗');
		}
	}

	/**
	 * 恢復到特定版本
	 */
	async restoreToVersion(noteId: NoteId, versionId: string, authorId: string): Promise<Note> {
		try {
			const versionRepo = repositoryFactory.versionRepository;
			const noteRepo = repositoryFactory.noteRepository;

			const version = await versionRepo.findById(versionId);
			if (!version) {
				throw new Error('版本不存在');
			}

			const note = await noteRepo.findById(noteId);
			if (!note) {
				throw new Error('筆記不存在');
			}

			// 創建恢復版本記錄
			await this.createVersion({
				noteId,
				title: version.title,
				content: version.content,
				changes: `恢復到版本 ${version.version}`,
				authorId
			});

			// 返回更新後的筆記
			return (await noteRepo.findById(noteId)) as Note;
		} catch (error) {
			console.error('Failed to restore version:', error);
			throw new Error('恢復版本失敗');
		}
	}

	/**
	 * 比較兩個版本的差異
	 */
	async compareVersions(versionId1: string, versionId2: string): Promise<VersionDiff[]> {
		try {
			const versionRepo = repositoryFactory.versionRepository;
			const [version1, version2] = await Promise.all([
				versionRepo.findById(versionId1),
				versionRepo.findById(versionId2)
			]);

			if (!version1 || !version2) {
				throw new Error('版本不存在');
			}

			return this.generateDiff(version1, version2);
		} catch (error) {
			console.error('Failed to compare versions:', error);
			throw new Error('比較版本失敗');
		}
	}

	/**
	 * 發布筆記（將草稿轉為正式版本）
	 */
	async publishNote(noteId: NoteId, authorId: string): Promise<Note> {
		try {
			const noteRepo = repositoryFactory.noteRepository;
			const note = await noteRepo.findById(noteId);

			if (!note) {
				throw new Error('筆記不存在');
			}

			if (note.status === 'published') {
				throw new Error('筆記已經發布');
			}

			// 創建發布版本
			await this.createVersion({
				noteId,
				title: note.title,
				content: note.content,
				changes: '發布筆記',
				authorId
			});

			// 更新筆記狀態
			const updatedNote = {
				...note,
				status: 'published' as const,
				metadata: {
					...note.metadata,
					publishedAt: new Date().toISOString(),
					publishedBy: authorId
				},
				updatedAt: new Date()
			};

			return await noteRepo.save(updatedNote);
		} catch (error) {
			console.error('Failed to publish note:', error);
			throw new Error('發布筆記失敗');
		}
	}

	/**
	 * 創建草稿版本
	 */
	async createDraft(noteId: NoteId, authorId: string): Promise<Note> {
		try {
			const noteRepo = repositoryFactory.noteRepository;
			const note = await noteRepo.findById(noteId);

			if (!note) {
				throw new Error('筆記不存在');
			}

			// 創建草稿版本記錄
			await this.createVersion({
				noteId,
				title: note.title,
				content: note.content,
				changes: '創建草稿版本',
				authorId
			});

			// 更新筆記狀態為草稿
			const updatedNote = {
				...note,
				status: 'draft' as const,
				metadata: {
					...note.metadata,
					draftCreatedAt: new Date().toISOString(),
					draftCreatedBy: authorId
				},
				updatedAt: new Date()
			};

			return await noteRepo.save(updatedNote);
		} catch (error) {
			console.error('Failed to create draft:', error);
			throw new Error('創建草稿失敗');
		}
	}

	/**
	 * 檢測變更類型
	 */
	private detectChangeType(oldNote: Note, newData: CreateVersionData): string {
		if (oldNote.title !== newData.title && oldNote.content !== newData.content) {
			return 'major'; // 標題和內容都變更
		} else if (oldNote.title !== newData.title) {
			return 'title'; // 只有標題變更
		} else if (oldNote.content !== newData.content) {
			return 'content'; // 只有內容變更
		} else {
			return 'minor'; // 微小變更
		}
	}

	/**
	 * 生成版本差異
	 */
	private generateDiff(version1: NoteVersion, version2: NoteVersion): VersionDiff[] {
		const diffs: VersionDiff[] = [];

		// 比較標題
		if (version1.title !== version2.title) {
			diffs.push({
				type: 'modified',
				field: 'title',
				oldValue: version1.title,
				newValue: version2.title,
				description: '標題已變更'
			});
		}

		// 比較內容
		if (version1.content !== version2.content) {
			diffs.push({
				type: 'modified',
				field: 'content',
				oldValue: version1.content,
				newValue: version2.content,
				description: '內容已變更'
			});
		}

		// 比較字數
		const oldWordCount = version1.content.length;
		const newWordCount = version2.content.length;
		if (oldWordCount !== newWordCount) {
			diffs.push({
				type: 'modified',
				field: 'wordCount',
				oldValue: oldWordCount,
				newValue: newWordCount,
				description: `字數從 ${oldWordCount} 變更為 ${newWordCount}`
			});
		}

		return diffs;
	}

	/**
	 * 生成版本 ID
	 */
	private generateVersionId(): string {
		return `version_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
	}
}

// 導出單例實例
export const versionService = new VersionService();
