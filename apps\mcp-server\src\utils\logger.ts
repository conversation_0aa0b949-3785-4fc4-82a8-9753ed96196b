import winston from "winston";
import { format } from "winston";

// 自定義日誌格式
const customFormat = format.combine(
  format.timestamp({
    format: "YYYY-MM-DD HH:mm:ss.SSS",
  }),
  format.errors({ stack: true }),
  format.json(),
  format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let log = `${timestamp} [${level.toUpperCase()}]`;

    if (typeof message === "object") {
      log += ` ${JSON.stringify(message)}`;
    } else {
      log += ` ${message}`;
    }

    if (Object.keys(meta).length > 0) {
      log += ` ${JSON.stringify(meta)}`;
    }

    if (stack) {
      log += `\n${stack}`;
    }

    return log;
  }),
);

// 控制台格式（更簡潔）
const consoleFormat = format.combine(
  format.timestamp({
    format: "HH:mm:ss",
  }),
  format.colorize(),
  format.printf(({ timestamp, level, message, ...meta }) => {
    let log = `${timestamp} ${level}`;

    if (typeof message === "object") {
      log += ` ${JSON.stringify(message, null, 2)}`;
    } else {
      log += ` ${message}`;
    }

    if (Object.keys(meta).length > 0) {
      log += ` ${JSON.stringify(meta)}`;
    }

    return log;
  }),
);

// 創建 logger 實例
export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || "info",
  format: customFormat,
  defaultMeta: {
    service: "life-note-mcp-server",
    pid: process.pid,
  },
  transports: [
    // 控制台輸出
    new winston.transports.Console({
      format: consoleFormat,
      handleExceptions: true,
      handleRejections: true,
    }),

    // 錯誤日誌文件
    new winston.transports.File({
      filename: "logs/error.log",
      level: "error",
      maxsize: 5242880, // 5MB
      maxFiles: 5,
      format: customFormat,
    }),

    // 組合日誌文件
    new winston.transports.File({
      filename: "logs/combined.log",
      maxsize: 5242880, // 5MB
      maxFiles: 10,
      format: customFormat,
    }),
  ],

  // 異常處理
  exceptionHandlers: [
    new winston.transports.File({
      filename: "logs/exceptions.log",
      maxsize: 5242880,
      maxFiles: 3,
    }),
  ],

  rejectionHandlers: [
    new winston.transports.File({
      filename: "logs/rejections.log",
      maxsize: 5242880,
      maxFiles: 3,
    }),
  ],
});

// 開發環境下的額外配置
if (process.env.NODE_ENV === "development") {
  logger.add(
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple(),
      ),
    }),
  );
}

// 生產環境下的額外配置
if (process.env.NODE_ENV === "production") {
  // 可以添加外部日誌服務，如 Elasticsearch, Splunk 等
  logger.level = "warn";
}

/**
 * 創建子 logger
 */
export function createChildLogger(module: string) {
  return logger.child({ module });
}

/**
 * 性能監控 logger
 */
export function createPerformanceLogger() {
  return logger.child({
    category: "performance",
    format: format.combine(format.timestamp(), format.json()),
  });
}

/**
 * 安全事件 logger
 */
export function createSecurityLogger() {
  return logger.child({
    category: "security",
    format: format.combine(format.timestamp(), format.json()),
  });
}

/**
 * API 請求 logger
 */
export function createApiLogger() {
  return logger.child({
    category: "api",
    format: format.combine(format.timestamp(), format.json()),
  });
}

/**
 * 日誌級別管理
 */
export class LogLevelManager {
  private static instance: LogLevelManager;
  private currentLevel: string;

  private constructor() {
    this.currentLevel = logger.level;
  }

  static getInstance(): LogLevelManager {
    if (!LogLevelManager.instance) {
      LogLevelManager.instance = new LogLevelManager();
    }
    return LogLevelManager.instance;
  }

  setLevel(level: string): void {
    if (["error", "warn", "info", "debug"].includes(level)) {
      logger.level = level;
      this.currentLevel = level;
      logger.info(`Log level changed to: ${level}`);
    } else {
      logger.warn(`Invalid log level: ${level}`);
    }
  }

  getLevel(): string {
    return this.currentLevel;
  }

  isLevelEnabled(level: string): boolean {
    const levels = ["error", "warn", "info", "debug"];
    const currentIndex = levels.indexOf(this.currentLevel);
    const checkIndex = levels.indexOf(level);

    return checkIndex <= currentIndex;
  }
}

/**
 * 日誌統計
 */
export class LogStats {
  private static instance: LogStats;
  private stats: Map<string, number> = new Map();
  private startTime: Date = new Date();

  private constructor() {
    // 監聽日誌事件
    logger.on("logged", (info) => {
      const level = info.level;
      this.stats.set(level, (this.stats.get(level) || 0) + 1);
    });
  }

  static getInstance(): LogStats {
    if (!LogStats.instance) {
      LogStats.instance = new LogStats();
    }
    return LogStats.instance;
  }

  getStats() {
    return {
      startTime: this.startTime,
      uptime: Date.now() - this.startTime.getTime(),
      counts: Object.fromEntries(this.stats),
      total: Array.from(this.stats.values()).reduce(
        (sum, count) => sum + count,
        0,
      ),
    };
  }

  reset(): void {
    this.stats.clear();
    this.startTime = new Date();
  }
}

/**
 * 結構化日誌輔助函數
 */
export const structuredLog = {
  request: (
    method: string,
    path: string,
    statusCode: number,
    duration: number,
    meta?: any,
  ) => {
    logger.info("HTTP Request", {
      type: "http_request",
      method,
      path,
      statusCode,
      duration,
      ...meta,
    });
  },

  error: (error: Error, context?: any) => {
    logger.error("Application Error", {
      type: "application_error",
      message: error.message,
      stack: error.stack,
      context,
    });
  },

  performance: (operation: string, duration: number, meta?: any) => {
    logger.info("Performance Metric", {
      type: "performance",
      operation,
      duration,
      ...meta,
    });
  },

  security: (event: string, details: any) => {
    logger.warn("Security Event", {
      type: "security",
      event,
      details,
      timestamp: new Date().toISOString(),
    });
  },

  business: (event: string, data: any) => {
    logger.info("Business Event", {
      type: "business",
      event,
      data,
      timestamp: new Date().toISOString(),
    });
  },
};

// 導出默認 logger
export default logger;
