import type { PrismaClient } from "../prisma/generated/index.js";
import { prismaClient } from "../prisma/client.js";
import { NoteRepository } from "./NoteRepository.js";
import { UserRepository } from "./UserRepository.js";
import { DependencyRepository } from "./DependencyRepository.js";

/**
 * 存儲庫工廠類
 * 負責創建和管理所有存儲庫實例
 */
export class RepositoryFactory {
  private _prisma: PrismaClient;
  private _noteRepository?: NoteRepository;
  private _userRepository?: UserRepository;
  private _dependencyRepository?: DependencyRepository;

  constructor(prisma?: PrismaClient) {
    this._prisma = prisma || prismaClient.client;
  }

  /**
   * 獲取筆記存儲庫
   */
  get noteRepository(): NoteRepository {
    if (!this._noteRepository) {
      this._noteRepository = new NoteRepository(this._prisma);
    }
    return this._noteRepository;
  }

  /**
   * 獲取用戶存儲庫
   */
  get userRepository(): UserRepository {
    if (!this._userRepository) {
      this._userRepository = new UserRepository(this._prisma);
    }
    return this._userRepository;
  }

  /**
   * 獲取依賴關係存儲庫
   */
  get dependencyRepository(): DependencyRepository {
    if (!this._dependencyRepository) {
      this._dependencyRepository = new DependencyRepository(this._prisma);
    }
    return this._dependencyRepository;
  }

  /**
   * 獲取所有存儲庫
   */
  getAllRepositories() {
    return {
      noteRepository: this.noteRepository,
      userRepository: this.userRepository,
      dependencyRepository: this.dependencyRepository,
    };
  }

  /**
   * 在事務中執行操作
   */
  async transaction<T>(
    operation: (repositories: {
      noteRepository: NoteRepository;
      userRepository: UserRepository;
      dependencyRepository: DependencyRepository;
    }) => Promise<T>,
  ): Promise<T> {
    return await this._prisma.$transaction(async (tx) => {
      const factory = new RepositoryFactory(tx);
      return await operation(factory.getAllRepositories());
    });
  }

  /**
   * 清理資源
   */
  async dispose(): Promise<void> {
    // 如果使用的是全局客戶端，則斷開連接
    if (this._prisma === prismaClient.client) {
      await prismaClient.disconnect();
    }
  }
}

// 創建全局存儲庫工廠實例
export const repositoryFactory = new RepositoryFactory();

// 導出便捷的存儲庫訪問器
export const noteRepository = repositoryFactory.noteRepository;
export const userRepository = repositoryFactory.userRepository;
export const dependencyRepository = repositoryFactory.dependencyRepository;
