import type { PrismaClient } from "../prisma/generated/index.js";
import type {
  IRepository,
  IPaginatedRepository,
  PaginationParams,
  PaginatedResult,
  QueryOptions,
} from "../interfaces/IRepository.js";

/**
 * 基礎存儲庫實現
 * 提供通用的 CRUD 操作和分頁功能
 */
export abstract class BaseRepository<TEntity, TId, TPrismaModel>
  implements IPaginatedRepository<TEntity, TId>
{
  protected readonly prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * 獲取 Prisma 模型的代理對象
   * 子類需要實現此方法來返回對應的 Prisma 模型
   */
  protected abstract getModel(): any;

  /**
   * 將 Prisma 模型轉換為領域實體
   * 子類需要實現此方法
   */
  protected abstract toDomainEntity(model: TPrismaModel): TEntity;

  /**
   * 將領域實體轉換為 Prisma 模型數據
   * 子類需要實現此方法
   */
  protected abstract toPrismaModel(
    entity: TEntity,
  ): Omit<TPrismaModel, "id" | "createdAt" | "updatedAt">;

  /**
   * 提取實體 ID
   * 子類需要實現此方法
   */
  protected abstract extractId(entity: TEntity): string;

  /**
   * 根據 ID 查找實體
   */
  async findById(id: TId): Promise<TEntity | null> {
    try {
      const model = this.getModel();
      const result = await model.findUnique({
        where: { id: this.idToString(id) },
      });

      return result ? this.toDomainEntity(result) : null;
    } catch (error) {
      console.error(`Error finding entity by id ${String(id)}:`, error);
      throw error;
    }
  }

  /**
   * 查找所有實體
   */
  async findAll(): Promise<TEntity[]> {
    try {
      const model = this.getModel();
      const results = await model.findMany({
        orderBy: { createdAt: "desc" },
      });

      return results.map((result: TPrismaModel) => this.toDomainEntity(result));
    } catch (error) {
      console.error("Error finding all entities:", error);
      throw error;
    }
  }

  /**
   * 保存實體（創建或更新）
   */
  async save(entity: TEntity): Promise<TEntity> {
    try {
      const model = this.getModel();
      const id = this.extractId(entity);
      const data = this.toPrismaModel(entity);

      let result: TPrismaModel;

      // 使用 upsert 來處理創建或更新
      result = await model.upsert({
        where: { id },
        update: data,
        create: {
          id,
          ...data,
        },
      });

      return this.toDomainEntity(result);
    } catch (error) {
      console.error("Error saving entity:", error);
      throw error;
    }
  }

  /**
   * 根據 ID 刪除實體
   */
  async deleteById(id: TId): Promise<void> {
    try {
      const model = this.getModel();
      await model.delete({
        where: { id: this.idToString(id) },
      });
    } catch (error) {
      console.error(`Error deleting entity by id ${String(id)}:`, error);
      throw error;
    }
  }

  /**
   * 檢查實體是否存在
   */
  async exists(id: TId): Promise<boolean> {
    try {
      const model = this.getModel();
      const result = await model.findUnique({
        where: { id: this.idToString(id) },
        select: { id: true },
      });

      return result !== null;
    } catch (error) {
      console.error(
        `Error checking entity existence for id ${String(id)}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * 計算實體總數
   */
  async count(): Promise<number> {
    try {
      const model = this.getModel();
      return await model.count();
    } catch (error) {
      console.error("Error counting entities:", error);
      throw error;
    }
  }

  /**
   * 分頁查詢
   */
  async findPaginated(
    params: PaginationParams,
  ): Promise<PaginatedResult<TEntity>> {
    try {
      const { page, limit, sortBy = "createdAt", sortOrder = "desc" } = params;
      const skip = (page - 1) * limit;

      const model = this.getModel();

      const [results, total] = await Promise.all([
        model.findMany({
          skip,
          take: limit,
          orderBy: { [sortBy]: sortOrder },
        }),
        model.count(),
      ]);

      const entities = results.map((result: TPrismaModel) =>
        this.toDomainEntity(result),
      );

      return this.createPaginatedResult(entities, total, page, limit);
    } catch (error) {
      console.error("Error in paginated query:", error);
      throw error;
    }
  }

  /**
   * 根據條件分頁查詢
   */
  async findByConditionPaginated(
    condition: Record<string, unknown>,
    params: PaginationParams,
  ): Promise<PaginatedResult<TEntity>> {
    try {
      const { page, limit, sortBy = "createdAt", sortOrder = "desc" } = params;
      const skip = (page - 1) * limit;

      const model = this.getModel();

      const [results, total] = await Promise.all([
        model.findMany({
          where: condition,
          skip,
          take: limit,
          orderBy: { [sortBy]: sortOrder },
        }),
        model.count({ where: condition }),
      ]);

      const entities = results.map((result: TPrismaModel) =>
        this.toDomainEntity(result),
      );

      return this.createPaginatedResult(entities, total, page, limit);
    } catch (error) {
      console.error("Error in conditional paginated query:", error);
      throw error;
    }
  }

  /**
   * 根據查詢選項查找實體
   */
  protected async findWithOptions(options: QueryOptions): Promise<TEntity[]> {
    try {
      const model = this.getModel();
      const results = await model.findMany(options);

      return results.map((result: TPrismaModel) => this.toDomainEntity(result));
    } catch (error) {
      console.error("Error finding entities with options:", error);
      throw error;
    }
  }

  /**
   * 批量保存實體
   */
  async batchSave(entities: TEntity[]): Promise<TEntity[]> {
    try {
      const results: TEntity[] = [];

      // 使用事務來確保原子性
      await this.prisma.$transaction(async (tx) => {
        for (const entity of entities) {
          const saved = await this.save(entity);
          results.push(saved);
        }
      });

      return results;
    } catch (error) {
      console.error("Error in batch save:", error);
      throw error;
    }
  }

  /**
   * 批量刪除實體
   */
  async batchDelete(ids: TId[]): Promise<void> {
    try {
      const model = this.getModel();
      const stringIds = ids.map((id) => this.idToString(id));

      await model.deleteMany({
        where: {
          id: {
            in: stringIds,
          },
        },
      });
    } catch (error) {
      console.error("Error in batch delete:", error);
      throw error;
    }
  }

  /**
   * 將 ID 轉換為字符串
   */
  protected idToString(id: TId): string {
    if (typeof id === "string") {
      return id;
    }
    if (typeof id === "object" && id !== null && "value" in id) {
      return (id as any).value;
    }
    return String(id);
  }

  /**
   * 創建分頁結果對象
   */
  protected createPaginatedResult<T>(
    data: T[],
    total: number,
    page: number,
    limit: number,
  ): PaginatedResult<T> {
    const totalPages = Math.ceil(total / limit);

    return {
      data,
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  /**
   * 解析 JSON 字符串
   */
  protected parseJson<T>(jsonString: string | null): T | null {
    if (!jsonString) return null;

    try {
      return JSON.parse(jsonString) as T;
    } catch (error) {
      console.error("Error parsing JSON:", error);
      return null;
    }
  }

  /**
   * 將對象序列化為 JSON 字符串
   */
  protected stringifyJson(obj: unknown): string | null {
    if (obj === null || obj === undefined) return null;

    try {
      return JSON.stringify(obj);
    } catch (error) {
      console.error("Error stringifying JSON:", error);
      return null;
    }
  }
}
