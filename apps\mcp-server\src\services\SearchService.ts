import Fuse from "fuse.js";
import { NotesService, Note } from "./NotesService.js";
import { logger } from "../utils/logger.js";

/**
 * 搜索結果接口
 */
export interface SearchResult {
  note: Note;
  score: number;
  matches?: SearchMatch[];
}

/**
 * 搜索匹配接口
 */
export interface SearchMatch {
  field: string;
  value: string;
  indices: [number, number][];
}

/**
 * 搜索選項接口
 */
export interface SearchOptions {
  query: string;
  fields?: string[];
  fuzzy?: boolean;
  threshold?: number;
  limit?: number;
  includeMatches?: boolean;
  tags?: string[];
  category?: string;
  status?: string;
  priority?: string;
}

/**
 * 高級搜索選項
 */
export interface AdvancedSearchOptions {
  titleQuery?: string;
  contentQuery?: string;
  tagQuery?: string;
  dateRange?: {
    start?: Date;
    end?: Date;
  };
  categories?: string[];
  statuses?: string[];
  priorities?: string[];
  sortBy?: "relevance" | "date" | "title";
  sortOrder?: "asc" | "desc";
  limit?: number;
  offset?: number;
}

/**
 * 搜索統計接口
 */
export interface SearchStats {
  totalQueries: number;
  averageResponseTime: number;
  popularQueries: Array<{ query: string; count: number }>;
  lastIndexUpdate: Date;
}

/**
 * 搜索服務
 *
 * 提供全文搜索、模糊搜索、標籤搜索等功能
 */
export class SearchService {
  private notesService: NotesService;
  private fuseInstance: Fuse<Note> | null = null;
  private isInitialized = false;
  private searchStats: SearchStats;
  private queryHistory: Map<string, number> = new Map();

  constructor(notesService: NotesService) {
    this.notesService = notesService;
    this.searchStats = {
      totalQueries: 0,
      averageResponseTime: 0,
      popularQueries: [],
      lastIndexUpdate: new Date(),
    };
  }

  /**
   * 初始化搜索服務
   */
  async initialize(): Promise<void> {
    try {
      await this.buildSearchIndex();
      this.isInitialized = true;
      logger.info("SearchService initialized successfully");
    } catch (error) {
      logger.error("Failed to initialize SearchService:", error);
      throw error;
    }
  }

  /**
   * 構建搜索索引
   */
  private async buildSearchIndex(): Promise<void> {
    try {
      const notes = await this.notesService.getAllNotes();

      const fuseOptions: Fuse.IFuseOptions<Note> = {
        keys: [
          { name: "title", weight: 0.4 },
          { name: "content", weight: 0.3 },
          { name: "tags", weight: 0.2 },
          { name: "category", weight: 0.1 },
        ],
        threshold: 0.3,
        includeScore: true,
        includeMatches: true,
        minMatchCharLength: 2,
        shouldSort: true,
        findAllMatches: true,
      };

      this.fuseInstance = new Fuse(notes, fuseOptions);
      this.searchStats.lastIndexUpdate = new Date();

      logger.info(`Search index built with ${notes.length} notes`);
    } catch (error) {
      logger.error("Failed to build search index:", error);
      throw error;
    }
  }

  /**
   * 執行搜索
   */
  async search(options: SearchOptions): Promise<SearchResult[]> {
    const startTime = Date.now();

    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      if (!this.fuseInstance) {
        throw new Error("Search index not available");
      }

      // 記錄查詢
      this.recordQuery(options.query);

      let results: SearchResult[] = [];

      if (options.fuzzy !== false && this.fuseInstance) {
        // 使用 Fuse.js 進行模糊搜索
        const fuseResults = this.fuseInstance.search(options.query, {
          limit: options.limit || 50,
        });

        results = fuseResults.map((result) => ({
          note: result.item,
          score: 1 - (result.score || 0), // 轉換為相似度分數
          matches: options.includeMatches
            ? this.convertFuseMatches(result.matches)
            : undefined,
        }));
      } else {
        // 精確搜索
        const notes = await this.notesService.getAllNotes();
        const query = options.query.toLowerCase();

        results = notes
          .map((note) => {
            const score = this.calculateExactMatchScore(
              note,
              query,
              options.fields,
            );
            return score > 0 ? { note, score } : null;
          })
          .filter((result): result is SearchResult => result !== null)
          .sort((a, b) => b.score - a.score)
          .slice(0, options.limit || 50);
      }

      // 應用額外過濾條件
      results = this.applyFilters(results, options);

      // 更新統計
      this.updateSearchStats(Date.now() - startTime);

      return results;
    } catch (error) {
      logger.error("Search failed:", error);
      throw error;
    }
  }

  /**
   * 高級搜索
   */
  async advancedSearch(
    options: AdvancedSearchOptions,
  ): Promise<SearchResult[]> {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const notes = await this.notesService.getAllNotes();
      let results: SearchResult[] = [];

      for (const note of notes) {
        const score = this.calculateAdvancedScore(note, options);
        if (score > 0) {
          results.push({ note, score });
        }
      }

      // 排序
      if (options.sortBy === "relevance" || !options.sortBy) {
        results.sort((a, b) => b.score - a.score);
      } else if (options.sortBy === "date") {
        results.sort((a, b) => {
          const aDate = a.note.updatedAt.getTime();
          const bDate = b.note.updatedAt.getTime();
          return options.sortOrder === "asc" ? aDate - bDate : bDate - aDate;
        });
      } else if (options.sortBy === "title") {
        results.sort((a, b) => {
          const comparison = a.note.title.localeCompare(b.note.title);
          return options.sortOrder === "asc" ? comparison : -comparison;
        });
      }

      // 分頁
      const offset = options.offset || 0;
      const limit = options.limit || results.length;

      return results.slice(offset, offset + limit);
    } catch (error) {
      logger.error("Advanced search failed:", error);
      throw error;
    }
  }

  /**
   * 標籤搜索
   */
  async searchByTags(
    tags: string[],
    options?: { operator?: "AND" | "OR"; limit?: number },
  ): Promise<Note[]> {
    try {
      const notes = await this.notesService.getAllNotes();
      const operator = options?.operator || "OR";

      const filteredNotes = notes.filter((note) => {
        if (operator === "AND") {
          return tags.every((tag) => note.tags.includes(tag));
        } else {
          return tags.some((tag) => note.tags.includes(tag));
        }
      });

      return filteredNotes.slice(0, options?.limit || filteredNotes.length);
    } catch (error) {
      logger.error("Tag search failed:", error);
      throw error;
    }
  }

  /**
   * 相似筆記搜索
   */
  async findSimilarNotes(noteId: string, limit = 5): Promise<SearchResult[]> {
    try {
      const targetNote = await this.notesService.getNoteById(noteId);
      if (!targetNote) {
        return [];
      }

      const allNotes = await this.notesService.getAllNotes();
      const results: SearchResult[] = [];

      for (const note of allNotes) {
        if (note.id === noteId) continue;

        const score = this.calculateSimilarityScore(targetNote, note);
        if (score > 0.1) {
          // 最低相似度閾值
          results.push({ note, score });
        }
      }

      return results.sort((a, b) => b.score - a.score).slice(0, limit);
    } catch (error) {
      logger.error("Similar notes search failed:", error);
      throw error;
    }
  }

  /**
   * 獲取搜索建議
   */
  async getSearchSuggestions(query: string, limit = 10): Promise<string[]> {
    try {
      const notes = await this.notesService.getAllNotes();
      const suggestions = new Set<string>();

      const queryLower = query.toLowerCase();

      // 從標題中提取建議
      for (const note of notes) {
        const words = note.title.toLowerCase().split(/\s+/);
        for (const word of words) {
          if (word.startsWith(queryLower) && word.length > queryLower.length) {
            suggestions.add(word);
          }
        }
      }

      // 從標籤中提取建議
      for (const note of notes) {
        for (const tag of note.tags) {
          if (tag.toLowerCase().startsWith(queryLower)) {
            suggestions.add(tag);
          }
        }
      }

      return Array.from(suggestions).slice(0, limit);
    } catch (error) {
      logger.error("Failed to get search suggestions:", error);
      return [];
    }
  }

  /**
   * 重新索引
   */
  async reindex(): Promise<void> {
    try {
      await this.buildSearchIndex();
      logger.info("Search index rebuilt successfully");
    } catch (error) {
      logger.error("Failed to reindex:", error);
      throw error;
    }
  }

  /**
   * 計算精確匹配分數
   */
  private calculateExactMatchScore(
    note: Note,
    query: string,
    fields?: string[],
  ): number {
    let score = 0;
    const searchFields = fields || ["title", "content", "tags"];

    for (const field of searchFields) {
      let fieldValue = "";

      if (field === "title") {
        fieldValue = note.title.toLowerCase();
      } else if (field === "content") {
        fieldValue = note.content.toLowerCase();
      } else if (field === "tags") {
        fieldValue = note.tags.join(" ").toLowerCase();
      }

      if (fieldValue.includes(query)) {
        // 根據字段重要性和匹配程度計算分數
        const fieldWeight =
          field === "title" ? 0.4 : field === "content" ? 0.3 : 0.2;
        const exactMatch = fieldValue === query ? 1 : 0.5;
        score += fieldWeight * exactMatch;
      }
    }

    return score;
  }

  /**
   * 計算高級搜索分數
   */
  private calculateAdvancedScore(
    note: Note,
    options: AdvancedSearchOptions,
  ): number {
    let score = 0;

    // 標題查詢
    if (options.titleQuery) {
      if (note.title.toLowerCase().includes(options.titleQuery.toLowerCase())) {
        score += 0.4;
      }
    }

    // 內容查詢
    if (options.contentQuery) {
      if (
        note.content.toLowerCase().includes(options.contentQuery.toLowerCase())
      ) {
        score += 0.3;
      }
    }

    // 標籤查詢
    if (options.tagQuery) {
      const hasTag = note.tags.some((tag) =>
        tag.toLowerCase().includes(options.tagQuery!.toLowerCase()),
      );
      if (hasTag) {
        score += 0.2;
      }
    }

    // 分類過濾
    if (options.categories && options.categories.length > 0) {
      if (!note.category || !options.categories.includes(note.category)) {
        return 0;
      }
    }

    // 狀態過濾
    if (options.statuses && options.statuses.length > 0) {
      if (!note.status || !options.statuses.includes(note.status)) {
        return 0;
      }
    }

    // 優先級過濾
    if (options.priorities && options.priorities.length > 0) {
      if (!note.priority || !options.priorities.includes(note.priority)) {
        return 0;
      }
    }

    // 日期範圍過濾
    if (options.dateRange) {
      const noteDate = note.updatedAt;
      if (options.dateRange.start && noteDate < options.dateRange.start) {
        return 0;
      }
      if (options.dateRange.end && noteDate > options.dateRange.end) {
        return 0;
      }
    }

    return score;
  }

  /**
   * 計算相似度分數
   */
  private calculateSimilarityScore(note1: Note, note2: Note): number {
    let score = 0;

    // 標籤相似度
    const commonTags = note1.tags.filter((tag) => note2.tags.includes(tag));
    const tagSimilarity =
      commonTags.length / Math.max(note1.tags.length, note2.tags.length, 1);
    score += tagSimilarity * 0.4;

    // 分類相似度
    if (note1.category && note2.category && note1.category === note2.category) {
      score += 0.2;
    }

    // 內容相似度（簡單的詞彙重疊）
    const words1 = new Set(note1.content.toLowerCase().split(/\s+/));
    const words2 = new Set(note2.content.toLowerCase().split(/\s+/));
    const commonWords = new Set([...words1].filter((word) => words2.has(word)));
    const contentSimilarity =
      commonWords.size / Math.max(words1.size, words2.size, 1);
    score += contentSimilarity * 0.3;

    // 標題相似度
    const titleWords1 = new Set(note1.title.toLowerCase().split(/\s+/));
    const titleWords2 = new Set(note2.title.toLowerCase().split(/\s+/));
    const commonTitleWords = new Set(
      [...titleWords1].filter((word) => titleWords2.has(word)),
    );
    const titleSimilarity =
      commonTitleWords.size / Math.max(titleWords1.size, titleWords2.size, 1);
    score += titleSimilarity * 0.1;

    return score;
  }

  /**
   * 應用過濾條件
   */
  private applyFilters(
    results: SearchResult[],
    options: SearchOptions,
  ): SearchResult[] {
    let filtered = results;

    if (options.tags && options.tags.length > 0) {
      filtered = filtered.filter((result) =>
        options.tags!.some((tag) => result.note.tags.includes(tag)),
      );
    }

    if (options.category) {
      filtered = filtered.filter(
        (result) => result.note.category === options.category,
      );
    }

    if (options.status) {
      filtered = filtered.filter(
        (result) => result.note.status === options.status,
      );
    }

    if (options.priority) {
      filtered = filtered.filter(
        (result) => result.note.priority === options.priority,
      );
    }

    return filtered;
  }

  /**
   * 轉換 Fuse.js 匹配結果
   */
  private convertFuseMatches(
    fuseMatches?: readonly Fuse.FuseResultMatch[],
  ): SearchMatch[] {
    if (!fuseMatches) return [];

    return fuseMatches.map((match) => ({
      field: match.key || "",
      value: match.value || "",
      indices: match.indices,
    }));
  }

  /**
   * 記錄查詢
   */
  private recordQuery(query: string): void {
    const count = this.queryHistory.get(query) || 0;
    this.queryHistory.set(query, count + 1);
    this.searchStats.totalQueries++;
  }

  /**
   * 更新搜索統計
   */
  private updateSearchStats(responseTime: number): void {
    const totalTime =
      this.searchStats.averageResponseTime *
        (this.searchStats.totalQueries - 1) +
      responseTime;
    this.searchStats.averageResponseTime =
      totalTime / this.searchStats.totalQueries;

    // 更新熱門查詢
    this.searchStats.popularQueries = Array.from(this.queryHistory.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([query, count]) => ({ query, count }));
  }

  /**
   * 獲取搜索統計
   */
  getSearchStats(): SearchStats {
    return { ...this.searchStats };
  }

  /**
   * 清除搜索統計
   */
  clearSearchStats(): void {
    this.queryHistory.clear();
    this.searchStats = {
      totalQueries: 0,
      averageResponseTime: 0,
      popularQueries: [],
      lastIndexUpdate: this.searchStats.lastIndexUpdate,
    };
  }
}
