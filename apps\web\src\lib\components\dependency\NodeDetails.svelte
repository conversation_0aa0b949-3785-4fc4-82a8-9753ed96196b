<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import { X, FileText, Tag, Calendar, Users, Link, TrendingUp } from 'lucide-svelte';
	import { <PERSON><PERSON>, Card } from '$components/ui';
	import type { DependencyNode, DependencyGraph } from '$lib/services/dependencyService';
	import type { Note } from '$types';

	export let node: DependencyNode | null = null;
	export let note: Note | null = null;
	export let graph: DependencyGraph | null = null;
	export let show = false;

	const dispatch = createEventDispatcher<{
		close: void;
		viewNote: { noteId: string };
		editNote: { noteId: string };
		highlightConnections: { nodeId: string | null };
	}>();

	$: connections = node && graph ? getConnections(node.id, graph) : [];
	$: centrality = node && graph ? calculateNodeCentrality(node.id, graph) : 0;

	function getConnections(nodeId: string, graph: DependencyGraph) {
		const connections: Array<{
			node: DependencyNode;
			type: string;
			strength: number;
		}> = [];

		for (const link of graph.links) {
			const sourceId = typeof link.source === 'string' ? link.source : link.source.id;
			const targetId = typeof link.target === 'string' ? link.target : link.target.id;

			if (sourceId === nodeId) {
				const targetNode = graph.nodes.find(n => n.id === targetId);
				if (targetNode) {
					connections.push({
						node: targetNode,
						type: link.type,
						strength: link.strength
					});
				}
			} else if (targetId === nodeId) {
				const sourceNode = graph.nodes.find(n => n.id === sourceId);
				if (sourceNode) {
					connections.push({
						node: sourceNode,
						type: link.type,
						strength: link.strength
					});
				}
			}
		}

		return connections.sort((a, b) => b.strength - a.strength);
	}

	function calculateNodeCentrality(nodeId: string, graph: DependencyGraph): number {
		let degree = 0;
		for (const link of graph.links) {
			const sourceId = typeof link.source === 'string' ? link.source : link.source.id;
			const targetId = typeof link.target === 'string' ? link.target : link.target.id;

			if (sourceId === nodeId || targetId === nodeId) {
				degree++;
			}
		}
		return degree;
	}

	function getStatusLabel(status: string): string {
		switch (status) {
			case 'draft':
				return '草稿';
			case 'published':
				return '已發布';
			case 'archived':
				return '已歸檔';
			default:
				return status;
		}
	}

	function getPriorityLabel(priority: string): string {
		switch (priority) {
			case 'urgent':
				return '緊急';
			case 'high':
				return '高';
			case 'medium':
				return '中';
			case 'low':
				return '低';
			default:
				return priority;
		}
	}

	function getTypeLabel(type: string): string {
		switch (type) {
			case 'reference':
				return '引用';
			case 'tag':
				return '標籤';
			case 'category':
				return '分類';
			case 'similar':
				return '相似';
			default:
				return type;
		}
	}

	function getStatusColor(status: string): string {
		switch (status) {
			case 'draft':
				return 'bg-yellow-100 text-yellow-800';
			case 'published':
				return 'bg-green-100 text-green-800';
			case 'archived':
				return 'bg-gray-100 text-gray-800';
			default:
				return 'bg-gray-100 text-gray-800';
		}
	}

	function getPriorityColor(priority: string): string {
		switch (priority) {
			case 'urgent':
				return 'bg-red-100 text-red-800';
			case 'high':
				return 'bg-orange-100 text-orange-800';
			case 'medium':
				return 'bg-blue-100 text-blue-800';
			case 'low':
				return 'bg-gray-100 text-gray-800';
			default:
				return 'bg-gray-100 text-gray-800';
		}
	}

	function formatDate(date: Date | string): string {
		const d = typeof date === 'string' ? new Date(date) : date;
		return new Intl.DateTimeFormat('zh-TW', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit'
		}).format(d);
	}

	function handleClose() {
		dispatch('close');
		dispatch('highlightConnections', { nodeId: null });
	}

	function handleViewNote() {
		if (node) {
			dispatch('viewNote', { noteId: node.id });
		}
	}

	function handleEditNote() {
		if (node) {
			dispatch('editNote', { noteId: node.id });
		}
	}

	function handleHighlightConnection(nodeId: string) {
		dispatch('highlightConnections', { nodeId });
	}
</script>

{#if show && node}
	<div class="node-details-overlay" onclick={handleClose}>
		<div class="node-details-panel" onclick={(e) => e.stopPropagation()}>
			<Card class="w-full h-full">
				<!-- 標題欄 -->
				<div class="flex items-center justify-between p-4 border-b">
					<div class="flex items-center gap-2">
						<FileText class="h-5 w-5 text-primary" />
						<h3 class="text-lg font-semibold">節點詳情</h3>
					</div>
					<Button variant="ghost" size="sm" onclick={handleClose}>
						<X class="h-4 w-4" />
					</Button>
				</div>

				<div class="p-4 space-y-4 max-h-96 overflow-y-auto">
					<!-- 基本信息 -->
					<div class="space-y-3">
						<h4 class="font-medium text-lg">{node.title}</h4>

						<div class="flex items-center gap-2">
							<span
								class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {getStatusColor(
									node.status
								)}"
							>
								{getStatusLabel(node.status)}
							</span>
							<span
								class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {getPriorityColor(
									node.priority
								)}"
							>
								{getPriorityLabel(node.priority)}
							</span>
						</div>

						{#if note}
							<p class="text-sm text-muted-foreground line-clamp-3">
								{note.excerpt || note.content.substring(0, 200) + '...'}
							</p>
						{/if}
					</div>

					<!-- 統計信息 -->
					<div class="grid grid-cols-3 gap-4 text-center">
						<div>
							<div class="text-lg font-bold text-primary">{Math.round(node.size)}</div>
							<div class="text-xs text-muted-foreground">大小</div>
						</div>
						<div>
							<div class="text-lg font-bold text-primary">{centrality}</div>
							<div class="text-xs text-muted-foreground">連接數</div>
						</div>
						<div>
							<div class="text-lg font-bold text-primary">{connections.length}</div>
							<div class="text-xs text-muted-foreground">鄰居</div>
						</div>
					</div>

					<!-- 標籤 -->
					{#if note && note.tags.length > 0}
						<div class="space-y-2">
							<div class="flex items-center gap-2">
								<Tag class="h-4 w-4" />
								<span class="text-sm font-medium">標籤</span>
							</div>
							<div class="flex flex-wrap gap-1">
								{#each note.tags as tag}
									<span
										class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-secondary text-secondary-foreground"
									>
										{tag.name}
									</span>
								{/each}
							</div>
						</div>
					{/if}

					<!-- 時間信息 -->
					{#if note}
						<div class="space-y-2">
							<div class="flex items-center gap-2">
								<Calendar class="h-4 w-4" />
								<span class="text-sm font-medium">時間信息</span>
							</div>
							<div class="text-sm text-muted-foreground space-y-1">
								<div>創建：{formatDate(note.createdAt)}</div>
								<div>更新：{formatDate(note.updatedAt)}</div>
							</div>
						</div>
					{/if}

					<!-- 連接關係 -->
					{#if connections.length > 0}
						<div class="space-y-2">
							<div class="flex items-center gap-2">
								<Link class="h-4 w-4" />
								<span class="text-sm font-medium">連接關係 ({connections.length})</span>
							</div>
							<div class="space-y-2 max-h-32 overflow-y-auto">
								{#each connections as connection}
									<div
										class="flex items-center justify-between p-2 bg-muted/50 rounded cursor-pointer hover:bg-muted"
										onclick={() => handleHighlightConnection(connection.node.id)}
										onmouseenter={() => handleHighlightConnection(connection.node.id)}
										onmouseleave={() => handleHighlightConnection('')}
									>
										<div class="flex-1 min-w-0">
											<div class="text-sm font-medium truncate">
												{connection.node.title}
											</div>
											<div class="text-xs text-muted-foreground">
												{getTypeLabel(connection.type)} • 強度: {(
													connection.strength * 100
												).toFixed(0)}%
											</div>
										</div>
										<TrendingUp class="h-4 w-4 text-muted-foreground" />
									</div>
								{/each}
							</div>
						</div>
					{/if}

					<!-- 操作按鈕 -->
					<div class="flex gap-2 pt-2">
						<Button onclick={handleViewNote} class="flex-1">
							<FileText class="h-4 w-4 mr-2" />
							查看筆記
						</Button>
						<Button variant="outline" onclick={handleEditNote} class="flex-1">編輯</Button>
					</div>
				</div></Card
			>
		</div>
	</div>
{/if}

<style>
	.node-details-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
		padding: 20px;
	}

	.node-details-panel {
		width: 100%;
		max-width: 400px;
		max-height: 80vh;
		background: white;
		border-radius: 8px;
		box-shadow:
			0 20px 25px -5px rgba(0, 0, 0, 0.1),
			0 10px 10px -5px rgba(0, 0, 0, 0.04);
	}

	.line-clamp-3 {
		display: -webkit-box;
		-webkit-line-clamp: 3;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
</style>
