// 基礎類型定義
export type NoteId = string;
export type UserId = string;
export type TagId = string;
export type CategoryId = string;

// 筆記狀態
export type NoteStatus = 'draft' | 'published' | 'archived';

// 筆記優先級
export type NotePriority = 'low' | 'medium' | 'high' | 'urgent';

// 標籤
export interface Tag {
	id: TagId;
	name: string;
	color?: string;
	description?: string;
	createdAt: Date;
	updatedAt: Date;
}

// 分類
export interface Category {
	id: CategoryId;
	name: string;
	description?: string;
	color?: string;
	parentId?: CategoryId;
	createdAt: Date;
	updatedAt: Date;
}

// 筆記
export interface Note {
	id: NoteId;
	title: string;
	content: string;
	excerpt?: string;
	status: NoteStatus;
	priority: NotePriority;
	authorId: UserId;
	categoryId?: CategoryId;
	tags: Tag[];
	metadata: Record<string, any>;
	createdAt: Date;
	updatedAt: Date;
	publishedAt?: Date;
	version: number;
}

// 用戶
export interface User {
	id: UserId;
	username: string;
	email: string;
	displayName?: string;
	avatar?: string;
	role: 'admin' | 'user';
	status: 'active' | 'inactive' | 'suspended';
	createdAt: Date;
	updatedAt: Date;
}

// 依賴關係
export interface Dependency {
	id: string;
	sourceNoteId: NoteId;
	targetNoteId: NoteId;
	type: 'reference' | 'prerequisite' | 'related';
	strength: number;
	createdAt: Date;
	updatedAt: Date;
}

// 搜索結果
export interface SearchResult {
	note: Note;
	score: number;
	highlights: string[];
}

// 分頁參數
export interface PaginationParams {
	page: number;
	limit: number;
	sortBy?: string;
	sortOrder?: 'asc' | 'desc';
}

// 分頁結果
export interface PaginatedResult<T> {
	items: T[];
	total: number;
	page: number;
	limit: number;
	totalPages: number;
	hasNext: boolean;
	hasPrev: boolean;
}

// 搜索參數
export interface SearchParams extends PaginationParams {
	query?: string;
	tags?: TagId[];
	categories?: CategoryId[];
	status?: NoteStatus;
	priority?: NotePriority;
	authorId?: UserId;
	dateFrom?: Date;
	dateTo?: Date;
}

// API 響應
export interface ApiResponse<T = any> {
	success: boolean;
	data?: T;
	error?: string;
	message?: string;
}

// 統計信息
export interface Statistics {
	totalNotes: number;
	totalUsers: number;
	totalTags: number;
	totalCategories: number;
	totalDependencies: number;
	notesByStatus: Record<NoteStatus, number>;
	notesByPriority: Record<NotePriority, number>;
	recentActivity: {
		date: string;
		count: number;
	}[];
}

// 主題設置
export interface ThemeSettings {
	mode: 'light' | 'dark' | 'system';
	primaryColor: string;
	fontSize: 'small' | 'medium' | 'large';
	fontFamily: string;
}

// 用戶偏好設置
export interface UserPreferences {
	theme: ThemeSettings;
	editor: {
		showLineNumbers: boolean;
		wordWrap: boolean;
		fontSize: number;
		tabSize: number;
		autoSave: boolean;
		autoSaveInterval: number;
	};
	ui: {
		sidebarWidth: number;
		showPreview: boolean;
		compactMode: boolean;
		showMinimap: boolean;
	};
	notifications: {
		desktop: boolean;
		email: boolean;
		sound: boolean;
	};
}

// 應用狀態
export interface AppState {
	loading: boolean;
	error: string | null;
	initialized: boolean;
	user: User | null;
	preferences: UserPreferences;
	sidebarOpen: boolean;
	currentView: string;
}

// 編輯器狀態
export interface EditorState {
	content: string;
	selection: {
		start: number;
		end: number;
	};
	history: {
		undo: string[];
		redo: string[];
	};
	isDirty: boolean;
	isPreviewMode: boolean;
	wordCount: number;
	characterCount: number;
}

// 通知
export interface Notification {
	id: string;
	type: 'info' | 'success' | 'warning' | 'error';
	title: string;
	message: string;
	duration?: number;
	actions?: {
		label: string;
		action: () => void;
	}[];
	createdAt: Date;
}

// 快捷鍵
export interface Shortcut {
	key: string;
	description: string;
	action: () => void;
	category: string;
}

// 搜索相關類型
export interface SearchParams {
	query?: string;
	tags?: string[];
	status?: NoteStatus[];
	priority?: NotePriority[];
	categoryId?: CategoryId;
	authorId?: UserId;
	dateRange?: {
		start?: Date;
		end?: Date;
	};
}

export interface SearchOptions {
	page?: number;
	limit?: number;
	sortBy?: 'relevance' | 'title' | 'createdAt' | 'updatedAt';
	sortOrder?: 'asc' | 'desc';
	includeContent?: boolean;
	fuzzySearch?: boolean;
}

export interface PaginatedResult<T> {
	items: T[];
	page: number;
	limit: number;
	total: number;
	totalPages: number;
	hasNext: boolean;
	hasPrev: boolean;
}

// 筆記版本類型
export interface NoteVersion {
	id: string;
	noteId: NoteId;
	version: string;
	title: string;
	content: string;
	summary: string;
	authorId: UserId;
	authorName: string;
	changeType: 'create' | 'update' | 'delete';
	wordCount: number;
	createdAt: Date;
	isCurrent: boolean;
}

// 筆記創建/更新數據
export interface CreateNoteData {
	title: string;
	content: string;
	tags?: Tag[];
	status?: NoteStatus;
	priority?: NotePriority;
	categoryId?: CategoryId;
}

export interface UpdateNoteData extends Partial<CreateNoteData> {
	excerpt?: string;
}

// 筆記統計
export interface NoteStats {
	total: number;
	byStatus: Record<NoteStatus, number>;
	byPriority: Record<NotePriority, number>;
	totalWords: number;
	averageWords: number;
	recentActivity: {
		created: number;
		updated: number;
		period: string;
	};
}

// 批量操作
export interface BatchOperation {
	type: 'update' | 'delete' | 'archive' | 'publish';
	noteIds: NoteId[];
	data?: Partial<UpdateNoteData>;
}

export interface BatchOperationResult {
	success: boolean;
	processed: number;
	failed: number;
	errors: Array<{
		noteId: NoteId;
		error: string;
	}>;
}

// 導出類型
export type {
	// 重新導出所有類型以便於使用
	NoteId,
	UserId,
	TagId,
	CategoryId,
	NoteStatus,
	NotePriority
};
