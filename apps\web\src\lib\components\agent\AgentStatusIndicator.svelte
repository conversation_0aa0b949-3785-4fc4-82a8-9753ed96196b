<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import {
		<PERSON><PERSON>,
		Wifi,
		WifiOff,
		AlertCircle,
		CheckCircle,
		Loader2,
		Settings,
		Activity
	} from 'lucide-svelte';

	import Button from '$components/ui/Button.svelte';
	import { simpleAgentManager } from '$lib/services/agent/SimpleAgentManager';
	import { goto } from '$app/navigation';

	// Props
	export let showDetails = false;
	export let size: 'sm' | 'md' | 'lg' = 'md';

	// 狀態
	let agentStatus = 'offline';
	let systemStatus: any = null;
	let isConnected = false;
	let lastUpdate = new Date();
	let updateInterval: any = null;

	onMount(() => {
		updateStatus();

		// 監聽 Agent 事件
		simpleAgentManager.addEventListener('agent_registered', handleAgentEvent);
		simpleAgentManager.addEventListener('agent_status_changed', handleAgentEvent);
		simpleAgentManager.addEventListener('task_started', handleAgentEvent);
		simpleAgentManager.addEventListener('task_completed', handleAgentEvent);
		simpleAgentManager.addEventListener('task_failed', handleAgentEvent);

		// 定期更新狀態
		updateInterval = setInterval(updateStatus, 5000);
	});

	onDestroy(() => {
		if (updateInterval) {
			clearInterval(updateInterval);
		}

		// 移除事件監聽器
		simpleAgentManager.removeEventListener('agent_registered', handleAgentEvent);
		simpleAgentManager.removeEventListener('agent_status_changed', handleAgentEvent);
		simpleAgentManager.removeEventListener('task_started', handleAgentEvent);
		simpleAgentManager.removeEventListener('task_completed', handleAgentEvent);
		simpleAgentManager.removeEventListener('task_failed', handleAgentEvent);
	});

	const handleAgentEvent = (event: any) => {
		console.log('Agent event received:', event);
		updateStatus();
	};

	const updateStatus = () => {
		isConnected = simpleAgentManager.isReady();
		systemStatus = simpleAgentManager.getSystemStatus();
		agentStatus = isConnected ? 'connected' : 'offline';
		lastUpdate = new Date();
	};

	const getStatusColor = (status: string) => {
		switch (status) {
			case 'connected':
				return 'text-green-600';
			case 'connecting':
				return 'text-yellow-600';
			case 'error':
				return 'text-red-600';
			default:
				return 'text-gray-600';
		}
	};

	const getStatusIcon = (status: string) => {
		switch (status) {
			case 'connected':
				return CheckCircle;
			case 'connecting':
				return Loader2;
			case 'error':
				return AlertCircle;
			default:
				return WifiOff;
		}
	};

	const getStatusText = (status: string) => {
		switch (status) {
			case 'connected':
				return '已連接';
			case 'connecting':
				return '連接中';
			case 'error':
				return '錯誤';
			default:
				return '離線';
		}
	};

	const getSizeClasses = (size: string) => {
		switch (size) {
			case 'sm':
				return 'h-3 w-3';
			case 'lg':
				return 'h-6 w-6';
			default:
				return 'h-4 w-4';
		}
	};

	const goToAgentSettings = () => {
		goto('/agent');
	};

	const formatUptime = (uptime: number) => {
		const seconds = Math.floor(uptime / 1000);
		const minutes = Math.floor(seconds / 60);
		const hours = Math.floor(minutes / 60);

		if (hours > 0) {
			return `${hours}h ${minutes % 60}m`;
		} else if (minutes > 0) {
			return `${minutes}m ${seconds % 60}s`;
		} else {
			return `${seconds}s`;
		}
	};

	const getLoadColor = (load: number) => {
		if (load < 0.3) return 'bg-green-500';
		if (load < 0.7) return 'bg-yellow-500';
		return 'bg-red-500';
	};
</script>

<div class="agent-status-indicator flex items-center space-x-2">
	<!-- 狀態圖標 -->
	<div class="flex items-center space-x-1">
		<svelte:component
			this={getStatusIcon(agentStatus)}
			class="{getSizeClasses(size)} {getStatusColor(agentStatus)} {agentStatus === 'connecting'
				? 'animate-spin'
				: ''}"
		/>

		{#if size !== 'sm'}
			<span class="text-sm font-medium {getStatusColor(agentStatus)}">
				{getStatusText(agentStatus)}
			</span>
		{/if}
	</div>

	<!-- 詳細信息 -->
	{#if showDetails && systemStatus}
		<div class="flex items-center space-x-3 text-sm text-muted-foreground">
			<!-- 任務狀態 -->
			<div class="flex items-center space-x-1">
				<Activity class="h-3 w-3" />
				<span>{systemStatus.runningTasks}/{systemStatus.totalTasks}</span>
			</div>

			<!-- 系統負載 -->
			<div class="flex items-center space-x-1">
				<div class="w-8 bg-gray-200 rounded-full h-1.5">
					<div
						class="h-1.5 rounded-full transition-all duration-300 {getLoadColor(
							systemStatus.systemLoad
						)}"
						style="width: {(systemStatus.systemLoad * 100).toFixed(0)}%"
					></div>
				</div>
				<span class="text-xs">{(systemStatus.systemLoad * 100).toFixed(0)}%</span>
			</div>

			<!-- 運行時間 -->
			{#if systemStatus.uptime > 0}
				<span class="text-xs">
					{formatUptime(systemStatus.uptime)}
				</span>
			{/if}
		</div>
	{/if}

	<!-- 設置按鈕 -->
	{#if !isConnected && size !== 'sm'}
		<Button variant="ghost" size="sm" on:click={goToAgentSettings} class="text-xs">
			<Settings class="h-3 w-3 mr-1" />
			設置
		</Button>
	{/if}
</div>

<!-- 詳細狀態面板（可選） -->
{#if showDetails && isConnected && systemStatus}
	<div class="mt-2 p-3 bg-muted/30 rounded-lg text-sm">
		<div class="grid grid-cols-2 md:grid-cols-4 gap-3">
			<div class="text-center">
				<div class="font-medium text-blue-600">{systemStatus.totalAgents}</div>
				<div class="text-xs text-muted-foreground">總 Agent</div>
			</div>

			<div class="text-center">
				<div class="font-medium text-green-600">{systemStatus.activeAgents}</div>
				<div class="text-xs text-muted-foreground">活躍</div>
			</div>

			<div class="text-center">
				<div class="font-medium text-purple-600">{systemStatus.runningTasks}</div>
				<div class="text-xs text-muted-foreground">運行中</div>
			</div>

			<div class="text-center">
				<div class="font-medium text-orange-600">{systemStatus.queuedTasks}</div>
				<div class="text-xs text-muted-foreground">隊列中</div>
			</div>
		</div>

		<div class="mt-3 pt-3 border-t border-border">
			<div class="flex justify-between items-center text-xs text-muted-foreground">
				<span>上次更新：{lastUpdate.toLocaleTimeString()}</span>
				<span>總任務：{systemStatus.totalTasks}</span>
			</div>
		</div>
	</div>
{/if}

<style>
	.agent-status-indicator {
		user-select: none;
	}
</style>
