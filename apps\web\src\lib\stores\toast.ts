import { writable } from 'svelte/store';

export interface ToastData {
	id: string;
	variant?: 'default' | 'success' | 'error' | 'warning' | 'info';
	title?: string;
	description?: string;
	duration?: number;
	closable?: boolean;
}

interface ToastState {
	toasts: ToastData[];
}

const initialState: ToastState = {
	toasts: []
};

function createToastStore() {
	const { subscribe, update } = writable<ToastState>(initialState);

	const generateId = () => Math.random().toString(36).substr(2, 9);

	return {
		subscribe,

		// Add a new toast
		add(toast: Omit<ToastData, 'id'>) {
			const id = generateId();
			const newToast: ToastData = {
				id,
				variant: 'default',
				duration: 5000,
				closable: true,
				...toast
			};

			update(state => ({
				...state,
				toasts: [...state.toasts, newToast]
			}));

			return id;
		},

		// Remove a toast by ID
		remove(id: string) {
			update(state => ({
				...state,
				toasts: state.toasts.filter(toast => toast.id !== id)
			}));
		},

		// Clear all toasts
		clear() {
			update(state => ({
				...state,
				toasts: []
			}));
		},

		// Convenience methods
		success(title: string, description?: string, options?: Partial<ToastData>) {
			return this.add({
				variant: 'success',
				title,
				description,
				...options
			});
		},

		error(title: string, description?: string, options?: Partial<ToastData>) {
			return this.add({
				variant: 'error',
				title,
				description,
				duration: 0, // Don't auto-dismiss errors
				...options
			});
		},

		warning(title: string, description?: string, options?: Partial<ToastData>) {
			return this.add({
				variant: 'warning',
				title,
				description,
				...options
			});
		},

		info(title: string, description?: string, options?: Partial<ToastData>) {
			return this.add({
				variant: 'info',
				title,
				description,
				...options
			});
		}
	};
}

export const toastStore = createToastStore();
