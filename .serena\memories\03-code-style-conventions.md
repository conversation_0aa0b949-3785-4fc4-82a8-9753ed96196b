# 程式碼風格統一規範

## 基於 Serena 分析的程式碼規範

### 技術棧配置

基於階段2技術規範和 Context7 搜索結果，確定以下配置：

#### 核心技術棧

- **React 18+ + TypeScript 5.0+**：主要開發框架
- **Vite 4.0+**：構建工具，支援最新 ES 模組
- **Tauri 1.5+**：跨平台桌面應用框架
- **MCP TypeScript SDK**：AI 服務整合

## Context7 搜索的最佳實踐

### TypeScript 配置最佳實踐

基於 `/microsoft/typescript` 和 `/typescript-eslint/typescript-eslint` 的最新實踐：

```json
// tsconfig.json
{
  "compilerOptions": {
    "target": "ES2022",
    "lib": ["ES2022", "DOM", "DOM.Iterable"],
    "allowJs": false,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true,
    "exactOptionalPropertyTypes": true,
    "forceConsistentCasingInFileNames": true,
    "module": "ESNext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@/components/*": ["./src/components/*"],
      "@/hooks/*": ["./src/hooks/*"],
      "@/stores/*": ["./src/stores/*"],
      "@/services/*": ["./src/services/*"],
      "@/types/*": ["./src/types/*"],
      "@/utils/*": ["./src/utils/*"]
    }
  },
  "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue"],
  "exclude": ["node_modules", "dist", "build"]
}
```

### ESLint 配置

基於 `/typescript-eslint/typescript-eslint` 最佳實踐：

```javascript
// .eslintrc.js
module.exports = {
  root: true,
  env: {
    browser: true,
    es2022: true,
    node: true,
  },
  extends: [
    "eslint:recommended",
    "@typescript-eslint/recommended",
    "@typescript-eslint/recommended-requiring-type-checking",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended",
    "plugin:jsx-a11y/recommended",
    "plugin:import/recommended",
    "plugin:import/typescript",
    "prettier",
  ],
  parser: "@typescript-eslint/parser",
  parserOptions: {
    ecmaVersion: "latest",
    sourceType: "module",
    project: "./tsconfig.json",
    ecmaFeatures: {
      jsx: true,
    },
  },
  plugins: ["@typescript-eslint", "react", "react-hooks", "jsx-a11y", "import"],
  rules: {
    // TypeScript 特定規則
    "@typescript-eslint/no-unused-vars": ["error", { argsIgnorePattern: "^_" }],
    "@typescript-eslint/explicit-function-return-type": "warn",
    "@typescript-eslint/no-explicit-any": "error",
    "@typescript-eslint/prefer-nullish-coalescing": "error",
    "@typescript-eslint/prefer-optional-chain": "error",
    "@typescript-eslint/strict-boolean-expressions": "error",

    // React 特定規則
    "react/react-in-jsx-scope": "off",
    "react/prop-types": "off",
    "react-hooks/rules-of-hooks": "error",
    "react-hooks/exhaustive-deps": "warn",

    // Import 規則
    "import/order": [
      "error",
      {
        groups: [
          "builtin",
          "external",
          "internal",
          "parent",
          "sibling",
          "index",
        ],
        "newlines-between": "always",
        alphabetize: {
          order: "asc",
          caseInsensitive: true,
        },
      },
    ],

    // 一般規則
    "no-console": ["warn", { allow: ["warn", "error"] }],
    "prefer-const": "error",
    "no-var": "error",
  },
  settings: {
    react: {
      version: "detect",
    },
    "import/resolver": {
      typescript: {
        alwaysTryTypes: true,
        project: "./tsconfig.json",
      },
    },
  },
};
```

### Prettier 配置

```json
// .prettierrc
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false,
  "bracketSpacing": true,
  "bracketSameLine": false,
  "arrowParens": "avoid",
  "endOfLine": "lf",
  "quoteProps": "as-needed",
  "jsxSingleQuote": true,
  "proseWrap": "preserve"
}
```

## 具體規範

### 檔案和目錄命名規範

#### 檔案命名

```typescript
// ✅ 正確的檔案命名
// 組件檔案：PascalCase
NoteEditor.tsx;
DependencyGraph.tsx;
UserProfile.tsx;

// Hook 檔案：camelCase with use prefix
useNote.ts;
useDependency.ts;
useLocalStorage.ts;

// 服務檔案：camelCase with Service suffix
noteService.ts;
dependencyService.ts;
aiService.ts;

// 類型檔案：camelCase
note.ts;
dependency.ts;
api.ts;

// 常量檔案：UPPER_SNAKE_CASE
API_ENDPOINTS.ts;
ERROR_MESSAGES.ts;
CONFIG_DEFAULTS.ts;

// 工具函數：camelCase
formatDate.ts;
validateEmail.ts;
parseMarkdown.ts;
```

#### 目錄命名

```
// ✅ 正確的目錄命名
src/
├── components/           # PascalCase 組件目錄
│   ├── NoteEditor/
│   ├── DependencyGraph/
│   └── UserProfile/
├── hooks/               # camelCase 功能目錄
├── services/            # camelCase 功能目錄
├── stores/              # camelCase 功能目錄
├── types/               # camelCase 功能目錄
├── utils/               # camelCase 功能目錄
└── note-management/     # kebab-case 功能模組目錄
```

### 程式碼命名規範

#### TypeScript 類型和介面

```typescript
// ✅ 正確的類型命名
// 介面：PascalCase with I prefix
interface INoteRepository {
  findById(id: string): Promise<Note | null>;
  save(note: Note): Promise<void>;
}

// 類型別名：PascalCase
type NoteStatus = "draft" | "published" | "archived";
type ApiResponse<T> = {
  data: T;
  status: number;
  message: string;
};

// 泛型：單個大寫字母
interface Repository<T, K> {
  findById(id: K): Promise<T | null>;
  save(entity: T): Promise<void>;
}

// 枚舉：PascalCase
enum NoteType {
  MARKDOWN = "markdown",
  RICH_TEXT = "rich_text",
  CODE = "code",
}
```

#### 類別和函數命名

```typescript
// ✅ 正確的類別命名
class NoteService {
  private readonly repository: INoteRepository;

  constructor(repository: INoteRepository) {
    this.repository = repository;
  }

  // 方法：camelCase
  async createNote(content: string): Promise<Note> {
    // 實現
  }

  async publishNote(id: string): Promise<void> {
    // 實現
  }
}

// ✅ 正確的函數命名
// 純函數：camelCase
function formatNoteTitle(title: string): string {
  return title.trim().toLowerCase();
}

// 異步函數：camelCase with async prefix (可選)
async function fetchNoteById(id: string): Promise<Note> {
  // 實現
}

// 事件處理函數：camelCase with handle prefix
function handleNoteClick(note: Note): void {
  // 實現
}

// 布林函數：camelCase with is/has/can prefix
function isNotePublished(note: Note): boolean {
  return note.status === "published";
}

function hasUnsavedChanges(note: Note): boolean {
  return note.isDirty;
}
```

#### 變數和常量命名

```typescript
// ✅ 正確的變數命名
// 一般變數：camelCase
const noteContent = "Hello World";
const userPreferences = { theme: "dark" };
const isLoading = false;

// 常量：UPPER_SNAKE_CASE
const MAX_NOTE_SIZE = 1024 * 1024; // 1MB
const API_BASE_URL = "https://api.example.com";
const DEFAULT_TIMEOUT = 5000;

// 私有成員：camelCase with underscore prefix
class NoteEditor {
  private _content: string = "";
  private _isDirty: boolean = false;

  get content(): string {
    return this._content;
  }
}
```

### React 組件規範

#### 組件結構

```typescript
// ✅ 正確的組件結構
import React, { useState, useEffect, useCallback } from 'react';
import { z } from 'zod';

import { Button } from '@/components/common/Button';
import { useNote } from '@/hooks/useNote';
import { noteService } from '@/services/noteService';
import type { Note, NoteStatus } from '@/types/note';

// Props 介面定義
interface NoteEditorProps {
  noteId?: string;
  initialContent?: string;
  onSave?: (note: Note) => void;
  onCancel?: () => void;
  className?: string;
}

// Props 驗證 schema
const NoteEditorPropsSchema = z.object({
  noteId: z.string().optional(),
  initialContent: z.string().optional(),
  onSave: z.function().optional(),
  onCancel: z.function().optional(),
  className: z.string().optional(),
});

// 組件實現
export const NoteEditor: React.FC<NoteEditorProps> = ({
  noteId,
  initialContent = '',
  onSave,
  onCancel,
  className = '',
}) => {
  // State hooks
  const [content, setContent] = useState<string>(initialContent);
  const [status, setStatus] = useState<NoteStatus>('draft');

  // Custom hooks
  const { note, loading, error } = useNote(noteId);

  // Effects
  useEffect(() => {
    if (note) {
      setContent(note.content);
      setStatus(note.status);
    }
  }, [note]);

  // Event handlers
  const handleSave = useCallback(async (): Promise<void> => {
    try {
      const savedNote = await noteService.save({
        id: noteId,
        content,
        status,
      });
      onSave?.(savedNote);
    } catch (error) {
      console.error('Failed to save note:', error);
    }
  }, [noteId, content, status, onSave]);

  const handleCancel = useCallback((): void => {
    onCancel?.();
  }, [onCancel]);

  // Render
  return (
    <div className={`note-editor ${className}`}>
      <textarea
        value={content}
        onChange={e => setContent(e.target.value)}
        placeholder="開始寫筆記..."
        className="w-full h-64 p-4 border rounded"
      />
      <div className="flex gap-2 mt-4">
        <Button onClick={handleSave} disabled={loading}>
          儲存
        </Button>
        <Button variant="secondary" onClick={handleCancel}>
          取消
        </Button>
      </div>
    </div>
  );
};

// 預設 props
NoteEditor.defaultProps = {
  initialContent: '',
  className: '',
};
```

#### Hook 規範

```typescript
// ✅ 正確的 Hook 實現
import { useState, useEffect, useCallback } from "react";

import { noteService } from "@/services/noteService";
import type { Note } from "@/types/note";

interface UseNoteReturn {
  note: Note | null;
  loading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
}

export function useNote(noteId?: string): UseNoteReturn {
  const [note, setNote] = useState<Note | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchNote = useCallback(async (): Promise<void> => {
    if (!noteId) return;

    try {
      setLoading(true);
      setError(null);
      const fetchedNote = await noteService.findById(noteId);
      setNote(fetchedNote);
    } catch (err) {
      setError(err instanceof Error ? err : new Error("Unknown error"));
    } finally {
      setLoading(false);
    }
  }, [noteId]);

  useEffect(() => {
    void fetchNote();
  }, [fetchNote]);

  return {
    note,
    loading,
    error,
    refetch: fetchNote,
  };
}
```

### MCP Server 實現規範

基於 Context7 搜索的 `/modelcontextprotocol/typescript-sdk` 最佳實踐：

```typescript
// ✅ 正確的 MCP Server 實現
import {
  McpServer,
  ResourceTemplate,
} from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { z } from "zod";

import type { Note } from "@/types/note";
import { noteService } from "@/services/noteService";

// MCP Server 配置
const server = new McpServer({
  name: "life-note-mcp-server",
  version: "1.0.0",
});

// 註冊筆記工具
server.registerTool(
  "create-note",
  {
    title: "創建筆記",
    description: "創建新的筆記",
    inputSchema: {
      title: z.string().min(1, "標題不能為空"),
      content: z.string(),
      tags: z.array(z.string()).optional(),
    },
  },
  async ({
    title,
    content,
    tags = [],
  }): Promise<{ content: Array<{ type: string; text: string }> }> => {
    try {
      const note = await noteService.create({
        title,
        content,
        tags,
        status: "draft",
      });

      return {
        content: [
          {
            type: "text",
            text: `筆記創建成功：${note.title} (ID: ${note.id})`,
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: "text",
            text: `創建筆記失敗：${error instanceof Error ? error.message : "未知錯誤"}`,
          },
        ],
      };
    }
  },
);

// 註冊筆記資源
server.registerResource(
  "note",
  new ResourceTemplate("note://{noteId}", { list: undefined }),
  {
    title: "筆記資源",
    description: "獲取特定筆記的內容",
  },
  async (
    uri,
    { noteId },
  ): Promise<{ contents: Array<{ uri: string; text: string }> }> => {
    try {
      const note = await noteService.findById(noteId);
      if (!note) {
        throw new Error(`筆記不存在：${noteId}`);
      }

      return {
        contents: [
          {
            uri: uri.href,
            text: `# ${note.title}\n\n${note.content}`,
          },
        ],
      };
    } catch (error) {
      throw new Error(
        `獲取筆記失敗：${error instanceof Error ? error.message : "未知錯誤"}`,
      );
    }
  },
);

// 啟動 MCP Server
async function startMcpServer(): Promise<void> {
  const transport = new StdioServerTransport();
  await server.connect(transport);
  console.log("MCP Server 已啟動");
}

export { startMcpServer };
```

### 錯誤處理規範

```typescript
// ✅ 正確的錯誤處理
// 自定義錯誤類型
export class NoteNotFoundError extends Error {
  constructor(noteId: string) {
    super(`筆記不存在：${noteId}`);
    this.name = "NoteNotFoundError";
  }
}

export class ValidationError extends Error {
  constructor(
    message: string,
    public field: string,
  ) {
    super(message);
    this.name = "ValidationError";
  }
}

// 錯誤處理函數
export function handleApiError(error: unknown): string {
  if (error instanceof NoteNotFoundError) {
    return "找不到指定的筆記";
  }

  if (error instanceof ValidationError) {
    return `驗證錯誤：${error.message}`;
  }

  if (error instanceof Error) {
    return error.message;
  }

  return "發生未知錯誤";
}

// 異步函數錯誤處理
export async function safeAsyncOperation<T>(
  operation: () => Promise<T>,
): Promise<[T | null, Error | null]> {
  try {
    const result = await operation();
    return [result, null];
  } catch (error) {
    return [null, error instanceof Error ? error : new Error("Unknown error")];
  }
}
```

### 測試規範

```typescript
// ✅ 正確的測試實現
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';

import { NoteEditor } from '@/components/NoteEditor';
import { noteService } from '@/services/noteService';
import type { Note } from '@/types/note';

// Mock 服務
vi.mock('@/services/noteService');

describe('NoteEditor', () => {
  const mockNote: Note = {
    id: '1',
    title: '測試筆記',
    content: '測試內容',
    status: 'draft',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('應該正確渲染初始內容', () => {
    render(<NoteEditor initialContent="初始內容" />);

    const textarea = screen.getByPlaceholderText('開始寫筆記...');
    expect(textarea).toHaveValue('初始內容');
  });

  it('應該在點擊儲存時調用 onSave', async () => {
    const mockOnSave = vi.fn();
    vi.mocked(noteService.save).mockResolvedValue(mockNote);

    render(<NoteEditor onSave={mockOnSave} />);

    const saveButton = screen.getByText('儲存');
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(mockOnSave).toHaveBeenCalledWith(mockNote);
    });
  });

  it('應該處理儲存錯誤', async () => {
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    vi.mocked(noteService.save).mockRejectedValue(new Error('儲存失敗'));

    render(<NoteEditor />);

    const saveButton = screen.getByText('儲存');
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith('Failed to save note:', expect.any(Error));
    });

    consoleSpy.mockRestore();
  });
});
```

## 程式碼範例（基於 context7 搜索）

### Tauri 整合範例

基於 `/specta-rs/tauri-specta` 的最佳實踐：

```typescript
// src/services/tauriService.ts
import { invoke } from "@tauri-apps/api/tauri";

export interface TauriNoteService {
  saveNote: (note: Note) => Promise<void>;
  loadNote: (id: string) => Promise<Note>;
  deleteNote: (id: string) => Promise<void>;
}

export const tauriNoteService: TauriNoteService = {
  async saveNote(note: Note): Promise<void> {
    await invoke("save_note", { note });
  },

  async loadNote(id: string): Promise<Note> {
    return await invoke("load_note", { id });
  },

  async deleteNote(id: string): Promise<void> {
    await invoke("delete_note", { id });
  },
};
```

這些規範確保了代碼的一致性、可維護性和最佳實踐的遵循。
