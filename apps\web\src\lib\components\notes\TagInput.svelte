<script lang="ts">
	import { createEventDispatcher, onMount } from 'svelte';
	import { Tag, X, Plus } from 'lucide-svelte';
	import { Button } from '$components/ui';
	import TagList from './TagList.svelte';

	export let tags: Array<{ name: string }> = [];
	export let placeholder: string = '添加標籤...';
	export let maxTags: number = 10;
	export let allowDuplicates: boolean = false;
	export let suggestions: Array<{ name: string }> = [];
	export let className: string = '';

	const dispatch = createEventDispatcher<{
		change: Array<{ name: string }>;
		tagAdd: { tag: { name: string } };
		tagRemove: { tag: { name: string } };
	}>();

	let inputValue = '';
	let inputElement: HTMLInputElement;
	let showSuggestions = false;
	let filteredSuggestions: Array<{ name: string }> = [];
	let selectedSuggestionIndex = -1;

	// 預設建議標籤
	const defaultSuggestions = [
		{ name: '工作' },
		{ name: '學習' },
		{ name: '項目' },
		{ name: '想法' },
		{ name: '筆記' },
		{ name: '重要' },
		{ name: '待辦' },
		{ name: '會議' },
		{ name: '計劃' },
		{ name: '總結' }
	];

	$: allSuggestions = [...suggestions, ...defaultSuggestions];
	$: {
		if (inputValue.trim()) {
			filteredSuggestions = allSuggestions.filter(
				suggestion =>
					suggestion.name.toLowerCase().includes(inputValue.toLowerCase()) &&
					(allowDuplicates || !tags.some(tag => tag.name === suggestion.name))
			);
			showSuggestions = filteredSuggestions.length > 0;
		} else {
			filteredSuggestions = [];
			showSuggestions = false;
		}
		selectedSuggestionIndex = -1;
	}

	function addTag(tagName: string) {
		const trimmedName = tagName.trim();
		if (!trimmedName) return;

		// 檢查是否已存在（如果不允許重複）
		if (!allowDuplicates && tags.some(tag => tag.name === trimmedName)) {
			return;
		}

		// 檢查是否超過最大數量
		if (tags.length >= maxTags) {
			return;
		}

		const newTag = { name: trimmedName };
		const newTags = [...tags, newTag];

		tags = newTags;
		inputValue = '';
		showSuggestions = false;

		dispatch('change', newTags);
		dispatch('tagAdd', { tag: newTag });
	}

	function removeTag(tag: { name: string }) {
		const newTags = tags.filter(t => t.name !== tag.name);
		tags = newTags;

		dispatch('change', newTags);
		dispatch('tagRemove', { tag });
	}

	function handleKeyDown(event: KeyboardEvent) {
		switch (event.key) {
			case 'Enter':
				event.preventDefault();
				if (selectedSuggestionIndex >= 0 && filteredSuggestions[selectedSuggestionIndex]) {
					addTag(filteredSuggestions[selectedSuggestionIndex].name);
				} else if (inputValue.trim()) {
					addTag(inputValue);
				}
				break;

			case 'ArrowDown':
				event.preventDefault();
				if (showSuggestions) {
					selectedSuggestionIndex = Math.min(
						selectedSuggestionIndex + 1,
						filteredSuggestions.length - 1
					);
				}
				break;

			case 'ArrowUp':
				event.preventDefault();
				if (showSuggestions) {
					selectedSuggestionIndex = Math.max(selectedSuggestionIndex - 1, -1);
				}
				break;

			case 'Escape':
				showSuggestions = false;
				selectedSuggestionIndex = -1;
				break;

			case 'Backspace':
				if (!inputValue && tags.length > 0) {
					removeTag(tags[tags.length - 1]);
				}
				break;

			case ',':
			case ';':
				event.preventDefault();
				if (inputValue.trim()) {
					addTag(inputValue);
				}
				break;
		}
	}

	function handleSuggestionClick(suggestion: { name: string }) {
		addTag(suggestion.name);
		inputElement.focus();
	}

	function handleInputFocus() {
		if (inputValue.trim()) {
			showSuggestions = filteredSuggestions.length > 0;
		}
	}

	function handleInputBlur() {
		// 延遲隱藏建議，讓點擊事件能夠觸發
		setTimeout(() => {
			showSuggestions = false;
		}, 200);
	}

	onMount(() => {
		return () => {
			// 清理
		};
	});
</script>

<div class="tag-input {className}">
	<!-- 已添加的標籤 -->
	{#if tags.length > 0}
		<div class="mb-3">
			<TagList {tags} editable={true} size="sm" ontagRemove={e => removeTag(e.detail.tag)} />
		</div>
	{/if}

	<!-- 輸入區域 -->
	<div class="input-container">
		<div class="input-wrapper">
			<Tag class="input-icon" />
			<input
				bind:this={inputElement}
				bind:value={inputValue}
				type="text"
				{placeholder}
				class="tag-input-field"
				onkeydown={handleKeyDown}
				onfocus={handleInputFocus}
				onblur={handleInputBlur}
				disabled={tags.length >= maxTags}
			/>

			{#if inputValue.trim()}
				<Button
					variant="ghost"
					size="sm"
					class="add-button"
					onclick={() => addTag(inputValue)}
					title="添加標籤"
				>
					<Plus class="h-3 w-3" />
				</Button>
			{/if}
		</div>

		<!-- 建議列表 -->
		{#if showSuggestions}
			<div class="suggestions-dropdown">
				{#each filteredSuggestions as suggestion, index}
					<button
						type="button"
						class="suggestion-item"
						class:selected={index === selectedSuggestionIndex}
						onclick={() => handleSuggestionClick(suggestion)}
					>
						<Tag class="h-3 w-3" />
						<span>{suggestion.name}</span>
					</button>
				{/each}
			</div>
		{/if}
	</div>

	<!-- 提示信息 -->
	<div class="help-text">
		<span class="text-xs text-muted-foreground">
			按 Enter、逗號或分號添加標籤 • 最多 {maxTags} 個標籤 • 已添加 {tags.length} 個
		</span>
	</div>
</div>

<style>
	.tag-input {
		position: relative;
	}

	.input-container {
		position: relative;
	}

	.input-wrapper {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		padding: 0.5rem 0.75rem;
		border: 1px solid hsl(var(--border));
		border-radius: 0.375rem;
		background: hsl(var(--background));
		transition: border-color 0.2s ease;
	}

	.input-wrapper:focus-within {
		border-color: hsl(var(--primary));
		box-shadow: 0 0 0 2px hsl(var(--primary) / 0.2);
	}

	.input-icon {
		width: 1rem;
		height: 1rem;
		color: hsl(var(--muted-foreground));
		flex-shrink: 0;
	}

	.tag-input-field {
		flex: 1;
		border: none;
		outline: none;
		background: transparent;
		color: hsl(var(--foreground));
		font-size: 0.875rem;
	}

	.tag-input-field::placeholder {
		color: hsl(var(--muted-foreground));
	}

	.tag-input-field:disabled {
		opacity: 0.5;
		cursor: not-allowed;
	}

	.add-button {
		padding: 0.25rem;
		height: auto;
		min-height: 1.5rem;
		width: 1.5rem;
	}

	.suggestions-dropdown {
		position: absolute;
		top: 100%;
		left: 0;
		right: 0;
		z-index: 50;
		margin-top: 0.25rem;
		background: hsl(var(--popover));
		border: 1px solid hsl(var(--border));
		border-radius: 0.375rem;
		box-shadow:
			0 4px 6px -1px rgba(0, 0, 0, 0.1),
			0 2px 4px -1px rgba(0, 0, 0, 0.06);
		max-height: 200px;
		overflow-y: auto;
	}

	.suggestion-item {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		width: 100%;
		padding: 0.5rem 0.75rem;
		text-align: left;
		font-size: 0.875rem;
		color: hsl(var(--popover-foreground));
		background: transparent;
		border: none;
		cursor: pointer;
		transition: background-color 0.2s ease;
	}

	.suggestion-item:hover,
	.suggestion-item.selected {
		background: hsl(var(--accent));
		color: hsl(var(--accent-foreground));
	}

	.suggestion-item:first-child {
		border-top-left-radius: 0.375rem;
		border-top-right-radius: 0.375rem;
	}

	.suggestion-item:last-child {
		border-bottom-left-radius: 0.375rem;
		border-bottom-right-radius: 0.375rem;
	}

	.help-text {
		margin-top: 0.5rem;
	}

	/* 滾動條樣式 */
	.suggestions-dropdown::-webkit-scrollbar {
		width: 6px;
	}

	.suggestions-dropdown::-webkit-scrollbar-track {
		background: hsl(var(--muted));
	}

	.suggestions-dropdown::-webkit-scrollbar-thumb {
		background: hsl(var(--muted-foreground) / 0.3);
		border-radius: 3px;
	}

	.suggestions-dropdown::-webkit-scrollbar-thumb:hover {
		background: hsl(var(--muted-foreground) / 0.5);
	}

	/* 響應式設計 */
	@media (max-width: 640px) {
		.input-wrapper {
			padding: 0.375rem 0.5rem;
		}

		.tag-input-field {
			font-size: 0.8rem;
		}

		.help-text {
			font-size: 0.75rem;
		}
	}

	/* 動畫效果 */
	.suggestions-dropdown {
		animation: slideDown 0.2s ease-out;
	}

	@keyframes slideDown {
		from {
			opacity: 0;
			transform: translateY(-0.5rem);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}
</style>
