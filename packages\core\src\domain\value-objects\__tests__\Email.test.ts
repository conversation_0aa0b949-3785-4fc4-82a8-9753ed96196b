import { describe, it, expect } from "vitest";
import { Email } from "../Email.js";
import { BusinessRuleViolationError } from "../../../shared/Entity.js";

describe("Email Value Object", () => {
  describe("Valid Email Creation", () => {
    it("should create email with valid format", () => {
      const email = new Email("<EMAIL>");
      expect(email.value).toBe("<EMAIL>");
    });

    it("should normalize email to lowercase", () => {
      const email = new Email("<EMAIL>");
      expect(email.value).toBe("<EMAIL>");
    });

    it("should trim whitespace", () => {
      const email = new Email("  <EMAIL>  ");
      expect(email.value).toBe("<EMAIL>");
    });

    it("should handle complex valid emails", () => {
      const validEmails = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
      ];

      validEmails.forEach((emailStr) => {
        expect(() => new Email(emailStr)).not.toThrow();
      });
    });
  });

  describe("Invalid Email Validation", () => {
    it("should reject empty email", () => {
      expect(() => new Email("")).toThrow(BusinessRuleViolationError);
      expect(() => new Email("   ")).toThrow(BusinessRuleViolationError);
    });

    it("should reject email without @ symbol", () => {
      expect(() => new Email("testexample.com")).toThrow(
        BusinessRuleViolationError,
      );
    });

    it("should reject email without domain", () => {
      expect(() => new Email("test@")).toThrow(BusinessRuleViolationError);
    });

    it("should reject email without local part", () => {
      expect(() => new Email("@example.com")).toThrow(
        BusinessRuleViolationError,
      );
    });

    it("should reject email with consecutive dots", () => {
      expect(() => new Email("<EMAIL>")).toThrow(
        BusinessRuleViolationError,
      );
    });

    it("should reject email starting or ending with dot", () => {
      expect(() => new Email(".<EMAIL>")).toThrow(
        BusinessRuleViolationError,
      );
      expect(() => new Email("<EMAIL>")).toThrow(
        BusinessRuleViolationError,
      );
    });

    it("should reject email with invalid domain", () => {
      expect(() => new Email("test@")).toThrow(BusinessRuleViolationError);
      expect(() => new Email("test@domain")).toThrow(
        BusinessRuleViolationError,
      );
    });

    it("should reject too long email", () => {
      const longEmail = "a".repeat(250) + "@example.com";
      expect(() => new Email(longEmail)).toThrow(BusinessRuleViolationError);
    });

    it("should reject too long local part", () => {
      const longLocal = "a".repeat(65) + "@example.com";
      expect(() => new Email(longLocal)).toThrow(BusinessRuleViolationError);
    });
  });

  describe("Email Properties", () => {
    const email = new Email("<EMAIL>");

    it("should extract local part correctly", () => {
      expect(email.localPart).toBe("user.name+tag");
    });

    it("should extract domain part correctly", () => {
      expect(email.domainPart).toBe("sub.example.com");
    });

    it("should extract top level domain correctly", () => {
      expect(email.topLevelDomain).toBe("com");
    });

    it("should check domain correctly", () => {
      expect(email.isFromDomain("sub.example.com")).toBe(true);
      expect(email.isFromDomain("example.com")).toBe(false);
    });
  });

  describe("Business Email Detection", () => {
    it("should identify business emails", () => {
      const businessEmail = new Email("<EMAIL>");
      expect(businessEmail.isBusinessEmail()).toBe(true);
    });

    it("should identify free email providers", () => {
      const freeEmails = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
      ];

      freeEmails.forEach((emailStr) => {
        const email = new Email(emailStr);
        expect(email.isBusinessEmail()).toBe(false);
      });
    });
  });

  describe("Email Masking", () => {
    it("should mask short emails", () => {
      const email = new Email("<EMAIL>");
      expect(email.toMaskedString()).toBe("a*@example.com");
    });

    it("should mask normal emails", () => {
      const email = new Email("<EMAIL>");
      expect(email.toMaskedString()).toBe("t******<EMAIL>");
    });

    it("should mask single character local part", () => {
      const email = new Email("<EMAIL>");
      expect(email.toMaskedString()).toBe("a*@example.com");
    });
  });

  describe("Value Object Behavior", () => {
    it("should be equal to another email with same value", () => {
      const email1 = new Email("<EMAIL>");
      const email2 = new Email("<EMAIL>");
      expect(email1.equals(email2)).toBe(true);
    });

    it("should not be equal to different email", () => {
      const email1 = new Email("<EMAIL>");
      const email2 = new Email("<EMAIL>");
      expect(email1.equals(email2)).toBe(false);
    });

    it("should not be equal to non-email object", () => {
      const email = new Email("<EMAIL>");
      expect(email.equals({} as any)).toBe(false);
    });

    it("should have consistent hash code", () => {
      const email1 = new Email("<EMAIL>");
      const email2 = new Email("<EMAIL>");
      expect(email1.hashCode()).toBe(email2.hashCode());
    });

    it("should convert to string correctly", () => {
      const email = new Email("<EMAIL>");
      expect(email.toString()).toBe("<EMAIL>");
    });
  });

  describe("Serialization", () => {
    it("should serialize to plain object", () => {
      const email = new Email("<EMAIL>");
      const plainObject = email.toPlainObject();

      expect(plainObject).toEqual({
        value: "<EMAIL>",
        localPart: "user",
        domainPart: "company.com",
        topLevelDomain: "com",
        isBusinessEmail: true,
      });
    });

    it("should deserialize from plain object", () => {
      const plainObject = {
        value: "<EMAIL>",
        localPart: "test",
        domainPart: "example.com",
        topLevelDomain: "com",
        isBusinessEmail: true,
      };

      const email = Email.fromPlainObject(plainObject);
      expect(email.value).toBe("<EMAIL>");
    });

    it("should throw error for invalid plain object", () => {
      const invalidObject = { value: 123 };
      expect(() => Email.fromPlainObject(invalidObject)).toThrow(
        BusinessRuleViolationError,
      );
    });
  });

  describe("Static Factory Methods", () => {
    it("should create email using create method", () => {
      const email = Email.create("<EMAIL>");
      expect(email.value).toBe("<EMAIL>");
    });

    it("should create email using fromString method", () => {
      const email = Email.fromString("<EMAIL>");
      expect(email.value).toBe("<EMAIL>");
    });
  });
});
