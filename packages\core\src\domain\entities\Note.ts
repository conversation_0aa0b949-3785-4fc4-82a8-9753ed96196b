/* eslint-disable @typescript-eslint/no-unused-vars */
import { z } from "zod";

import {
  AggregateRoot,
  BusinessRuleViolationError,
} from "../../shared/Entity.js";
import {
  NoteCreatedEvent,
  NotePublishedEvent,
  NoteUpdatedEvent,
} from "../events/NoteEvents.js";
import { NoteId } from "../value-objects/NoteId.js";
import { Tag } from "../value-objects/Tag.js";
import { Version } from "../value-objects/Version.js";

/**
 * 筆記狀態枚舉
 */
export enum NoteStatus {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  DRAFT = "draft",
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  PUBLISHED = "published",
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  ARCHIVED = "archived",
}

/**
 * 筆記優先級枚舉
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export enum NotePriority {
  LOW = "low",
  MEDIUM = "medium",
  HIGH = "high",
  URGENT = "urgent",
}

/**
 * 筆記創建參數
 */
export interface CreateNoteParams {
  title: string;
  content: string;
  category?: string;
  tags?: Tag[];
  priority?: NotePriority;
  metadata?: Record<string, unknown>;
}

/**
 * 筆記更新參數
 */
export interface UpdateNoteParams {
  title?: string;
  content?: string;
  category?: string;
  tags?: Tag[];
  priority?: NotePriority;
  metadata?: Record<string, unknown>;
}

/**
 * 筆記實體
 * 代表系統中的一個筆記，包含標題、內容、版本控制等信息
 */
export class Note extends AggregateRoot<NoteId> {
  private _title: string;
  private _content: string;
  private _category?: string;
  private _tags: Tag[];
  private _status: NoteStatus;
  private _priority: NotePriority;
  private _version: Version;
  private _metadata: Record<string, unknown>;
  private _filePath?: string;
  private _checksum?: string;

  constructor(
    id: NoteId,
    title: string,
    content: string,
    options: {
      category?: string;
      tags?: Tag[];
      status?: NoteStatus;
      priority?: NotePriority;
      version?: Version;
      metadata?: Record<string, unknown>;
      filePath?: string;
      checksum?: string;
      createdAt?: Date;
    } = {},
  ) {
    super(id, options.createdAt);

    this.validateTitle(title);
    this.validateContent(content);

    this._title = title;
    this._content = content;
    this._category = options.category;
    this._tags = options.tags || [];
    this._status = options.status || NoteStatus.DRAFT;
    this._priority = options.priority || NotePriority.MEDIUM;
    this._version = options.version || Version.initial();
    this._metadata = options.metadata || {};
    this._filePath = options.filePath;
    this._checksum = options.checksum;
  }

  /**
   * 創建新筆記
   */
  static create(params: CreateNoteParams): Note {
    const noteId = NoteId.generate();
    const note = new Note(noteId, params.title, params.content, {
      category: params.category,
      tags: params.tags,
      priority: params.priority,
      metadata: params.metadata,
    });

    note.addDomainEvent(new NoteCreatedEvent(noteId.value, params.title));
    return note;
  }

  /**
   * 更新筆記
   */
  update(params: UpdateNoteParams): void {
    let hasChanges = false;

    if (params.title !== undefined && params.title !== this._title) {
      this.validateTitle(params.title);
      this._title = params.title;
      hasChanges = true;
    }

    if (params.content !== undefined && params.content !== this._content) {
      this.validateContent(params.content);
      this._content = params.content;
      hasChanges = true;
    }

    if (params.category !== undefined && params.category !== this._category) {
      this._category = params.category;
      hasChanges = true;
    }

    if (params.tags !== undefined) {
      this._tags = [...params.tags];
      hasChanges = true;
    }

    if (params.priority !== undefined && params.priority !== this._priority) {
      this._priority = params.priority;
      hasChanges = true;
    }

    if (params.metadata !== undefined) {
      this._metadata = { ...this._metadata, ...params.metadata };
      hasChanges = true;
    }

    if (hasChanges) {
      this.markAsModified();
      this.addDomainEvent(new NoteUpdatedEvent(this._id.value, this._title));
    }
  }

  /**
   * 發布筆記（從草稿狀態變為已發布）
   */
  publish(): void {
    if (this._status !== NoteStatus.DRAFT) {
      throw new BusinessRuleViolationError(
        "Only draft notes can be published",
        { currentStatus: this._status },
      );
    }

    this._status = NoteStatus.PUBLISHED;
    this._version = this._version.increment();
    this.markAsModified();

    this.addDomainEvent(
      new NotePublishedEvent(this._id.value, this._version.toString()),
    );
  }

  /**
   * 創建新的草稿版本
   */
  createDraft(): void {
    if (this._status !== NoteStatus.PUBLISHED) {
      throw new BusinessRuleViolationError(
        "Can only create draft from published notes",
        { currentStatus: this._status },
      );
    }

    this._status = NoteStatus.DRAFT;
    this._version = this._version.incrementDraft();
    this.markAsModified();
  }

  /**
   * 歸檔筆記
   */
  archive(): void {
    this._status = NoteStatus.ARCHIVED;
    this.markAsModified();
  }

  /**
   * 添加標籤
   */
  addTag(tag: Tag): void {
    if (!this._tags.some((t) => t.equals(tag))) {
      this._tags.push(tag);
      this.markAsModified();
    }
  }

  /**
   * 移除標籤
   */
  removeTag(tag: Tag | string): void {
    let index = -1;

    if (typeof tag === "string") {
      index = this._tags.findIndex((t) => t.name === tag.toLowerCase());
    } else {
      index = this._tags.findIndex((t) => t.equals(tag));
    }

    if (index !== -1) {
      this._tags.splice(index, 1);
      this.markAsModified();
    }
  }

  /**
   * 設置文件路徑
   */
  setFilePath(filePath: string): void {
    this._filePath = filePath;
    this.markAsModified();
  }

  /**
   * 設置校驗和
   */
  setChecksum(checksum: string): void {
    this._checksum = checksum;
    this.markAsModified();
  }

  /**
   * 驗證標題
   */
  private validateTitle(title: string): void {
    if (!title || title.trim().length === 0) {
      throw new BusinessRuleViolationError("Note title cannot be empty");
    }

    if (title.length > 200) {
      throw new BusinessRuleViolationError(
        "Note title cannot exceed 200 characters",
      );
    }
  }

  /**
   * 驗證內容
   */
  private validateContent(content: string): void {
    if (content.length > 1000000) {
      // 1MB limit
      throw new BusinessRuleViolationError("Note content cannot exceed 1MB");
    }
  }

  // Getters
  get title(): string {
    return this._title;
  }

  get content(): string {
    return this._content;
  }

  get category(): string | undefined {
    return this._category;
  }

  get tags(): Tag[] {
    return [...this._tags];
  }

  get status(): NoteStatus {
    return this._status;
  }

  get priority(): NotePriority {
    return this._priority;
  }

  get version(): Version {
    return this._version;
  }

  get metadata(): Record<string, unknown> {
    return { ...this._metadata };
  }

  get filePath(): string | undefined {
    return this._filePath;
  }

  get checksum(): string | undefined {
    return this._checksum;
  }

  /**
   * 檢查是否為草稿
   */
  get isDraft(): boolean {
    return this._status === NoteStatus.DRAFT;
  }

  /**
   * 檢查是否已發布
   */
  get isPublished(): boolean {
    return this._status === NoteStatus.PUBLISHED;
  }

  /**
   * 檢查是否已歸檔
   */
  get isArchived(): boolean {
    return this._status === NoteStatus.ARCHIVED;
  }

  /**
   * 轉換為普通對象
   */
  toPlainObject(): Record<string, unknown> {
    return {
      id: this._id.value,
      title: this._title,
      content: this._content,
      category: this._category,
      tags: this._tags.map((tag) => tag.toPlainObject()),
      status: this._status,
      priority: this._priority,
      version: this._version.toString(),
      metadata: this._metadata,
      filePath: this._filePath,
      checksum: this._checksum,
      createdAt: this._createdAt.toISOString(),
      updatedAt: this._updatedAt.toISOString(),
    };
  }

  /**
   * 從普通對象創建筆記實體
   */
  static fromPlainObject(data: Record<string, unknown>): Note {
    const schema = z.object({
      id: z.string(),
      title: z.string(),
      content: z.string(),
      category: z.string().optional(),
      tags: z.array(z.record(z.unknown())).default([]),
      status: z.nativeEnum(NoteStatus).default(NoteStatus.DRAFT),
      priority: z.nativeEnum(NotePriority).default(NotePriority.MEDIUM),
      version: z.string().default("1.0.0"),
      metadata: z.record(z.unknown()).default({}),
      filePath: z.string().optional(),
      checksum: z.string().optional(),
      createdAt: z.string().optional(),
      updatedAt: z.string().optional(),
    });

    const parsed = schema.parse(data);

    return new Note(
      NoteId.fromString(parsed.id),
      parsed.title,
      parsed.content,
      {
        category: parsed.category,
        tags: parsed.tags.map((tagData) => Tag.fromPlainObject(tagData)),
        status: parsed.status,
        priority: parsed.priority,
        version: Version.fromString(parsed.version),
        metadata: parsed.metadata,
        filePath: parsed.filePath,
        checksum: parsed.checksum,
        createdAt: parsed.createdAt ? new Date(parsed.createdAt) : undefined,
      },
    );
  }
}
