import type { Note } from '$lib/types/note';
import type { DependencyGraph, DependencyNode, DependencyLink } from '$lib/types/dependency';
import { dependencyService } from './dependencyService';

/**
 * 優化建議
 */
export interface OptimizationSuggestion {
	id: string;
	type: 'restructure' | 'merge' | 'split' | 'connect' | 'disconnect' | 'reorganize';
	priority: 'low' | 'medium' | 'high' | 'critical';
	title: string;
	description: string;
	rationale: string;
	impact: OptimizationImpact;
	actions: OptimizationAction[];
	affectedNotes: string[];
	estimatedEffort: 'low' | 'medium' | 'high';
	expectedBenefit: 'low' | 'medium' | 'high';
}

/**
 * 優化影響
 */
export interface OptimizationImpact {
	connectivity: number; // -1 to 1
	maintainability: number; // -1 to 1
	discoverability: number; // -1 to 1
	complexity: number; // -1 to 1 (negative is better)
	overallScore: number; // 0 to 100
}

/**
 * 優化行動
 */
export interface OptimizationAction {
	type:
		| 'add_link'
		| 'remove_link'
		| 'merge_notes'
		| 'split_note'
		| 'add_tag'
		| 'remove_tag'
		| 'change_category';
	description: string;
	sourceNoteId?: string;
	targetNoteId?: string;
	parameters?: Record<string, any>;
	automated: boolean;
}

/**
 * 優化計劃
 */
export interface OptimizationPlan {
	suggestions: OptimizationSuggestion[];
	phases: OptimizationPhase[];
	totalEstimatedEffort: 'low' | 'medium' | 'high';
	expectedImprovementScore: number;
	generatedAt: Date;
}

/**
 * 優化階段
 */
export interface OptimizationPhase {
	phase: number;
	title: string;
	description: string;
	suggestions: string[]; // suggestion IDs
	dependencies: string[]; // phase dependencies
	estimatedDuration: string;
}

/**
 * 依賴關係優化服務
 */
export class DependencyOptimizationService {
	/**
	 * 生成優化計劃
	 */
	async generateOptimizationPlan(notes: Note[]): Promise<OptimizationPlan> {
		const graph = await dependencyService.analyzeDependencies(notes);

		const suggestions = await this.generateOptimizationSuggestions(graph, notes);
		const phases = this.organizeSuggestionsIntoPhases(suggestions);
		const totalEstimatedEffort = this.calculateTotalEffort(suggestions);
		const expectedImprovementScore = this.calculateExpectedImprovement(suggestions);

		return {
			suggestions,
			phases,
			totalEstimatedEffort,
			expectedImprovementScore,
			generatedAt: new Date()
		};
	}

	/**
	 * 生成優化建議
	 */
	private async generateOptimizationSuggestions(
		graph: DependencyGraph,
		notes: Note[]
	): Promise<OptimizationSuggestion[]> {
		const suggestions: OptimizationSuggestion[] = [];

		// 結構重組建議
		suggestions.push(...this.generateRestructureSuggestions(graph, notes));

		// 筆記合併建議
		suggestions.push(...this.generateMergeSuggestions(graph, notes));

		// 筆記拆分建議
		suggestions.push(...this.generateSplitSuggestions(graph, notes));

		// 連接優化建議
		suggestions.push(...this.generateConnectionSuggestions(graph, notes));

		// 組織優化建議
		suggestions.push(...this.generateOrganizationSuggestions(graph, notes));

		return suggestions.sort((a, b) => {
			const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
			return priorityOrder[b.priority] - priorityOrder[a.priority];
		});
	}

	/**
	 * 生成結構重組建議
	 */
	private generateRestructureSuggestions(
		graph: DependencyGraph,
		notes: Note[]
	): OptimizationSuggestion[] {
		const suggestions: OptimizationSuggestion[] = [];

		// 檢測循環依賴並建議重組
		const cycles = this.detectCycles(graph);
		for (const cycle of cycles) {
			suggestions.push({
				id: `restructure-cycle-${cycle.join('-')}`,
				type: 'restructure',
				priority: 'high',
				title: '解決循環依賴',
				description: `檢測到循環依賴：${cycle.map(id => notes.find(n => n.id === id)?.title || id).join(' → ')}`,
				rationale: '循環依賴會導致理解困難和維護問題，應該重新組織結構',
				impact: {
					connectivity: 0.1,
					maintainability: 0.3,
					discoverability: 0.2,
					complexity: -0.4,
					overallScore: 75
				},
				actions: [
					{
						type: 'remove_link',
						description: '移除造成循環的連接',
						automated: false
					},
					{
						type: 'add_link',
						description: '添加替代的連接路徑',
						automated: false
					}
				],
				affectedNotes: cycle,
				estimatedEffort: 'medium',
				expectedBenefit: 'high'
			});
		}

		return suggestions;
	}

	/**
	 * 生成合併建議
	 */
	private generateMergeSuggestions(
		graph: DependencyGraph,
		notes: Note[]
	): OptimizationSuggestion[] {
		const suggestions: OptimizationSuggestion[] = [];

		// 找到高度相似的筆記對
		const similarPairs = this.findSimilarNotes(notes);

		for (const pair of similarPairs) {
			const [note1, note2] = pair.notes;

			suggestions.push({
				id: `merge-${note1.id}-${note2.id}`,
				type: 'merge',
				priority: pair.similarity > 0.8 ? 'medium' : 'low',
				title: '合併相似筆記',
				description: `筆記「${note1.title}」和「${note2.title}」內容高度相似`,
				rationale: `相似度：${(pair.similarity * 100).toFixed(1)}%，合併可以減少重複並提高一致性`,
				impact: {
					connectivity: 0.1,
					maintainability: 0.2,
					discoverability: 0.1,
					complexity: -0.2,
					overallScore: 60
				},
				actions: [
					{
						type: 'merge_notes',
						description: '將兩個筆記的內容合併',
						sourceNoteId: note1.id,
						targetNoteId: note2.id,
						automated: false
					}
				],
				affectedNotes: [note1.id, note2.id],
				estimatedEffort: 'medium',
				expectedBenefit: 'medium'
			});
		}

		return suggestions;
	}

	/**
	 * 生成拆分建議
	 */
	private generateSplitSuggestions(
		graph: DependencyGraph,
		notes: Note[]
	): OptimizationSuggestion[] {
		const suggestions: OptimizationSuggestion[] = [];

		// 找到過於複雜的筆記
		const complexNotes = notes.filter(note => {
			const wordCount = note.content.split(/\s+/).length;
			const linkCount = this.getNodeConnections(note.id, graph);
			return wordCount > 2000 || linkCount > 10;
		});

		for (const note of complexNotes) {
			suggestions.push({
				id: `split-${note.id}`,
				type: 'split',
				priority: 'medium',
				title: '拆分複雜筆記',
				description: `筆記「${note.title}」內容過於複雜，建議拆分`,
				rationale: '大型筆記難以維護和理解，拆分可以提高可讀性',
				impact: {
					connectivity: 0.2,
					maintainability: 0.3,
					discoverability: 0.2,
					complexity: -0.3,
					overallScore: 70
				},
				actions: [
					{
						type: 'split_note',
						description: '將筆記拆分為多個較小的筆記',
						sourceNoteId: note.id,
						automated: false
					}
				],
				affectedNotes: [note.id],
				estimatedEffort: 'high',
				expectedBenefit: 'medium'
			});
		}

		return suggestions;
	}

	/**
	 * 生成連接優化建議
	 */
	private generateConnectionSuggestions(
		graph: DependencyGraph,
		notes: Note[]
	): OptimizationSuggestion[] {
		const suggestions: OptimizationSuggestion[] = [];

		// 找到應該連接但未連接的筆記
		const missingConnections = this.findMissingConnections(graph, notes);

		for (const connection of missingConnections) {
			const sourceNote = notes.find(n => n.id === connection.sourceId);
			const targetNote = notes.find(n => n.id === connection.targetId);

			if (sourceNote && targetNote) {
				suggestions.push({
					id: `connect-${connection.sourceId}-${connection.targetId}`,
					type: 'connect',
					priority: connection.strength > 0.7 ? 'medium' : 'low',
					title: '添加缺失的連接',
					description: `建議在「${sourceNote.title}」和「${targetNote.title}」之間建立連接`,
					rationale: `基於內容相似性（${(connection.strength * 100).toFixed(1)}%）建議建立連接`,
					impact: {
						connectivity: 0.2,
						maintainability: 0.1,
						discoverability: 0.3,
						complexity: 0.1,
						overallScore: 65
					},
					actions: [
						{
							type: 'add_link',
							description: '在筆記間添加引用連接',
							sourceNoteId: connection.sourceId,
							targetNoteId: connection.targetId,
							automated: true
						}
					],
					affectedNotes: [connection.sourceId, connection.targetId],
					estimatedEffort: 'low',
					expectedBenefit: 'medium'
				});
			}
		}

		return suggestions;
	}

	/**
	 * 生成組織優化建議
	 */
	private generateOrganizationSuggestions(
		graph: DependencyGraph,
		notes: Note[]
	): OptimizationSuggestion[] {
		const suggestions: OptimizationSuggestion[] = [];

		// 標籤優化建議
		const tagOptimizations = this.analyzeTagOptimizations(notes);
		suggestions.push(...tagOptimizations);

		// 分類優化建議
		const categoryOptimizations = this.analyzeCategoryOptimizations(notes);
		suggestions.push(...categoryOptimizations);

		return suggestions;
	}

	/**
	 * 將建議組織成階段
	 */
	private organizeSuggestionsIntoPhases(
		suggestions: OptimizationSuggestion[]
	): OptimizationPhase[] {
		const phases: OptimizationPhase[] = [];

		// 第一階段：關鍵問題修復
		const criticalSuggestions = suggestions.filter(s => s.priority === 'critical');
		if (criticalSuggestions.length > 0) {
			phases.push({
				phase: 1,
				title: '關鍵問題修復',
				description: '解決影響系統穩定性的關鍵問題',
				suggestions: criticalSuggestions.map(s => s.id),
				dependencies: [],
				estimatedDuration: '1-2 週'
			});
		}

		// 第二階段：結構優化
		const structuralSuggestions = suggestions.filter(
			s => s.priority === 'high' && ['restructure', 'split', 'merge'].includes(s.type)
		);
		if (structuralSuggestions.length > 0) {
			phases.push({
				phase: 2,
				title: '結構優化',
				description: '優化筆記結構和組織方式',
				suggestions: structuralSuggestions.map(s => s.id),
				dependencies: phases.length > 0 ? ['1'] : [],
				estimatedDuration: '2-4 週'
			});
		}

		// 第三階段：連接優化
		const connectionSuggestions = suggestions.filter(s =>
			['connect', 'disconnect'].includes(s.type)
		);
		if (connectionSuggestions.length > 0) {
			phases.push({
				phase: 3,
				title: '連接優化',
				description: '優化筆記間的連接關係',
				suggestions: connectionSuggestions.map(s => s.id),
				dependencies: phases.length > 0 ? [phases.length.toString()] : [],
				estimatedDuration: '1-2 週'
			});
		}

		// 第四階段：組織優化
		const organizationSuggestions = suggestions.filter(s => s.type === 'reorganize');
		if (organizationSuggestions.length > 0) {
			phases.push({
				phase: 4,
				title: '組織優化',
				description: '優化標籤和分類系統',
				suggestions: organizationSuggestions.map(s => s.id),
				dependencies: phases.length > 0 ? [phases.length.toString()] : [],
				estimatedDuration: '1 週'
			});
		}

		return phases;
	}

	/**
	 * 計算總體工作量
	 */
	private calculateTotalEffort(suggestions: OptimizationSuggestion[]): 'low' | 'medium' | 'high' {
		const effortScores = suggestions.map(s => {
			switch (s.estimatedEffort) {
				case 'low':
					return 1;
				case 'medium':
					return 2;
				case 'high':
					return 3;
				default:
					return 1;
			}
		});

		const totalScore = effortScores.reduce((sum, score) => sum + score, 0);
		const averageScore = totalScore / suggestions.length;

		if (averageScore <= 1.5) return 'low';
		if (averageScore <= 2.5) return 'medium';
		return 'high';
	}

	/**
	 * 計算預期改進分數
	 */
	private calculateExpectedImprovement(suggestions: OptimizationSuggestion[]): number {
		const totalImpact = suggestions.reduce(
			(sum, suggestion) => sum + suggestion.impact.overallScore,
			0
		);
		return suggestions.length > 0 ? totalImpact / suggestions.length : 0;
	}

	// 輔助方法
	private detectCycles(graph: DependencyGraph): string[][] {
		// 檢測循環依賴
		return [];
	}

	private findSimilarNotes(notes: Note[]): Array<{ notes: [Note, Note]; similarity: number }> {
		// 找到相似筆記
		return [];
	}

	private getNodeConnections(nodeId: string, graph: DependencyGraph): number {
		return graph.links.filter(link => {
			const sourceId = typeof link.source === 'string' ? link.source : link.source.id;
			const targetId = typeof link.target === 'string' ? link.target : link.target.id;
			return sourceId === nodeId || targetId === nodeId;
		}).length;
	}

	private findMissingConnections(
		graph: DependencyGraph,
		notes: Note[]
	): Array<{ sourceId: string; targetId: string; strength: number }> {
		// 找到缺失的連接
		return [];
	}

	private analyzeTagOptimizations(notes: Note[]): OptimizationSuggestion[] {
		// 分析標籤優化機會
		return [];
	}

	private analyzeCategoryOptimizations(notes: Note[]): OptimizationSuggestion[] {
		// 分析分類優化機會
		return [];
	}
}

// 導出單例實例
export const dependencyOptimizationService = new DependencyOptimizationService();
