import { writable, derived, get } from 'svelte/store';
import type { SearchFilters, SearchOptions, SearchResult } from '$lib/services/searchService';
import type { PaginatedResult } from '$types';
import { searchService } from '$lib/services/searchService';
import { noteStore } from './notes';

export interface SearchState {
	// 搜索狀態
	isSearching: boolean;
	hasSearched: boolean;

	// 搜索參數
	filters: SearchFilters;
	options: SearchOptions;

	// 搜索結果
	results: PaginatedResult<SearchResult> | null;
	suggestions: string[];
	popularTags: Array<{ name: string; count: number }>;

	// 錯誤處理
	error: string | null;

	// UI 狀態
	showAdvancedFilters: boolean;
	recentSearches: string[];
}

const initialState: SearchState = {
	isSearching: false,
	hasSearched: false,
	filters: {},
	options: {
		page: 1,
		limit: 20,
		sortBy: 'relevance',
		sortOrder: 'desc',
		includeContent: true,
		fuzzySearch: false
	},
	results: null,
	suggestions: [],
	popularTags: [],
	error: null,
	showAdvancedFilters: false,
	recentSearches: []
};

function createSearchStore() {
	const { subscribe, set, update } = writable<SearchState>(initialState);

	// 從 localStorage 加載最近搜索
	if (typeof window !== 'undefined') {
		const savedSearches = localStorage.getItem('life-note-recent-searches');
		if (savedSearches) {
			try {
				const recentSearches = JSON.parse(savedSearches);
				update(state => ({ ...state, recentSearches }));
			} catch (error) {
				console.warn('Failed to load recent searches:', error);
			}
		}
	}

	return {
		subscribe,

		/**
		 * 初始化搜索服務
		 */
		async initialize() {
			try {
				const notes = get(noteStore).notes;
				await searchService.initializeIndex(notes);

				// 獲取熱門標籤
				const popularTags = searchService.getPopularTags(20);
				update(state => ({ ...state, popularTags }));
			} catch (error) {
				console.error('Failed to initialize search service:', error);
				update(state => ({
					...state,
					error: 'Failed to initialize search service'
				}));
			}
		},

		/**
		 * 執行搜索
		 */
		async search(filters?: Partial<SearchFilters>, options?: Partial<SearchOptions>) {
			update(state => ({
				...state,
				isSearching: true,
				error: null,
				filters: { ...state.filters, ...filters },
				options: { ...state.options, ...options }
			}));

			try {
				const currentState = get({ subscribe });
				const results = await searchService.search(currentState.filters, currentState.options);

				// 保存搜索查詢到最近搜索
				if (filters?.query && filters.query.trim()) {
					this.addToRecentSearches(filters.query.trim());
				}

				update(state => ({
					...state,
					isSearching: false,
					hasSearched: true,
					results
				}));
			} catch (error) {
				console.error('Search failed:', error);
				update(state => ({
					...state,
					isSearching: false,
					error: error instanceof Error ? error.message : 'Search failed'
				}));
			}
		},

		/**
		 * 清空搜索
		 */
		clearSearch() {
			update(state => ({
				...state,
				hasSearched: false,
				filters: {},
				results: null,
				error: null
			}));
		},

		/**
		 * 更新搜索過濾器
		 */
		updateFilters(filters: Partial<SearchFilters>) {
			update(state => ({
				...state,
				filters: { ...state.filters, ...filters }
			}));
		},

		/**
		 * 更新搜索選項
		 */
		updateOptions(options: Partial<SearchOptions>) {
			update(state => ({
				...state,
				options: { ...state.options, ...options }
			}));
		},

		/**
		 * 切換高級過濾器顯示
		 */
		toggleAdvancedFilters() {
			update(state => ({
				...state,
				showAdvancedFilters: !state.showAdvancedFilters
			}));
		},

		/**
		 * 獲取搜索建議
		 */
		async getSuggestions(query: string) {
			if (!query || query.length < 2) {
				update(state => ({ ...state, suggestions: [] }));
				return;
			}

			try {
				const suggestions = await searchService.getSuggestions(query, 8);
				update(state => ({ ...state, suggestions }));
			} catch (error) {
				console.error('Failed to get suggestions:', error);
			}
		},

		/**
		 * 添加到最近搜索
		 */
		addToRecentSearches(query: string) {
			update(state => {
				const recentSearches = [query, ...state.recentSearches.filter(s => s !== query)].slice(
					0,
					10
				); // 保留最近 10 個搜索

				// 保存到 localStorage
				if (typeof window !== 'undefined') {
					localStorage.setItem('life-note-recent-searches', JSON.stringify(recentSearches));
				}

				return { ...state, recentSearches };
			});
		},

		/**
		 * 清除最近搜索
		 */
		clearRecentSearches() {
			update(state => ({ ...state, recentSearches: [] }));
			if (typeof window !== 'undefined') {
				localStorage.removeItem('life-note-recent-searches');
			}
		},

		/**
		 * 分頁導航
		 */
		async goToPage(page: number) {
			const currentState = get({ subscribe });
			if (page < 1 || (currentState.results && page > currentState.results.totalPages)) {
				return;
			}

			await this.search({}, { page });
		},

		/**
		 * 下一頁
		 */
		async nextPage() {
			const currentState = get({ subscribe });
			if (currentState.results?.hasNext) {
				await this.goToPage(currentState.options.page! + 1);
			}
		},

		/**
		 * 上一頁
		 */
		async prevPage() {
			const currentState = get({ subscribe });
			if (currentState.results?.hasPrev) {
				await this.goToPage(currentState.options.page! - 1);
			}
		},

		/**
		 * 重置到初始狀態
		 */
		reset() {
			set(initialState);
		},

		/**
		 * 清除錯誤
		 */
		clearError() {
			update(state => ({ ...state, error: null }));
		}
	};
}

export const searchStore = createSearchStore();

// 衍生狀態
export const searchQuery = derived(searchStore, $searchStore => $searchStore.filters.query || '');

export const searchResults = derived(
	searchStore,
	$searchStore => $searchStore.results?.items || []
);

export const searchPagination = derived(searchStore, $searchStore => {
	if (!$searchStore.results) return null;

	return {
		page: $searchStore.results.page,
		totalPages: $searchStore.results.totalPages,
		total: $searchStore.results.total,
		hasNext: $searchStore.results.hasNext,
		hasPrev: $searchStore.results.hasPrev,
		limit: $searchStore.results.limit
	};
});

export const activeFilters = derived(searchStore, $searchStore => {
	const filters = $searchStore.filters;
	const active: Array<{ type: string; value: string; label: string }> = [];

	if (filters.query) {
		active.push({
			type: 'query',
			value: filters.query,
			label: `搜索: "${filters.query}"`
		});
	}

	if (filters.tags && filters.tags.length > 0) {
		for (const tag of filters.tags) {
			active.push({
				type: 'tag',
				value: tag,
				label: `標籤: ${tag}`
			});
		}
	}

	if (filters.status && filters.status.length > 0) {
		for (const status of filters.status) {
			active.push({
				type: 'status',
				value: status,
				label: `狀態: ${status}`
			});
		}
	}

	if (filters.priority && filters.priority.length > 0) {
		for (const priority of filters.priority) {
			active.push({
				type: 'priority',
				value: priority,
				label: `優先級: ${priority}`
			});
		}
	}

	if (filters.dateRange?.start || filters.dateRange?.end) {
		const start = filters.dateRange.start?.toLocaleDateString() || '開始';
		const end = filters.dateRange.end?.toLocaleDateString() || '結束';
		active.push({
			type: 'dateRange',
			value: 'dateRange',
			label: `日期: ${start} - ${end}`
		});
	}

	return active;
});

export const hasActiveFilters = derived(activeFilters, $activeFilters => $activeFilters.length > 0);

export const searchStats = derived(searchStore, $searchStore => {
	if (!$searchStore.results) return null;

	return {
		total: $searchStore.results.total,
		page: $searchStore.results.page,
		totalPages: $searchStore.results.totalPages,
		showing: $searchStore.results.items.length,
		isSearching: $searchStore.isSearching
	};
});
