<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import {
		Bell,
		BellRing,
		X,
		Check,
		AlertTriangle,
		Info,
		ExternalLink,
		Settings,
		Trash2
	} from 'lucide-svelte';

	import Button from '$components/ui/Button.svelte';
	import Card from '$components/ui/Card.svelte';
	import { dependencyNotificationService } from '$lib/services/dependencyNotificationService';
	import type { DependencyNotification } from '$lib/services/dependencyNotificationService';

	// Props
	export let showNotifications = false;
	export let position: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' = 'top-right';

	// 狀態
	let notifications: DependencyNotification[] = [];
	let unreadCount = 0;
	let showSettings = false;
	let isMonitoring = false;

	// 通知回調
	const handleNotification = (notification: DependencyNotification) => {
		notifications = dependencyNotificationService.getNotifications();
		unreadCount = dependencyNotificationService.getUnreadNotifications().length;

		// 顯示瀏覽器通知（如果允許）
		if (Notification.permission === 'granted') {
			new Notification('Life Note - 依賴關係更新', {
				body: notification.message,
				icon: '/favicon.ico',
				tag: notification.id
			});
		}
	};

	onMount(() => {
		// 載入現有通知
		notifications = dependencyNotificationService.getNotifications();
		unreadCount = dependencyNotificationService.getUnreadNotifications().length;
		isMonitoring = dependencyNotificationService.isMonitoringActive();

		// 註冊通知回調
		dependencyNotificationService.onNotification(handleNotification);

		// 請求瀏覽器通知權限
		if (Notification.permission === 'default') {
			Notification.requestPermission();
		}
	});

	onDestroy(() => {
		// 移除通知回調
		dependencyNotificationService.removeNotificationCallback(handleNotification);
	});

	const toggleNotifications = () => {
		showNotifications = !showNotifications;
	};

	const markAsRead = (notificationId: string) => {
		dependencyNotificationService.markAsRead(notificationId);
		notifications = dependencyNotificationService.getNotifications();
		unreadCount = dependencyNotificationService.getUnreadNotifications().length;
	};

	const markAllAsRead = () => {
		dependencyNotificationService.markAllAsRead();
		notifications = dependencyNotificationService.getNotifications();
		unreadCount = 0;
	};

	const clearNotification = (notificationId: string) => {
		dependencyNotificationService.clearNotification(notificationId);
		notifications = dependencyNotificationService.getNotifications();
		unreadCount = dependencyNotificationService.getUnreadNotifications().length;
	};

	const clearAllNotifications = () => {
		dependencyNotificationService.clearAllNotifications();
		notifications = [];
		unreadCount = 0;
	};

	const getSeverityIcon = (severity: string) => {
		switch (severity) {
			case 'high':
				return AlertTriangle;
			case 'medium':
				return Info;
			default:
				return Bell;
		}
	};

	const getSeverityColor = (severity: string) => {
		switch (severity) {
			case 'high':
				return 'text-red-500';
			case 'medium':
				return 'text-orange-500';
			default:
				return 'text-blue-500';
		}
	};

	const formatTimestamp = (timestamp: Date) => {
		const now = new Date();
		const diff = now.getTime() - timestamp.getTime();
		const minutes = Math.floor(diff / 60000);
		const hours = Math.floor(diff / 3600000);
		const days = Math.floor(diff / 86400000);

		if (minutes < 1) return '剛剛';
		if (minutes < 60) return `${minutes} 分鐘前`;
		if (hours < 24) return `${hours} 小時前`;
		return `${days} 天前`;
	};

	const getPositionClasses = () => {
		switch (position) {
			case 'top-left':
				return 'top-4 left-4';
			case 'bottom-right':
				return 'bottom-4 right-4';
			case 'bottom-left':
				return 'bottom-4 left-4';
			default:
				return 'top-4 right-4';
		}
	};
</script>

<!-- 通知按鈕 -->
<div class="relative">
	<Button variant="ghost" size="sm" on:click={toggleNotifications} class="relative">
		{#if unreadCount > 0}
			<BellRing class="h-5 w-5" />
			<span
				class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center"
			>
				{unreadCount > 99 ? '99+' : unreadCount}
			</span>
		{:else}
			<Bell class="h-5 w-5" />
		{/if}
	</Button>
</div>

<!-- 通知面板 -->
{#if showNotifications}
	<div class="fixed {getPositionClasses()} z-50 w-96 max-h-96 overflow-hidden">
		<Card class="shadow-lg border">
			<!-- Header -->
			<div class="flex items-center justify-between p-4 border-b">
				<div class="flex items-center space-x-2">
					<Bell class="h-5 w-5" />
					<h3 class="font-semibold">依賴關係通知</h3>
					{#if unreadCount > 0}
						<span class="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
							{unreadCount}
						</span>
					{/if}
				</div>

				<div class="flex items-center space-x-1">
					<Button variant="ghost" size="sm" on:click={() => (showSettings = !showSettings)}>
						<Settings class="h-4 w-4" />
					</Button>
					<Button variant="ghost" size="sm" on:click={toggleNotifications}>
						<X class="h-4 w-4" />
					</Button>
				</div>
			</div>

			<!-- Settings Panel -->
			{#if showSettings}
				<div class="p-4 border-b bg-muted/30">
					<div class="space-y-3">
						<div class="flex items-center justify-between">
							<span class="text-sm font-medium">監控狀態</span>
							<span class="text-sm {isMonitoring ? 'text-green-600' : 'text-red-600'}">
								{isMonitoring ? '運行中' : '已停止'}
							</span>
						</div>

						<div class="flex items-center space-x-2">
							{#if unreadCount > 0}
								<Button size="sm" variant="outline" on:click={markAllAsRead}>
									<Check class="h-3 w-3 mr-1" />
									全部已讀
								</Button>
							{/if}

							{#if notifications.length > 0}
								<Button size="sm" variant="outline" on:click={clearAllNotifications}>
									<Trash2 class="h-3 w-3 mr-1" />
									清除全部
								</Button>
							{/if}
						</div>
					</div>
				</div>
			{/if}

			<!-- Notifications List -->
			<div class="max-h-80 overflow-y-auto">
				{#if notifications.length === 0}
					<div class="p-8 text-center text-muted-foreground">
						<Bell class="h-8 w-8 mx-auto mb-2 opacity-50" />
						<p>暫無依賴關係通知</p>
					</div>
				{:else}
					<div class="divide-y">
						{#each notifications as notification (notification.id)}
							<div
								class="p-4 hover:bg-muted/30 transition-colors {!notification.read
									? 'bg-blue-50/50'
									: ''}"
							>
								<div class="flex items-start space-x-3">
									<!-- Severity Icon -->
									<div class="flex-shrink-0 mt-1">
										<svelte:component
											this={getSeverityIcon(notification.severity)}
											class="h-4 w-4 {getSeverityColor(notification.severity)}"
										/>
									</div>

									<!-- Content -->
									<div class="flex-1 min-w-0">
										<p class="text-sm font-medium text-foreground mb-1">
											{notification.message}
										</p>

										<div class="flex items-center space-x-2 text-xs text-muted-foreground">
											<span>{formatTimestamp(notification.timestamp)}</span>
											<span>•</span>
											<span class="capitalize">{notification.severity}</span>
										</div>

										<!-- Note Links -->
										<div class="flex items-center space-x-2 mt-2">
											<Button
												variant="ghost"
												size="sm"
												class="h-6 px-2 text-xs"
												on:click={() => console.log('Navigate to:', notification.sourceNoteId)}
											>
												<ExternalLink class="h-3 w-3 mr-1" />
												{notification.sourceNoteTitle}
											</Button>

											{#if notification.affectedNoteId !== notification.sourceNoteId}
												<span class="text-muted-foreground">→</span>
												<Button
													variant="ghost"
													size="sm"
													class="h-6 px-2 text-xs"
													on:click={() => console.log('Navigate to:', notification.affectedNoteId)}
												>
													<ExternalLink class="h-3 w-3 mr-1" />
													{notification.affectedNoteTitle}
												</Button>
											{/if}
										</div>
									</div>

									<!-- Actions -->
									<div class="flex items-center space-x-1">
										{#if !notification.read}
											<Button
												variant="ghost"
												size="sm"
												on:click={() => markAsRead(notification.id)}
											>
												<Check class="h-3 w-3" />
											</Button>
										{/if}

										<Button
											variant="ghost"
											size="sm"
											on:click={() => clearNotification(notification.id)}
										>
											<X class="h-3 w-3" />
										</Button>
									</div>
								</div>
							</div>
						{/each}
					</div>
				{/if}
			</div>
		</Card>
	</div>
{/if}

<!-- 點擊外部關閉 -->
{#if showNotifications}
	<div
		class="fixed inset-0 z-40"
		on:click={toggleNotifications}
		on:keydown={e => e.key === 'Escape' && toggleNotifications()}
	></div>
{/if}
