import { generateRandomString } from "@life-note/utils";

import {
  ValueObject,
  BusinessRuleViolationError,
} from "../../shared/Entity.js";

/**
 * 用戶 ID 值對象
 * 代表用戶的唯一標識符
 */
export class UserId extends ValueObject {
  private readonly _value: string;

  constructor(value: string) {
    super();
    this.validateId(value);
    this._value = value;
  }

  /**
   * 生成新的用戶 ID
   */
  static generate(): UserId {
    return new UserId(`user_${generateRandomString(16)}`);
  }

  /**
   * 從字符串創建用戶 ID
   */
  static fromString(value: string): UserId {
    return new UserId(value);
  }

  /**
   * 驗證 ID 格式
   */
  private validateId(value: string): void {
    if (!value || value.trim().length === 0) {
      throw new BusinessRuleViolationError("User ID cannot be empty");
    }

    if (value.length < 8 || value.length > 64) {
      throw new BusinessRuleViolationError(
        "User ID must be between 8 and 64 characters",
      );
    }

    // 檢查是否只包含字母數字字符和下劃線
    if (!/^[a-zA-Z0-9_]+$/.test(value)) {
      throw new BusinessRuleViolationError(
        "User ID can only contain alphanumeric characters and underscores",
      );
    }
  }

  get value(): string {
    return this._value;
  }

  equals(other: ValueObject): boolean {
    if (!(other instanceof UserId)) {
      return false;
    }
    return this._value === other._value;
  }

  hashCode(): string {
    return this._value;
  }

  toString(): string {
    return this._value;
  }

  toPlainObject(): Record<string, unknown> {
    return {
      value: this._value,
    };
  }

  static fromPlainObject(data: Record<string, unknown>): UserId {
    if (typeof data.value !== "string") {
      throw new BusinessRuleViolationError("Invalid UserId data");
    }
    return new UserId(data.value);
  }
}
