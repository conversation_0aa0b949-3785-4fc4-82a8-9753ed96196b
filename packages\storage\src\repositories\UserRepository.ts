import type {
  PrismaClient,
  User as PrismaUser,
} from "../prisma/generated/index.js";
import type {
  PaginatedResult,
  PaginationParams,
} from "../interfaces/IRepository.js";
import { BaseRepository } from "./BaseRepository.js";

/**
 * 用戶實體接口（簡化版本）
 */
export interface User {
  id: string;
  username: string;
  email: string;
  displayName?: string;
  role: "admin" | "moderator" | "user" | "guest";
  status: "active" | "inactive" | "suspended";
  metadata?: Record<string, unknown>;
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 用戶查詢條件
 */
export interface UserQueryCondition {
  role?: User["role"];
  status?: User["status"];
  createdAfter?: Date;
  createdBefore?: Date;
  lastLoginAfter?: Date;
  lastLoginBefore?: Date;
}

/**
 * 用戶統計信息
 */
export interface UserStatistics {
  total: number;
  byRole: Record<User["role"], number>;
  byStatus: Record<User["status"], number>;
  activeUsers: number;
  newUsersThisWeek: number;
  newUsersThisMonth: number;
  activeUsersThisWeek: number;
  activeUsersThisMonth: number;
}

/**
 * 用戶會話信息
 */
export interface UserSession {
  id: string;
  userId: string;
  token: string;
  device?: string;
  ipAddress?: string;
  userAgent?: string;
  expiresAt: Date;
  createdAt: Date;
}

/**
 * 用戶偏好設置
 */
export interface UserPreferences {
  theme: "light" | "dark" | "auto";
  language: string;
  timezone: string;
  editorSettings: Record<string, unknown>;
  notificationSettings: Record<string, unknown>;
  privacySettings: Record<string, unknown>;
}

/**
 * 用戶存儲庫實現
 */
export class UserRepository extends BaseRepository<User, string, PrismaUser> {
  constructor(prisma: PrismaClient) {
    super(prisma);
  }

  protected getModel() {
    return this.prisma.user;
  }

  protected toDomainEntity(model: PrismaUser): User {
    return {
      id: model.id,
      username: model.username,
      email: model.email,
      displayName: model.displayName || undefined,
      role: model.role as User["role"],
      status: model.status as User["status"],
      metadata: this.parseJson<Record<string, unknown>>(model.metadata),
      lastLoginAt: model.lastLoginAt || undefined,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    };
  }

  protected toPrismaModel(
    entity: User,
  ): Omit<PrismaUser, "id" | "createdAt" | "updatedAt"> {
    return {
      username: entity.username,
      email: entity.email,
      displayName: entity.displayName || null,
      role: entity.role,
      status: entity.status,
      metadata: this.stringifyJson(entity.metadata),
      lastLoginAt: entity.lastLoginAt || null,
    };
  }

  protected extractId(entity: User): string {
    return entity.id;
  }

  /**
   * 根據用戶名查找用戶
   */
  async findByUsername(username: string): Promise<User | null> {
    try {
      const result = await this.prisma.user.findUnique({
        where: { username },
      });

      return result ? this.toDomainEntity(result) : null;
    } catch (error) {
      console.error("Error finding user by username:", error);
      throw error;
    }
  }

  /**
   * 根據電子郵件查找用戶
   */
  async findByEmail(email: string): Promise<User | null> {
    try {
      const result = await this.prisma.user.findUnique({
        where: { email },
      });

      return result ? this.toDomainEntity(result) : null;
    } catch (error) {
      console.error("Error finding user by email:", error);
      throw error;
    }
  }

  /**
   * 根據角色查找用戶
   */
  async findByRole(
    role: User["role"],
    params?: PaginationParams,
  ): Promise<PaginatedResult<User>> {
    const condition = { role };
    return params
      ? await this.findByConditionPaginated(condition, params)
      : await this.findByConditionPaginated(condition, { page: 1, limit: 50 });
  }

  /**
   * 根據狀態查找用戶
   */
  async findByStatus(
    status: User["status"],
    params?: PaginationParams,
  ): Promise<PaginatedResult<User>> {
    const condition = { status };
    return params
      ? await this.findByConditionPaginated(condition, params)
      : await this.findByConditionPaginated(condition, { page: 1, limit: 50 });
  }

  /**
   * 根據複合條件查找用戶
   */
  async findByCondition(
    condition: UserQueryCondition,
    params?: PaginationParams,
  ): Promise<PaginatedResult<User>> {
    try {
      const {
        page = 1,
        limit = 50,
        sortBy = "createdAt",
        sortOrder = "desc",
      } = params || {};
      const skip = (page - 1) * limit;

      // 構建查詢條件
      const whereClause: any = {};

      if (condition.role) {
        whereClause.role = condition.role;
      }

      if (condition.status) {
        whereClause.status = condition.status;
      }

      if (condition.createdAfter || condition.createdBefore) {
        whereClause.createdAt = {};
        if (condition.createdAfter) {
          whereClause.createdAt.gte = condition.createdAfter;
        }
        if (condition.createdBefore) {
          whereClause.createdAt.lte = condition.createdBefore;
        }
      }

      if (condition.lastLoginAfter || condition.lastLoginBefore) {
        whereClause.lastLoginAt = {};
        if (condition.lastLoginAfter) {
          whereClause.lastLoginAt.gte = condition.lastLoginAfter;
        }
        if (condition.lastLoginBefore) {
          whereClause.lastLoginAt.lte = condition.lastLoginBefore;
        }
      }

      const [results, total] = await Promise.all([
        this.prisma.user.findMany({
          where: whereClause,
          skip,
          take: limit,
          orderBy: { [sortBy]: sortOrder },
        }),
        this.prisma.user.count({
          where: whereClause,
        }),
      ]);

      const entities = results.map((result) => this.toDomainEntity(result));
      return this.createPaginatedResult(entities, total, page, limit);
    } catch (error) {
      console.error("Error finding users by condition:", error);
      throw error;
    }
  }

  /**
   * 查找活躍用戶
   */
  async findActiveUsers(
    params?: PaginationParams,
  ): Promise<PaginatedResult<User>> {
    return this.findByStatus("active", params);
  }

  /**
   * 查找最近註冊的用戶
   */
  async findRecentlyRegistered(limit: number = 10): Promise<User[]> {
    try {
      const results = await this.prisma.user.findMany({
        orderBy: { createdAt: "desc" },
        take: limit,
      });

      return results.map((result) => this.toDomainEntity(result));
    } catch (error) {
      console.error("Error finding recently registered users:", error);
      throw error;
    }
  }

  /**
   * 查找最近登錄的用戶
   */
  async findRecentlyLoggedIn(limit: number = 10): Promise<User[]> {
    try {
      const results = await this.prisma.user.findMany({
        where: { lastLoginAt: { not: null } },
        orderBy: { lastLoginAt: "desc" },
        take: limit,
      });

      return results.map((result) => this.toDomainEntity(result));
    } catch (error) {
      console.error("Error finding recently logged in users:", error);
      throw error;
    }
  }

  /**
   * 檢查用戶名是否可用
   */
  async isUsernameAvailable(username: string): Promise<boolean> {
    try {
      const count = await this.prisma.user.count({
        where: { username },
      });
      return count === 0;
    } catch (error) {
      console.error("Error checking username availability:", error);
      throw error;
    }
  }

  /**
   * 檢查電子郵件是否可用
   */
  async isEmailAvailable(email: string): Promise<boolean> {
    try {
      const count = await this.prisma.user.count({
        where: { email },
      });
      return count === 0;
    } catch (error) {
      console.error("Error checking email availability:", error);
      throw error;
    }
  }

  /**
   * 獲取用戶統計信息
   */
  async getStatistics(): Promise<UserStatistics> {
    try {
      const [total, roleCounts, statusCounts, activeUsers] = await Promise.all([
        this.prisma.user.count(),
        this.prisma.user.groupBy({
          by: ["role"],
          _count: { role: true },
        }),
        this.prisma.user.groupBy({
          by: ["status"],
          _count: { status: true },
        }),
        this.prisma.user.count({ where: { status: "active" } }),
      ]);

      // 計算時間範圍統計
      const now = new Date();
      const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

      const [
        newUsersThisWeek,
        newUsersThisMonth,
        activeUsersThisWeek,
        activeUsersThisMonth,
      ] = await Promise.all([
        this.prisma.user.count({ where: { createdAt: { gte: weekAgo } } }),
        this.prisma.user.count({ where: { createdAt: { gte: monthAgo } } }),
        this.prisma.user.count({
          where: {
            status: "active",
            lastLoginAt: { gte: weekAgo },
          },
        }),
        this.prisma.user.count({
          where: {
            status: "active",
            lastLoginAt: { gte: monthAgo },
          },
        }),
      ]);

      return {
        total,
        byRole: roleCounts.reduce(
          (acc, item) => {
            acc[item.role as User["role"]] = item._count.role;
            return acc;
          },
          {} as Record<User["role"], number>,
        ),
        byStatus: statusCounts.reduce(
          (acc, item) => {
            acc[item.status as User["status"]] = item._count.status;
            return acc;
          },
          {} as Record<User["status"], number>,
        ),
        activeUsers,
        newUsersThisWeek,
        newUsersThisMonth,
        activeUsersThisWeek,
        activeUsersThisMonth,
      };
    } catch (error) {
      console.error("Error getting user statistics:", error);
      throw error;
    }
  }

  /**
   * 記錄用戶登錄
   */
  async recordLogin(userId: string): Promise<void> {
    try {
      await this.prisma.user.update({
        where: { id: userId },
        data: { lastLoginAt: new Date() },
      });
    } catch (error) {
      console.error("Error recording user login:", error);
      throw error;
    }
  }

  /**
   * 更新最後活動時間
   */
  async updateLastActivity(userId: string): Promise<void> {
    try {
      await this.prisma.user.update({
        where: { id: userId },
        data: { lastLoginAt: new Date() },
      });
    } catch (error) {
      console.error("Error updating last activity:", error);
      throw error;
    }
  }

  // 實現搜索功能
  async search(
    query: string,
    params?: PaginationParams,
  ): Promise<PaginatedResult<User>> {
    try {
      const {
        page = 1,
        limit = 50,
        sortBy = "createdAt",
        sortOrder = "desc",
      } = params || {};
      const skip = (page - 1) * limit;

      const whereClause = {
        OR: [
          {
            username: {
              contains: query,
            },
          },
          {
            email: {
              contains: query,
            },
          },
          {
            displayName: {
              contains: query,
            },
          },
        ],
      };

      const [results, total] = await Promise.all([
        this.prisma.user.findMany({
          where: whereClause,
          skip,
          take: limit,
          orderBy: { [sortBy]: sortOrder },
        }),
        this.prisma.user.count({
          where: whereClause,
        }),
      ]);

      const entities = results.map((result) => this.toDomainEntity(result));
      return this.createPaginatedResult(entities, total, page, limit);
    } catch (error) {
      console.error("Error searching users:", error);
      throw error;
    }
  }

  async searchByCondition(
    query: string,
    condition: Record<string, unknown>,
    params?: PaginationParams,
  ): Promise<PaginatedResult<User>> {
    try {
      const {
        page = 1,
        limit = 50,
        sortBy = "createdAt",
        sortOrder = "desc",
      } = params || {};
      const skip = (page - 1) * limit;

      const whereClause = {
        AND: [
          condition,
          {
            OR: [
              {
                username: {
                  contains: query,
                },
              },
              {
                email: {
                  contains: query,
                },
              },
              {
                displayName: {
                  contains: query,
                },
              },
            ],
          },
        ],
      };

      const [results, total] = await Promise.all([
        this.prisma.user.findMany({
          where: whereClause,
          skip,
          take: limit,
          orderBy: { [sortBy]: sortOrder },
        }),
        this.prisma.user.count({
          where: whereClause,
        }),
      ]);

      const entities = results.map((result) => this.toDomainEntity(result));
      return this.createPaginatedResult(entities, total, page, limit);
    } catch (error) {
      console.error("Error searching users by condition:", error);
      throw error;
    }
  }
}
