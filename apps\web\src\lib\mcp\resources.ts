import { get } from 'svelte/store';
import { noteStore } from '$stores/notes';
import { dependencyStore } from '$stores/dependency';
import { searchService } from '$lib/services/searchService';
import type { MCPResource } from './types';

// MCP 資源內容類型
export interface MCPResourceContent {
	uri: string;
	mimeType: string;
	text?: string;
	blob?: Uint8Array;
}

// 資源處理結果
export interface MCPResourceResult {
	contents: MCPResourceContent[];
	isError?: boolean;
	error?: string;
}

// 錯誤處理輔助函數
function createErrorResult(message: string, uri: string): MCPResourceResult {
	return {
		contents: [
			{
				uri,
				mimeType: 'application/json',
				text: JSON.stringify(
					{
						error: message,
						timestamp: new Date().toISOString()
					},
					null,
					2
				)
			}
		],
		isError: true,
		error: message
	};
}

// 成功結果輔助函數
function createSuccessResult(
	data: any,
	uri: string,
	mimeType: string = 'application/json'
): MCPResourceResult {
	return {
		contents: [
			{
				uri,
				mimeType,
				text: JSON.stringify(data, null, 2)
			}
		]
	};
}

// MCP 資源處理器
export class MCPResourceHandler {
	/**
	 * 處理資源請求
	 */
	static async handleResource(uri: string): Promise<MCPResourceResult> {
		try {
			const url = new URL(uri);
			const protocol = url.protocol.slice(0, -1); // 移除末尾的 ':'
			const path = url.pathname;
			const searchParams = url.searchParams;

			switch (protocol) {
				case 'notes':
					return await this.handleNotesResource(path, searchParams, uri);
				case 'dependencies':
					return await this.handleDependenciesResource(path, searchParams, uri);
				default:
					return createErrorResult(`Unsupported protocol: ${protocol}`, uri);
			}
		} catch (error) {
			return createErrorResult(`Invalid URI format: ${error}`, uri);
		}
	}

	/**
	 * 處理筆記相關資源
	 */
	private static async handleNotesResource(
		path: string,
		searchParams: URLSearchParams,
		uri: string
	): Promise<MCPResourceResult> {
		const currentNotes = get(noteStore).notes;

		switch (path) {
			case '/all':
				return this.handleAllNotes(currentNotes, searchParams, uri);

			case '/search':
				return await this.handleNotesSearch(currentNotes, searchParams, uri);

			case '/by-id':
				return this.handleNoteById(currentNotes, searchParams, uri);

			case '/by-tag':
				return this.handleNotesByTag(currentNotes, searchParams, uri);

			case '/by-category':
				return this.handleNotesByCategory(currentNotes, searchParams, uri);

			case '/statistics':
				return this.handleNotesStatistics(currentNotes, uri);

			default:
				return createErrorResult(`Unknown notes resource path: ${path}`, uri);
		}
	}

	/**
	 * 處理依賴關係相關資源
	 */
	private static async handleDependenciesResource(
		path: string,
		searchParams: URLSearchParams,
		uri: string
	): Promise<MCPResourceResult> {
		const currentNotes = get(noteStore).notes;

		switch (path) {
			case '/graph':
				return await this.handleDependencyGraph(currentNotes, searchParams, uri);

			case '/analysis':
				return await this.handleDependencyAnalysis(currentNotes, searchParams, uri);

			case '/node':
				return await this.handleDependencyNode(currentNotes, searchParams, uri);

			case '/statistics':
				return await this.handleDependencyStatistics(currentNotes, uri);

			default:
				return createErrorResult(`Unknown dependencies resource path: ${path}`, uri);
		}
	}

	// 筆記資源處理方法
	private static handleAllNotes(
		notes: any[],
		searchParams: URLSearchParams,
		uri: string
	): MCPResourceResult {
		const limit = parseInt(searchParams.get('limit') || '50');
		const offset = parseInt(searchParams.get('offset') || '0');
		const includeContent = searchParams.get('includeContent') === 'true';

		let processedNotes = notes.map(note => ({
			id: note.id,
			title: note.title,
			excerpt: note.excerpt,
			tags: note.tags,
			status: note.status,
			priority: note.priority,
			categoryId: note.categoryId,
			createdAt: note.createdAt,
			updatedAt: note.updatedAt,
			...(includeContent && { content: note.content })
		}));

		const total = processedNotes.length;
		processedNotes = processedNotes.slice(offset, offset + limit);

		const result = {
			notes: processedNotes,
			pagination: {
				total,
				limit,
				offset,
				hasMore: offset + limit < total
			}
		};

		return createSuccessResult(result, uri);
	}

	private static async handleNotesSearch(
		notes: any[],
		searchParams: URLSearchParams,
		uri: string
	): MCPResourceResult {
		const query = searchParams.get('query');
		const tags = searchParams.getAll('tag');
		const status = searchParams.get('status');
		const priority = searchParams.get('priority');
		const categoryId = searchParams.get('categoryId');
		const limit = parseInt(searchParams.get('limit') || '20');
		const offset = parseInt(searchParams.get('offset') || '0');

		let filteredNotes = [...notes];

		// 應用搜索查詢
		if (query) {
			const searchResults = await searchService.search(query, {
				includeContent: true,
				includeTags: true,
				fuzzySearch: true
			});
			const searchNoteIds = new Set(searchResults.map(result => result.id));
			filteredNotes = filteredNotes.filter(note => searchNoteIds.has(note.id));
		}

		// 應用過濾條件
		if (tags.length > 0) {
			filteredNotes = filteredNotes.filter(note =>
				tags.some(tag => note.tags.some((noteTag: any) => noteTag.name === tag))
			);
		}

		if (status) {
			filteredNotes = filteredNotes.filter(note => note.status === status);
		}

		if (priority) {
			filteredNotes = filteredNotes.filter(note => note.priority === priority);
		}

		if (categoryId) {
			filteredNotes = filteredNotes.filter(note => note.categoryId === categoryId);
		}

		const total = filteredNotes.length;
		const paginatedNotes = filteredNotes.slice(offset, offset + limit);

		const result = {
			query: {
				text: query,
				tags,
				status,
				priority,
				categoryId
			},
			notes: paginatedNotes,
			pagination: {
				total,
				limit,
				offset,
				hasMore: offset + limit < total
			}
		};

		return createSuccessResult(result, uri);
	}

	private static handleNoteById(
		notes: any[],
		searchParams: URLSearchParams,
		uri: string
	): MCPResourceResult {
		const id = searchParams.get('id');
		if (!id) {
			return createErrorResult('Missing required parameter: id', uri);
		}

		const note = notes.find(note => note.id === id);
		if (!note) {
			return createErrorResult(`Note with ID "${id}" not found`, uri);
		}

		return createSuccessResult(note, uri);
	}

	private static handleNotesByTag(
		notes: any[],
		searchParams: URLSearchParams,
		uri: string
	): MCPResourceResult {
		const tag = searchParams.get('tag');
		if (!tag) {
			return createErrorResult('Missing required parameter: tag', uri);
		}

		const filteredNotes = notes.filter(note =>
			note.tags.some((noteTag: any) => noteTag.name === tag)
		);

		const result = {
			tag,
			count: filteredNotes.length,
			notes: filteredNotes
		};

		return createSuccessResult(result, uri);
	}

	private static handleNotesByCategory(
		notes: any[],
		searchParams: URLSearchParams,
		uri: string
	): MCPResourceResult {
		const categoryId = searchParams.get('categoryId');
		if (!categoryId) {
			return createErrorResult('Missing required parameter: categoryId', uri);
		}

		const filteredNotes = notes.filter(note => note.categoryId === categoryId);

		const result = {
			categoryId,
			count: filteredNotes.length,
			notes: filteredNotes
		};

		return createSuccessResult(result, uri);
	}

	private static handleNotesStatistics(notes: any[], uri: string): MCPResourceResult {
		const stats = {
			total: notes.length,
			byStatus: notes.reduce((acc: any, note) => {
				acc[note.status] = (acc[note.status] || 0) + 1;
				return acc;
			}, {}),
			byPriority: notes.reduce((acc: any, note) => {
				acc[note.priority] = (acc[note.priority] || 0) + 1;
				return acc;
			}, {}),
			byCategory: notes.reduce((acc: any, note) => {
				const category = note.categoryId || 'uncategorized';
				acc[category] = (acc[category] || 0) + 1;
				return acc;
			}, {}),
			tagCloud: this.generateTagCloud(notes),
			recentActivity: notes
				.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
				.slice(0, 10)
				.map(note => ({
					id: note.id,
					title: note.title,
					updatedAt: note.updatedAt
				}))
		};

		return createSuccessResult(stats, uri);
	}

	// 依賴關係資源處理方法
	private static async handleDependencyGraph(
		notes: any[],
		searchParams: URLSearchParams,
		uri: string
	): Promise<MCPResourceResult> {
		const includeIsolated = searchParams.get('includeIsolated') !== 'false';
		const similarityThreshold = parseFloat(searchParams.get('similarityThreshold') || '0.3');

		const options = {
			includeTagConnections: true,
			includeCategoryConnections: true,
			includeContentSimilarity: true,
			similarityThreshold,
			maxDistance: 3,
			excludeIsolatedNodes: !includeIsolated
		};

		const graph = await dependencyStore.analyzeDependencies(notes);

		const result = {
			graph: get(dependencyStore).graph,
			options,
			generated: new Date().toISOString()
		};

		return createSuccessResult(result, uri);
	}

	private static async handleDependencyAnalysis(
		notes: any[],
		searchParams: URLSearchParams,
		uri: string
	): Promise<MCPResourceResult> {
		const graph = await dependencyStore.analyzeDependencies(notes);
		const currentGraph = get(dependencyStore).graph;

		if (!currentGraph) {
			return createErrorResult('No dependency graph available', uri);
		}

		const analysis = {
			summary: currentGraph.metadata,
			nodeAnalysis: {
				totalNodes: currentGraph.nodes.length,
				nodesByGroup: currentGraph.nodes.reduce((acc: any, node) => {
					acc[node.group] = (acc[node.group] || 0) + 1;
					return acc;
				}, {}),
				averageSize:
					currentGraph.nodes.reduce((sum, node) => sum + node.size, 0) / currentGraph.nodes.length
			},
			linkAnalysis: {
				totalLinks: currentGraph.links.length,
				linksByType: currentGraph.links.reduce((acc: any, link) => {
					acc[link.type] = (acc[link.type] || 0) + 1;
					return acc;
				}, {}),
				averageStrength:
					currentGraph.links.reduce((sum, link) => sum + link.strength, 0) /
					currentGraph.links.length
			},
			generated: new Date().toISOString()
		};

		return createSuccessResult(analysis, uri);
	}

	private static async handleDependencyNode(
		notes: any[],
		searchParams: URLSearchParams,
		uri: string
	): Promise<MCPResourceResult> {
		const nodeId = searchParams.get('nodeId');
		if (!nodeId) {
			return createErrorResult('Missing required parameter: nodeId', uri);
		}

		const currentGraph = get(dependencyStore).graph;
		if (!currentGraph) {
			return createErrorResult('No dependency graph available', uri);
		}

		const node = currentGraph.nodes.find(n => n.id === nodeId);
		if (!node) {
			return createErrorResult(`Node with ID "${nodeId}" not found`, uri);
		}

		const neighbors = dependencyStore.findNeighbors(nodeId, 1);
		const connections = currentGraph.links.filter(link => {
			const sourceId = typeof link.source === 'string' ? link.source : link.source.id;
			const targetId = typeof link.target === 'string' ? link.target : link.target.id;
			return sourceId === nodeId || targetId === nodeId;
		});

		const result = {
			node,
			neighbors: neighbors.map(neighborId => {
				const neighborNode = currentGraph.nodes.find(n => n.id === neighborId);
				const neighborNote = notes.find(note => note.id === neighborId);
				return {
					id: neighborId,
					title: neighborNode?.title || neighborNote?.title || 'Unknown',
					type: neighborNode?.type || 'note'
				};
			}),
			connections: connections.map(link => ({
				type: link.type,
				strength: link.strength,
				source: typeof link.source === 'string' ? link.source : link.source.id,
				target: typeof link.target === 'string' ? link.target : link.target.id
			})),
			statistics: {
				degree: connections.length,
				neighborCount: neighbors.length
			}
		};

		return createSuccessResult(result, uri);
	}

	private static async handleDependencyStatistics(
		notes: any[],
		uri: string
	): Promise<MCPResourceResult> {
		const currentGraph = get(dependencyStore).graph;
		if (!currentGraph) {
			return createErrorResult('No dependency graph available', uri);
		}

		const centrality = dependencyStore.calculateCentrality();
		const centralityValues = Array.from(centrality.values());

		const stats = {
			graph: currentGraph.metadata,
			centrality: {
				average: centralityValues.reduce((sum, val) => sum + val, 0) / centralityValues.length,
				max: Math.max(...centralityValues),
				min: Math.min(...centralityValues),
				distribution: centralityValues.reduce((acc: any, val) => {
					const bucket = Math.floor(val / 5) * 5;
					acc[bucket] = (acc[bucket] || 0) + 1;
					return acc;
				}, {})
			},
			topNodes: Array.from(centrality.entries())
				.sort(([, a], [, b]) => b - a)
				.slice(0, 10)
				.map(([nodeId, centralityValue]) => {
					const node = currentGraph.nodes.find(n => n.id === nodeId);
					const note = notes.find(note => note.id === nodeId);
					return {
						id: nodeId,
						title: node?.title || note?.title || 'Unknown',
						centrality: centralityValue
					};
				}),
			generated: new Date().toISOString()
		};

		return createSuccessResult(stats, uri);
	}

	// 輔助方法
	private static generateTagCloud(notes: any[]): Array<{ tag: string; count: number }> {
		const tagCounts: Record<string, number> = {};

		notes.forEach(note => {
			note.tags.forEach((tag: any) => {
				tagCounts[tag.name] = (tagCounts[tag.name] || 0) + 1;
			});
		});

		return Object.entries(tagCounts)
			.map(([tag, count]) => ({ tag, count }))
			.sort((a, b) => b.count - a.count);
	}

	/**
	 * 獲取所有可用資源的列表
	 */
	static getAvailableResources(): MCPResource[] {
		return [
			{
				uri: 'notes://all',
				name: 'All Notes',
				description: 'Access to all notes in the system with pagination support',
				mimeType: 'application/json'
			},
			{
				uri: 'notes://search',
				name: 'Note Search',
				description: 'Search interface for notes with filtering options',
				mimeType: 'application/json'
			},
			{
				uri: 'notes://by-id?id={noteId}',
				name: 'Note by ID',
				description: 'Get a specific note by its ID',
				mimeType: 'application/json'
			},
			{
				uri: 'notes://by-tag?tag={tagName}',
				name: 'Notes by Tag',
				description: 'Get all notes with a specific tag',
				mimeType: 'application/json'
			},
			{
				uri: 'notes://statistics',
				name: 'Notes Statistics',
				description: 'Statistical overview of all notes',
				mimeType: 'application/json'
			},
			{
				uri: 'dependencies://graph',
				name: 'Dependency Graph',
				description: 'Complete dependency graph of all notes',
				mimeType: 'application/json'
			},
			{
				uri: 'dependencies://analysis',
				name: 'Dependency Analysis',
				description: 'Detailed analysis of the dependency graph',
				mimeType: 'application/json'
			},
			{
				uri: 'dependencies://node?nodeId={nodeId}',
				name: 'Dependency Node',
				description: 'Detailed information about a specific node in the dependency graph',
				mimeType: 'application/json'
			},
			{
				uri: 'dependencies://statistics',
				name: 'Dependency Statistics',
				description: 'Statistical analysis of the dependency graph',
				mimeType: 'application/json'
			}
		];
	}
}
