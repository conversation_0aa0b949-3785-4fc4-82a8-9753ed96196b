import { describe, it, expect, beforeAll, afterAll, beforeEach } from "vitest";
import { prismaClient } from "../prisma/client.js";
import { RepositoryFactory } from "../repositories/RepositoryFactory.js";
import type { User } from "../repositories/UserRepository.js";
import type { Note } from "../repositories/NoteRepository.js";
import type { Dependency } from "../repositories/DependencyRepository.js";

describe("Simple Repository Tests", () => {
  let repositoryFactory: RepositoryFactory;

  beforeAll(async () => {
    await prismaClient.connect();
    repositoryFactory = new RepositoryFactory();
  });

  afterAll(async () => {
    await prismaClient.disconnect();
  });

  beforeEach(async () => {
    // 清理測試數據
    await prismaClient.client.dependency.deleteMany({
      where: { id: { startsWith: "test-simple-" } },
    });
    await prismaClient.client.note.deleteMany({
      where: { id: { startsWith: "test-simple-" } },
    });
    await prismaClient.client.user.deleteMany({
      where: { id: { startsWith: "test-simple-" } },
    });
  });

  describe("UserRepository Basic Operations", () => {
    it("should create, read, update, and delete a user", async () => {
      const userRepo = repositoryFactory.userRepository;

      // Create
      const userData: User = {
        id: "test-simple-user-1",
        username: "simpleuser",
        email: "<EMAIL>",
        displayName: "簡單用戶",
        role: "user",
        status: "active",
        metadata: { test: true },
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const createdUser = await userRepo.save(userData);
      expect(createdUser.id).toBe("test-simple-user-1");
      expect(createdUser.username).toBe("simpleuser");

      // Read
      const foundUser = await userRepo.findById("test-simple-user-1");
      expect(foundUser).toBeDefined();
      expect(foundUser?.email).toBe("<EMAIL>");

      // Update
      if (foundUser) {
        foundUser.displayName = "更新的用戶";
        const updatedUser = await userRepo.save(foundUser);
        expect(updatedUser.displayName).toBe("更新的用戶");
      }

      // Delete
      await userRepo.deleteById("test-simple-user-1");
      const deletedUser = await userRepo.findById("test-simple-user-1");
      expect(deletedUser).toBeNull();
    });

    it("should find users by username and email", async () => {
      const userRepo = repositoryFactory.userRepository;

      const userData: User = {
        id: "test-simple-user-2",
        username: "finduser",
        email: "<EMAIL>",
        role: "user",
        status: "active",
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      await userRepo.save(userData);

      // Find by username
      const userByUsername = await userRepo.findByUsername("finduser");
      expect(userByUsername).toBeDefined();
      expect(userByUsername?.id).toBe("test-simple-user-2");

      // Find by email
      const userByEmail = await userRepo.findByEmail("<EMAIL>");
      expect(userByEmail).toBeDefined();
      expect(userByEmail?.id).toBe("test-simple-user-2");

      // Check availability
      expect(await userRepo.isUsernameAvailable("finduser")).toBe(false);
      expect(await userRepo.isUsernameAvailable("newuser")).toBe(true);
      expect(await userRepo.isEmailAvailable("<EMAIL>")).toBe(false);
      expect(await userRepo.isEmailAvailable("<EMAIL>")).toBe(true);
    });
  });

  describe("NoteRepository Basic Operations", () => {
    let testUserId: string;

    beforeEach(async () => {
      // 創建測試用戶
      testUserId = "test-simple-user-note";
      const userRepo = repositoryFactory.userRepository;
      const userData: User = {
        id: testUserId,
        username: "noteuser",
        email: "<EMAIL>",
        role: "user",
        status: "active",
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      await userRepo.save(userData);
    });

    it("should create and retrieve notes", async () => {
      const noteRepo = repositoryFactory.noteRepository;

      const noteData: Note = {
        id: "test-simple-note-1",
        title: "測試筆記",
        content: "這是一個測試筆記的內容",
        category: "測試",
        tags: ["測試", "筆記"],
        status: "draft",
        priority: "medium",
        version: "1.0.0",
        authorId: testUserId,
        metadata: { test: true },
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Create
      const createdNote = await noteRepo.save(noteData);
      expect(createdNote.id).toBe("test-simple-note-1");
      expect(createdNote.title).toBe("測試筆記");

      // Read
      const foundNote = await noteRepo.findById("test-simple-note-1");
      expect(foundNote).toBeDefined();
      expect(foundNote?.content).toBe("這是一個測試筆記的內容");
      expect(foundNote?.tags).toEqual(["測試", "筆記"]);
    });

    it("should find notes by author", async () => {
      const noteRepo = repositoryFactory.noteRepository;

      const notes: Note[] = [
        {
          id: "test-simple-note-2",
          title: "筆記1",
          content: "內容1",
          status: "draft",
          priority: "medium",
          version: "1.0.0",
          authorId: testUserId,
          tags: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: "test-simple-note-3",
          title: "筆記2",
          content: "內容2",
          status: "published",
          priority: "high",
          version: "1.0.0",
          authorId: testUserId,
          tags: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      for (const note of notes) {
        await noteRepo.save(note);
      }

      // Find by author
      const authorNotes = await noteRepo.findByAuthorId(testUserId);
      expect(authorNotes.data).toHaveLength(2);

      // Find drafts by author
      const draftNotes = await noteRepo.findDraftsByAuthor(testUserId);
      expect(draftNotes.data).toHaveLength(1);
      expect(draftNotes.data[0].status).toBe("draft");

      // Find published by author
      const publishedNotes = await noteRepo.findPublishedByAuthor(testUserId);
      expect(publishedNotes.data).toHaveLength(1);
      expect(publishedNotes.data[0].status).toBe("published");
    });

    it("should search notes", async () => {
      const noteRepo = repositoryFactory.noteRepository;

      const noteData: Note = {
        id: "test-simple-note-search",
        title: "可搜索的筆記",
        content: "這個筆記包含特殊關鍵字：JavaScript",
        category: "程式設計",
        tags: ["JavaScript", "程式設計"],
        status: "published",
        priority: "medium",
        version: "1.0.0",
        authorId: testUserId,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      await noteRepo.save(noteData);

      // Search by title
      const titleResults = await noteRepo.search("可搜索");
      expect(titleResults.data).toHaveLength(1);
      expect(titleResults.data[0].title).toBe("可搜索的筆記");

      // Search by content
      const contentResults = await noteRepo.search("JavaScript");
      expect(contentResults.data).toHaveLength(1);

      // Search by category
      const categoryResults = await noteRepo.search("程式設計");
      expect(categoryResults.data).toHaveLength(1);
    });
  });

  describe("DependencyRepository Basic Operations", () => {
    let testUserId: string;
    let sourceNoteId: string;
    let targetNoteId: string;

    beforeEach(async () => {
      // 創建測試用戶和筆記
      testUserId = "test-simple-user-dep";
      sourceNoteId = "test-simple-note-source";
      targetNoteId = "test-simple-note-target";

      const userRepo = repositoryFactory.userRepository;
      const noteRepo = repositoryFactory.noteRepository;

      // 創建用戶
      const userData: User = {
        id: testUserId,
        username: "depuser",
        email: "<EMAIL>",
        role: "user",
        status: "active",
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      await userRepo.save(userData);

      // 創建筆記
      const sourceNote: Note = {
        id: sourceNoteId,
        title: "源筆記",
        content: "這是源筆記",
        status: "draft",
        priority: "medium",
        version: "1.0.0",
        authorId: testUserId,
        tags: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const targetNote: Note = {
        id: targetNoteId,
        title: "目標筆記",
        content: "這是目標筆記",
        status: "draft",
        priority: "medium",
        version: "1.0.0",
        authorId: testUserId,
        tags: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      await noteRepo.save(sourceNote);
      await noteRepo.save(targetNote);
    });

    it("should create and manage dependencies", async () => {
      const depRepo = repositoryFactory.dependencyRepository;

      // Create dependency
      const dependency = await depRepo.createDependency(
        sourceNoteId,
        targetNoteId,
        "reference",
        "medium",
        "測試依賴關係",
      );

      expect(dependency).toBeDefined();
      expect(dependency.sourceNoteId).toBe(sourceNoteId);
      expect(dependency.targetNoteId).toBe(targetNoteId);
      expect(dependency.type).toBe("reference");
      expect(dependency.isActive).toBe(true);

      // Check existence
      const exists = await depRepo.existsBetween(sourceNoteId, targetNoteId);
      expect(exists).toBe(true);

      // Find dependency
      const foundDep = await depRepo.findBetween(sourceNoteId, targetNoteId);
      expect(foundDep).toBeDefined();
      expect(foundDep?.id).toBe(dependency.id);

      // Update strength
      await depRepo.updateStrength(dependency.id, "strong");
      const updatedDep = await depRepo.findById(dependency.id);
      expect(updatedDep?.strength).toBe("strong");

      // Deactivate
      await depRepo.deactivate(dependency.id);
      const deactivatedDep = await depRepo.findById(dependency.id);
      expect(deactivatedDep?.isActive).toBe(false);

      // Reactivate
      await depRepo.activate(dependency.id);
      const reactivatedDep = await depRepo.findById(dependency.id);
      expect(reactivatedDep?.isActive).toBe(true);
    });

    it("should get dependency statistics", async () => {
      const depRepo = repositoryFactory.dependencyRepository;

      // Create some dependencies
      await depRepo.createDependency(
        sourceNoteId,
        targetNoteId,
        "reference",
        "medium",
      );

      const stats = await depRepo.getStatistics();
      expect(stats.total).toBeGreaterThanOrEqual(1);
      expect(stats.active).toBeGreaterThanOrEqual(1);

      const noteStats = await depRepo.getNoteStatistics(sourceNoteId);
      expect(noteStats.outgoingCount).toBe(1);
      expect(noteStats.totalConnections).toBe(1);
    });
  });
});
