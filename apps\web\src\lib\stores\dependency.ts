import { writable, derived, get } from 'svelte/store';
import type {
	DependencyGraph,
	DependencyNode,
	DependencyAnalysisOptions,
	DependencyService
} from '$lib/services/dependencyService';
import { dependencyService } from '$lib/services/dependencyService';
import { noteStore } from './notes';
import type { Note } from '$types';

export interface DependencyState {
	// 圖數據
	graph: DependencyGraph | null;

	// 分析選項
	options: DependencyAnalysisOptions;

	// UI 狀態
	selectedNode: DependencyNode | null;
	highlightedNode: string | null;
	showLabels: boolean;
	showLegend: boolean;
	interactive: boolean;

	// 加載狀態
	isAnalyzing: boolean;
	error: string | null;

	// 視圖狀態
	viewMode: 'graph' | 'list' | 'matrix';
	filterQuery: string;
	sortBy: 'centrality' | 'size' | 'title' | 'connections';
	sortOrder: 'asc' | 'desc';
}

const initialState: DependencyState = {
	graph: null,
	options: {
		includeTagConnections: true,
		includeCategoryConnections: true,
		includeContentSimilarity: true,
		similarityThreshold: 0.3,
		maxDistance: 3,
		excludeIsolatedNodes: true
	},
	selectedNode: null,
	highlightedNode: null,
	showLabels: true,
	showLegend: true,
	interactive: true,
	isAnalyzing: false,
	error: null,
	viewMode: 'graph',
	filterQuery: '',
	sortBy: 'centrality',
	sortOrder: 'desc'
};

function createDependencyStore() {
	const { subscribe, set, update } = writable<DependencyState>(initialState);

	return {
		subscribe,

		/**
		 * 分析依賴關係
		 */
		async analyzeDependencies(notes?: Note[]) {
			update(state => ({ ...state, isAnalyzing: true, error: null }));

			try {
				// 使用提供的筆記或從 noteStore 獲取
				const notesToAnalyze = notes || get(noteStore).notes;

				if (notesToAnalyze.length === 0) {
					update(state => ({
						...state,
						isAnalyzing: false,
						graph: {
							nodes: [],
							links: [],
							metadata: { totalNodes: 0, totalLinks: 0, clusters: 0, density: 0 }
						}
					}));
					return;
				}

				const currentState = get({ subscribe });
				const graph = await dependencyService.analyzeDependencies(
					notesToAnalyze,
					currentState.options
				);

				update(state => ({
					...state,
					isAnalyzing: false,
					graph,
					selectedNode: null,
					highlightedNode: null
				}));
			} catch (error) {
				console.error('Failed to analyze dependencies:', error);
				update(state => ({
					...state,
					isAnalyzing: false,
					error: error instanceof Error ? error.message : 'Analysis failed'
				}));
			}
		},

		/**
		 * 更新分析選項
		 */
		async updateOptions(newOptions: Partial<DependencyAnalysisOptions>) {
			update(state => ({
				...state,
				options: { ...state.options, ...newOptions }
			}));

			// 重新分析
			await this.analyzeDependencies();
		},

		/**
		 * 選擇節點
		 */
		selectNode(node: DependencyNode | null) {
			update(state => ({ ...state, selectedNode: node }));
		},

		/**
		 * 高亮節點
		 */
		highlightNode(nodeId: string | null) {
			update(state => ({ ...state, highlightedNode: nodeId }));
		},

		/**
		 * 切換標籤顯示
		 */
		toggleLabels() {
			update(state => ({ ...state, showLabels: !state.showLabels }));
		},

		/**
		 * 切換圖例顯示
		 */
		toggleLegend() {
			update(state => ({ ...state, showLegend: !state.showLegend }));
		},

		/**
		 * 切換交互模式
		 */
		toggleInteractive() {
			update(state => ({ ...state, interactive: !state.interactive }));
		},

		/**
		 * 設置視圖模式
		 */
		setViewMode(mode: DependencyState['viewMode']) {
			update(state => ({ ...state, viewMode: mode }));
		},

		/**
		 * 設置過濾查詢
		 */
		setFilterQuery(query: string) {
			update(state => ({ ...state, filterQuery: query }));
		},

		/**
		 * 設置排序
		 */
		setSorting(sortBy: DependencyState['sortBy'], sortOrder: DependencyState['sortOrder']) {
			update(state => ({ ...state, sortBy, sortOrder }));
		},

		/**
		 * 查找節點鄰居
		 */
		findNeighbors(nodeId: string, maxDistance: number = 1): string[] {
			const currentState = get({ subscribe });
			if (!currentState.graph) return [];

			return dependencyService.findNeighbors(nodeId, currentState.graph, maxDistance);
		},

		/**
		 * 計算節點中心性
		 */
		calculateCentrality(): Map<string, number> {
			const currentState = get({ subscribe });
			if (!currentState.graph) return new Map();

			return dependencyService.calculateCentrality(currentState.graph);
		},

		/**
		 * 重置狀態
		 */
		reset() {
			set(initialState);
		},

		/**
		 * 清除錯誤
		 */
		clearError() {
			update(state => ({ ...state, error: null }));
		}
	};
}

export const dependencyStore = createDependencyStore();

// 衍生狀態
export const filteredNodes = derived(dependencyStore, $dependencyStore => {
	if (!$dependencyStore.graph) return [];

	let nodes = [...$dependencyStore.graph.nodes];

	// 應用過濾
	if ($dependencyStore.filterQuery) {
		const query = $dependencyStore.filterQuery.toLowerCase();
		nodes = nodes.filter(
			node =>
				node.title.toLowerCase().includes(query) ||
				node.status.toLowerCase().includes(query) ||
				node.priority.toLowerCase().includes(query)
		);
	}

	// 應用排序
	nodes.sort((a, b) => {
		let comparison = 0;

		switch ($dependencyStore.sortBy) {
			case 'title':
				comparison = a.title.localeCompare(b.title);
				break;
			case 'size':
				comparison = a.size - b.size;
				break;
			case 'centrality':
				// 計算中心性需要圖數據
				const centrality = dependencyService.calculateCentrality($dependencyStore.graph!);
				const aCentrality = centrality.get(a.id) || 0;
				const bCentrality = centrality.get(b.id) || 0;
				comparison = aCentrality - bCentrality;
				break;
			case 'connections':
				// 計算連接數
				const aConnections = $dependencyStore.graph!.links.filter(link => {
					const sourceId = typeof link.source === 'string' ? link.source : link.source.id;
					const targetId = typeof link.target === 'string' ? link.target : link.target.id;
					return sourceId === a.id || targetId === a.id;
				}).length;
				const bConnections = $dependencyStore.graph!.links.filter(link => {
					const sourceId = typeof link.source === 'string' ? link.source : link.source.id;
					const targetId = typeof link.target === 'string' ? link.target : link.target.id;
					return sourceId === b.id || targetId === b.id;
				}).length;
				comparison = aConnections - bConnections;
				break;
		}

		return $dependencyStore.sortOrder === 'desc' ? -comparison : comparison;
	});

	return nodes;
});

export const graphMetadata = derived(
	dependencyStore,
	$dependencyStore => $dependencyStore.graph?.metadata || null
);

export const selectedNodeDetails = derived(
	[dependencyStore, noteStore],
	([$dependencyStore, $noteStore]) => {
		if (!$dependencyStore.selectedNode) return null;

		const note = $noteStore.notes.find(n => n.id === $dependencyStore.selectedNode!.id);
		return {
			node: $dependencyStore.selectedNode,
			note: note || null
		};
	}
);

export const graphStats = derived(dependencyStore, $dependencyStore => {
	if (!$dependencyStore.graph) return null;

	const { nodes, links } = $dependencyStore.graph;

	// 計算各種統計
	const nodesByStatus = nodes.reduce(
		(acc, node) => {
			acc[node.status] = (acc[node.status] || 0) + 1;
			return acc;
		},
		{} as Record<string, number>
	);

	const nodesByPriority = nodes.reduce(
		(acc, node) => {
			acc[node.priority] = (acc[node.priority] || 0) + 1;
			return acc;
		},
		{} as Record<string, number>
	);

	const linksByType = links.reduce(
		(acc, link) => {
			acc[link.type] = (acc[link.type] || 0) + 1;
			return acc;
		},
		{} as Record<string, number>
	);

	// 計算中心性分布
	const centrality = dependencyService.calculateCentrality($dependencyStore.graph);
	const centralityValues = Array.from(centrality.values());
	const avgCentrality =
		centralityValues.length > 0
			? centralityValues.reduce((sum, val) => sum + val, 0) / centralityValues.length
			: 0;

	return {
		totalNodes: nodes.length,
		totalLinks: links.length,
		nodesByStatus,
		nodesByPriority,
		linksByType,
		avgCentrality,
		maxCentrality: Math.max(...centralityValues, 0),
		density: $dependencyStore.graph.metadata.density
	};
});

// 自動分析依賴關係當筆記更新時
noteStore.subscribe($noteStore => {
	if ($noteStore.notes.length > 0) {
		// 延遲分析以避免頻繁更新
		setTimeout(() => {
			dependencyStore.analyzeDependencies($noteStore.notes);
		}, 500);
	}
});
