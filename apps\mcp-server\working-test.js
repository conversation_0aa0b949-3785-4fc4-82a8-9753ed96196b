#!/usr/bin/env node

/**
 * Working MCP Test Client
 */

import { Client } from "@modelcontextprotocol/sdk/client/index.js";
import { StdioClientTransport } from "@modelcontextprotocol/sdk/client/stdio.js";
import { fileURLToPath } from "url";
import { dirname, join } from "path";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function testWorkingMCPServer() {
  console.log("🚀 Testing Working MCP Server...\n");

  const client = new Client(
    {
      name: "working-test-client",
      version: "1.0.0",
    },
    {
      capabilities: {},
    },
  );

  try {
    // 連接到服務器
    console.log("📡 Connecting to MCP server...");
    const serverPath = join(__dirname, "working-server.js");
    const transport = new StdioClientTransport({
      command: "node",
      args: [serverPath],
    });

    await client.connect(transport);
    console.log("✅ Connected successfully!\n");

    // 測試工具列表
    console.log("🔧 Testing tools list...");
    const toolsResponse = await client.request({
      method: "tools/list",
      params: {},
    });

    console.log(`Found ${toolsResponse.tools.length} tools:`);
    toolsResponse.tools.forEach((tool) => {
      console.log(`  - ${tool.name}: ${tool.description}`);
    });
    console.log();

    // 測試資源列表
    console.log("📚 Testing resources list...");
    const resourcesResponse = await client.request({
      method: "resources/list",
      params: {},
    });

    console.log(`Found ${resourcesResponse.resources.length} resources:`);
    resourcesResponse.resources.forEach((resource) => {
      console.log(
        `  - ${resource.name} (${resource.uri}): ${resource.description}`,
      );
    });
    console.log();

    // 測試列出現有筆記
    console.log("📋 Testing list existing notes...");
    const listResponse = await client.request({
      method: "tools/call",
      params: {
        name: "list_notes",
        arguments: {},
      },
    });

    const listResult = JSON.parse(listResponse.content[0].text);
    console.log(`Found ${listResult.total} existing notes:`);
    listResult.notes.forEach((note) => {
      console.log(`  - ${note.title} (ID: ${note.id})`);
    });
    console.log();

    // 測試創建筆記
    console.log("📝 Testing note creation...");
    const createResponse = await client.request({
      method: "tools/call",
      params: {
        name: "create_note",
        arguments: {
          title: "測試筆記 - MCP 功能驗證",
          content:
            "這是一個通過 MCP 服務器創建的測試筆記。\n\n## 測試內容\n\n- MCP 工具調用\n- 筆記創建功能\n- 數據持久化\n\n測試時間：" +
            new Date().toLocaleString(),
          tags: ["測試", "MCP", "驗證", "自動化"],
        },
      },
    });

    const createResult = JSON.parse(createResponse.content[0].text);
    console.log(`✅ ${createResult.message}`);
    console.log(`Created note ID: ${createResult.note.id}`);
    const newNoteId = createResult.note.id;
    console.log();

    // 測試獲取筆記
    console.log("📖 Testing note retrieval...");
    const getResponse = await client.request({
      method: "tools/call",
      params: {
        name: "get_note",
        arguments: {
          id: newNoteId,
        },
      },
    });

    const getResult = JSON.parse(getResponse.content[0].text);
    if (getResult.success) {
      console.log(`✅ Retrieved note: "${getResult.note.title}"`);
      console.log(
        `Content preview: ${getResult.note.content.substring(0, 100)}...`,
      );
      console.log(`Tags: ${getResult.note.tags.join(", ")}`);
    } else {
      console.log(`❌ Failed to retrieve note: ${getResult.error}`);
    }
    console.log();

    // 測試搜索筆記
    console.log("🔍 Testing note search...");
    const searchResponse = await client.request({
      method: "tools/call",
      params: {
        name: "search_notes",
        arguments: {
          query: "MCP",
        },
      },
    });

    const searchResult = JSON.parse(searchResponse.content[0].text);
    console.log(`Found ${searchResult.count} notes matching "MCP":`);
    searchResult.results.forEach((note) => {
      console.log(`  - ${note.title} (ID: ${note.id})`);
    });
    console.log();

    // 測試讀取資源 - 所有筆記
    console.log("📊 Testing resource reading - All Notes...");
    const allNotesResponse = await client.request({
      method: "resources/read",
      params: {
        uri: "notes://all",
      },
    });

    const allNotesData = JSON.parse(allNotesResponse.contents[0].text);
    console.log(`Resource contains ${allNotesData.total} notes`);
    console.log(`Timestamp: ${allNotesData.timestamp}`);
    console.log();

    // 測試讀取資源 - 統計信息
    console.log("📈 Testing resource reading - Statistics...");
    const statsResponse = await client.request({
      method: "resources/read",
      params: {
        uri: "notes://stats",
      },
    });

    const statsData = JSON.parse(statsResponse.contents[0].text);
    console.log(`Statistics:`);
    console.log(`  Total notes: ${statsData.total}`);
    console.log(`  Status distribution:`, statsData.byStatus);
    console.log(`  Priority distribution:`, statsData.byPriority);
    console.log(`  Total unique tags: ${statsData.totalTags}`);
    console.log();

    // 測試最終列表
    console.log("📋 Testing final notes list...");
    const finalListResponse = await client.request({
      method: "tools/call",
      params: {
        name: "list_notes",
        arguments: {
          limit: 10,
        },
      },
    });

    const finalListResult = JSON.parse(finalListResponse.content[0].text);
    console.log(`Final count: ${finalListResult.total} notes`);
    finalListResult.notes.forEach((note) => {
      console.log(`  - ${note.title} (${note.status}, ${note.priority})`);
    });
    console.log();

    console.log("✅ All tests completed successfully!");
    console.log("🎉 MCP Server is working correctly!");
  } catch (error) {
    console.error("❌ Test failed:", error);
    console.error("Stack trace:", error.stack);
    process.exit(1);
  } finally {
    try {
      await client.close();
      console.log("\n🔌 Disconnected from server");
    } catch (error) {
      console.error("Error closing client:", error);
    }
  }
}

// 運行測試
testWorkingMCPServer().catch((error) => {
  console.error("Test execution failed:", error);
  process.exit(1);
});
