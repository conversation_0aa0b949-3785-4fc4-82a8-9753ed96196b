import { BaseAgent } from './BaseAgent';
import { LocalAgent } from './LocalAgent';
import { RemoteAgent } from './RemoteAgent';
import type {
	AgentConfig,
	AgentTaskRequest,
	LocalModelConfig,
	RemoteServiceConfig,
	SmartRoutingConfig,
	TaskComplexityAssessment,
	AgentExecutionMode
} from '$lib/types/agent';

/**
 * 混合 AI Agent 實現
 * 智能路由：根據任務複雜度自動選擇本地或遠程處理
 */
export class HybridAgent extends BaseAgent {
	private localAgent: LocalAgent;
	private remoteAgent: RemoteAgent;
	private routingConfig: SmartRoutingConfig;
	private complexityCache = new Map<string, TaskComplexityAssessment>();

	constructor(
		config: AgentConfig,
		localModelConfig: LocalModelConfig,
		remoteServiceConfig: RemoteServiceConfig,
		routingConfig: SmartRoutingConfig
	) {
		super(config);
		this.localAgent = new LocalAgent({ ...config, id: `${config.id}-local` }, localModelConfig);
		this.remoteAgent = new RemoteAgent(
			{ ...config, id: `${config.id}-remote` },
			remoteServiceConfig
		);
		this.routingConfig = routingConfig;
	}

	/**
	 * 初始化混合 Agent
	 */
	protected async onInitialize(): Promise<void> {
		try {
			console.log(`Initializing hybrid agent ${this.id}...`);

			// 並行初始化本地和遠程 Agent
			await Promise.all([this.localAgent.initialize(), this.remoteAgent.initialize()]);

			console.log(`Hybrid agent ${this.id} initialized successfully`);
		} catch (error) {
			console.error(`Failed to initialize hybrid agent ${this.id}:`, error);
			throw error;
		}
	}

	/**
	 * 執行任務實現
	 */
	protected async onExecuteTask(request: AgentTaskRequest): Promise<any> {
		try {
			// 評估任務複雜度
			const complexity = this.assessTaskComplexity(request);

			// 選擇執行模式
			const executionMode = this.selectExecutionMode(complexity);

			// 發送路由決策事件
			this.emitEvent('routing_decision', {
				taskId: request.id,
				complexity,
				selectedMode: executionMode
			});

			// 根據選擇的模式執行任務
			switch (executionMode) {
				case 'local':
					return await this.executeLocally(request);
				case 'remote':
					return await this.executeRemotely(request);
				case 'hybrid':
					return await this.executeHybrid(request);
				default:
					throw new Error(`Unsupported execution mode: ${executionMode}`);
			}
		} catch (error) {
			// 如果主要執行模式失敗，嘗試回退策略
			return await this.handleExecutionFailure(request, error);
		}
	}

	/**
	 * 停止實現
	 */
	protected async onStop(): Promise<void> {
		try {
			await Promise.all([this.localAgent.stop(), this.remoteAgent.stop()]);
			this.complexityCache.clear();
			console.log(`Hybrid agent ${this.id} stopped successfully`);
		} catch (error) {
			console.error(`Error stopping hybrid agent ${this.id}:`, error);
		}
	}

	/**
	 * 配置更新實現
	 */
	protected async onConfigUpdate(oldConfig: AgentConfig, newConfig: AgentConfig): Promise<void> {
		// 更新子 Agent 的配置
		await Promise.all([
			this.localAgent.updateConfig(newConfig),
			this.remoteAgent.updateConfig(newConfig)
		]);
	}

	/**
	 * 取消任務實現
	 */
	protected async onCancelTask(taskId: string): Promise<void> {
		// 嘗試在兩個 Agent 中取消任務
		await Promise.allSettled([
			(this.localAgent as any).onCancelTask(taskId),
			(this.remoteAgent as any).onCancelTask(taskId)
		]);
	}

	/**
	 * 評估任務複雜度
	 */
	private assessTaskComplexity(request: AgentTaskRequest): TaskComplexityAssessment {
		const cacheKey = this.generateComplexityKey(request);

		// 檢查緩存
		if (this.complexityCache.has(cacheKey)) {
			return this.complexityCache.get(cacheKey)!;
		}

		const weights = this.routingConfig.complexityWeights;
		let score = 0;

		// 文本長度因子
		const textLength = this.getTextLength(request.input);
		const lengthFactor = Math.min(textLength / 10000, 1); // 10k 字符為滿分
		score += lengthFactor * weights.textLength;

		// 任務複雜度因子
		const taskComplexity = this.getTaskComplexity(request.type);
		score += taskComplexity * weights.taskComplexity;

		// 上下文大小因子
		const contextSize = this.getContextSize(request.context);
		const contextFactor = Math.min(contextSize / 5000, 1); // 5k 字符為滿分
		score += contextFactor * weights.contextSize;

		// 實時性要求因子
		const realTimeRequirement = this.getRealTimeRequirement(request);
		score += realTimeRequirement * weights.realTimeRequirement;

		// 歸一化分數
		const normalizedScore = Math.min(score, 1);

		// 推薦執行模式
		let recommendedMode: AgentExecutionMode;
		if (normalizedScore < this.routingConfig.localThreshold) {
			recommendedMode = 'local';
		} else if (normalizedScore > this.routingConfig.remoteThreshold) {
			recommendedMode = 'remote';
		} else {
			recommendedMode = 'hybrid';
		}

		const assessment: TaskComplexityAssessment = {
			score: normalizedScore,
			factors: {
				textLength: lengthFactor,
				semanticComplexity: taskComplexity,
				contextRequirement: contextFactor,
				computationalLoad: normalizedScore
			},
			recommendedMode,
			confidence: this.calculateConfidence(normalizedScore)
		};

		// 緩存結果
		this.complexityCache.set(cacheKey, assessment);

		return assessment;
	}

	/**
	 * 選擇執行模式
	 */
	private selectExecutionMode(complexity: TaskComplexityAssessment): AgentExecutionMode {
		// 基於複雜度評估和當前系統狀態選擇執行模式
		const baseMode = complexity.recommendedMode;

		// 檢查本地 Agent 可用性
		const localAvailable =
			this.localAgent.status === 'idle' && this.localAgent.getCurrentLoad() < 0.8;

		// 檢查遠程 Agent 可用性
		const remoteAvailable =
			this.remoteAgent.status === 'idle' && this.remoteAgent.getCurrentLoad() < 0.8;

		// 根據可用性調整選擇
		if (baseMode === 'local' && !localAvailable && remoteAvailable) {
			return 'remote';
		}

		if (baseMode === 'remote' && !remoteAvailable && localAvailable) {
			return 'local';
		}

		if (baseMode === 'hybrid') {
			if (localAvailable && remoteAvailable) {
				return 'hybrid';
			} else if (localAvailable) {
				return 'local';
			} else if (remoteAvailable) {
				return 'remote';
			}
		}

		return baseMode;
	}

	/**
	 * 本地執行
	 */
	private async executeLocally(request: AgentTaskRequest): Promise<any> {
		console.log(`Executing task ${request.id} locally`);
		return await this.localAgent.executeTask(request);
	}

	/**
	 * 遠程執行
	 */
	private async executeRemotely(request: AgentTaskRequest): Promise<any> {
		console.log(`Executing task ${request.id} remotely`);
		return await this.remoteAgent.executeTask(request);
	}

	/**
	 * 混合執行
	 */
	private async executeHybrid(request: AgentTaskRequest): Promise<any> {
		console.log(`Executing task ${request.id} in hybrid mode`);

		// 根據任務類型決定混合策略
		switch (request.type) {
			case 'content_generation':
				// 本地生成草稿，遠程優化
				return await this.hybridContentGeneration(request);
			case 'note_analysis':
				// 並行分析，合併結果
				return await this.hybridNoteAnalysis(request);
			case 'summarization':
				// 本地提取關鍵點，遠程生成摘要
				return await this.hybridSummarization(request);
			default:
				// 默認使用遠程執行
				return await this.executeRemotely(request);
		}
	}

	/**
	 * 混合內容生成
	 */
	private async hybridContentGeneration(request: AgentTaskRequest): Promise<any> {
		try {
			// 本地生成初稿
			const localResult = await this.localAgent.executeTask({
				...request,
				input: { ...request.input, maxLength: 300 }
			});

			// 遠程優化和擴展
			const enhancedRequest = {
				...request,
				input: {
					prompt: `Please enhance and expand this content: ${localResult.output.content}`,
					maxLength: request.input.maxLength || 500
				}
			};

			const remoteResult = await this.remoteAgent.executeTask(enhancedRequest);

			return {
				content: remoteResult.output.content,
				localDraft: localResult.output.content,
				enhancementApplied: true,
				executionMode: 'hybrid'
			};
		} catch (error) {
			// 如果混合執行失敗，回退到單一模式
			return await this.executeLocally(request);
		}
	}

	/**
	 * 混合筆記分析
	 */
	private async hybridNoteAnalysis(request: AgentTaskRequest): Promise<any> {
		try {
			// 並行執行本地和遠程分析
			const [localResult, remoteResult] = await Promise.allSettled([
				this.localAgent.executeTask(request),
				this.remoteAgent.executeTask(request)
			]);

			// 合併結果
			const localAnalysis = localResult.status === 'fulfilled' ? localResult.value.output : null;
			const remoteAnalysis = remoteResult.status === 'fulfilled' ? remoteResult.value.output : null;

			return this.mergeAnalysisResults(localAnalysis, remoteAnalysis);
		} catch (error) {
			return await this.executeRemotely(request);
		}
	}

	/**
	 * 混合摘要生成
	 */
	private async hybridSummarization(request: AgentTaskRequest): Promise<any> {
		try {
			// 本地提取關鍵點
			const keyPointsRequest = {
				...request,
				type: 'extraction' as const,
				input: { ...request.input, extractionType: 'keywords' }
			};

			const keyPointsResult = await this.localAgent.executeTask(keyPointsRequest);

			// 遠程基於關鍵點生成摘要
			const summaryRequest = {
				...request,
				input: {
					...request.input,
					context: `Key points: ${keyPointsResult.output.extracted.join(', ')}`
				}
			};

			const summaryResult = await this.remoteAgent.executeTask(summaryRequest);

			return {
				...summaryResult.output,
				keyPoints: keyPointsResult.output.extracted,
				executionMode: 'hybrid'
			};
		} catch (error) {
			return await this.executeRemotely(request);
		}
	}

	/**
	 * 處理執行失敗
	 */
	private async handleExecutionFailure(request: AgentTaskRequest, error: any): Promise<any> {
		console.warn(`Primary execution failed for task ${request.id}, attempting fallback:`, error);

		try {
			switch (this.routingConfig.fallbackStrategy) {
				case 'local':
					return await this.executeLocally(request);
				case 'remote':
					return await this.executeRemotely(request);
				case 'queue':
					// 重新加入隊列等待重試
					throw new Error('Task queued for retry');
				default:
					throw error;
			}
		} catch (fallbackError) {
			console.error(`Fallback execution also failed for task ${request.id}:`, fallbackError);
			throw fallbackError;
		}
	}

	// 輔助方法

	private generateComplexityKey(request: AgentTaskRequest): string {
		return `${request.type}-${JSON.stringify(request.input).length}-${Object.keys(request.context || {}).length}`;
	}

	private getTextLength(input: any): number {
		if (typeof input === 'string') return input.length;
		if (typeof input === 'object') {
			return JSON.stringify(input).length;
		}
		return 0;
	}

	private getTaskComplexity(taskType: string): number {
		const complexityMap: Record<string, number> = {
			search_query: 0.1,
			classification: 0.2,
			extraction: 0.3,
			summarization: 0.4,
			translation: 0.5,
			question_answering: 0.6,
			note_analysis: 0.7,
			content_generation: 0.8,
			code_analysis: 0.9
		};
		return complexityMap[taskType] || 0.5;
	}

	private getContextSize(context: any): number {
		if (!context) return 0;
		return JSON.stringify(context).length;
	}

	private getRealTimeRequirement(request: AgentTaskRequest): number {
		// 基於超時時間評估實時性要求
		const timeout = request.timeout || 30000;
		return Math.max(0, 1 - timeout / 60000); // 1分鐘為基準
	}

	private calculateConfidence(score: number): number {
		// 基於分數計算信心度
		if (score < 0.2 || score > 0.8) return 0.9;
		if (score < 0.4 || score > 0.6) return 0.7;
		return 0.5;
	}

	private mergeAnalysisResults(local: any, remote: any): any {
		if (!local && !remote) {
			throw new Error('Both local and remote analysis failed');
		}

		if (!local) return { ...remote, executionMode: 'remote' };
		if (!remote) return { ...local, executionMode: 'local' };

		// 合併兩個分析結果
		return {
			sentiment: remote.sentiment || local.sentiment,
			topics: [...new Set([...(local.topics || []), ...(remote.topics || [])])],
			complexity: remote.complexity || local.complexity,
			readability: (local.readability + (remote.readability || local.readability)) / 2,
			keyPoints: [...new Set([...(local.keyPoints || []), ...(remote.keyPoints || [])])],
			suggestions: [...new Set([...(local.suggestions || []), ...(remote.suggestions || [])])],
			executionMode: 'hybrid',
			localAnalysis: local,
			remoteAnalysis: remote
		};
	}
}
