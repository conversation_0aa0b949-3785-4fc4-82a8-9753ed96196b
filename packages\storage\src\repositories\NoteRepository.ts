// 簡化的類型定義，不依賴於 @life-note/core
export type NoteStatus = "draft" | "published" | "archived";
export type NotePriority = "low" | "medium" | "high" | "urgent";

export interface Note {
  id: string;
  title: string;
  content: string;
  category?: string;
  tags: string[];
  status: NoteStatus;
  priority: NotePriority;
  version: string;
  filePath?: string;
  checksum?: string;
  metadata?: Record<string, unknown>;
  authorId: string;
  workspaceId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Tag {
  name: string;
}
import type {
  PrismaClient,
  Note as PrismaNote,
} from "../prisma/generated/index.js";
import type {
  PaginatedResult,
  PaginationParams,
} from "../interfaces/IRepository.js";
import { BaseRepository } from "./BaseRepository.js";

/**
 * 筆記查詢條件
 */
export interface NoteQueryCondition {
  authorId?: string;
  workspaceId?: string;
  status?: NoteStatus;
  priority?: NotePriority;
  category?: string;
  tags?: string[];
  createdAfter?: Date;
  createdBefore?: Date;
  updatedAfter?: Date;
  updatedBefore?: Date;
}

/**
 * 筆記統計信息
 */
export interface NoteStatistics {
  total: number;
  byStatus: Record<NoteStatus, number>;
  byPriority: Record<NotePriority, number>;
  byCategory: Record<string, number>;
  byAuthor: Record<string, number>;
  byWorkspace: Record<string, number>;
  totalWords: number;
  averageWordsPerNote: number;
  createdThisWeek: number;
  createdThisMonth: number;
  updatedThisWeek: number;
  updatedThisMonth: number;
}

/**
 * 筆記存儲庫實現
 */
export class NoteRepository extends BaseRepository<Note, string, PrismaNote> {
  constructor(prisma: PrismaClient) {
    super(prisma);
  }

  protected getModel() {
    return this.prisma.note;
  }

  protected toDomainEntity(model: PrismaNote): Note {
    // 解析標籤
    const tags = this.parseJson<string[]>(model.tags) || [];

    // 解析元數據
    const metadata =
      this.parseJson<Record<string, unknown>>(model.metadata) || {};

    return {
      id: model.id,
      title: model.title,
      content: model.content,
      category: model.category || undefined,
      tags,
      status: model.status as NoteStatus,
      priority: model.priority as NotePriority,
      version: model.version,
      filePath: model.filePath || undefined,
      checksum: model.checksum || undefined,
      metadata,
      authorId: model.authorId,
      workspaceId: model.workspaceId || undefined,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt,
    };
  }

  protected toPrismaModel(
    entity: Note,
  ): Omit<PrismaNote, "id" | "createdAt" | "updatedAt"> {
    // 序列化標籤
    const tagsJson = this.stringifyJson(entity.tags);

    // 序列化元數據
    const metadataJson = this.stringifyJson(entity.metadata);

    return {
      title: entity.title,
      content: entity.content,
      category: entity.category || null,
      tags: tagsJson,
      status: entity.status,
      priority: entity.priority,
      version: entity.version,
      filePath: entity.filePath || null,
      checksum: entity.checksum || null,
      metadata: metadataJson,
      authorId: entity.authorId,
      workspaceId: entity.workspaceId || null,
    };
  }

  protected extractId(entity: Note): string {
    return entity.id;
  }

  /**
   * 根據作者 ID 查找筆記
   */
  async findByAuthorId(
    authorId: string,
    params?: PaginationParams,
  ): Promise<PaginatedResult<Note>> {
    const condition = { authorId };
    return params
      ? await this.findByConditionPaginated(condition, params)
      : await this.findByConditionPaginated(condition, { page: 1, limit: 50 });
  }

  /**
   * 根據工作空間 ID 查找筆記
   */
  async findByWorkspaceId(
    workspaceId: string,
    params?: PaginationParams,
  ): Promise<PaginatedResult<Note>> {
    const condition = { workspaceId };
    return params
      ? await this.findByConditionPaginated(condition, params)
      : await this.findByConditionPaginated(condition, { page: 1, limit: 50 });
  }

  /**
   * 根據狀態查找筆記
   */
  async findByStatus(
    status: NoteStatus,
    params?: PaginationParams,
  ): Promise<PaginatedResult<Note>> {
    const condition = { status };
    return params
      ? await this.findByConditionPaginated(condition, params)
      : await this.findByConditionPaginated(condition, { page: 1, limit: 50 });
  }

  /**
   * 根據分類查找筆記
   */
  async findByCategory(
    category: string,
    params?: PaginationParams,
  ): Promise<PaginatedResult<Note>> {
    const condition = { category };
    return params
      ? await this.findByConditionPaginated(condition, params)
      : await this.findByConditionPaginated(condition, { page: 1, limit: 50 });
  }

  /**
   * 根據標籤查找筆記
   */
  async findByTags(
    tags: string[],
    params?: PaginationParams,
  ): Promise<PaginatedResult<Note>> {
    try {
      const {
        page = 1,
        limit = 50,
        sortBy = "createdAt",
        sortOrder = "desc",
      } = params || {};
      const skip = (page - 1) * limit;

      // 構建標籤查詢條件
      const tagNames = tags;

      // 使用 SQL LIKE 查詢來匹配 JSON 數組中的標籤
      const whereConditions = tagNames.map((tagName) => ({
        tags: {
          contains: `"${tagName}"`,
        },
      }));

      const [results, total] = await Promise.all([
        this.prisma.note.findMany({
          where: {
            OR: whereConditions,
          },
          skip,
          take: limit,
          orderBy: { [sortBy]: sortOrder },
        }),
        this.prisma.note.count({
          where: {
            OR: whereConditions,
          },
        }),
      ]);

      const entities = results.map((result) => this.toDomainEntity(result));
      return this.createPaginatedResult(entities, total, page, limit);
    } catch (error) {
      console.error("Error finding notes by tags:", error);
      throw error;
    }
  }

  /**
   * 根據複合條件查找筆記
   */
  async findByCondition(
    condition: NoteQueryCondition,
    params?: PaginationParams,
  ): Promise<PaginatedResult<Note>> {
    try {
      const {
        page = 1,
        limit = 50,
        sortBy = "createdAt",
        sortOrder = "desc",
      } = params || {};
      const skip = (page - 1) * limit;

      // 構建查詢條件
      const whereClause: any = {};

      if (condition.authorId) {
        whereClause.authorId = condition.authorId;
      }

      if (condition.workspaceId) {
        whereClause.workspaceId = condition.workspaceId;
      }

      if (condition.status) {
        whereClause.status = condition.status;
      }

      if (condition.priority) {
        whereClause.priority = condition.priority;
      }

      if (condition.category) {
        whereClause.category = condition.category;
      }

      if (condition.tags && condition.tags.length > 0) {
        const tagConditions = condition.tags.map((tagName) => ({
          tags: {
            contains: `"${tagName}"`,
          },
        }));
        whereClause.OR = tagConditions;
      }

      if (condition.createdAfter || condition.createdBefore) {
        whereClause.createdAt = {};
        if (condition.createdAfter) {
          whereClause.createdAt.gte = condition.createdAfter;
        }
        if (condition.createdBefore) {
          whereClause.createdAt.lte = condition.createdBefore;
        }
      }

      if (condition.updatedAfter || condition.updatedBefore) {
        whereClause.updatedAt = {};
        if (condition.updatedAfter) {
          whereClause.updatedAt.gte = condition.updatedAfter;
        }
        if (condition.updatedBefore) {
          whereClause.updatedAt.lte = condition.updatedBefore;
        }
      }

      const [results, total] = await Promise.all([
        this.prisma.note.findMany({
          where: whereClause,
          skip,
          take: limit,
          orderBy: { [sortBy]: sortOrder },
        }),
        this.prisma.note.count({
          where: whereClause,
        }),
      ]);

      const entities = results.map((result) => this.toDomainEntity(result));
      return this.createPaginatedResult(entities, total, page, limit);
    } catch (error) {
      console.error("Error finding notes by condition:", error);
      throw error;
    }
  }

  /**
   * 查找最近更新的筆記
   */
  async findRecentlyUpdated(limit: number = 10): Promise<Note[]> {
    try {
      const results = await this.prisma.note.findMany({
        orderBy: { updatedAt: "desc" },
        take: limit,
      });

      return results.map((result) => this.toDomainEntity(result));
    } catch (error) {
      console.error("Error finding recently updated notes:", error);
      throw error;
    }
  }

  /**
   * 查找最近創建的筆記
   */
  async findRecentlyCreated(limit: number = 10): Promise<Note[]> {
    try {
      const results = await this.prisma.note.findMany({
        orderBy: { createdAt: "desc" },
        take: limit,
      });

      return results.map((result) => this.toDomainEntity(result));
    } catch (error) {
      console.error("Error finding recently created notes:", error);
      throw error;
    }
  }

  /**
   * 查找用戶的草稿筆記
   */
  async findDraftsByAuthor(
    authorId: string,
    params?: PaginationParams,
  ): Promise<PaginatedResult<Note>> {
    const condition = {
      authorId,
      status: "draft" as NoteStatus,
    };
    return params
      ? await this.findByConditionPaginated(condition, params)
      : await this.findByConditionPaginated(condition, { page: 1, limit: 50 });
  }

  /**
   * 查找用戶的已發布筆記
   */
  async findPublishedByAuthor(
    authorId: string,
    params?: PaginationParams,
  ): Promise<PaginatedResult<Note>> {
    const condition = {
      authorId,
      status: "published" as NoteStatus,
    };
    return params
      ? await this.findByConditionPaginated(condition, params)
      : await this.findByConditionPaginated(condition, { page: 1, limit: 50 });
  }

  /**
   * 查找相關筆記（基於標籤和分類的相似性）
   */
  async findRelatedNotes(noteId: string, limit: number = 5): Promise<Note[]> {
    try {
      // 首先獲取目標筆記
      const targetNote = await this.findById(noteId);
      if (!targetNote) {
        return [];
      }

      // 基於標籤和分類查找相關筆記
      const whereConditions: any[] = [];

      // 基於分類的相似性
      if (targetNote.category) {
        whereConditions.push({
          category: targetNote.category,
        });
      }

      // 基於標籤的相似性
      if (targetNote.tags.length > 0) {
        const tagConditions = targetNote.tags.map((tag) => ({
          tags: {
            contains: `"${tag}"`,
          },
        }));
        whereConditions.push(...tagConditions);
      }

      if (whereConditions.length === 0) {
        return [];
      }

      const results = await this.prisma.note.findMany({
        where: {
          AND: [
            {
              id: {
                not: noteId,
              },
            },
            {
              OR: whereConditions,
            },
          ],
        },
        orderBy: { updatedAt: "desc" },
        take: limit,
      });

      return results.map((result) => this.toDomainEntity(result));
    } catch (error) {
      console.error("Error finding related notes:", error);
      throw error;
    }
  }

  // 實現搜索功能
  async search(
    query: string,
    params?: PaginationParams,
  ): Promise<PaginatedResult<Note>> {
    try {
      const {
        page = 1,
        limit = 50,
        sortBy = "createdAt",
        sortOrder = "desc",
      } = params || {};
      const skip = (page - 1) * limit;

      const whereClause = {
        OR: [
          {
            title: {
              contains: query,
            },
          },
          {
            content: {
              contains: query,
            },
          },
          {
            category: {
              contains: query,
            },
          },
        ],
      };

      const [results, total] = await Promise.all([
        this.prisma.note.findMany({
          where: whereClause,
          skip,
          take: limit,
          orderBy: { [sortBy]: sortOrder },
        }),
        this.prisma.note.count({
          where: whereClause,
        }),
      ]);

      const entities = results.map((result) => this.toDomainEntity(result));
      return this.createPaginatedResult(entities, total, page, limit);
    } catch (error) {
      console.error("Error searching notes:", error);
      throw error;
    }
  }

  async searchByCondition(
    query: string,
    condition: Record<string, unknown>,
    params?: PaginationParams,
  ): Promise<PaginatedResult<Note>> {
    try {
      const {
        page = 1,
        limit = 50,
        sortBy = "createdAt",
        sortOrder = "desc",
      } = params || {};
      const skip = (page - 1) * limit;

      const whereClause = {
        AND: [
          condition,
          {
            OR: [
              {
                title: {
                  contains: query,
                },
              },
              {
                content: {
                  contains: query,
                },
              },
              {
                category: {
                  contains: query,
                },
              },
            ],
          },
        ],
      };

      const [results, total] = await Promise.all([
        this.prisma.note.findMany({
          where: whereClause,
          skip,
          take: limit,
          orderBy: { [sortBy]: sortOrder },
        }),
        this.prisma.note.count({
          where: whereClause,
        }),
      ]);

      const entities = results.map((result) => this.toDomainEntity(result));
      return this.createPaginatedResult(entities, total, page, limit);
    } catch (error) {
      console.error("Error searching notes by condition:", error);
      throw error;
    }
  }

  /**
   * 獲取筆記統計信息
   */
  async getStatistics(): Promise<NoteStatistics> {
    try {
      const [
        total,
        statusCounts,
        priorityCounts,
        categoryCounts,
        authorCounts,
        workspaceCounts,
      ] = await Promise.all([
        this.prisma.note.count(),
        this.prisma.note.groupBy({
          by: ["status"],
          _count: { status: true },
        }),
        this.prisma.note.groupBy({
          by: ["priority"],
          _count: { priority: true },
        }),
        this.prisma.note.groupBy({
          by: ["category"],
          _count: { category: true },
          where: { category: { not: null } },
        }),
        this.prisma.note.groupBy({
          by: ["authorId"],
          _count: { authorId: true },
        }),
        this.prisma.note.groupBy({
          by: ["workspaceId"],
          _count: { workspaceId: true },
          where: { workspaceId: { not: null } },
        }),
      ]);

      // 計算時間範圍統計
      const now = new Date();
      const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

      const [
        createdThisWeek,
        createdThisMonth,
        updatedThisWeek,
        updatedThisMonth,
      ] = await Promise.all([
        this.prisma.note.count({ where: { createdAt: { gte: weekAgo } } }),
        this.prisma.note.count({ where: { createdAt: { gte: monthAgo } } }),
        this.prisma.note.count({ where: { updatedAt: { gte: weekAgo } } }),
        this.prisma.note.count({ where: { updatedAt: { gte: monthAgo } } }),
      ]);

      // 計算字數統計（簡化版本）
      const totalWords = 0; // 需要實際計算
      const averageWordsPerNote = total > 0 ? totalWords / total : 0;

      return {
        total,
        byStatus: statusCounts.reduce(
          (acc, item) => {
            acc[item.status as NoteStatus] = item._count.status;
            return acc;
          },
          {} as Record<NoteStatus, number>,
        ),
        byPriority: priorityCounts.reduce(
          (acc, item) => {
            acc[item.priority as NotePriority] = item._count.priority;
            return acc;
          },
          {} as Record<NotePriority, number>,
        ),
        byCategory: categoryCounts.reduce(
          (acc, item) => {
            if (item.category) {
              acc[item.category] = item._count.category;
            }
            return acc;
          },
          {} as Record<string, number>,
        ),
        byAuthor: authorCounts.reduce(
          (acc, item) => {
            acc[item.authorId] = item._count.authorId;
            return acc;
          },
          {} as Record<string, number>,
        ),
        byWorkspace: workspaceCounts.reduce(
          (acc, item) => {
            if (item.workspaceId) {
              acc[item.workspaceId] = item._count.workspaceId;
            }
            return acc;
          },
          {} as Record<string, number>,
        ),
        totalWords,
        averageWordsPerNote,
        createdThisWeek,
        createdThisMonth,
        updatedThisWeek,
        updatedThisMonth,
      };
    } catch (error) {
      console.error("Error getting note statistics:", error);
      throw error;
    }
  }

  /**
   * 獲取用戶的筆記統計信息
   */
  async getUserStatistics(authorId: string): Promise<NoteStatistics> {
    // 類似於 getStatistics，但添加 authorId 過濾條件
    // 實現邏輯類似，這裡簡化
    return this.getStatistics();
  }

  /**
   * 獲取工作空間的筆記統計信息
   */
  async getWorkspaceStatistics(workspaceId: string): Promise<NoteStatistics> {
    // 類似於 getStatistics，但添加 workspaceId 過濾條件
    // 實現邏輯類似，這裡簡化
    return this.getStatistics();
  }

  /**
   * 批量更新筆記狀態
   */
  async batchUpdateStatus(
    noteIds: string[],
    status: NoteStatus,
  ): Promise<void> {
    try {
      await this.prisma.note.updateMany({
        where: {
          id: { in: noteIds },
        },
        data: { status },
      });
    } catch (error) {
      console.error("Error batch updating note status:", error);
      throw error;
    }
  }

  /**
   * 批量刪除筆記
   */
  async batchDelete(noteIds: string[]): Promise<void> {
    try {
      await this.prisma.note.deleteMany({
        where: {
          id: { in: noteIds },
        },
      });
    } catch (error) {
      console.error("Error batch deleting notes:", error);
      throw error;
    }
  }

  /**
   * 檢查筆記標題是否重複
   */
  async isTitleDuplicate(
    title: string,
    authorId: string,
    excludeId?: string,
  ): Promise<boolean> {
    try {
      const whereClause: any = {
        title,
        authorId,
      };

      if (excludeId) {
        whereClause.id = { not: excludeId };
      }

      const count = await this.prisma.note.count({
        where: whereClause,
      });

      return count > 0;
    } catch (error) {
      console.error("Error checking title duplicate:", error);
      throw error;
    }
  }

  /**
   * 獲取所有使用的分類
   */
  async getAllCategories(): Promise<string[]> {
    try {
      const results = await this.prisma.note.findMany({
        where: { category: { not: null } },
        select: { category: true },
        distinct: ["category"],
      });

      return results
        .map((result) => result.category)
        .filter((category): category is string => category !== null);
    } catch (error) {
      console.error("Error getting all categories:", error);
      throw error;
    }
  }

  /**
   * 獲取所有使用的標籤
   */
  async getAllTags(): Promise<string[]> {
    try {
      const results = await this.prisma.note.findMany({
        where: { tags: { not: null } },
        select: { tags: true },
      });

      const tagSet = new Set<string>();

      results.forEach((result) => {
        if (result.tags) {
          const tags = this.parseJson<string[]>(result.tags) || [];
          tags.forEach((tag) => tagSet.add(tag));
        }
      });

      return Array.from(tagSet);
    } catch (error) {
      console.error("Error getting all tags:", error);
      throw error;
    }
  }

  /**
   * 獲取用戶使用的分類
   */
  async getUserCategories(authorId: string): Promise<string[]> {
    try {
      const results = await this.prisma.note.findMany({
        where: {
          authorId,
          category: { not: null },
        },
        select: { category: true },
        distinct: ["category"],
      });

      return results
        .map((result) => result.category)
        .filter((category): category is string => category !== null);
    } catch (error) {
      console.error("Error getting user categories:", error);
      throw error;
    }
  }

  /**
   * 獲取用戶使用的標籤
   */
  async getUserTags(authorId: string): Promise<string[]> {
    try {
      const results = await this.prisma.note.findMany({
        where: {
          authorId,
          tags: { not: null },
        },
        select: { tags: true },
      });

      const tagSet = new Set<string>();

      results.forEach((result) => {
        if (result.tags) {
          const tags = this.parseJson<string[]>(result.tags) || [];
          tags.forEach((tag) => tagSet.add(tag));
        }
      });

      return Array.from(tagSet);
    } catch (error) {
      console.error("Error getting user tags:", error);
      throw error;
    }
  }

  /**
   * 獲取工作空間使用的分類
   */
  async getWorkspaceCategories(workspaceId: string): Promise<string[]> {
    try {
      const results = await this.prisma.note.findMany({
        where: {
          workspaceId,
          category: { not: null },
        },
        select: { category: true },
        distinct: ["category"],
      });

      return results
        .map((result) => result.category)
        .filter((category): category is string => category !== null);
    } catch (error) {
      console.error("Error getting workspace categories:", error);
      throw error;
    }
  }

  /**
   * 獲取工作空間使用的標籤
   */
  async getWorkspaceTags(workspaceId: string): Promise<string[]> {
    try {
      const results = await this.prisma.note.findMany({
        where: {
          workspaceId,
          tags: { not: null },
        },
        select: { tags: true },
      });

      const tagSet = new Set<string>();

      results.forEach((result) => {
        if (result.tags) {
          const tags = this.parseJson<string[]>(result.tags) || [];
          tags.forEach((tag) => tagSet.add(tag));
        }
      });

      return Array.from(tagSet);
    } catch (error) {
      console.error("Error getting workspace tags:", error);
      throw error;
    }
  }

  /**
   * 更新筆記的搜索索引
   */
  async updateSearchIndex(noteId: string): Promise<void> {
    try {
      const note = await this.prisma.note.findUnique({
        where: { id: noteId },
      });

      if (!note) return;

      const tags = this.parseJson<string[]>(note.tags) || [];
      const tagsText = tags.join(" ");

      await this.prisma.searchIndex.upsert({
        where: { noteId },
        update: {
          title: note.title,
          content: note.content,
          tags: tagsText,
          category: note.category,
        },
        create: {
          noteId,
          title: note.title,
          content: note.content,
          tags: tagsText,
          category: note.category,
        },
      });
    } catch (error) {
      console.error("Error updating search index:", error);
      throw error;
    }
  }

  /**
   * 重建所有筆記的搜索索引
   */
  async rebuildSearchIndex(): Promise<void> {
    try {
      // 清空現有索引
      await this.prisma.searchIndex.deleteMany();

      // 獲取所有筆記
      const notes = await this.prisma.note.findMany();

      // 重建索引
      for (const note of notes) {
        const tags = this.parseJson<string[]>(note.tags) || [];
        const tagsText = tags.join(" ");

        await this.prisma.searchIndex.create({
          data: {
            noteId: note.id,
            title: note.title,
            content: note.content,
            tags: tagsText,
            category: note.category,
          },
        });
      }
    } catch (error) {
      console.error("Error rebuilding search index:", error);
      throw error;
    }
  }
}
