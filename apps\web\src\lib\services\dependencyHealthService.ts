import type { Note } from '$lib/types/note';
import type { DependencyGraph } from '$lib/types/dependency';
import { dependencyService } from './dependencyService';

/**
 * 依賴關係健康檢查結果
 */
export interface DependencyHealthCheck {
	overallHealth: HealthScore;
	issues: HealthIssue[];
	metrics: HealthMetrics;
	suggestions: HealthSuggestion[];
	checkedAt: Date;
}

/**
 * 健康分數
 */
export interface HealthScore {
	score: number; // 0-100
	grade: 'A' | 'B' | 'C' | 'D' | 'F';
	status: 'excellent' | 'good' | 'fair' | 'poor' | 'critical';
}

/**
 * 健康問題
 */
export interface HealthIssue {
	id: string;
	type:
		| 'circular_dependency'
		| 'orphaned_note'
		| 'over_connected'
		| 'under_connected'
		| 'broken_link'
		| 'inconsistent_tagging';
	severity: 'low' | 'medium' | 'high' | 'critical';
	title: string;
	description: string;
	affectedNotes: string[];
	autoFixable: boolean;
	recommendation: string;
}

/**
 * 健康指標
 */
export interface HealthMetrics {
	connectivity: {
		score: number;
		connectedRatio: number;
		averageConnections: number;
		isolatedNodes: number;
	};
	structure: {
		score: number;
		circularDependencies: number;
		maxDepth: number;
		balanceScore: number;
	};
	consistency: {
		score: number;
		tagConsistency: number;
		namingConsistency: number;
		categoryConsistency: number;
	};
	maintainability: {
		score: number;
		complexityScore: number;
		modularityScore: number;
		documentationScore: number;
	};
}

/**
 * 健康建議
 */
export interface HealthSuggestion {
	type: 'improvement' | 'maintenance' | 'optimization';
	priority: 'low' | 'medium' | 'high';
	title: string;
	description: string;
	expectedImpact: string;
	effort: 'low' | 'medium' | 'high';
	actionSteps: string[];
}

/**
 * 依賴關係健康檢查服務
 */
export class DependencyHealthService {
	/**
	 * 執行完整的健康檢查
	 */
	async performHealthCheck(notes: Note[]): Promise<DependencyHealthCheck> {
		const graph = await dependencyService.analyzeDependencies(notes);

		const issues = await this.detectIssues(graph, notes);
		const metrics = this.calculateHealthMetrics(graph, notes);
		const overallHealth = this.calculateOverallHealth(metrics, issues);
		const suggestions = this.generateSuggestions(metrics, issues);

		return {
			overallHealth,
			issues,
			metrics,
			suggestions,
			checkedAt: new Date()
		};
	}

	/**
	 * 檢測健康問題
	 */
	private async detectIssues(graph: DependencyGraph, notes: Note[]): Promise<HealthIssue[]> {
		const issues: HealthIssue[] = [];

		// 檢測循環依賴
		const circularDeps = this.detectCircularDependencies(graph);
		issues.push(...circularDeps);

		// 檢測孤立筆記
		const orphanedNotes = this.detectOrphanedNotes(graph, notes);
		issues.push(...orphanedNotes);

		// 檢測過度連接的筆記
		const overConnected = this.detectOverConnectedNotes(graph, notes);
		issues.push(...overConnected);

		// 檢測連接不足的筆記
		const underConnected = this.detectUnderConnectedNotes(graph, notes);
		issues.push(...underConnected);

		// 檢測標籤不一致
		const tagInconsistencies = this.detectTagInconsistencies(notes);
		issues.push(...tagInconsistencies);

		return issues;
	}

	/**
	 * 計算健康指標
	 */
	private calculateHealthMetrics(graph: DependencyGraph, notes: Note[]): HealthMetrics {
		const connectivity = this.calculateConnectivityMetrics(graph, notes);
		const structure = this.calculateStructureMetrics(graph);
		const consistency = this.calculateConsistencyMetrics(notes);
		const maintainability = this.calculateMaintainabilityMetrics(graph, notes);

		return {
			connectivity,
			structure,
			consistency,
			maintainability
		};
	}

	/**
	 * 計算連接性指標
	 */
	private calculateConnectivityMetrics(graph: DependencyGraph, notes: Note[]) {
		const totalNodes = graph.nodes.length;
		const connectedNodes = new Set<string>();

		graph.links.forEach(link => {
			const sourceId = typeof link.source === 'string' ? link.source : link.source.id;
			const targetId = typeof link.target === 'string' ? link.target : link.target.id;
			connectedNodes.add(sourceId);
			connectedNodes.add(targetId);
		});

		const connectedRatio = totalNodes > 0 ? connectedNodes.size / totalNodes : 0;
		const averageConnections = totalNodes > 0 ? graph.links.length / totalNodes : 0;
		const isolatedNodes = totalNodes - connectedNodes.size;

		// 連接性分數：基於連接比例和平均連接數
		const score = Math.min(100, connectedRatio * 60 + Math.min(averageConnections / 3, 1) * 40);

		return {
			score,
			connectedRatio,
			averageConnections,
			isolatedNodes
		};
	}

	/**
	 * 計算結構指標
	 */
	private calculateStructureMetrics(graph: DependencyGraph) {
		const circularDependencies = this.countCircularDependencies(graph);
		const maxDepth = this.calculateMaxDepth(graph);
		const balanceScore = this.calculateStructuralBalance(graph);

		// 結構分數：無循環依賴得分高，深度適中得分高
		let score = 100;
		score -= circularDependencies * 20; // 每個循環依賴扣20分
		score -= Math.max(0, maxDepth - 5) * 5; // 深度超過5層每層扣5分
		score = Math.max(0, score);

		return {
			score,
			circularDependencies,
			maxDepth,
			balanceScore
		};
	}

	/**
	 * 計算一致性指標
	 */
	private calculateConsistencyMetrics(notes: Note[]) {
		const tagConsistency = this.calculateTagConsistency(notes);
		const namingConsistency = this.calculateNamingConsistency(notes);
		const categoryConsistency = this.calculateCategoryConsistency(notes);

		const score = (tagConsistency + namingConsistency + categoryConsistency) / 3;

		return {
			score,
			tagConsistency,
			namingConsistency,
			categoryConsistency
		};
	}

	/**
	 * 計算可維護性指標
	 */
	private calculateMaintainabilityMetrics(graph: DependencyGraph, notes: Note[]) {
		const complexityScore = this.calculateComplexityScore(graph);
		const modularityScore = this.calculateModularityScore(graph);
		const documentationScore = this.calculateDocumentationScore(notes);

		const score = (complexityScore + modularityScore + documentationScore) / 3;

		return {
			score,
			complexityScore,
			modularityScore,
			documentationScore
		};
	}

	/**
	 * 計算整體健康分數
	 */
	private calculateOverallHealth(metrics: HealthMetrics, issues: HealthIssue[]): HealthScore {
		// 基礎分數：各項指標的加權平均
		const baseScore =
			metrics.connectivity.score * 0.3 +
			metrics.structure.score * 0.3 +
			metrics.consistency.score * 0.2 +
			metrics.maintainability.score * 0.2;

		// 根據問題嚴重程度扣分
		let penaltyScore = 0;
		issues.forEach(issue => {
			switch (issue.severity) {
				case 'critical':
					penaltyScore += 15;
					break;
				case 'high':
					penaltyScore += 10;
					break;
				case 'medium':
					penaltyScore += 5;
					break;
				case 'low':
					penaltyScore += 2;
					break;
			}
		});

		const finalScore = Math.max(0, Math.min(100, baseScore - penaltyScore));

		let grade: 'A' | 'B' | 'C' | 'D' | 'F';
		let status: 'excellent' | 'good' | 'fair' | 'poor' | 'critical';

		if (finalScore >= 90) {
			grade = 'A';
			status = 'excellent';
		} else if (finalScore >= 80) {
			grade = 'B';
			status = 'good';
		} else if (finalScore >= 70) {
			grade = 'C';
			status = 'fair';
		} else if (finalScore >= 60) {
			grade = 'D';
			status = 'poor';
		} else {
			grade = 'F';
			status = 'critical';
		}

		return {
			score: finalScore,
			grade,
			status
		};
	}

	/**
	 * 生成健康建議
	 */
	private generateSuggestions(metrics: HealthMetrics, issues: HealthIssue[]): HealthSuggestion[] {
		const suggestions: HealthSuggestion[] = [];

		// 基於指標生成建議
		if (metrics.connectivity.score < 70) {
			suggestions.push({
				type: 'improvement',
				priority: 'high',
				title: '改善筆記連接性',
				description: '當前筆記間的連接較少，建議增加相關筆記的引用',
				expectedImpact: '提高知識發現能力和筆記間的關聯性',
				effort: 'medium',
				actionSteps: ['檢查孤立的筆記並添加相關連接', '使用標籤建立隱式連接', '創建主題索引筆記']
			});
		}

		if (metrics.structure.score < 70) {
			suggestions.push({
				type: 'improvement',
				priority: 'high',
				title: '優化依賴結構',
				description: '依賴結構存在問題，可能影響筆記的組織和理解',
				expectedImpact: '提高筆記結構的清晰度和可維護性',
				effort: 'high',
				actionSteps: ['解決循環依賴問題', '重新組織深層嵌套的依賴', '建立清晰的層次結構']
			});
		}

		// 基於問題生成建議
		const criticalIssues = issues.filter(issue => issue.severity === 'critical');
		if (criticalIssues.length > 0) {
			suggestions.push({
				type: 'maintenance',
				priority: 'high',
				title: '解決關鍵問題',
				description: '存在需要立即處理的關鍵問題',
				expectedImpact: '防止知識庫結構進一步惡化',
				effort: 'high',
				actionSteps: criticalIssues.map(issue => issue.recommendation)
			});
		}

		return suggestions;
	}

	// 輔助方法實現
	private detectCircularDependencies(graph: DependencyGraph): HealthIssue[] {
		// 檢測循環依賴的實現
		return [];
	}

	private detectOrphanedNotes(graph: DependencyGraph, notes: Note[]): HealthIssue[] {
		const connectedNodes = new Set<string>();
		graph.links.forEach(link => {
			const sourceId = typeof link.source === 'string' ? link.source : link.source.id;
			const targetId = typeof link.target === 'string' ? link.target : link.target.id;
			connectedNodes.add(sourceId);
			connectedNodes.add(targetId);
		});

		const orphanedNotes = notes.filter(note => !connectedNodes.has(note.id));

		return orphanedNotes.map(note => ({
			id: `orphaned-${note.id}`,
			type: 'orphaned_note' as const,
			severity: 'medium' as const,
			title: '孤立筆記',
			description: `筆記「${note.title}」沒有與其他筆記建立連接`,
			affectedNotes: [note.id],
			autoFixable: false,
			recommendation: '為此筆記添加相關的引用或被引用關係'
		}));
	}

	private detectOverConnectedNotes(graph: DependencyGraph, notes: Note[]): HealthIssue[] {
		// 檢測過度連接筆記的實現
		return [];
	}

	private detectUnderConnectedNotes(graph: DependencyGraph, notes: Note[]): HealthIssue[] {
		// 檢測連接不足筆記的實現
		return [];
	}

	private detectTagInconsistencies(notes: Note[]): HealthIssue[] {
		// 檢測標籤不一致的實現
		return [];
	}

	private countCircularDependencies(graph: DependencyGraph): number {
		// 計算循環依賴數量
		return 0;
	}

	private calculateMaxDepth(graph: DependencyGraph): number {
		// 計算最大深度
		return 0;
	}

	private calculateStructuralBalance(graph: DependencyGraph): number {
		// 計算結構平衡性
		return 0;
	}

	private calculateTagConsistency(notes: Note[]): number {
		// 計算標籤一致性
		return 80; // 示例值
	}

	private calculateNamingConsistency(notes: Note[]): number {
		// 計算命名一致性
		return 75; // 示例值
	}

	private calculateCategoryConsistency(notes: Note[]): number {
		// 計算分類一致性
		return 85; // 示例值
	}

	private calculateComplexityScore(graph: DependencyGraph): number {
		// 計算複雜度分數
		return 70; // 示例值
	}

	private calculateModularityScore(graph: DependencyGraph): number {
		// 計算模塊化分數
		return 75; // 示例值
	}

	private calculateDocumentationScore(notes: Note[]): number {
		// 計算文檔化分數
		const notesWithContent = notes.filter(note => note.content.length > 100);
		return notes.length > 0 ? (notesWithContent.length / notes.length) * 100 : 0;
	}
}

// 導出單例實例
export const dependencyHealthService = new DependencyHealthService();
