import {
  ValueObject,
  BusinessRuleViolationError,
} from "../../shared/Entity.js";

/**
 * 密碼強度等級
 */
export enum PasswordStrength {
  VERY_WEAK = "very-weak",
  WEAK = "weak",
  MEDIUM = "medium",
  STRONG = "strong",
  VERY_STRONG = "very-strong",
}

/**
 * 密碼值對象
 * 代表用戶密碼（已哈希）
 */
export class Password extends ValueObject {
  private readonly _hashedValue: string;
  private readonly _salt: string;
  private readonly _algorithm: string;

  constructor(hashedValue: string, salt: string, algorithm: string = "bcrypt") {
    super();
    this.validatePassword(hashedValue, salt, algorithm);
    this._hashedValue = hashedValue;
    this._salt = salt;
    this._algorithm = algorithm;
  }

  /**
   * 創建密碼值對象（用於已哈希的密碼）
   */
  static create(
    hashedValue: string,
    salt: string,
    algorithm: string = "bcrypt",
  ): Password {
    return new Password(hashedValue, salt, algorithm);
  }

  /**
   * 從明文密碼創建（需要在應用層實現哈希）
   * 這裡只是接口定義，實際哈希應該在應用層完成
   */
  static async fromPlainText(
    plainText: string,
    salt?: string,
  ): Promise<Password> {
    // 驗證明文密碼強度
    Password.validatePlainTextPassword(plainText);

    // 實際的哈希操作應該在應用層實現
    throw new Error(
      "Password hashing must be implemented in the application layer",
    );
  }

  /**
   * 驗證已哈希密碼的格式
   */
  private validatePassword(
    hashedValue: string,
    salt: string,
    algorithm: string,
  ): void {
    if (!hashedValue || hashedValue.trim().length === 0) {
      throw new BusinessRuleViolationError("Hashed password cannot be empty");
    }

    if (!salt || salt.trim().length === 0) {
      throw new BusinessRuleViolationError("Password salt cannot be empty");
    }

    if (!algorithm || algorithm.trim().length === 0) {
      throw new BusinessRuleViolationError(
        "Password algorithm cannot be empty",
      );
    }

    // 檢查支持的算法
    const supportedAlgorithms = ["bcrypt", "scrypt", "argon2", "pbkdf2"];
    if (!supportedAlgorithms.includes(algorithm.toLowerCase())) {
      throw new BusinessRuleViolationError(
        `Unsupported password algorithm: ${algorithm}`,
      );
    }

    // 基本格式檢查
    if (hashedValue.length < 32) {
      throw new BusinessRuleViolationError("Hashed password is too short");
    }

    if (salt.length < 16) {
      throw new BusinessRuleViolationError("Password salt is too short");
    }
  }

  /**
   * 驗證明文密碼強度
   */
  static validatePlainTextPassword(plainText: string): void {
    if (!plainText || plainText.length === 0) {
      throw new BusinessRuleViolationError("Password cannot be empty");
    }

    // 長度檢查
    if (plainText.length < 8) {
      throw new BusinessRuleViolationError(
        "Password must be at least 8 characters long",
      );
    }

    if (plainText.length > 128) {
      throw new BusinessRuleViolationError(
        "Password cannot be longer than 128 characters",
      );
    }

    // 強度檢查
    const strength = Password.calculatePasswordStrength(plainText);
    if (strength === PasswordStrength.VERY_WEAK) {
      throw new BusinessRuleViolationError("Password is too weak");
    }

    // 常見密碼檢查
    const commonPasswords = [
      "password",
      "123456",
      "123456789",
      "qwerty",
      "abc123",
      "password123",
      "admin",
      "letmein",
      "welcome",
      "monkey",
      "dragon",
      "master",
      "shadow",
      "superman",
      "michael",
      "football",
      "baseball",
      "liverpool",
      "jordan",
      "princess",
    ];

    if (commonPasswords.includes(plainText.toLowerCase())) {
      throw new BusinessRuleViolationError("Password is too common");
    }

    // 檢查是否包含重複字符
    if (/(.)\1{3,}/.test(plainText)) {
      throw new BusinessRuleViolationError(
        "Password cannot contain more than 3 consecutive identical characters",
      );
    }

    // 檢查是否為鍵盤序列
    const keyboardPatterns = [
      "qwerty",
      "asdfgh",
      "zxcvbn",
      "123456",
      "654321",
      "qwertyuiop",
      "asdfghjkl",
      "zxcvbnm",
    ];

    for (const pattern of keyboardPatterns) {
      if (plainText.toLowerCase().includes(pattern)) {
        throw new BusinessRuleViolationError(
          "Password cannot contain keyboard patterns",
        );
      }
    }
  }

  /**
   * 計算密碼強度
   */
  static calculatePasswordStrength(plainText: string): PasswordStrength {
    let score = 0;

    // 長度評分
    if (plainText.length >= 8) score += 1;
    if (plainText.length >= 12) score += 1;
    if (plainText.length >= 16) score += 1;

    // 字符類型評分
    if (/[a-z]/.test(plainText)) score += 1; // 小寫字母
    if (/[A-Z]/.test(plainText)) score += 1; // 大寫字母
    if (/[0-9]/.test(plainText)) score += 1; // 數字
    if (/[^a-zA-Z0-9]/.test(plainText)) score += 1; // 特殊字符

    // 複雜性評分
    if (/[a-z].*[A-Z]|[A-Z].*[a-z]/.test(plainText)) score += 1; // 大小寫混合
    if (/[a-zA-Z].*[0-9]|[0-9].*[a-zA-Z]/.test(plainText)) score += 1; // 字母數字混合
    if (/[a-zA-Z0-9].*[^a-zA-Z0-9]|[^a-zA-Z0-9].*[a-zA-Z0-9]/.test(plainText))
      score += 1; // 包含特殊字符

    // 根據評分確定強度
    if (score <= 2) return PasswordStrength.VERY_WEAK;
    if (score <= 4) return PasswordStrength.WEAK;
    if (score <= 6) return PasswordStrength.MEDIUM;
    if (score <= 8) return PasswordStrength.STRONG;
    return PasswordStrength.VERY_STRONG;
  }

  /**
   * 獲取密碼強度建議
   */
  static getPasswordStrengthSuggestions(plainText: string): string[] {
    const suggestions: string[] = [];

    if (plainText.length < 12) {
      suggestions.push("增加密碼長度至少12個字符");
    }

    if (!/[a-z]/.test(plainText)) {
      suggestions.push("添加小寫字母");
    }

    if (!/[A-Z]/.test(plainText)) {
      suggestions.push("添加大寫字母");
    }

    if (!/[0-9]/.test(plainText)) {
      suggestions.push("添加數字");
    }

    if (!/[^a-zA-Z0-9]/.test(plainText)) {
      suggestions.push("添加特殊字符（如 !@#$%^&*）");
    }

    if (/(.)\1{2,}/.test(plainText)) {
      suggestions.push("避免重複字符");
    }

    return suggestions;
  }

  /**
   * 驗證明文密碼是否匹配此哈希密碼
   * 注意：實際驗證需要在應用層實現
   */
  verify(plainText: string): Promise<boolean> {
    throw new Error(
      "Password verification must be implemented in the application layer",
    );
  }

  /**
   * 檢查密碼是否需要更新（基於算法和創建時間）
   */
  needsRehash(): boolean {
    // 如果使用的是舊算法，建議重新哈希
    const deprecatedAlgorithms = ["md5", "sha1", "sha256"];
    return deprecatedAlgorithms.includes(this._algorithm.toLowerCase());
  }

  /**
   * 獲取算法強度等級
   */
  getAlgorithmStrength(): "weak" | "medium" | "strong" | "very-strong" {
    switch (this._algorithm.toLowerCase()) {
      case "md5":
      case "sha1":
        return "weak";
      case "sha256":
      case "pbkdf2":
        return "medium";
      case "bcrypt":
      case "scrypt":
        return "strong";
      case "argon2":
        return "very-strong";
      default:
        return "weak";
    }
  }

  /**
   * 獲取哈希值（用於存儲）
   */
  get hashedValue(): string {
    return this._hashedValue;
  }

  /**
   * 獲取鹽值
   */
  get salt(): string {
    return this._salt;
  }

  /**
   * 獲取算法
   */
  get algorithm(): string {
    return this._algorithm;
  }

  /**
   * 獲取密碼的顯示格式（隱藏實際值）
   */
  toDisplayString(): string {
    return "••••••••";
  }

  /**
   * 獲取密碼摘要（用於日誌等，不包含敏感信息）
   */
  getSummary(): Record<string, unknown> {
    return {
      algorithm: this._algorithm,
      algorithmStrength: this.getAlgorithmStrength(),
      needsRehash: this.needsRehash(),
      saltLength: this._salt.length,
      hashLength: this._hashedValue.length,
    };
  }

  equals(other: ValueObject): boolean {
    if (!(other instanceof Password)) {
      return false;
    }
    return (
      this._hashedValue === other._hashedValue &&
      this._salt === other._salt &&
      this._algorithm === other._algorithm
    );
  }

  hashCode(): string {
    return `${this._algorithm}:${this._hashedValue}:${this._salt}`;
  }

  toString(): string {
    return this.toDisplayString();
  }

  toPlainObject(): Record<string, unknown> {
    return {
      hashedValue: this._hashedValue,
      salt: this._salt,
      algorithm: this._algorithm,
      algorithmStrength: this.getAlgorithmStrength(),
      needsRehash: this.needsRehash(),
    };
  }

  static fromPlainObject(data: Record<string, unknown>): Password {
    if (
      typeof data.hashedValue !== "string" ||
      typeof data.salt !== "string" ||
      typeof data.algorithm !== "string"
    ) {
      throw new BusinessRuleViolationError("Invalid Password data");
    }

    return new Password(data.hashedValue, data.salt, data.algorithm);
  }
}
